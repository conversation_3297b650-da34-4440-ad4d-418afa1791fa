import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from './ui/card';
import { Badge } from './ui/badge';
import { Button } from './ui/button';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from './ui/tabs';
import { Alert, AlertDescription } from './ui/alert';
// Simple icon components (replacing lucide-react)
const Brain = ({ className }: { className?: string }) => <span className={className}>🧠</span>;
const Shield = ({ className }: { className?: string }) => <span className={className}>🛡️</span>;
const TrendingUp = ({ className }: { className?: string }) => <span className={className}>📈</span>;
const Bot = ({ className }: { className?: string }) => <span className={className}>🤖</span>;
const AlertTriangle = ({ className }: { className?: string }) => <span className={className}>⚠️</span>;
const CheckCircle = ({ className }: { className?: string }) => <span className={className}>✅</span>;
const Activity = ({ className }: { className?: string }) => <span className={className}>📊</span>;
const Globe = ({ className }: { className?: string }) => <span className={className}>🌍</span>;
const DollarSign = ({ className }: { className?: string }) => <span className={className}>💰</span>;
const BarChart3 = ({ className }: { className?: string }) => <span className={className}>📊</span>;
const Zap = ({ className }: { className?: string }) => <span className={className}>⚡</span>;
const Eye = ({ className }: { className?: string }) => <span className={className}>👁️</span>;

interface AIMetrics {
  fraudDetection: {
    accuracy: number;
    transactionsAnalyzed: number;
    fraudPrevented: number;
    riskScore: number;
  };
  predictiveAnalytics: {
    forecastAccuracy: number;
    predictionsGenerated: number;
    inventoryOptimized: number;
    revenueImpact: number;
  };
  automation: {
    workflowsActive: number;
    tasksAutomated: number;
    timeSaved: number;
    successRate: number;
  };
  global: {
    currenciesSupported: number;
    countriesActive: number;
    complianceScore: number;
    internationalTransactions: number;
  };
}

const AIDashboard: React.FC = () => {
  const [metrics, setMetrics] = useState<AIMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [realTimeAlerts, setRealTimeAlerts] = useState<any[]>([]);

  useEffect(() => {
    fetchAIMetrics();
    setupRealTimeUpdates();
  }, []);

  const fetchAIMetrics = async () => {
    try {
      setLoading(true);
      
      // Simulate API calls to our Phase 5 & 6 endpoints
      const mockMetrics: AIMetrics = {
        fraudDetection: {
          accuracy: 96.5,
          transactionsAnalyzed: 15420,
          fraudPrevented: 127,
          riskScore: 0.15
        },
        predictiveAnalytics: {
          forecastAccuracy: 87.5,
          predictionsGenerated: 2840,
          inventoryOptimized: 89,
          revenueImpact: 125000
        },
        automation: {
          workflowsActive: 24,
          tasksAutomated: 1560,
          timeSaved: 340,
          successRate: 98.7
        },
        global: {
          currenciesSupported: 15,
          countriesActive: 23,
          complianceScore: 99.2,
          internationalTransactions: 8920
        }
      };

      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching AI metrics:', error);
    } finally {
      setLoading(false);
    }
  };

  const setupRealTimeUpdates = () => {
    // Simulate real-time alerts
    const alerts = [
      {
        id: 1,
        type: 'fraud',
        severity: 'high',
        message: 'High-risk transaction detected and blocked',
        timestamp: new Date(),
        icon: Shield
      },
      {
        id: 2,
        type: 'prediction',
        severity: 'info',
        message: 'Sales forecast updated: 15% increase expected',
        timestamp: new Date(Date.now() - 300000),
        icon: TrendingUp
      },
      {
        id: 3,
        type: 'automation',
        severity: 'success',
        message: 'Inventory reorder workflow completed successfully',
        timestamp: new Date(Date.now() - 600000),
        icon: Bot
      }
    ];
    
    setRealTimeAlerts(alerts);
  };

  const salesForecastData = [
    { date: '2024-01-01', actual: 12500, predicted: 12800 },
    { date: '2024-01-02', actual: 13200, predicted: 13100 },
    { date: '2024-01-03', actual: 11800, predicted: 12000 },
    { date: '2024-01-04', actual: 14500, predicted: 14200 },
    { date: '2024-01-05', actual: 13900, predicted: 14100 },
    { date: '2024-01-06', actual: null, predicted: 15200 },
    { date: '2024-01-07', actual: null, predicted: 14800 }
  ];

  const fraudRiskData = [
    { name: 'Low Risk', value: 85, color: '#10B981' },
    { name: 'Medium Risk', value: 12, color: '#F59E0B' },
    { name: 'High Risk', value: 3, color: '#EF4444' }
  ];

  const automationData = [
    { workflow: 'Inventory Reorder', executions: 45, success: 44 },
    { workflow: 'Dynamic Pricing', executions: 32, success: 31 },
    { workflow: 'Staff Scheduling', executions: 28, success: 28 },
    { workflow: 'Maintenance Alert', executions: 15, success: 15 },
    { workflow: 'Customer Retention', executions: 22, success: 21 }
  ];

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            <Brain className="h-8 w-8 text-blue-600" />
            AI & Global Intelligence Dashboard
          </h1>
          <p className="text-gray-600 mt-2">
            Advanced AI analytics, fraud detection, and global expansion insights
          </p>
        </div>
        <div className="flex gap-2">
          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
            <CheckCircle className="h-3 w-3 mr-1" />
            All Systems Operational
          </Badge>
          <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
            <Globe className="h-3 w-3 mr-1" />
            Global Ready
          </Badge>
        </div>
      </div>

      {/* Real-time Alerts */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {realTimeAlerts.map((alert) => (
          <Alert key={alert.id} className={`border-l-4 ${
            alert.severity === 'high' ? 'border-red-500 bg-red-50' :
            alert.severity === 'success' ? 'border-green-500 bg-green-50' :
            'border-blue-500 bg-blue-50'
          }`}>
            <alert.icon className="h-4 w-4" />
            <AlertDescription className="font-medium">
              {alert.message}
            </AlertDescription>
          </Alert>
        ))}
      </div>

      {/* Key Metrics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Fraud Detection</CardTitle>
            <Shield className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.fraudDetection.accuracy}%</div>
            <p className="text-xs text-blue-100">
              {metrics?.fraudDetection.transactionsAnalyzed.toLocaleString()} transactions analyzed
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Forecast Accuracy</CardTitle>
            <TrendingUp className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.predictiveAnalytics.forecastAccuracy}%</div>
            <p className="text-xs text-green-100">
              ${metrics?.predictiveAnalytics.revenueImpact.toLocaleString()} revenue impact
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Automation Success</CardTitle>
            <Bot className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.automation.successRate}%</div>
            <p className="text-xs text-purple-100">
              {metrics?.automation.timeSaved} hours saved this month
            </p>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Global Reach</CardTitle>
            <Globe className="h-4 w-4" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{metrics?.global.countriesActive}</div>
            <p className="text-xs text-orange-100">
              {metrics?.global.currenciesSupported} currencies supported
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="fraud">Fraud Detection</TabsTrigger>
          <TabsTrigger value="analytics">Predictive Analytics</TabsTrigger>
          <TabsTrigger value="global">Global Intelligence</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Sales Forecast vs Actual
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gradient-to-r from-blue-50 to-green-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600 mb-2">📈</div>
                    <p className="text-sm text-gray-600">Sales Forecast Chart</p>
                    <p className="text-xs text-gray-500 mt-1">87.5% Accuracy</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5" />
                  System Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">AI Processing Speed</span>
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    <Zap className="h-3 w-3 mr-1" />
                    &lt;500ms
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Global Response Time</span>
                  <Badge variant="outline" className="bg-blue-50 text-blue-700">
                    <Globe className="h-3 w-3 mr-1" />
                    &lt;2s worldwide
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Compliance Score</span>
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    {metrics?.global.complianceScore}%
                  </Badge>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm font-medium">Uptime</span>
                  <Badge variant="outline" className="bg-green-50 text-green-700">
                    <Activity className="h-3 w-3 mr-1" />
                    99.9%
                  </Badge>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="fraud" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  Risk Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gradient-to-br from-green-50 to-red-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600 mb-2">🛡️</div>
                    <p className="text-sm text-gray-600">Risk Distribution</p>
                    <div className="mt-4 space-y-2">
                      <div className="flex justify-between text-xs">
                        <span className="text-green-600">Low Risk: 85%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-yellow-600">Medium Risk: 12%</span>
                      </div>
                      <div className="flex justify-between text-xs">
                        <span className="text-red-600">High Risk: 3%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Eye className="h-5 w-5" />
                  Fraud Prevention Stats
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="text-center p-4 bg-red-50 rounded-lg">
                    <div className="text-2xl font-bold text-red-600">
                      {metrics?.fraudDetection.fraudPrevented}
                    </div>
                    <div className="text-sm text-red-700">Fraud Prevented</div>
                  </div>
                  <div className="text-center p-4 bg-green-50 rounded-lg">
                    <div className="text-2xl font-bold text-green-600">
                      ${(metrics?.fraudDetection.fraudPrevented || 0) * 150}
                    </div>
                    <div className="text-sm text-green-700">Losses Prevented</div>
                  </div>
                </div>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-sm">Model Accuracy</span>
                    <span className="text-sm font-medium">{metrics?.fraudDetection.accuracy}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${metrics?.fraudDetection.accuracy}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <div className="grid grid-cols-1 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  Automation Workflow Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg flex items-center justify-center">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-600 mb-2">🤖</div>
                    <p className="text-sm text-gray-600">Automation Performance</p>
                    <div className="mt-4 space-y-2">
                      <div className="text-xs text-gray-600">24 Active Workflows</div>
                      <div className="text-xs text-gray-600">98.7% Success Rate</div>
                      <div className="text-xs text-gray-600">340 Hours Saved</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="global" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">Multi-Currency</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {metrics?.global.currenciesSupported}
                </div>
                <p className="text-sm text-gray-600">Currencies Supported</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>USD/EUR</span>
                    <span className="text-green-600">0.85</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>USD/GBP</span>
                    <span className="text-green-600">0.73</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>USD/JPY</span>
                    <span className="text-green-600">110.0</span>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-center">Global Reach</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {metrics?.global.countriesActive}
                </div>
                <p className="text-sm text-gray-600">Countries Active</p>
                <div className="mt-4 space-y-2">
                  <Badge variant="outline" className="mr-1">North America</Badge>
                  <Badge variant="outline" className="mr-1">Europe</Badge>
                  <Badge variant="outline" className="mr-1">Asia-Pacific</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-center">Compliance</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {metrics?.global.complianceScore}%
                </div>
                <p className="text-sm text-gray-600">Compliance Score</p>
                <div className="mt-4 space-y-2">
                  <div className="flex justify-between text-xs">
                    <span>GDPR</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>CCPA</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>PCI-DSS</span>
                    <CheckCircle className="h-4 w-4 text-green-600" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>

      {/* Action Buttons */}
      <div className="flex gap-4 justify-center">
        <Button onClick={fetchAIMetrics} className="bg-blue-600 hover:bg-blue-700">
          <Activity className="h-4 w-4 mr-2" />
          Refresh Metrics
        </Button>
        <Button variant="outline" onClick={() => window.open('/api/ai/system/health', '_blank')}>
          <Eye className="h-4 w-4 mr-2" />
          View System Health
        </Button>
        <Button variant="outline" onClick={() => window.open('/api/global/system/gateway-status', '_blank')}>
          <Globe className="h-4 w-4 mr-2" />
          Global Status
        </Button>
      </div>
    </div>
  );
};

export default AIDashboard;
