import React, { useState, useEffect } from 'react';
import { 
  ShoppingBag, 
  X, 
  Plus, 
  Minus, 
  CreditCard, 
  DollarSign, 
  Tag, 
  Users,
  MapPin,
  Clock,
  AlertTriangle,
  CheckCircle,
  Utensils,
  Coffee,
  Receipt
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface Table {
  id: string;
  number: number;
  name?: string;
  seats: number;
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order';
  section: string;
  guestCount?: number;
}

const EnhancedOrderPanelWithFloor: React.FC = () => {
  const { state, dispatch, apiCall } = useEnhancedAppContext();
  const [showPayment, setShowPayment] = useState(false);
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [availableTables, setAvailableTables] = useState<Table[]>([]);
  const [tip, setTip] = useState(0);
  const [tabName, setTabName] = useState('');
  const [isEditingTab, setIsEditingTab] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [orderType, setOrderType] = useState<'dine_in' | 'takeout' | 'delivery'>('dine_in');
  const [guestCount, setGuestCount] = useState(1);
  
  const currentOrder = state.currentOrder;
  
  useEffect(() => {
    if (orderType === 'dine_in' && !currentOrder?.tableId) {
      fetchAvailableTables();
    }
  }, [orderType]);

  const fetchAvailableTables = async () => {
    try {
      const response = await apiCall('/api/floor/tables?status=available');
      if (response.ok) {
        const tables = await response.json();
        setAvailableTables(tables);
      }
    } catch (error) {
      console.error('Error fetching available tables:', error);
    }
  };

  const handleOrderTypeChange = (type: 'dine_in' | 'takeout' | 'delivery') => {
    setOrderType(type);
    
    if (type === 'dine_in' && currentOrder && currentOrder.items.length > 0 && !currentOrder.tableId) {
      // Show table selector for dine-in orders
      setShowTableSelector(true);
    } else if (type !== 'dine_in' && currentOrder?.tableId) {
      // Clear table assignment for non-dine-in orders
      dispatch({
        type: 'SET_CURRENT_ORDER',
        payload: {
          ...currentOrder,
          tableId: undefined,
          tableNumber: undefined,
          guestCount: undefined
        }
      });
    }
  };

  const handleTableSelection = async (table: Table) => {
    if (!currentOrder) return;

    try {
      // Update table status to occupied
      await apiCall(`/api/floor/tables/${table.id}/status`, {
        method: 'PUT',
        body: JSON.stringify({
          status: 'occupied',
          substatus: 'ordering',
          guestCount: guestCount
        })
      });

      // Update current order with table information
      dispatch({
        type: 'SET_CURRENT_ORDER',
        payload: {
          ...currentOrder,
          tableId: table.id,
          tableNumber: table.number,
          guestCount: guestCount,
          orderType: 'dine_in'
        }
      });

      setShowTableSelector(false);
    } catch (error) {
      console.error('Error assigning table:', error);
      alert('Failed to assign table. Please try again.');
    }
  };

  const handleRemoveItem = (itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM_FROM_ORDER', payload: itemId });
  };
  
  const handleUpdateQuantity = (itemId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(itemId);
    } else {
      dispatch({
        type: 'UPDATE_ITEM_QUANTITY',
        payload: { id: itemId, quantity: newQuantity }
      });
    }
  };
  
  const handleClearOrder = async () => {
    // If order is assigned to a table, clear the table status
    if (currentOrder?.tableId) {
      try {
        await apiCall(`/api/floor/tables/${currentOrder.tableId}/status`, {
          method: 'PUT',
          body: JSON.stringify({
            status: 'available',
            substatus: undefined,
            guestCount: 0
          })
        });
      } catch (error) {
        console.error('Error clearing table status:', error);
      }
    }

    dispatch({ type: 'CLEAR_CURRENT_ORDER' });
    setShowPayment(false);
    setTip(0);
    setGuestCount(1);
  };
  
  const handlePayment = async (method: 'cash' | 'card' | 'mobile') => {
    if (!currentOrder) return;

    // Validate dine-in orders have table assignment
    if (orderType === 'dine_in' && !currentOrder.tableId) {
      alert('Please select a table for dine-in orders.');
      setShowTableSelector(true);
      return;
    }

    // Validate order has items
    if (!currentOrder.items || currentOrder.items.length === 0) {
      alert('Cannot process payment for empty order.');
      return;
    }

    setIsProcessing(true);

    try {
      const orderData = {
        items: currentOrder.items,
        subtotal: currentOrder.subtotal || 0,
        tax: currentOrder.tax || 0,
        tip: tip || 0,
        total: (currentOrder.subtotal || 0) + (currentOrder.tax || 0) + (tip || 0),
        payment_method: method,
        order_type: orderType,
        table_id: currentOrder.tableId,
        table_number: currentOrder.tableNumber,
        guest_count: currentOrder.guestCount || guestCount,
        tab_name: currentOrder.tabName || tabName || undefined,
        employee_id: state.currentEmployee?.id,
        tenant_id: state.currentTenant?.id,
        location_id: state.currentLocation?.id
      };

      console.log('💳 Processing payment:', { ...orderData, items: `${orderData.items.length} items` });

      // Enhanced payment processing with timeout
      const response = await Promise.race([
        apiCall('/api/orders', {
          method: 'POST',
          body: JSON.stringify(orderData)
        }),
        new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Payment timeout')), 30000)
        )
      ]) as Response;

      if (response.ok) {
        const completedOrder = await response.json();
        console.log('✅ Order completed:', completedOrder);

        // Update table status if dine-in
        if (orderType === 'dine_in' && currentOrder.tableId) {
          try {
            await apiCall(`/api/floor/tables/${currentOrder.tableId}/status`, {
              method: 'PUT',
              body: JSON.stringify({
                status: 'occupied',
                substatus: 'eating',
                currentOrderId: completedOrder.id,
                orderTotal: orderData.total,
                orderItems: orderData.items.length
              })
            });
          } catch (tableError) {
            console.warn('⚠️ Failed to update table status:', tableError);
            // Don't fail the entire payment for table status update
          }
        }

        // Clear the current order
        dispatch({ type: 'CLEAR_CURRENT_ORDER' });

        // Add to order history if action exists
        try {
          dispatch({ type: 'ADD_ORDER_TO_HISTORY', payload: completedOrder });
        } catch (historyError) {
          console.warn('⚠️ Failed to add to order history:', historyError);
        }

        // Reset states
        setShowPayment(false);
        setTip(0);
        setTabName('');
        setIsEditingTab(false);
        setGuestCount(1);

        // Show success message with order details
        const orderSummary = `Order #${completedOrder.id || 'N/A'} - $${orderData.total.toFixed(2)}`;
        alert(`✅ Payment successful!\n${orderSummary}\nMethod: ${method.toUpperCase()}`);

        // Auto-print receipt if available
        if (window.print && method !== 'mobile') {
          setTimeout(() => {
            if (confirm('Would you like to print a receipt?')) {
              window.print();
            }
          }, 1000);
        }

      } else {
        const errorData = await response.text();
        console.error('❌ Payment failed:', response.status, errorData);
        alert(`Payment failed: ${response.status === 400 ? 'Invalid order data' : 'Server error'}. Please try again.`);
      }
    } catch (error) {
      console.error('💥 Payment error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      if (errorMessage.includes('timeout')) {
        alert('Payment timeout. Please check your connection and try again.');
      } else if (errorMessage.includes('fetch')) {
        alert('Connection error. Please check your network and try again.');
      } else {
        alert(`Payment error: ${errorMessage}. Please try again.`);
      }
    } finally {
      setIsProcessing(false);
    }
  };
  
  const handleSaveTab = () => {
    if (tabName.trim()) {
      dispatch({ type: 'SET_TAB_NAME', payload: tabName.trim() });
    }
    setIsEditingTab(false);
  };
  
  const calculateTipAmount = (percentage: number) => {
    if (!currentOrder) return 0;
    return Math.round(currentOrder.subtotal * percentage * 100) / 100;
  };

  const getTableInfo = () => {
    if (!currentOrder?.tableId) return null;
    
    return {
      tableNumber: currentOrder.tableNumber,
      guestCount: currentOrder.guestCount || guestCount
    };
  };
  
  // If there's no current order, show empty state
  if (!currentOrder || currentOrder.items.length === 0) {
    return (
      <div className="bg-white border-l border-gray-200 p-6 flex flex-col items-center justify-center h-full text-center">
        <ShoppingBag className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Items in Order</h3>
        <p className="text-gray-500">
          Add items from the menu to create a new order
        </p>
      </div>
    );
  }

  // Payment view
  if (showPayment) {
    return (
      <div className="bg-white border-l border-gray-200 p-4 flex flex-col h-full">
        <div className="flex justify-between items-center mb-6">
          <h3 className="text-xl font-semibold text-gray-900">Payment</h3>
          <button
            onClick={() => setShowPayment(false)}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Order Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">Subtotal</span>
            <span className="font-medium">${currentOrder.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">Tax</span>
            <span className="font-medium">${currentOrder.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between items-center mb-2">
            <span className="text-gray-600">Tip</span>
            <span className="font-medium">${tip.toFixed(2)}</span>
          </div>
          <div className="border-t border-gray-200 pt-2">
            <div className="flex justify-between items-center">
              <span className="text-lg font-semibold text-gray-900">Total</span>
              <span className="text-lg font-bold text-gray-900">
                ${(currentOrder.subtotal + currentOrder.tax + tip).toFixed(2)}
              </span>
            </div>
          </div>
        </div>

        {/* Table Information */}
        {orderType === 'dine_in' && getTableInfo() && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <div className="flex items-center space-x-2 mb-2">
              <MapPin className="h-4 w-4 text-blue-600" />
              <span className="font-medium text-blue-900">Table {getTableInfo()?.tableNumber}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4 text-blue-600" />
              <span className="text-sm text-blue-700">{getTableInfo()?.guestCount} guests</span>
            </div>
          </div>
        )}

        {/* Tip Selection */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-gray-700 mb-3">Add Tip</label>
          <div className="grid grid-cols-4 gap-2 mb-3">
            {[0.15, 0.18, 0.20, 0.25].map((percentage) => (
              <button
                key={percentage}
                onClick={() => setTip(calculateTipAmount(percentage))}
                className={`py-2 px-3 text-sm rounded-md border transition-colors ${
                  Math.abs(tip - calculateTipAmount(percentage)) < 0.01
                    ? 'bg-blue-600 text-white border-blue-600'
                    : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                }`}
              >
                {(percentage * 100).toFixed(0)}%
              </button>
            ))}
          </div>
          <input
            type="number"
            step="0.01"
            min="0"
            value={tip}
            onChange={(e) => setTip(parseFloat(e.target.value) || 0)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            placeholder="Custom tip amount"
          />
        </div>
        
        {/* Payment methods */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('cash')}
            disabled={isProcessing}
          >
            <DollarSign className="h-8 w-8 text-green-500 mb-2" />
            <span className="text-gray-900">Cash</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('card')}
            disabled={isProcessing}
          >
            <CreditCard className="h-8 w-8 text-blue-500 mb-2" />
            <span className="text-gray-900">Card</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 border border-gray-200 rounded-lg p-4 transition-colors disabled:opacity-50"
            onClick={() => handlePayment('mobile')}
            disabled={isProcessing}
          >
            <Receipt className="h-8 w-8 text-purple-500 mb-2" />
            <span className="text-gray-900">Mobile</span>
          </button>
        </div>

        {isProcessing && (
          <div className="text-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-2"></div>
            <p className="text-gray-600">Processing payment...</p>
          </div>
        )}
      </div>
    );
  }
  
  // Order view
  return (
    <div className="bg-white border-l border-gray-200 p-4 flex flex-col h-full">
      {/* Order header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h3 className="text-xl font-semibold text-gray-900">Current Order</h3>
          {currentOrder.tabName && !isEditingTab && (
            <div className="ml-2 bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full flex items-center">
              <Tag className="h-3 w-3 mr-1" />
              {currentOrder.tabName}
            </div>
          )}
        </div>
        <button
          className="text-red-500 hover:text-red-600 transition-colors"
          onClick={handleClearOrder}
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      
      {/* Order Type Selector */}
      <div className="mb-4">
        <label className="block text-sm font-medium text-gray-700 mb-2">Order Type</label>
        <div className="grid grid-cols-3 gap-2">
          {[
            { id: 'dine_in', label: 'Dine In', icon: '🍽️' },
            { id: 'takeout', label: 'Takeout', icon: '🥡' },
            { id: 'delivery', label: 'Delivery', icon: '🚚' }
          ].map((type) => (
            <button
              key={type.id}
              onClick={() => handleOrderTypeChange(type.id as any)}
              className={`p-2 rounded-md text-sm font-medium transition-colors border ${
                orderType === type.id
                  ? 'bg-blue-600 text-white border-blue-600'
                  : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
              }`}
            >
              <div className="text-center">
                <div className="text-lg mb-1">{type.icon}</div>
                <div>{type.label}</div>
              </div>
            </button>
          ))}
        </div>
      </div>

      {/* Table Information for Dine-in */}
      {orderType === 'dine_in' && (
        <div className="mb-4">
          {currentOrder.tableId ? (
            <div className="bg-green-50 border border-green-200 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-4 w-4 text-green-600" />
                  <span className="font-medium text-green-900">Table {currentOrder.tableNumber}</span>
                  <span className="text-sm text-green-700">({currentOrder.guestCount} guests)</span>
                </div>
                <button
                  onClick={() => setShowTableSelector(true)}
                  className="text-sm text-green-600 hover:text-green-700"
                >
                  Change
                </button>
              </div>
            </div>
          ) : (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <AlertTriangle className="h-4 w-4 text-yellow-600" />
                  <span className="text-sm text-yellow-800">No table selected</span>
                </div>
                <button
                  onClick={() => setShowTableSelector(true)}
                  className="px-3 py-1 bg-yellow-600 text-white text-sm rounded hover:bg-yellow-700"
                >
                  Select Table
                </button>
              </div>
            </div>
          )}
        </div>
      )}
      
      {/* Items list */}
      <div className="flex-grow overflow-y-auto mb-4">
        {currentOrder.items.map(item => (
          <div key={item.id} className="bg-gray-50 rounded-lg p-3 mb-2 flex items-center">
            <div className="flex-grow">
              <div className="flex justify-between">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                <button
                  className="text-gray-400 hover:text-red-400 transition-colors"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center bg-white rounded-md border border-gray-200">
                  <button
                    className="px-2 py-1 text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={() => handleUpdateQuantity(item.id, item.quantity - 1)}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-3 py-1 text-gray-900 font-medium">{item.quantity}</span>
                  <button
                    className="px-2 py-1 text-gray-600 hover:text-gray-900 transition-colors"
                    onClick={() => handleUpdateQuantity(item.id, item.quantity + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="font-semibold text-gray-900">
                  ${(item.price * item.quantity).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Order summary */}
      <div className="border-t border-gray-200 pt-4 mb-4">
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">Subtotal</span>
          <span className="font-medium">${currentOrder.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between items-center mb-2">
          <span className="text-gray-600">Tax</span>
          <span className="font-medium">${currentOrder.tax.toFixed(2)}</span>
        </div>
        <div className="flex justify-between items-center text-lg font-semibold">
          <span>Total</span>
          <span>${currentOrder.total.toFixed(2)}</span>
        </div>
      </div>
      
      {/* Checkout button */}
      <button
        className="bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center transition-colors"
        onClick={() => setShowPayment(true)}
      >
        <CreditCard className="h-5 w-5 mr-2" />
        Process Payment
      </button>

      {/* Table Selector Modal */}
      {showTableSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Select Table</h3>
              <button
                onClick={() => setShowTableSelector(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              {/* Guest Count */}
              <div className="mb-6">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Number of Guests
                </label>
                <div className="flex items-center space-x-3">
                  <button
                    onClick={() => setGuestCount(Math.max(1, guestCount - 1))}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-4 py-2 border border-gray-300 rounded-md font-medium">
                    {guestCount}
                  </span>
                  <button
                    onClick={() => setGuestCount(guestCount + 1)}
                    className="p-2 border border-gray-300 rounded-md hover:bg-gray-50"
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* Available Tables */}
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {availableTables
                  .filter(table => table.seats >= guestCount)
                  .map(table => (
                    <button
                      key={table.id}
                      onClick={() => handleTableSelection(table)}
                      className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors text-center"
                    >
                      <div className="flex items-center justify-center mb-2">
                        <div className="w-12 h-12 bg-green-500 text-white rounded-lg flex items-center justify-center">
                          <span className="font-bold">{table.number}</span>
                        </div>
                      </div>
                      {table.name && (
                        <div className="text-sm text-gray-600 mb-1">{table.name}</div>
                      )}
                      <div className="text-sm text-gray-500">
                        {table.seats} seats
                      </div>
                    </button>
                  ))}
              </div>
              
              {availableTables.filter(table => table.seats >= guestCount).length === 0 && (
                <div className="text-center py-8">
                  <AlertTriangle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Suitable Tables Available</h3>
                  <p className="text-gray-500">
                    No tables with {guestCount} or more seats are currently available.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedOrderPanelWithFloor;
