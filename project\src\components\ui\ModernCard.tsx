import React from 'react';
import { LucideIcon } from 'lucide-react';
import { useTheme, getThemeClasses, animations } from '../../contexts/ThemeContext';

interface ModernCardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  gradient?: boolean;
  border?: boolean;
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl';
  onClick?: () => void;
}

export function ModernCard({ 
  children, 
  className = '', 
  hover = false, 
  gradient = false,
  border = true,
  padding = 'md',
  onClick 
}: ModernCardProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const paddingClasses = {
    none: '',
    sm: 'p-3',
    md: 'p-6',
    lg: 'p-8',
    xl: 'p-10'
  };

  return (
    <div
      className={`
        ${themeClasses.bg.card}
        ${border ? themeClasses.border.primary : ''}
        ${border ? 'border' : ''}
        ${paddingClasses[padding]}
        ${hover ? themeClasses.shadow.hover : themeClasses.shadow.card}
        ${hover ? animations.card : ''}
        ${gradient && theme === 'dark' ? 'bg-gradient-to-br from-gray-800 to-gray-900' : ''}
        ${gradient && theme === 'light' ? 'bg-gradient-to-br from-white to-gray-50' : ''}
        rounded-xl
        ${onClick ? 'cursor-pointer' : ''}
        ${animations.transition}
        ${className}
      `}
      onClick={onClick}
    >
      {children}
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string | number;
  change?: {
    value: number;
    type: 'increase' | 'decrease' | 'neutral';
  };
  icon?: LucideIcon;
  description?: string;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  loading?: boolean;
}

export function StatCard({ 
  title, 
  value, 
  change, 
  icon: Icon, 
  description, 
  color = 'blue',
  loading = false 
}: StatCardProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const colorClasses = {
    blue: {
      icon: 'text-blue-600 dark:text-blue-400 bg-blue-100 dark:bg-blue-900/30',
      change: 'text-blue-600 dark:text-blue-400',
    },
    green: {
      icon: 'text-green-600 dark:text-green-400 bg-green-100 dark:bg-green-900/30',
      change: 'text-green-600 dark:text-green-400',
    },
    yellow: {
      icon: 'text-yellow-600 dark:text-yellow-400 bg-yellow-100 dark:bg-yellow-900/30',
      change: 'text-yellow-600 dark:text-yellow-400',
    },
    red: {
      icon: 'text-red-600 dark:text-red-400 bg-red-100 dark:bg-red-900/30',
      change: 'text-red-600 dark:text-red-400',
    },
    purple: {
      icon: 'text-purple-600 dark:text-purple-400 bg-purple-100 dark:bg-purple-900/30',
      change: 'text-purple-600 dark:text-purple-400',
    },
    indigo: {
      icon: 'text-indigo-600 dark:text-indigo-400 bg-indigo-100 dark:bg-indigo-900/30',
      change: 'text-indigo-600 dark:text-indigo-400',
    },
  };

  const changeClasses = {
    increase: 'text-green-600 dark:text-green-400',
    decrease: 'text-red-600 dark:text-red-400',
    neutral: 'text-gray-600 dark:text-gray-400',
  };

  if (loading) {
    return (
      <ModernCard hover>
        <div className="animate-pulse">
          <div className="flex items-center justify-between mb-4">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            <div className="h-8 w-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
          </div>
          <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mb-2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
        </div>
      </ModernCard>
    );
  }

  return (
    <ModernCard hover className={animations.fadeIn}>
      <div className="flex items-center justify-between mb-4">
        <h3 className={`text-sm font-medium ${themeClasses.text.secondary}`}>
          {title}
        </h3>
        {Icon && (
          <div className={`p-2 rounded-lg ${colorClasses[color].icon}`}>
            <Icon className="h-5 w-5" />
          </div>
        )}
      </div>
      
      <div className="flex items-baseline justify-between">
        <div>
          <p className={`text-3xl font-bold ${themeClasses.text.primary} mb-1`}>
            {value}
          </p>
          {description && (
            <p className={`text-sm ${themeClasses.text.tertiary}`}>
              {description}
            </p>
          )}
        </div>
        
        {change && (
          <div className={`text-sm font-medium ${changeClasses[change.type]} flex items-center`}>
            <span className="mr-1">
              {change.type === 'increase' ? '↗' : change.type === 'decrease' ? '↘' : '→'}
            </span>
            {Math.abs(change.value)}%
          </div>
        )}
      </div>
    </ModernCard>
  );
}

interface ActionCardProps {
  title: string;
  description: string;
  icon: LucideIcon;
  action: string;
  onClick: () => void;
  color?: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  disabled?: boolean;
}

export function ActionCard({ 
  title, 
  description, 
  icon: Icon, 
  action, 
  onClick, 
  color = 'blue',
  disabled = false 
}: ActionCardProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700',
    green: 'from-green-500 to-green-600 hover:from-green-600 hover:to-green-700',
    yellow: 'from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700',
    red: 'from-red-500 to-red-600 hover:from-red-600 hover:to-red-700',
    purple: 'from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700',
    indigo: 'from-indigo-500 to-indigo-600 hover:from-indigo-600 hover:to-indigo-700',
  };

  return (
    <ModernCard 
      hover 
      onClick={disabled ? undefined : onClick}
      className={`${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} ${animations.fadeIn}`}
    >
      <div className="flex items-start space-x-4">
        <div className={`p-3 rounded-xl bg-gradient-to-r ${colorClasses[color]} text-white`}>
          <Icon className="h-6 w-6" />
        </div>
        
        <div className="flex-1 min-w-0">
          <h3 className={`text-lg font-semibold ${themeClasses.text.primary} mb-1`}>
            {title}
          </h3>
          <p className={`text-sm ${themeClasses.text.secondary} mb-3`}>
            {description}
          </p>
          <button
            className={`
              text-sm font-medium px-4 py-2 rounded-lg transition-all duration-200
              ${disabled 
                ? 'bg-gray-100 dark:bg-gray-800 text-gray-400 cursor-not-allowed' 
                : `bg-gradient-to-r ${colorClasses[color]} text-white hover:shadow-lg transform hover:scale-105`
              }
            `}
            disabled={disabled}
          >
            {action}
          </button>
        </div>
      </div>
    </ModernCard>
  );
}

interface InfoCardProps {
  title: string;
  children: React.ReactNode;
  icon?: LucideIcon;
  badge?: {
    text: string;
    color: 'blue' | 'green' | 'yellow' | 'red' | 'purple' | 'indigo';
  };
}

export function InfoCard({ title, children, icon: Icon, badge }: InfoCardProps) {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);

  const badgeColors = {
    blue: 'bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300',
    green: 'bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300',
    yellow: 'bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300',
    red: 'bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300',
    purple: 'bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300',
    indigo: 'bg-indigo-100 dark:bg-indigo-900/30 text-indigo-800 dark:text-indigo-300',
  };

  return (
    <ModernCard className={animations.fadeIn}>
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-3">
          {Icon && (
            <div className={`p-2 rounded-lg ${themeClasses.bg.tertiary}`}>
              <Icon className={`h-5 w-5 ${themeClasses.text.accent}`} />
            </div>
          )}
          <h3 className={`text-lg font-semibold ${themeClasses.text.primary}`}>
            {title}
          </h3>
        </div>
        
        {badge && (
          <span className={`px-2 py-1 text-xs font-medium rounded-full ${badgeColors[badge.color]}`}>
            {badge.text}
          </span>
        )}
      </div>
      
      <div className={themeClasses.text.secondary}>
        {children}
      </div>
    </ModernCard>
  );
}
