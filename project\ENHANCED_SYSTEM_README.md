# Enhanced Multi-Tenant Restaurant POS System

This document describes the enhanced multi-tenant version of the Restaurant POS system, which adds enterprise-level features including multi-tenancy, real-time synchronization, and advanced authentication.

## 🚀 New Features

### Multi-Tenancy
- **Tenant Isolation**: Complete data separation between different restaurant chains/franchises
- **Location Management**: Support for multiple locations per tenant
- **Tenant-Specific Settings**: Customizable business rules, tax rates, and branding per tenant
- **Subscription Management**: Built-in support for different subscription plans and limits

### Enhanced Authentication
- **JWT-based Authentication**: Secure token-based authentication system
- **Role-based Access Control**: Granular permissions based on employee roles
- **Tenant Context**: Authentication includes tenant and location context
- **Session Management**: Secure session handling with automatic token refresh

### Real-time Capabilities
- **WebSocket Integration**: Real-time updates across all connected clients
- **Tenant-specific Rooms**: Isolated real-time channels per tenant/location
- **Live Order Updates**: Instant synchronization of orders, inventory, and system changes
- **Kitchen Display Integration**: Real-time kitchen order management

### Advanced Data Management
- **Tenant-scoped Queries**: All database operations are automatically scoped to the current tenant
- **Location-based Filtering**: Support for location-specific data when applicable
- **Data Migration**: Automated database schema updates with multi-tenant support
- **Backup and Recovery**: Tenant-specific data backup capabilities

## 🏗️ Architecture Overview

### Database Schema
```
tenants/
├── id (Primary Key)
├── name
├── slug (Unique identifier)
├── email
├── subscription details
└── settings (JSON)

locations/
├── id (Primary Key)
├── tenant_id (Foreign Key)
├── name
├── address
└── settings (JSON)

employees/
├── id (Primary Key)
├── tenant_id (Foreign Key)
├── location_id (Foreign Key)
├── name, role, permissions
└── authentication details

products/
├── id (Primary Key)
├── tenant_id (Foreign Key)
├── location_id (Foreign Key)
└── product details

orders/
├── id (Primary Key)
├── tenant_id (Foreign Key)
├── location_id (Foreign Key)
├── employee_id (Foreign Key)
└── order details
```

### API Structure
```
/api/auth/
├── POST /login - Multi-tenant login
├── POST /refresh - Token refresh
└── POST /logout - Secure logout

/api/tenants/
├── GET / - List tenants (super admin only)
├── POST / - Create tenant
├── GET /:id - Get tenant details
├── PUT /:id - Update tenant
└── GET /:id/settings - Get tenant settings

/api/locations/
├── GET / - List locations (tenant-scoped)
├── POST / - Create location
├── GET /:id - Get location details
└── PUT /:id - Update location

/api/products/
├── GET / - List products (tenant/location-scoped)
├── POST / - Create product
├── GET /:id - Get product
├── PUT /:id - Update product
└── DELETE /:id - Delete product

/api/orders/
├── GET / - List orders (tenant/location-scoped)
├── POST / - Create order
├── GET /:id - Get order
└── PUT /:id - Update order
```

## 🛠️ Setup Instructions

### 1. Database Migration
```bash
cd backend
node migrations/migrate.js
```

### 2. Start Enhanced Server
```bash
cd backend
node server-enhanced.js
```

### 3. Run Frontend (Enhanced Version)
```bash
cd project
npm run dev -- enhanced.html
```

### 4. Test the System
```bash
cd backend
node test-enhanced-system.js
```

## 🔧 Configuration

### Environment Variables
```bash
# Database
DATABASE_URL=sqlite:./pos_enhanced.db

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRES_IN=24h

# Server Configuration
PORT=3001
NODE_ENV=development

# WebSocket Configuration
WS_PORT=3001
```

### Tenant Settings Structure
```json
{
  "business_name": "Restaurant Name",
  "business_type": "restaurant",
  "timezone": "America/New_York",
  "currency": "USD",
  "tax_rate": 0.08,
  "receipt_header": "Welcome to Our Restaurant",
  "receipt_footer": "Thank you for your visit!",
  "theme_primary_color": "#3B82F6",
  "theme_secondary_color": "#6B7280",
  "features": {
    "multi_location": true,
    "kitchen_display": true,
    "loyalty_program": true,
    "inventory_management": true,
    "advanced_reporting": true,
    "third_party_integrations": false,
    "custom_branding": true
  }
}
```

## 🎯 Usage Examples

### Creating a New Tenant
```javascript
const tenant = await fetch('/api/tenants', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    name: 'Pizza Palace',
    slug: 'pizza-palace',
    email: '<EMAIL>',
    phone: '555-0123'
  })
});
```

### Multi-Tenant Login
```javascript
const response = await fetch('/api/auth/login', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({
    pin: '123456',
    tenant_slug: 'pizza-palace' // Optional
  })
});

const { token, employee, tenant, location } = await response.json();
```

### Creating Tenant-Scoped Data
```javascript
// Products are automatically scoped to the authenticated tenant
const product = await fetch('/api/products', {
  method: 'POST',
  headers: { 
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    name: 'Margherita Pizza',
    price: 14.99,
    category: 'Pizza'
  })
});
```

## 🔐 Security Features

### Authentication & Authorization
- **JWT Tokens**: Secure, stateless authentication
- **Role-based Permissions**: Granular access control
- **Tenant Isolation**: Complete data separation
- **Session Management**: Automatic token refresh and secure logout

### Data Protection
- **Input Validation**: Comprehensive request validation
- **SQL Injection Prevention**: Parameterized queries
- **XSS Protection**: Input sanitization
- **CORS Configuration**: Secure cross-origin requests

### Multi-Tenant Security
- **Tenant Scoping**: All queries automatically filtered by tenant
- **Location Access Control**: Location-based data access restrictions
- **Employee Permissions**: Role-based feature access
- **Audit Logging**: Comprehensive activity tracking

## 📊 Real-time Features

### WebSocket Events
```javascript
// Client-side WebSocket connection
const socket = io('ws://localhost:3001', {
  auth: { token: authToken }
});

// Join tenant-specific room
socket.emit('join-tenant', tenantId);

// Listen for real-time updates
socket.on('order-updated', (order) => {
  // Handle order update
});

socket.on('product-updated', (product) => {
  // Handle product update
});

socket.on('kitchen-order', (kitchenOrder) => {
  // Handle kitchen order update
});
```

### Supported Real-time Events
- `order-created` - New order placed
- `order-updated` - Order status changed
- `product-updated` - Product information changed
- `kitchen-order` - Kitchen display updates
- `employee-login` - Employee authentication events
- `system-notification` - System-wide notifications

## 🧪 Testing

### Automated Tests
```bash
# Test basic functionality
node backend/test-enhanced-system.js

# Test multi-tenant isolation
node backend/test-tenant-isolation.js

# Test real-time features
node backend/test-websocket.js
```

### Manual Testing Scenarios
1. **Multi-tenant Login**: Test login with different tenant slugs
2. **Data Isolation**: Verify tenant A cannot see tenant B's data
3. **Real-time Updates**: Test WebSocket synchronization
4. **Permission System**: Test role-based access control
5. **Location Management**: Test multi-location scenarios

## 🚀 Deployment

### Production Checklist
- [ ] Set strong JWT secret
- [ ] Configure production database
- [ ] Enable HTTPS/WSS
- [ ] Set up monitoring and logging
- [ ] Configure backup strategy
- [ ] Test disaster recovery
- [ ] Set up load balancing (if needed)
- [ ] Configure CDN for static assets

### Docker Deployment
```dockerfile
# Dockerfile example
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["node", "server-enhanced.js"]
```

### Environment-specific Configuration
```bash
# Production
NODE_ENV=production
DATABASE_URL=********************************/pos_prod
JWT_SECRET=super-secure-production-secret
REDIS_URL=redis://redis-host:6379

# Staging
NODE_ENV=staging
DATABASE_URL=********************************/pos_staging
JWT_SECRET=staging-secret
```

## 📈 Performance Considerations

### Database Optimization
- **Indexing**: Proper indexes on tenant_id, location_id columns
- **Query Optimization**: Efficient tenant-scoped queries
- **Connection Pooling**: Database connection management
- **Caching**: Redis caching for frequently accessed data

### Real-time Performance
- **WebSocket Scaling**: Socket.io clustering for multiple servers
- **Room Management**: Efficient tenant/location room handling
- **Message Queuing**: Background job processing
- **Rate Limiting**: API and WebSocket rate limiting

## 🔄 Migration from Single-Tenant

### Data Migration Steps
1. **Backup existing data**
2. **Run migration script**: `node migrations/migrate.js`
3. **Create default tenant**: Migrate existing data to a default tenant
4. **Update employee records**: Add tenant associations
5. **Test data integrity**: Verify all data is properly migrated
6. **Update frontend**: Switch to enhanced version

### Migration Script Example
```javascript
// migrations/migrate-to-multi-tenant.js
const db = require('../database');

async function migrateToMultiTenant() {
  // Create default tenant
  const defaultTenant = await db.run(`
    INSERT INTO tenants (name, slug, email, created_at, updated_at)
    VALUES ('Default Restaurant', 'default', '<EMAIL>', datetime('now'), datetime('now'))
  `);

  // Update existing records with tenant_id
  await db.run(`
    UPDATE employees SET tenant_id = ? WHERE tenant_id IS NULL
  `, [defaultTenant.lastID]);

  await db.run(`
    UPDATE products SET tenant_id = ? WHERE tenant_id IS NULL
  `, [defaultTenant.lastID]);

  // ... continue for other tables
}
```

## 📞 Support & Troubleshooting

### Common Issues
1. **Authentication Errors**: Check JWT secret and token expiration
2. **Data Isolation Issues**: Verify tenant_id is properly set in all queries
3. **WebSocket Connection Issues**: Check CORS and authentication
4. **Migration Errors**: Ensure database backup before running migrations

### Debug Mode
```bash
# Enable debug logging
DEBUG=pos:* node server-enhanced.js

# Database query logging
DB_DEBUG=true node server-enhanced.js
```

### Monitoring
- **Health Check Endpoint**: `GET /api/health`
- **Metrics Endpoint**: `GET /api/metrics`
- **Database Status**: `GET /api/status/database`
- **WebSocket Status**: `GET /api/status/websocket`

## 🎉 Conclusion

The Enhanced Multi-Tenant Restaurant POS System provides enterprise-level capabilities while maintaining the simplicity and usability of the original system. It's designed to scale from single restaurants to large franchise operations with complete data isolation, real-time synchronization, and advanced security features.

For additional support or feature requests, please refer to the project documentation or contact the development team.
