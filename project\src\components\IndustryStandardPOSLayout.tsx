import React, { useState, useEffect } from 'react';
import {
  Home,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Package,
  Clock,
  Receipt,
  CreditCard,
  Utensils,
  Coffee,
  MapPin,
  Bell,
  Search,
  Filter,
  Grid,
  List,
  Plus,
  Menu,
  X,
  Sun,
  Moon,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  User,
  LogOut,
  Printer,
  Calculator,
  Star,
  Heart,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Info,
  Zap,
  Shield
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import IndustryStandardPOS from './IndustryStandardPOS';
import ModernPaymentProcessor from './ModernPaymentProcessor';
import EnhancedFloorLayoutManager from './EnhancedFloorLayoutManager';
import Phase3InventoryManagement from './Phase3InventoryManagement';
import Phase3AdvancedAnalytics from './Phase3AdvancedAnalytics';
import UnifiedStaffScheduling from './UnifiedStaffScheduling';

interface Tab {
  id: string;
  name: string;
  icon: React.ReactNode;
  component: React.ReactNode;
  badge?: number;
  isActive?: boolean;
  permissions?: string[];
}

interface IndustryStandardPOSLayoutProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const IndustryStandardPOSLayout: React.FC<IndustryStandardPOSLayoutProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [activeTab, setActiveTab] = useState('pos');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [showPayment, setShowPayment] = useState(false);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [isOnline, setIsOnline] = useState(true);
  const [systemStatus, setSystemStatus] = useState({
    printer: 'online',
    payment: 'online',
    kitchen: 'online',
    inventory: 'synced'
  });

  // Define tabs with modern industry-standard features
  const tabs: Tab[] = [
    {
      id: 'pos',
      name: 'Point of Sale',
      icon: <ShoppingCart className="w-5 h-5" />,
      component: (
        <IndustryStandardPOS 
          isDarkMode={isDarkMode} 
          onThemeToggle={onThemeToggle}
        />
      ),
      badge: state.currentOrder?.items?.length || 0
    },
    {
      id: 'floor',
      name: 'Floor Layout',
      icon: <MapPin className="w-5 h-5" />,
      component: <EnhancedFloorLayoutManager />,
      permissions: ['floor_management']
    },
    {
      id: 'orders',
      name: 'Order Queue',
      icon: <Clock className="w-5 h-5" />,
      component: (
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-4">Order Management</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">Pending Orders</h3>
              <p className="text-3xl font-bold text-orange-500">12</p>
            </div>
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">In Progress</h3>
              <p className="text-3xl font-bold text-blue-500">8</p>
            </div>
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">Ready</h3>
              <p className="text-3xl font-bold text-green-500">5</p>
            </div>
          </div>
        </div>
      ),
      badge: 25
    },
    {
      id: 'inventory',
      name: 'Inventory',
      icon: <Package className="w-5 h-5" />,
      component: <Phase3InventoryManagement />,
      permissions: ['inventory_management']
    },
    {
      id: 'analytics',
      name: 'Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      component: <Phase3AdvancedAnalytics />,
      permissions: ['analytics']
    },
    {
      id: 'staff',
      name: 'Staff',
      icon: <Users className="w-5 h-5" />,
      component: <UnifiedStaffScheduling />,
      permissions: ['staff_management']
    },
    {
      id: 'reports',
      name: 'Reports',
      icon: <Receipt className="w-5 h-5" />,
      component: (
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-4">Business Reports</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">Daily Sales</h3>
              <p className="text-2xl font-bold text-green-500">$2,847.50</p>
              <p className="text-sm text-gray-500">+12% from yesterday</p>
            </div>
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">Orders Today</h3>
              <p className="text-2xl font-bold text-blue-500">156</p>
              <p className="text-sm text-gray-500">+8% from yesterday</p>
            </div>
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-2">Avg Order Value</h3>
              <p className="text-2xl font-bold text-purple-500">$18.25</p>
              <p className="text-sm text-gray-500">+3% from yesterday</p>
            </div>
          </div>
        </div>
      ),
      permissions: ['reports']
    },
    {
      id: 'settings',
      name: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      component: (
        <div className="p-6">
          <h2 className="text-2xl font-bold mb-6">System Settings</h2>
          <div className="space-y-6">
            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-4">Restaurant Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Restaurant Name</label>
                  <input 
                    type="text" 
                    value={state.currentTenant?.name || ''} 
                    className={`w-full px-3 py-2 rounded-lg border ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-2">Phone Number</label>
                  <input 
                    type="tel" 
                    className={`w-full px-3 py-2 rounded-lg border ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  />
                </div>
              </div>
            </div>

            <div className={`p-6 rounded-lg border ${
              isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
            }`}>
              <h3 className="font-semibold mb-4">System Status</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {Object.entries(systemStatus).map(([key, status]) => (
                  <div key={key} className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${
                      status === 'online' || status === 'synced' ? 'bg-green-500' : 'bg-red-500'
                    }`}></div>
                    <span className="capitalize">{key}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      ),
      permissions: ['settings']
    }
  ];

  // Filter tabs based on user permissions
  const availableTabs = tabs.filter(tab => {
    if (!tab.permissions) return true;
    const userRole = state.currentEmployee?.role;
    if (userRole === 'super_admin' || userRole === 'tenant_admin') return true;
    return tab.permissions.some(permission => 
      state.currentEmployee?.permissions?.includes(permission)
    );
  });

  // Network status simulation
  useEffect(() => {
    const interval = setInterval(() => {
      setIsOnline(Math.random() > 0.05); // 95% uptime
    }, 30000);
    return () => clearInterval(interval);
  }, []);

  // Notification system
  useEffect(() => {
    const interval = setInterval(() => {
      if (Math.random() > 0.8) { // 20% chance of notification
        const newNotification = {
          id: Date.now(),
          type: ['order', 'payment', 'inventory', 'system'][Math.floor(Math.random() * 4)],
          message: 'New order received',
          timestamp: new Date()
        };
        setNotifications(prev => [newNotification, ...prev.slice(0, 4)]);
      }
    }, 10000);
    return () => clearInterval(interval);
  }, []);

  const handlePaymentComplete = (paymentData: any) => {
    console.log('Payment completed:', paymentData);
    setShowPayment(false);
    setCurrentOrder(null);
    // Add success notification
    setNotifications(prev => [{
      id: Date.now(),
      type: 'payment',
      message: `Payment of $${paymentData.total.toFixed(2)} processed successfully`,
      timestamp: new Date()
    }, ...prev.slice(0, 4)]);
  };

  return (
    <div className={`h-screen flex transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`}>
      {/* Modern Sidebar */}
      <div className={`${
        sidebarCollapsed ? 'w-16' : 'w-64'
      } transition-all duration-300 ${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      } border-r flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
            }`}>
              <Home className="w-6 h-6 text-white" />
            </div>
            {!sidebarCollapsed && (
              <div>
                <h1 className="font-bold text-lg">RestroFlow</h1>
                <p className={`text-sm ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  Industry POS
                </p>
              </div>
            )}
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="flex-1 overflow-y-auto py-4">
          <nav className="space-y-1 px-2">
            {availableTabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 ${
                  activeTab === tab.id
                    ? isDarkMode 
                      ? 'bg-blue-600 text-white' 
                      : 'bg-blue-500 text-white'
                    : isDarkMode 
                      ? 'text-gray-300 hover:bg-gray-700' 
                      : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <div className="relative">
                  {tab.icon}
                  {tab.badge && tab.badge > 0 && (
                    <span className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {tab.badge > 99 ? '99+' : tab.badge}
                    </span>
                  )}
                </div>
                {!sidebarCollapsed && (
                  <span className="font-medium">{tab.name}</span>
                )}
              </button>
            ))}
          </nav>
        </div>

        {/* Sidebar Footer */}
        <div className={`p-4 border-t ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-full ${
              isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
            }`}>
              <User className="w-5 h-5" />
            </div>
            {!sidebarCollapsed && (
              <div className="flex-1">
                <p className="font-medium text-sm">
                  {state.currentEmployee?.name || 'Employee'}
                </p>
                <p className={`text-xs ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {state.currentEmployee?.role || 'Staff'}
                </p>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top Header Bar */}
        <header className={`flex items-center justify-between px-6 py-4 border-b transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setSidebarCollapsed(!sidebarCollapsed)}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <Menu className="w-5 h-5" />
            </button>
            
            <h2 className="text-xl font-semibold">
              {availableTabs.find(tab => tab.id === activeTab)?.name}
            </h2>
          </div>

          <div className="flex items-center space-x-4">
            {/* System Status Indicators */}
            <div className="flex items-center space-x-2">
              {isOnline ? (
                <Wifi className="w-5 h-5 text-green-500" />
              ) : (
                <WifiOff className="w-5 h-5 text-red-500" />
              )}
              <Signal className="w-5 h-5 text-green-500" />
              <Battery className="w-5 h-5 text-green-500" />
            </div>

            {/* Notifications */}
            <div className="relative">
              <button className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}>
                <Bell className="w-5 h-5" />
                {notifications.length > 0 && (
                  <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                    {notifications.length}
                  </span>
                )}
              </button>
            </div>

            {/* Theme Toggle */}
            <button
              onClick={onThemeToggle}
              className={`p-2 rounded-lg transition-colors duration-200 ${
                isDarkMode 
                  ? 'hover:bg-gray-700 text-gray-400' 
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              {isDarkMode ? <Sun className="w-5 h-5" /> : <Moon className="w-5 h-5" />}
            </button>
          </div>
        </header>

        {/* Tab Content */}
        <main className="flex-1 overflow-hidden">
          {availableTabs.find(tab => tab.id === activeTab)?.component}
        </main>
      </div>

      {/* Modern Payment Processor Modal */}
      <ModernPaymentProcessor
        order={currentOrder}
        isOpen={showPayment}
        onClose={() => setShowPayment(false)}
        onPaymentComplete={handlePaymentComplete}
        isDarkMode={isDarkMode}
      />
    </div>
  );
};

export default IndustryStandardPOSLayout;
