import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Globe,
  Smartphone,
  Monitor,
  ShoppingCart,
  Users,
  MessageSquare,
  Mail,
  Phone,
  MapPin,
  Clock,
  Star,
  TrendingUp,
  BarChart3,
  Settings,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  Eye,
  Heart,
  Share2
} from 'lucide-react';

interface Channel {
  id: string;
  name: string;
  type: 'online' | 'mobile' | 'instore' | 'social' | 'delivery';
  icon: React.ReactNode;
  status: 'active' | 'inactive' | 'maintenance';
  orders: number;
  revenue: number;
  customers: number;
  satisfaction: number;
  conversionRate: number;
}

interface CustomerJourney {
  id: string;
  customerId: string;
  customerName: string;
  touchpoints: Array<{
    channel: string;
    action: string;
    timestamp: Date;
    value?: number;
  }>;
  totalValue: number;
  status: 'active' | 'completed' | 'abandoned';
}

export function Phase2DOmnichannelExperience() {
  const [channels, setChannels] = useState<Channel[]>([]);
  const [customerJourneys, setCustomerJourneys] = useState<CustomerJourney[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedView, setSelectedView] = useState<'overview' | 'channels' | 'journeys'>('overview');

  const mockChannels: Channel[] = [
    {
      id: 'web',
      name: 'Website',
      type: 'online',
      icon: <Globe className="h-6 w-6" />,
      status: 'active',
      orders: 1250,
      revenue: 45600,
      customers: 3400,
      satisfaction: 4.3,
      conversionRate: 3.2
    },
    {
      id: 'mobile',
      name: 'Mobile App',
      type: 'mobile',
      icon: <Smartphone className="h-6 w-6" />,
      status: 'active',
      orders: 890,
      revenue: 32100,
      customers: 2100,
      satisfaction: 4.5,
      conversionRate: 4.1
    },
    {
      id: 'instore',
      name: 'In-Store POS',
      type: 'instore',
      icon: <Monitor className="h-6 w-6" />,
      status: 'active',
      orders: 2340,
      revenue: 78900,
      customers: 1890,
      satisfaction: 4.7,
      conversionRate: 12.5
    },
    {
      id: 'ubereats',
      name: 'Uber Eats',
      type: 'delivery',
      icon: <ShoppingCart className="h-6 w-6" />,
      status: 'active',
      orders: 567,
      revenue: 18900,
      customers: 1200,
      satisfaction: 4.1,
      conversionRate: 2.8
    },
    {
      id: 'doordash',
      name: 'DoorDash',
      type: 'delivery',
      icon: <ShoppingCart className="h-6 w-6" />,
      status: 'maintenance',
      orders: 423,
      revenue: 14200,
      customers: 890,
      satisfaction: 3.9,
      conversionRate: 2.5
    },
    {
      id: 'social',
      name: 'Social Media',
      type: 'social',
      icon: <Share2 className="h-6 w-6" />,
      status: 'active',
      orders: 156,
      revenue: 5600,
      customers: 2300,
      satisfaction: 4.2,
      conversionRate: 1.2
    }
  ];

  const mockCustomerJourneys: CustomerJourney[] = [
    {
      id: 'journey-1',
      customerId: 'cust-001',
      customerName: 'Sarah Johnson',
      touchpoints: [
        { channel: 'social', action: 'viewed_post', timestamp: new Date(Date.now() - 7200000) },
        { channel: 'web', action: 'visited_menu', timestamp: new Date(Date.now() - 3600000) },
        { channel: 'mobile', action: 'placed_order', timestamp: new Date(Date.now() - 1800000), value: 45.50 }
      ],
      totalValue: 45.50,
      status: 'completed'
    },
    {
      id: 'journey-2',
      customerId: 'cust-002',
      customerName: 'Mike Chen',
      touchpoints: [
        { channel: 'web', action: 'browsed_menu', timestamp: new Date(Date.now() - 10800000) },
        { channel: 'instore', action: 'visited_location', timestamp: new Date(Date.now() - 7200000) },
        { channel: 'instore', action: 'placed_order', timestamp: new Date(Date.now() - 7000000), value: 78.25 },
        { channel: 'mobile', action: 'left_review', timestamp: new Date(Date.now() - 3600000) }
      ],
      totalValue: 78.25,
      status: 'completed'
    },
    {
      id: 'journey-3',
      customerId: 'cust-003',
      customerName: 'Emma Davis',
      touchpoints: [
        { channel: 'mobile', action: 'downloaded_app', timestamp: new Date(Date.now() - 14400000) },
        { channel: 'mobile', action: 'browsed_menu', timestamp: new Date(Date.now() - 3600000) },
        { channel: 'mobile', action: 'added_to_cart', timestamp: new Date(Date.now() - 1800000) }
      ],
      totalValue: 0,
      status: 'abandoned'
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setChannels(mockChannels);
      setCustomerJourneys(mockCustomerJourneys);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed': return 'bg-green-100 text-green-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      case 'inactive':
      case 'abandoned': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'maintenance': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'inactive':
      case 'abandoned': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getChannelTypeColor = (type: string) => {
    switch (type) {
      case 'online': return 'bg-blue-100 text-blue-800';
      case 'mobile': return 'bg-purple-100 text-purple-800';
      case 'instore': return 'bg-green-100 text-green-800';
      case 'delivery': return 'bg-orange-100 text-orange-800';
      case 'social': return 'bg-pink-100 text-pink-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Omnichannel Experience</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2D Omnichannel Experience</h2>
          <p className="text-gray-600">Unified customer experience across all touchpoints</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Channel Settings
          </Button>
          <Button variant="outline" size="sm">
            <BarChart3 className="h-4 w-4 mr-2" />
            Analytics
          </Button>
        </div>
      </div>

      {/* View Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={selectedView === 'overview' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('overview')}
        >
          Overview
        </Button>
        <Button
          variant={selectedView === 'channels' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('channels')}
        >
          Channels
        </Button>
        <Button
          variant={selectedView === 'journeys' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedView('journeys')}
        >
          Customer Journeys
        </Button>
      </div>

      {selectedView === 'overview' && (
        <>
          {/* Overview Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Orders</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {channels.reduce((sum, channel) => sum + channel.orders, 0).toLocaleString()}
                    </p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-green-600">
                      ${channels.reduce((sum, channel) => sum + channel.revenue, 0).toLocaleString()}
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Customers</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {channels.reduce((sum, channel) => sum + channel.customers, 0).toLocaleString()}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Satisfaction</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {(channels.reduce((sum, channel) => sum + channel.satisfaction, 0) / channels.length).toFixed(1)}⭐
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Channel Performance Chart */}
          <Card>
            <CardHeader>
              <CardTitle>Channel Performance Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {channels.map((channel) => (
                  <div key={channel.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {channel.icon}
                      </div>
                      <div>
                        <h4 className="font-medium">{channel.name}</h4>
                        <div className="flex items-center space-x-2">
                          <Badge className={getChannelTypeColor(channel.type)}>
                            {channel.type}
                          </Badge>
                          <Badge className={getStatusColor(channel.status)}>
                            {channel.status}
                          </Badge>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-center">
                      <div>
                        <p className="text-sm text-gray-600">Orders</p>
                        <p className="font-semibold">{channel.orders}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Revenue</p>
                        <p className="font-semibold">${channel.revenue.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Customers</p>
                        <p className="font-semibold">{channel.customers}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600">Conversion</p>
                        <p className="font-semibold">{channel.conversionRate}%</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedView === 'channels' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {channels.map((channel) => (
            <Card key={channel.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {channel.icon}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{channel.name}</CardTitle>
                      <Badge className={getChannelTypeColor(channel.type)}>
                        {channel.type}
                      </Badge>
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getStatusIcon(channel.status)}
                    <Badge className={getStatusColor(channel.status)}>
                      {channel.status}
                    </Badge>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Orders</p>
                      <p className="text-xl font-bold">{channel.orders}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Revenue</p>
                      <p className="text-xl font-bold">${channel.revenue.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Customers</p>
                      <p className="text-xl font-bold">{channel.customers}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Satisfaction</p>
                      <p className="text-xl font-bold">{channel.satisfaction}⭐</p>
                    </div>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Conversion Rate</p>
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${Math.min(channel.conversionRate * 8, 100)}%` }}
                        ></div>
                      </div>
                      <span className="text-sm font-medium">{channel.conversionRate}%</span>
                    </div>
                  </div>
                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="h-3 w-3 mr-1" />
                      View Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {selectedView === 'journeys' && (
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Recent Customer Journeys</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {customerJourneys.map((journey) => (
                  <div key={journey.id} className="border border-gray-200 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div>
                        <h4 className="font-medium">{journey.customerName}</h4>
                        <p className="text-sm text-gray-600">Customer ID: {journey.customerId}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={getStatusColor(journey.status)}>
                          {journey.status}
                        </Badge>
                        {journey.totalValue > 0 && (
                          <span className="font-semibold text-green-600">
                            ${journey.totalValue.toFixed(2)}
                          </span>
                        )}
                      </div>
                    </div>
                    <div className="space-y-3">
                      <h5 className="font-medium text-sm">Customer Journey:</h5>
                      <div className="flex items-center space-x-2 overflow-x-auto pb-2">
                        {journey.touchpoints.map((touchpoint, index) => (
                          <React.Fragment key={index}>
                            <div className="flex flex-col items-center space-y-1 min-w-0 flex-shrink-0">
                              <div className="p-2 bg-blue-100 rounded-full">
                                {channels.find(c => c.id === touchpoint.channel)?.icon || <Globe className="h-4 w-4" />}
                              </div>
                              <div className="text-center">
                                <p className="text-xs font-medium">{touchpoint.channel}</p>
                                <p className="text-xs text-gray-600">{touchpoint.action.replace('_', ' ')}</p>
                                <p className="text-xs text-gray-500">{touchpoint.timestamp.toLocaleTimeString()}</p>
                                {touchpoint.value && (
                                  <p className="text-xs font-semibold text-green-600">${touchpoint.value}</p>
                                )}
                              </div>
                            </div>
                            {index < journey.touchpoints.length - 1 && (
                              <div className="flex-shrink-0 w-8 h-px bg-gray-300"></div>
                            )}
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
