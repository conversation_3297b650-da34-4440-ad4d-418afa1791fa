import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Store, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Users, 
  Settings,
  Palette,
  Image,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface TenantProfile {
  id: number;
  name: string;
  slug: string;
  business_name: string;
  business_type: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  logo_url?: string;
  primary_color: string;
  secondary_color: string;
  accent_color: string;
  status: 'active' | 'inactive' | 'suspended';
  employee_count: number;
  subscription_plan: string;
  created_at: string;
}

interface TenantProfileManagerProps {
  tenantSlug: string;
  onProfileUpdate?: (profile: TenantProfile) => void;
  readOnly?: boolean;
}

const TenantProfileManager: React.FC<TenantProfileManagerProps> = ({ 
  tenantSlug, 
  onProfileUpdate,
  readOnly = false
}) => {
  const [profile, setProfile] = useState<TenantProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    if (tenantSlug) {
      loadTenantProfile();
    }
  }, [tenantSlug]);

  const loadTenantProfile = async () => {
    try {
      setLoading(true);
      setError('');

      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/tenants/profile/${tenantSlug}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setProfile(data);
        if (onProfileUpdate) {
          onProfileUpdate(data);
        }
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to load tenant profile');
      }
    } catch (error) {
      console.error('Error loading tenant profile:', error);
      setError('Connection failed. Please check your network.');
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<TenantProfile>) => {
    if (readOnly) return;

    try {
      setSaving(true);
      setError('');
      setSuccess('');

      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/tenants/profile/${tenantSlug}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(updates)
      });

      if (response.ok) {
        const updatedProfile = await response.json();
        setProfile(updatedProfile);
        setSuccess('Profile updated successfully');
        
        if (onProfileUpdate) {
          onProfileUpdate(updatedProfile);
        }

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(''), 3000);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Failed to update profile');
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setError('Update failed. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return isDarkMode ? 'text-green-400' : 'text-green-600';
      case 'inactive':
        return isDarkMode ? 'text-gray-400' : 'text-gray-600';
      case 'suspended':
        return isDarkMode ? 'text-red-400' : 'text-red-600';
      default:
        return isDarkMode ? 'text-gray-400' : 'text-gray-600';
    }
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "px-2 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200`;
      case 'suspended':
        return `${baseClasses} bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200`;
    }
  };

  if (loading) {
    return (
      <div className={`p-6 rounded-lg ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="flex items-center justify-center space-x-2">
          <Loader2 className="w-5 h-5 animate-spin text-blue-600" />
          <span className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
            Loading tenant profile...
          </span>
        </div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className={`p-6 rounded-lg ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="text-center">
          <AlertCircle className="w-12 h-12 mx-auto mb-4 text-red-500" />
          <h3 className={`text-lg font-medium mb-2 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Profile Not Found
          </h3>
          <p className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
            Unable to load tenant profile for: {tenantSlug}
          </p>
          {error && (
            <p className="mt-2 text-sm text-red-600">{error}</p>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`p-6 rounded-lg ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    } shadow-lg space-y-6`}>
      
      {/* Header with Branding */}
      <div className="flex items-center space-x-4">
        <div 
          className="w-16 h-16 rounded-lg flex items-center justify-center shadow-md"
          style={{
            background: `linear-gradient(45deg, ${profile.primary_color}, ${profile.secondary_color})`
          }}
        >
          {profile.logo_url ? (
            <img 
              src={profile.logo_url} 
              alt={profile.business_name}
              className="w-12 h-12 rounded-md object-cover"
            />
          ) : (
            <Store className="w-8 h-8 text-white" />
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center space-x-3">
            <h2 className={`text-2xl font-bold ${
              isDarkMode ? 'text-white' : 'text-gray-900'
            }`}>
              {profile.business_name}
            </h2>
            <span className={getStatusBadge(profile.status)}>
              {profile.status.charAt(0).toUpperCase() + profile.status.slice(1)}
            </span>
          </div>
          
          <p className={`text-sm ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            {profile.business_type} • Slug: {profile.slug}
          </p>
        </div>
      </div>

      {/* Status Messages */}
      {error && (
        <div className="p-3 rounded-lg bg-red-50 border border-red-200 text-red-700 dark:bg-red-900/50 dark:border-red-700 dark:text-red-300">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{error}</span>
          </div>
        </div>
      )}

      {success && (
        <div className="p-3 rounded-lg bg-green-50 border border-green-200 text-green-700 dark:bg-green-900/50 dark:border-green-700 dark:text-green-300">
          <div className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 flex-shrink-0" />
            <span className="text-sm">{success}</span>
          </div>
        </div>
      )}

      {/* Business Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        
        {/* Contact Information */}
        <div className={`p-4 rounded-lg ${
          isDarkMode ? 'bg-gray-700/50' : 'bg-gray-50'
        }`}>
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            <Building2 className="w-5 h-5 mr-2" />
            Contact Information
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Mail className={`w-4 h-4 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`} />
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {profile.email}
              </span>
            </div>
            
            <div className="flex items-center space-x-3">
              <Phone className={`w-4 h-4 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`} />
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {profile.phone}
              </span>
            </div>
            
            <div className="flex items-start space-x-3">
              <MapPin className={`w-4 h-4 mt-0.5 ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`} />
              <div className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                <div>{profile.address}</div>
                <div>{profile.city}, {profile.state} {profile.zip_code}</div>
              </div>
            </div>
          </div>
        </div>

        {/* System Information */}
        <div className={`p-4 rounded-lg ${
          isDarkMode ? 'bg-gray-700/50' : 'bg-gray-50'
        }`}>
          <h3 className={`text-lg font-semibold mb-4 flex items-center ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            <Settings className="w-5 h-5 mr-2" />
            System Information
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Employee Count
              </span>
              <div className="flex items-center space-x-2">
                <Users className="w-4 h-4 text-blue-600" />
                <span className={`text-sm font-medium ${
                  isDarkMode ? 'text-gray-300' : 'text-gray-700'
                }`}>
                  {profile.employee_count}
                </span>
              </div>
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Subscription Plan
              </span>
              <span className={`text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {profile.subscription_plan}
              </span>
            </div>
            
            <div className="flex items-center justify-between">
              <span className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Created
              </span>
              <span className={`text-sm font-medium ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                {new Date(profile.created_at).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Branding Colors */}
      <div className={`p-4 rounded-lg ${
        isDarkMode ? 'bg-gray-700/50' : 'bg-gray-50'
      }`}>
        <h3 className={`text-lg font-semibold mb-4 flex items-center ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          <Palette className="w-5 h-5 mr-2" />
          Brand Colors
        </h3>
        
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center">
            <div 
              className="w-12 h-12 rounded-lg mx-auto mb-2 shadow-md"
              style={{ backgroundColor: profile.primary_color }}
            ></div>
            <span className={`text-xs ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Primary
            </span>
            <div className={`text-xs font-mono ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {profile.primary_color}
            </div>
          </div>
          
          <div className="text-center">
            <div 
              className="w-12 h-12 rounded-lg mx-auto mb-2 shadow-md"
              style={{ backgroundColor: profile.secondary_color }}
            ></div>
            <span className={`text-xs ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Secondary
            </span>
            <div className={`text-xs font-mono ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {profile.secondary_color}
            </div>
          </div>
          
          <div className="text-center">
            <div 
              className="w-12 h-12 rounded-lg mx-auto mb-2 shadow-md"
              style={{ backgroundColor: profile.accent_color }}
            ></div>
            <span className={`text-xs ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              Accent
            </span>
            <div className={`text-xs font-mono ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              {profile.accent_color}
            </div>
          </div>
        </div>
      </div>

      {/* Tenant Isolation Notice */}
      <div className={`p-3 rounded-lg border-2 border-dashed ${
        isDarkMode 
          ? 'border-blue-600 bg-blue-900/20' 
          : 'border-blue-300 bg-blue-50'
      }`}>
        <div className="flex items-center space-x-2">
          <CheckCircle className={`w-5 h-5 ${
            isDarkMode ? 'text-blue-400' : 'text-blue-600'
          }`} />
          <div>
            <p className={`text-sm font-medium ${
              isDarkMode ? 'text-blue-300' : 'text-blue-700'
            }`}>
              Tenant Data Isolation Active
            </p>
            <p className={`text-xs ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`}>
              All data and authentication is isolated to this tenant profile
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TenantProfileManager;
