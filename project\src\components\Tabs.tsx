import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { Order } from '../types';
import { Clock, History, Settings, Home, BarChart, QrCode, Package, Menu, X } from 'lucide-react';

interface TabProps {
  activeTab: string;
  setActiveTab: (tab: string) => void;
  availableTabs?: string[];
}

const Tabs: React.FC<TabProps> = ({ activeTab, setActiveTab, availableTabs }) => {
  const { state } = useAppContext();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  
  const getTabs = () => {
    const allTabs = [
      {
        id: 'pos',
        label: 'POS',
        icon: <Home className="h-5 w-5" />,
        disabled: false
      },
      {
        id: 'inventory',
        label: 'Inventory',
        icon: <Package className="h-5 w-5" />,
        disabled: false
      },
      {
        id: 'history',
        label: 'History',
        icon: <History className="h-5 w-5" />,
        disabled: false
      },
      {
        id: 'reports',
        label: 'Reports',
        icon: <BarChart className="h-5 w-5" />,
        disabled: false
      },
      {
        id: 'menu',
        label: 'Menu QR',
        icon: <QrCode className="h-5 w-5" />,
        disabled: false
      },
      {
        id: 'settings',
        label: 'Settings',
        icon: <Settings className="h-5 w-5" />,
        disabled: state.currentEmployee?.role !== 'super_admin' && state.currentEmployee?.role !== 'tenant_admin' && state.currentEmployee?.role !== 'manager'
      }
    ];

    // Filter tabs based on availableTabs prop if provided
    if (availableTabs && availableTabs.length > 0) {
      return allTabs.filter(tab => availableTabs.includes(tab.id));
    }

    return allTabs;
  };

  const handleTabClick = (tabId: string, disabled: boolean) => {
    if (!disabled) {
      setActiveTab(tabId);
      setIsMobileMenuOpen(false); // Close mobile menu when tab is selected
    }
  };
  
  return (
    <>
      {/* Desktop Navigation */}
      <div className="hidden md:flex bg-gray-900 p-2 space-x-2 overflow-x-auto scrollbar-thin scrollbar-thumb-gray-700">
        {getTabs().map(tab => (
          <button
            key={tab.id}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm transition-colors whitespace-nowrap ${
              activeTab === tab.id
                ? 'bg-purple-600 text-white'
                : 'bg-gray-800 text-gray-300 hover:bg-gray-700'
            } ${tab.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
            onClick={() => handleTabClick(tab.id, tab.disabled)}
            disabled={tab.disabled}
          >
            {tab.icon}
            <span>{tab.label}</span>
          </button>
        ))}
      </div>

      {/* Mobile Navigation */}
      <div className="md:hidden bg-gray-900">
        {/* Mobile menu button */}
        <div className="flex items-center justify-between p-2">
          <div className="flex items-center space-x-2">
            {getTabs().find(tab => tab.id === activeTab)?.icon}
            <span className="text-white font-medium">
              {getTabs().find(tab => tab.id === activeTab)?.label}
            </span>
          </div>
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="p-2 text-gray-300 hover:text-white transition-colors"
            aria-label="Toggle navigation menu"
          >
            {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile menu dropdown */}
        {isMobileMenuOpen && (
          <div className="border-t border-gray-700 bg-gray-800">
            <div className="grid grid-cols-2 gap-1 p-2">
              {getTabs().map(tab => (
                <button
                  key={tab.id}
                  className={`flex items-center space-x-2 px-3 py-3 rounded-md text-sm transition-colors ${
                    activeTab === tab.id
                      ? 'bg-purple-600 text-white'
                      : 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  } ${tab.disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
                  onClick={() => handleTabClick(tab.id, tab.disabled)}
                  disabled={tab.disabled}
                >
                  {tab.icon}
                  <span className="truncate">{tab.label}</span>
                </button>
              ))}
            </div>
          </div>
        )}
      </div>
    </>
  );
};

export default Tabs;
