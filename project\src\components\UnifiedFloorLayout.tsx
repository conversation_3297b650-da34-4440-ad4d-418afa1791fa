import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Users, Clock, CheckCircle, AlertCircle, Edit3, RefreshCw } from 'lucide-react';

interface Table {
  id: number;
  x: number;
  y: number;
  seats: number;
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning';
  order_id?: string;
}

const UnifiedFloorLayout: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [tables, setTables] = useState<Table[]>([]);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [isEditMode, setIsEditMode] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load floor layout
  useEffect(() => {
    const loadFloorLayout = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🏢 Loading floor layout...');
        
        const response = await apiCall('/api/floor/layout');
        if (response.ok) {
          const data = await response.json();
          setTables(data.tables || []);
          console.log('✅ Floor layout loaded:', data.tables?.length || 0, 'tables');
        }
      } catch (error) {
        console.error('❌ Error loading floor layout:', error);
        setError('Failed to load floor layout. Using default layout.');
        
        // Fallback to default tables
        const defaultTables = [
          { id: 1, x: 100, y: 100, seats: 4, status: 'available' as const },
          { id: 2, x: 200, y: 100, seats: 2, status: 'occupied' as const },
          { id: 3, x: 300, y: 100, seats: 6, status: 'reserved' as const },
          { id: 4, x: 100, y: 200, seats: 4, status: 'available' as const },
          { id: 5, x: 200, y: 200, seats: 8, status: 'occupied' as const },
          { id: 6, x: 300, y: 200, seats: 2, status: 'needs-cleaning' as const },
          { id: 7, x: 400, y: 100, seats: 4, status: 'available' as const },
          { id: 8, x: 400, y: 200, seats: 6, status: 'available' as const }
        ];
        setTables(defaultTables);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadFloorLayout();
  }, [apiCall]);

  const getTableStatusColor = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return 'bg-green-500 hover:bg-green-600 border-green-600';
      case 'occupied':
        return 'bg-red-500 hover:bg-red-600 border-red-600';
      case 'reserved':
        return 'bg-yellow-500 hover:bg-yellow-600 border-yellow-600';
      case 'needs-cleaning':
        return 'bg-orange-500 hover:bg-orange-600 border-orange-600';
      default:
        return 'bg-gray-500 hover:bg-gray-600 border-gray-600';
    }
  };

  const getTableStatusIcon = (status: Table['status']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        return <Users className="h-4 w-4" />;
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleTableClick = async (table: Table) => {
    if (isEditMode) {
      setSelectedTable(table);
      return;
    }

    // Select table for order taking
    setSelectedTable(table);
    
    // If table is available, mark as occupied
    if (table.status === 'available') {
      await updateTableStatus(table.id, 'occupied');
    }
  };

  const updateTableStatus = async (tableId: number, newStatus: Table['status']) => {
    try {
      console.log(`🏢 Updating table ${tableId} status to: ${newStatus}`);
      
      const response = await apiCall(`/api/floor/tables/${tableId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status: newStatus })
      });
      
      if (response.ok) {
        // Update local state
        setTables(prev => prev.map(table => 
          table.id === tableId ? { ...table, status: newStatus } : table
        ));
        console.log('✅ Table status updated successfully');
      }
    } catch (error) {
      console.error('❌ Error updating table status:', error);
      alert('Failed to update table status. Please try again.');
    }
  };

  const getStatusLabel = (status: Table['status']) => {
    switch (status) {
      case 'available': return 'Available';
      case 'occupied': return 'Occupied';
      case 'reserved': return 'Reserved';
      case 'needs-cleaning': return 'Needs Cleaning';
      default: return status;
    }
  };

  const getTableCounts = () => {
    return {
      available: tables.filter(t => t.status === 'available').length,
      occupied: tables.filter(t => t.status === 'occupied').length,
      reserved: tables.filter(t => t.status === 'reserved').length,
      cleaning: tables.filter(t => t.status === 'needs-cleaning').length,
      total: tables.length
    };
  };

  const counts = getTableCounts();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading floor layout...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Restaurant Floor Layout</h2>
            <p className="text-sm text-gray-500">Manage table status and reservations</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setIsEditMode(!isEditMode)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                isEditMode
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200 border border-gray-300'
              }`}
            >
              <Edit3 className="h-4 w-4" />
              <span>{isEditMode ? 'Exit Edit' : 'Edit Mode'}</span>
            </button>
          </div>
        </div>
      </div>

      {/* Stats */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="grid grid-cols-5 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{counts.total}</div>
            <div className="text-sm text-gray-500">Total Tables</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{counts.available}</div>
            <div className="text-sm text-gray-500">Available</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{counts.occupied}</div>
            <div className="text-sm text-gray-500">Occupied</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">{counts.reserved}</div>
            <div className="text-sm text-gray-500">Reserved</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-600">{counts.cleaning}</div>
            <div className="text-sm text-gray-500">Cleaning</div>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Floor Plan */}
      <div className="flex-1 overflow-auto p-4">
        <div className="relative bg-gray-100 rounded-lg min-h-[500px] min-w-[600px]" style={{ height: '500px', width: '600px' }}>
          {/* Background grid */}
          <div 
            className="absolute inset-0 opacity-20"
            style={{
              backgroundImage: 'radial-gradient(circle, #9CA3AF 1px, transparent 1px)',
              backgroundSize: '20px 20px'
            }}
          />
          
          {/* Tables */}
          {tables.map((table) => (
            <div
              key={table.id}
              className={`absolute cursor-pointer transition-all duration-200 rounded-lg border-2 ${getTableStatusColor(table.status)} ${
                selectedTable?.id === table.id ? 'ring-4 ring-blue-400 ring-opacity-75 scale-110' : ''
              } flex flex-col items-center justify-center text-white font-semibold shadow-lg`}
              style={{
                left: `${table.x}px`,
                top: `${table.y}px`,
                width: '80px',
                height: '80px',
              }}
              onClick={() => handleTableClick(table)}
            >
              <div className="flex items-center space-x-1">
                {getTableStatusIcon(table.status)}
                <span className="text-sm font-bold">{table.id}</span>
              </div>
              <div className="text-xs opacity-90">
                {table.seats} seats
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Selected Table Info */}
      {selectedTable && (
        <div className="bg-white border-t border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Table {selectedTable.id}</h3>
              <p className="text-sm text-gray-500">{selectedTable.seats} seats • {getStatusLabel(selectedTable.status)}</p>
            </div>
            {isEditMode && (
              <div className="flex space-x-2">
                {(['available', 'occupied', 'reserved', 'needs-cleaning'] as const).map((status) => (
                  <button
                    key={status}
                    onClick={() => updateTableStatus(selectedTable.id, status)}
                    className={`px-3 py-1 text-xs rounded-md transition-colors ${
                      selectedTable.status === status
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {getStatusLabel(status)}
                  </button>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-gray-50 border-t border-gray-200 p-3">
        <p className="text-gray-600 text-sm text-center">
          {isEditMode
            ? 'Click on tables to select them, then use the status buttons to change their state.'
            : 'Click on a table to select it. Available tables can be marked as occupied for new orders.'}
        </p>
      </div>
    </div>
  );
};

export default UnifiedFloorLayout;
