import React, { useState, useMemo } from 'react';
import { useAppContext } from '../context/AppContext';
import { BarChart, DollarSign, TrendingUp, Users, Calendar, Filter, Download } from 'lucide-react';

const SalesDashboard: React.FC = () => {
  const { state } = useAppContext();
  const [dateRange, setDateRange] = useState({
    start: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });
  const [selectedPeriod, setSelectedPeriod] = useState<'today' | 'week' | 'month' | 'custom'>('week');

  // Set date range based on selected period
  const updateDateRange = (period: typeof selectedPeriod) => {
    const now = new Date();
    let start: Date;
    
    switch (period) {
      case 'today':
        start = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case 'week':
        start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      default:
        return;
    }
    
    setDateRange({
      start: start.toISOString().split('T')[0],
      end: now.toISOString().split('T')[0]
    });
  };

  // Filter orders by date range
  const filteredOrders = useMemo(() => {
    const startDate = new Date(dateRange.start).getTime();
    const endDate = new Date(dateRange.end).getTime() + 24 * 60 * 60 * 1000; // Include end date
    
    return state.orders.filter(order => 
      order.status === 'paid' && 
      order.timestamp >= startDate && 
      order.timestamp < endDate
    );
  }, [state.orders, dateRange]);

  // Calculate analytics
  const analytics = useMemo(() => {
    const totalRevenue = filteredOrders.reduce((sum, order) => sum + order.total, 0);
    const totalOrders = filteredOrders.length;
    const averageOrderValue = totalOrders > 0 ? totalRevenue / totalOrders : 0;
    
    // Revenue by category
    const revenueByCategory: Record<string, number> = {};
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        const product = state.products.find(p => p.id === item.productId);
        if (product) {
          const category = product.category;
          revenueByCategory[category] = (revenueByCategory[category] || 0) + (item.price * item.quantity);
        }
      });
    });

    // Top selling items
    const itemSales: Record<string, { name: string; quantity: number; revenue: number }> = {};
    filteredOrders.forEach(order => {
      order.items.forEach(item => {
        if (!itemSales[item.productId]) {
          itemSales[item.productId] = {
            name: item.name,
            quantity: 0,
            revenue: 0
          };
        }
        
        itemSales[item.productId].quantity += item.quantity;
        itemSales[item.productId].revenue += item.price * item.quantity;
      });
    });

    const topItems = Object.values(itemSales)
      .sort((a, b) => b.revenue - a.revenue)
      .slice(0, 10);

    // Daily sales trend
    const dailySales: Record<string, { revenue: number; orders: number }> = {};
    filteredOrders.forEach(order => {
      const date = new Date(order.timestamp).toISOString().split('T')[0];
      if (!dailySales[date]) {
        dailySales[date] = { revenue: 0, orders: 0 };
      }
      dailySales[date].revenue += order.total;
      dailySales[date].orders += 1;
    });

    // Hourly sales pattern
    const hourlySales: Record<number, { revenue: number; orders: number }> = {};
    for (let i = 0; i < 24; i++) {
      hourlySales[i] = { revenue: 0, orders: 0 };
    }
    
    filteredOrders.forEach(order => {
      const hour = new Date(order.timestamp).getHours();
      hourlySales[hour].revenue += order.total;
      hourlySales[hour].orders += 1;
    });

    // Payment method breakdown
    const paymentMethods: Record<string, number> = {};
    filteredOrders.forEach(order => {
      const method = order.paymentMethod || 'unknown';
      paymentMethods[method] = (paymentMethods[method] || 0) + order.total;
    });

    return {
      totalRevenue,
      totalOrders,
      averageOrderValue,
      revenueByCategory,
      topItems,
      dailySales,
      hourlySales,
      paymentMethods
    };
  }, [filteredOrders, state.products]);

  const formatCurrency = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };

  const formatCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  };

  const getCategoryPercentage = (value: number): number => {
    return analytics.totalRevenue > 0 ? (value / analytics.totalRevenue) * 100 : 0;
  };

  const exportData = () => {
    const data = {
      dateRange,
      analytics,
      orders: filteredOrders
    };
    
    const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `sales-report-${dateRange.start}-to-${dateRange.end}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (state.orders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <BarChart className="h-12 w-12 text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Sales Data Available</h3>
        <p className="text-gray-400">
          Complete some orders to see sales analytics
        </p>
      </div>
    );
  }

  return (
    <div className="p-4 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700 bg-gray-900">
      {/* Header with filters */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <h2 className="text-xl font-semibold text-white">Sales Dashboard</h2>
        <div className="flex flex-wrap items-center gap-3">
          {/* Period selector */}
          <div className="flex bg-gray-700 rounded-lg">
            {(['today', 'week', 'month', 'custom'] as const).map(period => (
              <button
                key={period}
                onClick={() => {
                  setSelectedPeriod(period);
                  if (period !== 'custom') {
                    updateDateRange(period);
                  }
                }}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  selectedPeriod === period
                    ? 'bg-purple-600 text-white'
                    : 'text-gray-300 hover:text-white'
                }`}
              >
                {period.charAt(0).toUpperCase() + period.slice(1)}
              </button>
            ))}
          </div>

          {/* Custom date range */}
          {selectedPeriod === 'custom' && (
            <div className="flex items-center space-x-2">
              <input
                type="date"
                value={dateRange.start}
                onChange={(e) => setDateRange({ ...dateRange, start: e.target.value })}
                className="bg-gray-700 text-white px-3 py-2 rounded-md text-sm"
              />
              <span className="text-gray-400">to</span>
              <input
                type="date"
                value={dateRange.end}
                onChange={(e) => setDateRange({ ...dateRange, end: e.target.value })}
                className="bg-gray-700 text-white px-3 py-2 rounded-md text-sm"
              />
            </div>
          )}

          <button
            onClick={exportData}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
          >
            <Download className="h-4 w-4" />
            <span>Export</span>
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-4 mb-6">
        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <div className="flex items-center mb-2">
            <div className="bg-purple-900 p-2 rounded-md mr-3">
              <DollarSign className="h-5 w-5 text-purple-400" />
            </div>
            <span className="text-gray-300">Total Revenue</span>
          </div>
          <div className="text-2xl font-bold text-white">{formatCurrency(analytics.totalRevenue)}</div>
          <div className="text-sm text-gray-400 mt-1">{analytics.totalOrders} orders</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <div className="flex items-center mb-2">
            <div className="bg-amber-900 p-2 rounded-md mr-3">
              <TrendingUp className="h-5 w-5 text-amber-400" />
            </div>
            <span className="text-gray-300">Average Order</span>
          </div>
          <div className="text-2xl font-bold text-white">{formatCurrency(analytics.averageOrderValue)}</div>
          <div className="text-sm text-gray-400 mt-1">per transaction</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <div className="flex items-center mb-2">
            <div className="bg-blue-900 p-2 rounded-md mr-3">
              <Users className="h-5 w-5 text-blue-400" />
            </div>
            <span className="text-gray-300">Total Orders</span>
          </div>
          <div className="text-2xl font-bold text-white">{analytics.totalOrders}</div>
          <div className="text-sm text-gray-400 mt-1">in selected period</div>
        </div>

        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <div className="flex items-center mb-2">
            <div className="bg-green-900 p-2 rounded-md mr-3">
              <Calendar className="h-5 w-5 text-green-400" />
            </div>
            <span className="text-gray-300">Daily Average</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {formatCurrency(analytics.totalRevenue / Math.max(1, Object.keys(analytics.dailySales).length))}
          </div>
          <div className="text-sm text-gray-400 mt-1">revenue per day</div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Revenue by Category */}
        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Revenue by Category</h3>
          
          {Object.entries(analytics.revenueByCategory)
            .sort((a, b) => b[1] - a[1])
            .map(([category, revenue]) => (
              <div key={category} className="mb-3 last:mb-0">
                <div className="flex justify-between mb-1">
                  <span className="text-gray-300">{formatCategoryName(category)}</span>
                  <span className="text-white">{formatCurrency(revenue)}</span>
                </div>
                <div className="bg-gray-700 rounded-full h-2 w-full">
                  <div 
                    className="bg-purple-600 rounded-full h-2" 
                    style={{ width: `${getCategoryPercentage(revenue)}%` }}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {getCategoryPercentage(revenue).toFixed(1)}% of total
                </div>
              </div>
            ))}
        </div>

        {/* Payment Methods */}
        <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
          <h3 className="text-lg font-semibold text-white mb-4">Payment Methods</h3>
          
          {Object.entries(analytics.paymentMethods)
            .sort((a, b) => b[1] - a[1])
            .map(([method, revenue]) => (
              <div key={method} className="mb-3 last:mb-0">
                <div className="flex justify-between mb-1">
                  <span className="text-gray-300 capitalize">{method}</span>
                  <span className="text-white">{formatCurrency(revenue)}</span>
                </div>
                <div className="bg-gray-700 rounded-full h-2 w-full">
                  <div 
                    className="bg-blue-600 rounded-full h-2" 
                    style={{ width: `${(revenue / analytics.totalRevenue) * 100}%` }}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-1">
                  {((revenue / analytics.totalRevenue) * 100).toFixed(1)}% of total
                </div>
              </div>
            ))}
        </div>
      </div>

      {/* Hourly Sales Pattern */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6 shadow-lg border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Hourly Sales Pattern</h3>
        <div className="grid grid-cols-12 gap-1">
          {Object.entries(analytics.hourlySales).map(([hour, data]) => {
            const maxRevenue = Math.max(...Object.values(analytics.hourlySales).map(d => d.revenue));
            const height = maxRevenue > 0 ? (data.revenue / maxRevenue) * 100 : 0;
            
            return (
              <div key={hour} className="flex flex-col items-center">
                <div className="text-xs text-gray-400 mb-1">{hour}:00</div>
                <div className="bg-gray-700 w-full h-20 rounded-sm relative flex items-end">
                  <div 
                    className="bg-purple-600 w-full rounded-sm"
                    style={{ height: `${height}%` }}
                    title={`${hour}:00 - ${formatCurrency(data.revenue)} (${data.orders} orders)`}
                  />
                </div>
                <div className="text-xs text-gray-400 mt-1">{data.orders}</div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Top Selling Items */}
      <div className="bg-gray-800 rounded-lg p-4 shadow-lg border border-gray-700">
        <h3 className="text-lg font-semibold text-white mb-4">Top Selling Items</h3>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead>
              <tr className="text-left text-gray-400 border-b border-gray-700">
                <th className="pb-2">Rank</th>
                <th className="pb-2">Item</th>
                <th className="pb-2 text-right">Quantity</th>
                <th className="pb-2 text-right">Revenue</th>
                <th className="pb-2 text-right">Avg Price</th>
              </tr>
            </thead>
            <tbody>
              {analytics.topItems.map((item, index) => (
                <tr key={index} className="border-b border-gray-700 last:border-0 hover:bg-gray-700 transition-colors">
                  <td className="py-3 text-gray-400">#{index + 1}</td>
                  <td className="py-3 text-white">{item.name}</td>
                  <td className="py-3 text-gray-300 text-right">{item.quantity}</td>
                  <td className="py-3 text-amber-400 text-right font-medium">{formatCurrency(item.revenue)}</td>
                  <td className="py-3 text-gray-300 text-right">{formatCurrency(item.revenue / item.quantity)}</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default SalesDashboard;
