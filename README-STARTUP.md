# 🚀 RESTROFLOW - Quick Start Guide

## ⚡ One-Command Startup

### **Option 1: NPM Command (Recommended)**
```bash
npm start
```
This starts both frontend and backend servers simultaneously.

### **Option 2: Windows Batch File**
```bash
start-restroflow.bat
```
Double-click the batch file to start both servers in separate windows.

### **Option 3: Node.js Script**
```bash
node start-restroflow.js
```
Advanced startup with colored output and process management.

---

## 🌐 Access Your System

Once started, access your RESTROFLOW POS system:

- **🎯 Frontend (POS Interface)**: http://localhost:3000
- **🔧 Backend (API Server)**: http://localhost:4000
- **🔑 Login PIN**: `123456`
- **👤 User Role**: Super Administrator

---

## 📊 What's Running

### **Frontend Server**
- **Port**: 3000
- **Technology**: React + TypeScript + Vite
- **Features**: Clean POS interface, product management, order processing

### **Backend Server**
- **Port**: 4000
- **Technology**: Node.js + Express + PostgreSQL
- **Features**: 42 API endpoints, authentication, database management

---

## 🛑 Stopping the System

### **If using `npm start`:**
Press `Ctrl + C` in the terminal to stop both servers.

### **If using batch file:**
Close the terminal windows that opened.

### **If using Node.js script:**
Press `Ctrl + C` to gracefully shutdown both servers.

---

## 🔧 Development Commands

```bash
# Start both servers (production mode)
npm start

# Start both servers (development mode with hot reload)
npm run dev

# Start only backend
npm run start:backend

# Start only frontend
npm run start:frontend

# Install all dependencies
npm run install:all

# Build for production
npm run build
```

---

## 🎯 Features Available

✅ **Product Management**: Browse products by category
✅ **Order Processing**: Add/remove items, manage quantities
✅ **Payment System**: Process payments and complete orders
✅ **User Authentication**: Secure login with PIN
✅ **Real-time Updates**: Live order totals and calculations
✅ **Responsive Design**: Works on desktop and mobile
✅ **Clean Interface**: Professional, easy-to-use design

---

## 🆘 Troubleshooting

### **Port Already in Use**
If you get port errors:
1. Stop any existing servers
2. Wait 30 seconds
3. Try starting again

### **Database Connection Issues**
Make sure PostgreSQL is running and configured properly.

### **Frontend Not Loading**
1. Check if both servers are running
2. Clear browser cache
3. Try accessing http://localhost:3000 directly

---

## 📞 Support

Your RESTROFLOW POS system is ready to use! 

**Login with PIN `123456` and start processing orders immediately.**
