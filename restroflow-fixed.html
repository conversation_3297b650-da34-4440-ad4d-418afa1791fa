<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - Fixed POS System</title>
    
    <!-- React and ReactDOM -->
    <script crossorigin src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script crossorigin src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    
    <!-- Babel for JSX transformation -->
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Axios for API calls -->
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    
    <style>
        body { font-family: 'Inter', sans-serif; transition: all 0.3s ease; }
        .gradient-bg { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .gradient-bg-dark { background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); }
        .card-hover { transition: all 0.3s ease; }
        .card-hover:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
        .pulse-animation { animation: pulse 2s infinite; }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }
        .slide-in { animation: slideIn 0.5s ease-out; }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .success-checkmark { animation: checkmark 0.6s ease-in-out; }
        @keyframes checkmark {
            0% { transform: scale(0); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Dark theme styles */
        .dark {
            background-color: #1a1a2e;
            color: #ffffff;
        }
        .dark .bg-white { background-color: #2d2d44; }
        .dark .text-gray-800 { color: #ffffff; }
        .dark .text-gray-600 { color: #a0a0a0; }
        .dark .text-gray-500 { color: #888888; }
        .dark .border-gray-200 { border-color: #404040; }
        .dark .border-gray-300 { border-color: #505050; }
        .dark .bg-gray-50 { background-color: #1a1a2e; }
        .dark .bg-gray-100 { background-color: #2d2d44; }
        .dark .hover\:bg-gray-300:hover { background-color: #505050; }
        .dark .card-hover:hover { box-shadow: 0 10px 25px rgba(255,255,255,0.1); }

        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        }
        .dark .theme-toggle {
            background: rgba(45, 45, 68, 0.9);
            color: #ffffff;
        }
    </style>
</head>
<body class="bg-gray-50">
    <div id="root"></div>

    <script type="text/babel">
        const { useState, useEffect } = React;

        // Login Component
        const LoginScreen = ({ onLogin, loading, error, darkMode, toggleTheme }) => {
            const [pin, setPin] = useState('');

            const handleSubmit = (e) => {
                e.preventDefault();
                onLogin(pin);
            };

            return (
                <div className={`min-h-screen ${darkMode ? 'gradient-bg-dark' : 'gradient-bg'} flex items-center justify-center p-4`}>
                    <ThemeToggle darkMode={darkMode} toggleTheme={toggleTheme} />
                    <div className="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md slide-in">
                        <div className="text-center mb-8">
                            <h1 className="text-3xl font-bold text-gray-800 mb-2">RestroFlow</h1>
                            <p className="text-gray-600">Enter your PIN to access the system</p>
                        </div>
                        
                        <form onSubmit={handleSubmit} className="space-y-6">
                            <div>
                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                    PIN Code
                                </label>
                                <input
                                    type="password"
                                    value={pin}
                                    onChange={(e) => setPin(e.target.value)}
                                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                                    placeholder="Enter your PIN"
                                    required
                                />
                            </div>
                            
                            {error && (
                                <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                                    {error}
                                </div>
                            )}
                            
                            <button
                                type="submit"
                                disabled={loading}
                                className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                            >
                                {loading ? (
                                    <div className="flex items-center justify-center">
                                        <div className="loading-spinner mr-2"></div>
                                        Authenticating...
                                    </div>
                                ) : (
                                    'Access System'
                                )}
                            </button>
                        </form>
                        
                        <div className="mt-6 text-center text-sm text-gray-500">
                            <p>Demo PIN: 1234</p>
                        </div>
                    </div>
                </div>
            );
        };

        // POS System Component
        const POSSystem = ({ user, onLogout, darkMode, toggleTheme }) => {
            const [currentView, setCurrentView] = useState('pos');
            const [cart, setCart] = useState([]);
            const [products] = useState([
                { id: 1, name: 'Burger', price: 12.99, category: 'Main' },
                { id: 2, name: 'Pizza', price: 18.99, category: 'Main' },
                { id: 3, name: 'Salad', price: 8.99, category: 'Appetizer' },
                { id: 4, name: 'Soda', price: 2.99, category: 'Beverage' },
                { id: 5, name: 'Coffee', price: 3.99, category: 'Beverage' },
                { id: 6, name: 'Dessert', price: 6.99, category: 'Dessert' }
            ]);

            const addToCart = (product) => {
                const existingItem = cart.find(item => item.id === product.id);
                if (existingItem) {
                    setCart(cart.map(item => 
                        item.id === product.id 
                            ? { ...item, quantity: item.quantity + 1 }
                            : item
                    ));
                } else {
                    setCart([...cart, { ...product, quantity: 1 }]);
                }
            };

            const removeFromCart = (productId) => {
                setCart(cart.filter(item => item.id !== productId));
            };

            const updateQuantity = (productId, quantity) => {
                if (quantity <= 0) {
                    removeFromCart(productId);
                } else {
                    setCart(cart.map(item => 
                        item.id === productId 
                            ? { ...item, quantity }
                            : item
                    ));
                }
            };

            const getTotalAmount = () => {
                return cart.reduce((total, item) => total + (item.price * item.quantity), 0);
            };

            const processPayment = () => {
                alert(`Payment processed successfully! Total: $${getTotalAmount().toFixed(2)}`);
                setCart([]);
            };

            return (
                <div className="min-h-screen bg-gray-50">
                    <ThemeToggle darkMode={darkMode} toggleTheme={toggleTheme} />
                    {/* Header */}
                    <header className="bg-white shadow-sm border-b border-gray-200">
                        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <div className="flex items-center justify-between h-16">
                                <div className="flex items-center">
                                    <h1 className="text-2xl font-bold text-blue-600">RestroFlow</h1>
                                    <span className="ml-4 text-gray-600">Point of Sale System</span>
                                </div>
                                <div className="flex items-center space-x-4">
                                    <span className="text-sm text-gray-600">Welcome, {user?.name || 'User'}</span>
                                    <button
                                        onClick={onLogout}
                                        className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                                    >
                                        Logout
                                    </button>
                                </div>
                            </div>
                        </div>
                    </header>

                    <div className="flex h-screen pt-16">
                        {/* Product Grid */}
                        <div className="flex-1 p-6">
                            <h2 className="text-xl font-semibold mb-4">Products</h2>
                            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                                {products.map(product => (
                                    <div
                                        key={product.id}
                                        className="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer"
                                        onClick={() => addToCart(product)}
                                    >
                                        <h3 className="font-semibold text-gray-800">{product.name}</h3>
                                        <p className="text-sm text-gray-600">{product.category}</p>
                                        <p className="text-lg font-bold text-blue-600 mt-2">${product.price}</p>
                                    </div>
                                ))}
                            </div>
                        </div>

                        {/* Cart Panel */}
                        <div className="w-80 bg-white border-l border-gray-200 p-6">
                            <h2 className="text-xl font-semibold mb-4">Current Order</h2>
                            
                            {cart.length === 0 ? (
                                <p className="text-gray-500 text-center py-8">No items in cart</p>
                            ) : (
                                <>
                                    <div className="space-y-3 mb-6">
                                        {cart.map(item => (
                                            <div key={item.id} className="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                                <div className="flex-1">
                                                    <h4 className="font-medium">{item.name}</h4>
                                                    <p className="text-sm text-gray-600">${item.price} each</p>
                                                </div>
                                                <div className="flex items-center space-x-2">
                                                    <button
                                                        onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                                        className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300"
                                                    >
                                                        -
                                                    </button>
                                                    <span className="w-8 text-center">{item.quantity}</span>
                                                    <button
                                                        onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                                        className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300"
                                                    >
                                                        +
                                                    </button>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                    
                                    <div className="border-t pt-4">
                                        <div className="flex justify-between items-center mb-4">
                                            <span className="text-lg font-semibold">Total:</span>
                                            <span className="text-2xl font-bold text-blue-600">
                                                ${getTotalAmount().toFixed(2)}
                                            </span>
                                        </div>
                                        
                                        <button
                                            onClick={processPayment}
                                            className="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold"
                                        >
                                            Process Payment
                                        </button>
                                    </div>
                                </>
                            )}
                        </div>
                    </div>
                </div>
            );
        };

        // Theme Toggle Component
        const ThemeToggle = ({ darkMode, toggleTheme }) => {
            return (
                <button
                    onClick={toggleTheme}
                    className="theme-toggle"
                    title={darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode'}
                >
                    {darkMode ? '☀️' : '🌙'}
                </button>
            );
        };

        // Main App Component
        const App = () => {
            const [isAuthenticated, setIsAuthenticated] = useState(false);
            const [user, setUser] = useState(null);
            const [loading, setLoading] = useState(false);
            const [error, setError] = useState('');
            const [darkMode, setDarkMode] = useState(false);

            // Check for existing authentication and theme
            useEffect(() => {
                const token = localStorage.getItem('authToken');
                const userData = localStorage.getItem('user');
                const savedTheme = localStorage.getItem('theme');

                if (token && userData) {
                    setIsAuthenticated(true);
                    setUser(JSON.parse(userData));
                }

                if (savedTheme === 'dark') {
                    setDarkMode(true);
                    document.documentElement.classList.add('dark');
                }
            }, []);

            const toggleTheme = () => {
                const newDarkMode = !darkMode;
                setDarkMode(newDarkMode);

                if (newDarkMode) {
                    document.documentElement.classList.add('dark');
                    localStorage.setItem('theme', 'dark');
                } else {
                    document.documentElement.classList.remove('dark');
                    localStorage.setItem('theme', 'light');
                }
            };

            const handleLogin = async (pin) => {
                setLoading(true);
                setError('');
                
                try {
                    // Simulate API call
                    await new Promise(resolve => setTimeout(resolve, 1000));
                    
                    if (pin === '1234') {
                        const userData = { id: 1, name: 'Demo User', role: 'admin' };
                        localStorage.setItem('authToken', 'demo-token');
                        localStorage.setItem('user', JSON.stringify(userData));
                        setUser(userData);
                        setIsAuthenticated(true);
                    } else {
                        setError('Invalid PIN. Please try again.');
                    }
                } catch (err) {
                    setError('Authentication failed. Please try again.');
                } finally {
                    setLoading(false);
                }
            };

            const handleLogout = () => {
                localStorage.removeItem('authToken');
                localStorage.removeItem('user');
                setIsAuthenticated(false);
                setUser(null);
            };

            if (!isAuthenticated) {
                return <LoginScreen onLogin={handleLogin} loading={loading} error={error} darkMode={darkMode} toggleTheme={toggleTheme} />;
            }

            return <POSSystem user={user} onLogout={handleLogout} darkMode={darkMode} toggleTheme={toggleTheme} />;
        };

        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
