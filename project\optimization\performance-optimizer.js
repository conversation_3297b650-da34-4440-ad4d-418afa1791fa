// Production Performance Optimizer for RestroFlow POS
// Implements advanced caching, CDN integration, and performance tuning

const Redis = require('redis');
const compression = require('compression');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const slowDown = require('express-slow-down');
const cluster = require('cluster');
const os = require('os');

class ProductionOptimizer {
  constructor(app, config = {}) {
    this.app = app;
    this.config = {
      redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: process.env.REDIS_PORT || 6379,
        password: process.env.REDIS_PASSWORD,
        db: process.env.REDIS_CACHE_DB || 1
      },
      cache: {
        defaultTTL: 3600, // 1 hour
        maxSize: 1000,
        checkPeriod: 600 // 10 minutes
      },
      compression: {
        level: 6,
        threshold: 1024,
        filter: this.shouldCompress
      },
      cluster: {
        enabled: process.env.CLUSTER_ENABLED === 'true',
        workers: process.env.CLUSTER_WORKERS || os.cpus().length
      },
      cdn: {
        enabled: process.env.CDN_ENABLED === 'true',
        baseUrl: process.env.CDN_BASE_URL,
        staticPaths: ['/static', '/assets', '/images', '/css', '/js']
      },
      ...config
    };
    
    this.redisClient = null;
    this.cache = new Map();
    this.performanceMetrics = {
      requests: 0,
      cacheHits: 0,
      cacheMisses: 0,
      averageResponseTime: 0,
      totalResponseTime: 0
    };
    
    this.initializeOptimizations();
  }

  // Initialize all performance optimizations
  initializeOptimizations() {
    console.log('🚀 Initializing production optimizations...');
    
    this.setupRedisCache();
    this.setupCompression();
    this.setupSecurity();
    this.setupRateLimiting();
    this.setupCaching();
    this.setupStaticAssets();
    this.setupPerformanceMonitoring();
    this.setupClusterMode();
    
    console.log('✅ Production optimizations initialized');
  }

  // Setup Redis caching
  setupRedisCache() {
    try {
      this.redisClient = Redis.createClient(this.config.redis);
      
      this.redisClient.on('connect', () => {
        console.log('📦 Redis cache connected');
      });
      
      this.redisClient.on('error', (err) => {
        console.error('❌ Redis cache error:', err);
      });
      
      this.redisClient.connect();
    } catch (error) {
      console.error('❌ Failed to setup Redis cache:', error);
    }
  }

  // Setup compression middleware
  setupCompression() {
    this.app.use(compression(this.config.compression));
    console.log('🗜️ Compression middleware enabled');
  }

  // Setup security headers
  setupSecurity() {
    this.app.use(helmet({
      contentSecurityPolicy: {
        directives: {
          defaultSrc: ["'self'"],
          styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
          fontSrc: ["'self'", "https://fonts.gstatic.com"],
          imgSrc: ["'self'", "data:", "https:"],
          scriptSrc: ["'self'"],
          connectSrc: ["'self'", "ws:", "wss:"]
        }
      },
      hsts: {
        maxAge: 31536000,
        includeSubDomains: true,
        preload: true
      }
    }));
    
    console.log('🛡️ Security headers configured');
  }

  // Setup rate limiting
  setupRateLimiting() {
    // General API rate limiting
    const apiLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 1000, // limit each IP to 1000 requests per windowMs
      message: 'Too many requests from this IP, please try again later.',
      standardHeaders: true,
      legacyHeaders: false
    });

    // Authentication rate limiting
    const authLimiter = rateLimit({
      windowMs: 15 * 60 * 1000, // 15 minutes
      max: 5, // limit each IP to 5 login attempts per windowMs
      message: 'Too many login attempts, please try again later.',
      skipSuccessfulRequests: true
    });

    // Slow down repeated requests
    const speedLimiter = slowDown({
      windowMs: 15 * 60 * 1000, // 15 minutes
      delayAfter: 100, // allow 100 requests per 15 minutes at full speed
      delayMs: 500 // slow down subsequent requests by 500ms per request
    });

    this.app.use('/api/', apiLimiter);
    this.app.use('/api/auth/', authLimiter);
    this.app.use(speedLimiter);
    
    console.log('🚦 Rate limiting configured');
  }

  // Setup intelligent caching
  setupCaching() {
    // Cache middleware
    const cacheMiddleware = (duration = 3600) => {
      return async (req, res, next) => {
        if (req.method !== 'GET') {
          return next();
        }

        const key = `cache:${req.originalUrl}`;
        
        try {
          // Check Redis cache first
          if (this.redisClient) {
            const cached = await this.redisClient.get(key);
            if (cached) {
              this.performanceMetrics.cacheHits++;
              res.set('X-Cache', 'HIT');
              return res.json(JSON.parse(cached));
            }
          }

          // Check memory cache
          if (this.cache.has(key)) {
            const { data, expiry } = this.cache.get(key);
            if (Date.now() < expiry) {
              this.performanceMetrics.cacheHits++;
              res.set('X-Cache', 'HIT-MEMORY');
              return res.json(data);
            } else {
              this.cache.delete(key);
            }
          }

          this.performanceMetrics.cacheMisses++;
          res.set('X-Cache', 'MISS');

          // Override res.json to cache the response
          const originalJson = res.json;
          res.json = (data) => {
            // Cache in Redis
            if (this.redisClient) {
              this.redisClient.setEx(key, duration, JSON.stringify(data));
            }

            // Cache in memory
            this.cache.set(key, {
              data,
              expiry: Date.now() + (duration * 1000)
            });

            return originalJson.call(res, data);
          };

          next();
        } catch (error) {
          console.error('Cache middleware error:', error);
          next();
        }
      };
    };

    // Apply caching to specific routes
    this.app.use('/api/tenants/public', cacheMiddleware(1800)); // 30 minutes
    this.app.use('/api/analytics/dashboard', cacheMiddleware(300)); // 5 minutes
    this.app.use('/api/system/config', cacheMiddleware(3600)); // 1 hour
    
    console.log('💾 Intelligent caching configured');
  }

  // Setup static asset optimization
  setupStaticAssets() {
    // Static file caching
    this.app.use('/static', (req, res, next) => {
      res.set({
        'Cache-Control': 'public, max-age=31536000', // 1 year
        'Expires': new Date(Date.now() + 31536000000).toUTCString()
      });
      next();
    });

    // CDN integration
    if (this.config.cdn.enabled) {
      this.app.use((req, res, next) => {
        if (this.config.cdn.staticPaths.some(path => req.path.startsWith(path))) {
          const cdnUrl = `${this.config.cdn.baseUrl}${req.path}`;
          return res.redirect(301, cdnUrl);
        }
        next();
      });
      
      console.log('🌐 CDN integration enabled');
    }
    
    console.log('📁 Static asset optimization configured');
  }

  // Setup performance monitoring
  setupPerformanceMonitoring() {
    // Request timing middleware
    this.app.use((req, res, next) => {
      const startTime = Date.now();
      
      res.on('finish', () => {
        const responseTime = Date.now() - startTime;
        this.performanceMetrics.requests++;
        this.performanceMetrics.totalResponseTime += responseTime;
        this.performanceMetrics.averageResponseTime = 
          this.performanceMetrics.totalResponseTime / this.performanceMetrics.requests;
        
        // Log slow requests
        if (responseTime > 1000) {
          console.warn(`🐌 Slow request: ${req.method} ${req.path} - ${responseTime}ms`);
        }
      });
      
      next();
    });

    // Performance metrics endpoint
    this.app.get('/api/performance/metrics', (req, res) => {
      const memoryUsage = process.memoryUsage();
      const cpuUsage = process.cpuUsage();
      
      res.json({
        ...this.performanceMetrics,
        memory: {
          rss: Math.round(memoryUsage.rss / 1024 / 1024), // MB
          heapUsed: Math.round(memoryUsage.heapUsed / 1024 / 1024), // MB
          heapTotal: Math.round(memoryUsage.heapTotal / 1024 / 1024), // MB
          external: Math.round(memoryUsage.external / 1024 / 1024) // MB
        },
        cpu: {
          user: cpuUsage.user,
          system: cpuUsage.system
        },
        uptime: process.uptime(),
        cacheSize: this.cache.size,
        cacheHitRate: this.performanceMetrics.cacheHits / 
          (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100
      });
    });
    
    console.log('📊 Performance monitoring configured');
  }

  // Setup cluster mode for production
  setupClusterMode() {
    if (this.config.cluster.enabled && cluster.isMaster) {
      console.log(`🔄 Starting ${this.config.cluster.workers} worker processes...`);
      
      for (let i = 0; i < this.config.cluster.workers; i++) {
        cluster.fork();
      }
      
      cluster.on('exit', (worker, code, signal) => {
        console.log(`💀 Worker ${worker.process.pid} died. Restarting...`);
        cluster.fork();
      });
      
      cluster.on('online', (worker) => {
        console.log(`✅ Worker ${worker.process.pid} is online`);
      });
      
      return true; // Master process
    }
    
    return false; // Worker process or cluster disabled
  }

  // Cache invalidation
  async invalidateCache(pattern = '*') {
    try {
      if (this.redisClient) {
        const keys = await this.redisClient.keys(`cache:${pattern}`);
        if (keys.length > 0) {
          await this.redisClient.del(keys);
          console.log(`🗑️ Invalidated ${keys.length} cache entries`);
        }
      }

      // Clear memory cache
      for (const [key] of this.cache) {
        if (key.includes(pattern.replace('*', ''))) {
          this.cache.delete(key);
        }
      }
    } catch (error) {
      console.error('Cache invalidation error:', error);
    }
  }

  // Preload critical data
  async preloadCache() {
    console.log('🔄 Preloading critical cache data...');
    
    try {
      // Preload public tenant data
      const tenants = await this.fetchPublicTenants();
      if (tenants) {
        const key = 'cache:/api/tenants/public';
        if (this.redisClient) {
          await this.redisClient.setEx(key, 1800, JSON.stringify(tenants));
        }
        this.cache.set(key, {
          data: tenants,
          expiry: Date.now() + 1800000
        });
      }

      // Preload system configuration
      const config = await this.fetchSystemConfig();
      if (config) {
        const key = 'cache:/api/system/config';
        if (this.redisClient) {
          await this.redisClient.setEx(key, 3600, JSON.stringify(config));
        }
        this.cache.set(key, {
          data: config,
          expiry: Date.now() + 3600000
        });
      }
      
      console.log('✅ Cache preloading completed');
    } catch (error) {
      console.error('Cache preloading error:', error);
    }
  }

  // Helper method to determine if response should be compressed
  shouldCompress(req, res) {
    if (req.headers['x-no-compression']) {
      return false;
    }
    return compression.filter(req, res);
  }

  // Fetch public tenants (placeholder)
  async fetchPublicTenants() {
    // Implementation would fetch from database
    return null;
  }

  // Fetch system config (placeholder)
  async fetchSystemConfig() {
    // Implementation would fetch from database
    return null;
  }

  // Cleanup resources
  async cleanup() {
    if (this.redisClient) {
      await this.redisClient.quit();
    }
    this.cache.clear();
    console.log('🧹 Performance optimizer cleanup completed');
  }

  // Get optimization status
  getStatus() {
    return {
      redis: this.redisClient ? 'connected' : 'disconnected',
      cache: {
        size: this.cache.size,
        hitRate: this.performanceMetrics.cacheHits / 
          (this.performanceMetrics.cacheHits + this.performanceMetrics.cacheMisses) * 100
      },
      cluster: {
        enabled: this.config.cluster.enabled,
        workers: this.config.cluster.workers,
        isMaster: cluster.isMaster,
        workerId: cluster.worker ? cluster.worker.id : null
      },
      cdn: {
        enabled: this.config.cdn.enabled,
        baseUrl: this.config.cdn.baseUrl
      },
      metrics: this.performanceMetrics
    };
  }
}

module.exports = ProductionOptimizer;
