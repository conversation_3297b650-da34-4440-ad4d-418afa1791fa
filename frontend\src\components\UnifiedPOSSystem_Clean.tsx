import React, { useState, useEffect } from 'react';

interface OrderItem {
  name: string;
  price: number;
  emoji: string;
  quantity: number;
}

interface UnifiedPOSSystemProps {
  onLogout: () => void;
  user: any;
}

const UnifiedPOSSystem: React.FC<UnifiedPOSSystemProps> = ({ onLogout, user }) => {
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');

  // Sample products
  const products = [
    { name: 'Coffee', price: 4.99, category: 'Beverages', emoji: '☕' },
    { name: 'Sandwich', price: 8.99, category: 'Food', emoji: '🥪' },
    { name: 'Salad', price: 12.99, category: 'Food', emoji: '🥗' },
    { name: 'Pizza', price: 15.99, category: 'Food', emoji: '🍕' },
    { name: 'Burger', price: 11.99, category: 'Food', emoji: '🍔' },
    { name: 'Pasta', price: 13.99, category: 'Food', emoji: '🍝' },
    { name: 'Soup', price: 6.99, category: 'Food', emoji: '🍲' },
    { name: 'Dessert', price: 5.99, category: 'Desserts', emoji: '🍰' },
    { name: 'Tea', price: 3.99, category: 'Beverages', emoji: '🍵' },
    { name: 'Juice', price: 4.49, category: 'Beverages', emoji: '🧃' },
    { name: 'Wine', price: 12.99, category: 'Beverages', emoji: '🍷' },
    { name: 'Beer', price: 6.99, category: 'Beverages', emoji: '🍺' }
  ];

  const categories = ['all', 'Food', 'Beverages', 'Desserts'];

  const filteredProducts = selectedCategory === 'all' 
    ? products 
    : products.filter(product => product.category === selectedCategory);

  const addToOrder = (product: any) => {
    const existingItem = currentOrder.find(item => item.name === product.name);
    
    if (existingItem) {
      setCurrentOrder(currentOrder.map(item =>
        item.name === product.name
          ? { ...item, quantity: item.quantity + 1 }
          : item
      ));
    } else {
      setCurrentOrder([...currentOrder, { ...product, quantity: 1 }]);
    }
  };

  const removeFromOrder = (productName: string) => {
    setCurrentOrder(currentOrder.filter(item => item.name !== productName));
  };

  const updateQuantity = (productName: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromOrder(productName);
    } else {
      setCurrentOrder(currentOrder.map(item =>
        item.name === productName
          ? { ...item, quantity: newQuantity }
          : item
      ));
    }
  };

  const calculateTotal = () => {
    return currentOrder.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  };

  const processPayment = () => {
    if (currentOrder.length === 0) return;
    
    alert(`Payment processed successfully! Total: $${calculateTotal().toFixed(2)}`);
    setCurrentOrder([]);
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">R</span>
                </div>
                <div>
                  <h1 className="text-2xl font-bold text-gray-900">RESTROFLOW</h1>
                  <p className="text-sm text-gray-600">Restaurant POS System</p>
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">{user?.name || 'Admin User'}</p>
                <p className="text-xs text-gray-600">{user?.role || 'Administrator'}</p>
              </div>
              <button
                onClick={onLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-full mx-auto p-6">
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Product Grid */}
          <div className="xl:col-span-3">
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
                <h3 className="text-lg font-semibold text-gray-900">Menu Items</h3>
                
                {/* Category Filter */}
                <div className="flex flex-wrap gap-2 mt-3">
                  {categories.map((category) => (
                    <button
                      key={category}
                      onClick={() => setSelectedCategory(category)}
                      className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                        selectedCategory === category
                          ? 'bg-blue-600 text-white'
                          : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-300'
                      }`}
                    >
                      {category === 'all' ? 'All Items' : category}
                    </button>
                  ))}
                </div>
              </div>
              
              <div className="p-6">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {filteredProducts.map((product, index) => (
                    <button
                      key={index}
                      onClick={() => addToOrder(product)}
                      className="p-4 bg-gray-50 rounded-lg hover:bg-blue-50 transition-all duration-200 text-center border border-gray-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1"
                    >
                      <div className="text-3xl mb-2">{product.emoji}</div>
                      <div className="font-medium text-gray-900">{product.name}</div>
                      <div className="text-lg font-bold text-blue-600">${product.price}</div>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Order Panel */}
          <div className="xl:col-span-1">
            <div className="bg-white rounded-lg shadow-md border border-gray-200">
              <div className="px-4 py-3 border-b border-gray-200 bg-gray-50">
                <h4 className="font-semibold text-gray-900">Current Order</h4>
                <p className="text-sm text-gray-600">{currentOrder.length} items</p>
              </div>
              
              <div className="p-4">
                {currentOrder.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <div className="text-4xl mb-2">🛒</div>
                    <p>No items in order</p>
                  </div>
                ) : (
                  <div className="space-y-3 max-h-64 overflow-y-auto">
                    {currentOrder.map((item, index) => (
                      <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{item.emoji}</span>
                          <div>
                            <div className="text-sm font-medium">{item.name}</div>
                            <div className="text-xs text-gray-500">
                              ${item.price} x {item.quantity}
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center gap-1">
                          <button
                            onClick={() => updateQuantity(item.name, item.quantity - 1)}
                            className="w-6 h-6 rounded bg-red-100 text-red-600 hover:bg-red-200 text-xs"
                          >
                            -
                          </button>
                          <span className="w-8 text-center text-sm">{item.quantity}</span>
                          <button
                            onClick={() => updateQuantity(item.name, item.quantity + 1)}
                            className="w-6 h-6 rounded bg-green-100 text-green-600 hover:bg-green-200 text-xs"
                          >
                            +
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {currentOrder.length > 0 && (
                <div className="p-4 border-t border-gray-200 bg-gray-50">
                  <div className="flex justify-between items-center mb-4">
                    <span className="text-lg font-semibold text-gray-900">Total:</span>
                    <span className="text-2xl font-bold text-blue-600">${calculateTotal().toFixed(2)}</span>
                  </div>
                  
                  <button
                    onClick={processPayment}
                    className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  >
                    Process Payment
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedPOSSystem;
