-- Complete Schema Tables Migration for RESTROFLOW
-- Creates missing essential tables identified in system validation

-- Create payments table for transaction records
CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    order_id INTEGER REFERENCES orders(id) ON DELETE SET NULL,
    payment_method VARCHAR(50) NOT NULL CHECK (payment_method IN ('cash', 'card', 'digital_wallet', 'split', 'comp')),
    payment_processor VARCHAR(50), -- stripe, moneris, square, etc.
    transaction_id VARCHAR(255) UNIQUE,
    amount DECIMAL(10,2) NOT NULL CHECK (amount >= 0),
    currency VARCHAR(3) DEFAULT 'CAD',
    tax_amount DECIMAL(10,2) DEFAULT 0,
    tip_amount DECIMAL(10,2) DEFAULT 0,
    discount_amount DECIMAL(10,2) DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL CHECK (total_amount >= 0),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'refunded', 'cancelled')),
    payment_data JSONB, -- Store payment processor specific data
    receipt_data JSONB, -- Store receipt information
    refund_data JSONB, -- Store refund information if applicable
    processed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by INTEGER REFERENCES users(id)
);

-- Create tables table for restaurant floor layout management
CREATE TABLE IF NOT EXISTS tables (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    location_id INTEGER, -- For multi-location support
    table_number VARCHAR(20) NOT NULL,
    table_name VARCHAR(100),
    capacity INTEGER NOT NULL CHECK (capacity > 0),
    table_type VARCHAR(20) DEFAULT 'regular' CHECK (table_type IN ('regular', 'booth', 'bar', 'outdoor', 'private', 'counter')),
    position_x DECIMAL(8,2), -- X coordinate for floor layout
    position_y DECIMAL(8,2), -- Y coordinate for floor layout
    width DECIMAL(6,2) DEFAULT 100, -- Table width in pixels/units
    height DECIMAL(6,2) DEFAULT 100, -- Table height in pixels/units
    rotation DECIMAL(5,2) DEFAULT 0, -- Rotation angle in degrees
    status VARCHAR(20) DEFAULT 'available' CHECK (status IN ('available', 'occupied', 'reserved', 'cleaning', 'maintenance', 'unavailable')),
    current_order_id INTEGER REFERENCES orders(id) ON DELETE SET NULL,
    reservation_data JSONB, -- Store reservation information
    table_settings JSONB, -- Store table-specific settings
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW(),
    created_by INTEGER REFERENCES users(id),

    -- Unique constraint for table number per tenant/location
    UNIQUE(tenant_id, location_id, table_number)
);

-- Create table_sessions table for tracking table occupancy
CREATE TABLE IF NOT EXISTS table_sessions (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    table_id INTEGER NOT NULL REFERENCES tables(id) ON DELETE CASCADE,
    session_start TIMESTAMP DEFAULT NOW(),
    session_end TIMESTAMP,
    party_size INTEGER,
    server_id INTEGER REFERENCES employees(id),
    total_orders INTEGER DEFAULT 0,
    total_amount DECIMAL(10,2) DEFAULT 0,
    session_notes TEXT,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create payment_splits table for split payment functionality
CREATE TABLE IF NOT EXISTS payment_splits (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    order_id INTEGER NOT NULL REFERENCES orders(id) ON DELETE CASCADE,
    payment_id INTEGER REFERENCES payments(id) ON DELETE CASCADE,
    split_type VARCHAR(20) NOT NULL CHECK (split_type IN ('equal', 'custom', 'by_item', 'percentage')),
    split_amount DECIMAL(10,2) NOT NULL CHECK (split_amount >= 0),
    split_percentage DECIMAL(5,2), -- For percentage-based splits
    customer_identifier VARCHAR(100), -- Name, phone, or identifier for the split
    payment_method VARCHAR(50),
    payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'completed', 'failed')),
    transaction_data JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);

-- Create inventory_transactions table for inventory tracking
CREATE TABLE IF NOT EXISTS inventory_transactions (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    product_id INTEGER NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    transaction_type VARCHAR(20) NOT NULL CHECK (transaction_type IN ('sale', 'restock', 'adjustment', 'waste', 'transfer')),
    quantity_change DECIMAL(10,3) NOT NULL, -- Can be negative for sales/waste
    unit_cost DECIMAL(10,2),
    total_cost DECIMAL(10,2),
    reference_id INTEGER, -- Order ID for sales, PO ID for restocks, etc.
    reference_type VARCHAR(20), -- 'order', 'purchase_order', 'adjustment', etc.
    notes TEXT,
    processed_by INTEGER REFERENCES employees(id),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Create audit_logs table for system audit trail
CREATE TABLE IF NOT EXISTS audit_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id) ON DELETE SET NULL,
    employee_id INTEGER REFERENCES employees(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(50) NOT NULL, -- 'order', 'product', 'user', etc.
    resource_id INTEGER,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    session_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT NOW()
);

-- Update existing tables with missing columns if needed
ALTER TABLE orders ADD COLUMN IF NOT EXISTS table_id INTEGER REFERENCES tables(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS session_id INTEGER REFERENCES table_sessions(id);
ALTER TABLE orders ADD COLUMN IF NOT EXISTS payment_status VARCHAR(20) DEFAULT 'pending' CHECK (payment_status IN ('pending', 'partial', 'completed', 'refunded'));

-- Create indexes for performance optimization
-- Payments table indexes
CREATE INDEX IF NOT EXISTS idx_payments_tenant_id ON payments(tenant_id);
CREATE INDEX IF NOT EXISTS idx_payments_order_id ON payments(order_id);
CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
CREATE INDEX IF NOT EXISTS idx_payments_created_at ON payments(created_at);
CREATE INDEX IF NOT EXISTS idx_payments_transaction_id ON payments(transaction_id);

-- Tables table indexes
CREATE INDEX IF NOT EXISTS idx_tables_tenant_id ON tables(tenant_id);
CREATE INDEX IF NOT EXISTS idx_tables_status ON tables(status);
CREATE INDEX IF NOT EXISTS idx_tables_location_id ON tables(location_id);
CREATE INDEX IF NOT EXISTS idx_tables_current_order ON tables(current_order_id);

-- Table sessions indexes
CREATE INDEX IF NOT EXISTS idx_table_sessions_tenant_id ON table_sessions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_table_sessions_table_id ON table_sessions(table_id);
CREATE INDEX IF NOT EXISTS idx_table_sessions_status ON table_sessions(status);
CREATE INDEX IF NOT EXISTS idx_table_sessions_server_id ON table_sessions(server_id);
CREATE INDEX IF NOT EXISTS idx_table_sessions_session_start ON table_sessions(session_start);

-- Payment splits indexes
CREATE INDEX IF NOT EXISTS idx_payment_splits_tenant_id ON payment_splits(tenant_id);
CREATE INDEX IF NOT EXISTS idx_payment_splits_order_id ON payment_splits(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_splits_payment_id ON payment_splits(payment_id);
CREATE INDEX IF NOT EXISTS idx_payment_splits_status ON payment_splits(payment_status);

-- Inventory transactions indexes
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_tenant_id ON inventory_transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_product_id ON inventory_transactions(product_id);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_type ON inventory_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS idx_inventory_transactions_created_at ON inventory_transactions(created_at);

-- Audit logs indexes
CREATE INDEX IF NOT EXISTS idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_user_id ON audit_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_employee_id ON audit_logs(employee_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_action ON audit_logs(action);
CREATE INDEX IF NOT EXISTS idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX IF NOT EXISTS idx_audit_logs_created_at ON audit_logs(created_at);

-- Add indexes to existing tables for better performance
CREATE INDEX IF NOT EXISTS idx_orders_table_id ON orders(table_id);
CREATE INDEX IF NOT EXISTS idx_orders_session_id ON orders(session_id);
CREATE INDEX IF NOT EXISTS idx_orders_payment_status ON orders(payment_status);

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to tables with updated_at columns
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tables_updated_at BEFORE UPDATE ON tables FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_table_sessions_updated_at BEFORE UPDATE ON table_sessions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_splits_updated_at BEFORE UPDATE ON payment_splits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert sample data for testing (optional)
-- Sample tables for a restaurant
INSERT INTO tables (tenant_id, table_number, table_name, capacity, table_type, position_x, position_y, status) VALUES
(1, 'T1', 'Table 1', 4, 'regular', 100, 100, 'available'),
(1, 'T2', 'Table 2', 2, 'regular', 250, 100, 'available'),
(1, 'T3', 'Table 3', 6, 'booth', 100, 250, 'available'),
(1, 'T4', 'Table 4', 8, 'private', 400, 250, 'available'),
(1, 'B1', 'Bar Seat 1', 1, 'bar', 500, 100, 'available'),
(1, 'B2', 'Bar Seat 2', 1, 'bar', 550, 100, 'available')
ON CONFLICT (tenant_id, location_id, table_number) DO NOTHING;

-- Create views for common queries
CREATE OR REPLACE VIEW table_status_summary AS
SELECT 
    t.tenant_id,
    t.status,
    COUNT(*) as table_count,
    SUM(t.capacity) as total_capacity
FROM tables t
WHERE t.is_active = true
GROUP BY t.tenant_id, t.status;

CREATE OR REPLACE VIEW payment_summary AS
SELECT 
    p.tenant_id,
    DATE(p.created_at) as payment_date,
    p.payment_method,
    p.status,
    COUNT(*) as transaction_count,
    SUM(p.total_amount) as total_amount,
    AVG(p.total_amount) as average_amount
FROM payments p
GROUP BY p.tenant_id, DATE(p.created_at), p.payment_method, p.status;

-- Grant permissions would be handled separately based on actual database roles
