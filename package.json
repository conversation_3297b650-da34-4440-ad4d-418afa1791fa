{"name": "enterprise-pos-system", "version": "2.0.0", "private": true, "dependencies": {"@stripe/react-stripe-js": "^2.4.0", "@stripe/stripe-js": "^2.4.0", "@tailwindcss/postcss": "^4.1.7", "axios": "^1.9.0", "date-fns": "^3.0.6", "html2canvas": "^1.4.1", "idb": "^8.0.0", "jest-environment-jsdom": "^30.0.0-beta.3", "jspdf": "^2.5.1", "lucide-react": "^0.344.0", "qrcode": "^1.5.3", "react": "^18.3.1", "react-dom": "^18.3.1", "react-query": "^3.39.3", "react-router-dom": "^6.21.0", "recharts": "^2.10.3", "socket.io-client": "^4.7.4", "uuid": "^9.0.1", "workbox-window": "^7.0.0", "xlsx": "^0.18.5"}, "devDependencies": {"@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/jest": "^29.5.14", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "@types/testing-library__react": "^10.0.1", "@types/uuid": "^10.0.0", "cypress": "^14.4.0", "jest": "^29.7.0", "react-scripts": "^5.0.1", "ts-jest": "^29.3.4", "typescript": "^5.6.3", "vite": "^4.0.0", "vite-plugin-pwa": "^0.17.4", "workbox-cli": "^7.0.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "preview": "vite preview", "generate-sw": "workbox generateSW workbox-config.js", "dev": "react-scripts start", "test": "jest", "test:all": "node tests/unified-test-runner.js all", "test:unit": "jest", "test:integration": "node tests/unified-test-runner.js integration", "test:e2e": "node tests/unified-test-runner.js e2e", "test:security": "node tests/security/security-audit.js", "test:performance": "node tests/unified-test-runner.js performance", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "lint": "eslint src --ext .ts,.tsx,.js,.jsx", "lint:fix": "eslint src --ext .ts,.tsx,.js,.jsx --fix", "type-check": "tsc --noEmit", "docs:api": "echo 'API documentation available in docs/API_DOCUMENTATION.md'", "deploy:prod": "docker-compose -f docker-compose.production.yml up -d", "deploy:dev": "docker-compose up -d", "backup:db": "docker-compose -f docker-compose.production.yml run --rm backup", "logs:backend": "docker-compose logs -f backend", "logs:frontend": "docker-compose logs -f frontend", "health:check": "curl -f http://localhost:4000/api/health && curl -f http://localhost:3000/health"}}