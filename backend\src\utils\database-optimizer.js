// Database Query Optimization Utilities for RESTROFLOW
const { pool } = require('../database/config/connection');

class DatabaseOptimizer {
  constructor() {
    this.queryStats = {
      totalQueries: 0,
      slowQueries: 0,
      averageTime: 0,
      queryTimes: []
    };

    console.log('🔧 Database Optimizer initialized');
  }

  // Optimized query wrapper with performance monitoring
  async executeQuery(query, params = [], options = {}) {
    const startTime = Date.now();
    const queryId = Math.random().toString(36).substr(2, 9);
    
    try {
      console.log(`🔍 Query ${queryId} starting:`, query.substring(0, 100) + '...');
      
      const result = await pool.query(query, params);
      const executionTime = Date.now() - startTime;
      
      // Track query performance
      this.trackQueryPerformance(query, executionTime, result.rowCount);
      
      // Log slow queries
      if (executionTime > (options.slowQueryThreshold || 1000)) {
        console.warn(`🐌 SLOW QUERY ${queryId}: ${executionTime}ms`);
        console.warn('Query:', query);
        console.warn('Params:', params);
      } else {
        console.log(`⚡ Query ${queryId} completed: ${executionTime}ms, ${result.rowCount} rows`);
      }
      
      return result;
    } catch (error) {
      const executionTime = Date.now() - startTime;
      console.error(`❌ Query ${queryId} failed after ${executionTime}ms:`, error.message);
      throw error;
    }
  }

  // Track query performance statistics
  trackQueryPerformance(query, executionTime, rowCount) {
    this.queryStats.totalQueries++;
    this.queryStats.queryTimes.push(executionTime);
    
    // Keep only last 1000 query times for memory efficiency
    if (this.queryStats.queryTimes.length > 1000) {
      this.queryStats.queryTimes = this.queryStats.queryTimes.slice(-1000);
    }
    
    // Calculate average
    this.queryStats.averageTime = 
      this.queryStats.queryTimes.reduce((a, b) => a + b, 0) / this.queryStats.queryTimes.length;
    
    // Count slow queries (>1000ms)
    if (executionTime > 1000) {
      this.queryStats.slowQueries++;
    }
  }

  // Optimized queries for common operations
  getOptimizedQueries() {
    return {
      // Products with category and inventory - optimized with proper joins
      getProductsWithDetails: `
        SELECT 
          p.id, p.name, p.description, p.price, p.image_url, p.is_active,
          p.sort_order, p.created_at, p.updated_at,
          c.name as category_name, c.color as category_color, c.sort_order as category_sort,
          COALESCE(i.current_stock, 0) as current_stock,
          COALESCE(i.min_stock_level, 0) as min_stock_level,
          CASE WHEN i.current_stock <= i.min_stock_level THEN true ELSE false END as low_stock
        FROM products p
        LEFT JOIN categories c ON p.category_id = c.id
        LEFT JOIN inventory i ON p.id = i.product_id
        WHERE p.tenant_id = $1 AND p.is_active = true 
        ORDER BY c.sort_order NULLS LAST, p.sort_order, p.name
      `,

      // Orders with items - optimized to reduce N+1 queries
      getOrdersWithItems: `
        SELECT 
          o.id, o.order_number, o.table_id, o.status, o.total_amount,
          o.tax_amount, o.discount_amount, o.payment_status, o.created_at,
          t.table_number, t.section,
          json_agg(
            json_build_object(
              'id', oi.id,
              'product_id', oi.product_id,
              'product_name', p.name,
              'quantity', oi.quantity,
              'unit_price', oi.unit_price,
              'total_price', oi.total_price,
              'special_instructions', oi.special_instructions
            ) ORDER BY oi.id
          ) as items
        FROM orders o
        LEFT JOIN tables t ON o.table_id = t.id
        LEFT JOIN order_items oi ON o.id = oi.order_id
        LEFT JOIN products p ON oi.product_id = p.id
        WHERE o.tenant_id = $1
        GROUP BY o.id, t.table_number, t.section
        ORDER BY o.created_at DESC
        LIMIT $2 OFFSET $3
      `,

      // Analytics query - optimized with proper aggregations
      getSalesAnalytics: `
        SELECT 
          DATE(o.created_at) as date,
          COUNT(o.id) as total_orders,
          SUM(o.total_amount) as total_revenue,
          AVG(o.total_amount) as average_order_value,
          COUNT(DISTINCT o.table_id) as tables_served,
          COUNT(CASE WHEN o.status = 'completed' THEN 1 END) as completed_orders,
          COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders,
          -- Top selling products
          (
            SELECT json_agg(
              json_build_object(
                'product_name', p.name,
                'quantity_sold', SUM(oi.quantity),
                'revenue', SUM(oi.total_price)
              ) ORDER BY SUM(oi.quantity) DESC
            )
            FROM order_items oi
            JOIN products p ON oi.product_id = p.id
            JOIN orders o2 ON oi.order_id = o2.id
            WHERE o2.tenant_id = $1 
              AND DATE(o2.created_at) = DATE(o.created_at)
              AND o2.status = 'completed'
            GROUP BY p.id, p.name
            LIMIT 5
          ) as top_products
        FROM orders o
        WHERE o.tenant_id = $1 
          AND o.created_at >= $2 
          AND o.created_at <= $3
          AND o.status = 'completed'
        GROUP BY DATE(o.created_at)
        ORDER BY date DESC
      `,

      // Table status - optimized for real-time updates
      getTableStatus: `
        SELECT 
          t.id, t.table_number, t.section, t.capacity, t.status,
          t.x_position, t.y_position, t.is_active,
          CASE 
            WHEN o.id IS NOT NULL THEN 'occupied'
            ELSE t.status
          END as current_status,
          o.id as current_order_id,
          o.order_number as current_order_number,
          o.total_amount as current_order_total,
          o.created_at as order_start_time,
          EXTRACT(EPOCH FROM (NOW() - o.created_at))/60 as minutes_occupied
        FROM tables t
        LEFT JOIN orders o ON t.id = o.table_id 
          AND o.status IN ('pending', 'confirmed', 'preparing', 'ready')
        WHERE t.tenant_id = $1 AND t.is_active = true
        ORDER BY t.section, t.table_number
      `,

      // Employee performance - optimized aggregation
      getEmployeePerformance: `
        SELECT 
          e.id, e.name, e.role,
          COUNT(o.id) as orders_handled,
          COALESCE(SUM(o.total_amount), 0) as total_sales,
          COALESCE(AVG(o.total_amount), 0) as average_order_value,
          COUNT(CASE WHEN o.status = 'completed' THEN 1 END) as completed_orders,
          COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders,
          CASE 
            WHEN COUNT(o.id) > 0 THEN 
              ROUND((COUNT(CASE WHEN o.status = 'completed' THEN 1 END)::decimal / COUNT(o.id) * 100), 2)
            ELSE 0 
          END as completion_rate
        FROM employees e
        LEFT JOIN orders o ON e.id = o.created_by 
          AND o.created_at >= $2 
          AND o.created_at <= $3
        WHERE e.tenant_id = $1 AND e.is_active = true
        GROUP BY e.id, e.name, e.role
        ORDER BY total_sales DESC
      `
    };
  }

  // Create database indexes for better performance
  async createOptimizationIndexes() {
    const indexes = [
      // Products indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_tenant_active ON products(tenant_id, is_active)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_products_category ON products(category_id)',
      
      // Orders indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_tenant_status ON orders(tenant_id, status)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_created_at ON orders(created_at DESC)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_orders_table_status ON orders(table_id, status)',
      
      // Order items indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_order_id ON order_items(order_id)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_order_items_product_id ON order_items(product_id)',
      
      // Tables indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_tables_tenant_active ON tables(tenant_id, is_active)',
      
      // Employees indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_employees_tenant_active ON employees(tenant_id, is_active)',
      
      // Categories indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_categories_tenant_active ON categories(tenant_id, is_active)',
      
      // Inventory indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_inventory_product_id ON inventory(product_id)',
      
      // Payments indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_order_id ON payments(order_id)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_payments_tenant_status ON payments(tenant_id, status)',
      
      // User sessions indexes
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_token ON user_sessions(session_token)',
      'CREATE INDEX CONCURRENTLY IF NOT EXISTS idx_user_sessions_user_active ON user_sessions(user_id, is_active)'
    ];

    console.log('🔧 Creating database optimization indexes...');
    
    for (const indexQuery of indexes) {
      try {
        await this.executeQuery(indexQuery);
        console.log('✅ Index created:', indexQuery.match(/idx_\w+/)?.[0] || 'unnamed');
      } catch (error) {
        if (error.message.includes('already exists')) {
          console.log('ℹ️ Index already exists:', indexQuery.match(/idx_\w+/)?.[0] || 'unnamed');
        } else {
          console.error('❌ Failed to create index:', error.message);
        }
      }
    }
    
    console.log('✅ Database optimization indexes completed');
  }

  // Analyze query performance
  async analyzeQueryPerformance() {
    try {
      const result = await this.executeQuery(`
        SELECT 
          query,
          calls,
          total_time,
          mean_time,
          rows,
          100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
        FROM pg_stat_statements 
        WHERE query NOT LIKE '%pg_stat_statements%'
        ORDER BY total_time DESC 
        LIMIT 10
      `);
      
      return result.rows;
    } catch (error) {
      console.log('ℹ️ pg_stat_statements not available for query analysis');
      return [];
    }
  }

  // Get performance statistics
  getPerformanceStats() {
    return {
      ...this.queryStats,
      slow_query_percentage: this.queryStats.totalQueries > 0 
        ? (this.queryStats.slowQueries / this.queryStats.totalQueries * 100).toFixed(2) + '%'
        : '0%',
      queries_per_second: this.queryStats.totalQueries / (process.uptime() || 1)
    };
  }

  // Connection pool monitoring
  async getConnectionPoolStats() {
    return {
      total_connections: pool.totalCount,
      idle_connections: pool.idleCount,
      waiting_requests: pool.waitingCount,
      max_connections: pool.options.max || 20
    };
  }

  // Database health check
  async healthCheck() {
    try {
      const startTime = Date.now();
      await this.executeQuery('SELECT 1 as health_check');
      const responseTime = Date.now() - startTime;
      
      const poolStats = await this.getConnectionPoolStats();
      const perfStats = this.getPerformanceStats();
      
      return {
        status: 'healthy',
        response_time_ms: responseTime,
        connection_pool: poolStats,
        query_performance: perfStats,
        database_connected: true
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        database_connected: false
      };
    }
  }
}

// Create singleton instance
const dbOptimizer = new DatabaseOptimizer();

module.exports = {
  DatabaseOptimizer,
  dbOptimizer
};
