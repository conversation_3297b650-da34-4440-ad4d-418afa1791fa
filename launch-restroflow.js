#!/usr/bin/env node

/**
 * RESTROFLOW System Launcher
 * Automated system startup with verification
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 RESTROFLOW SYSTEM LAUNCHER');
console.log('=============================');
console.log(`⏰ Launch initiated: ${new Date().toLocaleString()}`);

class RestroFlowLauncher {
  constructor() {
    this.processes = [];
    this.isShuttingDown = false;
  }

  async launch() {
    try {
      console.log('\n📋 Pre-launch System Check...');
      await this.prelaunchCheck();

      console.log('\n🔧 Starting Backend Services...');
      await this.startBackend();

      console.log('\n🎨 Starting Frontend Services...');
      await this.startFrontend();

      console.log('\n✅ System Launch Complete!');
      this.displayAccessInformation();
      this.setupGracefulShutdown();

    } catch (error) {
      console.error('\n❌ Launch failed:', error.message);
      await this.cleanup();
      process.exit(1);
    }
  }

  async prelaunchCheck() {
    const requiredFiles = [
      'package.json',
      'backend/package.json',
      'src/App.tsx',
      'backend/src/server.js'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }

    console.log('✅ All required files present');

    // Check if dependencies are installed
    if (!fs.existsSync('node_modules')) {
      console.log('📦 Installing frontend dependencies...');
      await this.runCommand('npm install');
    }

    if (!fs.existsSync('backend/node_modules')) {
      console.log('📦 Installing backend dependencies...');
      await this.runCommand('npm install', { cwd: 'backend' });
    }

    console.log('✅ Dependencies verified');
  }

  async startBackend() {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting backend server...');
      
      const backend = spawn('npm', ['start'], {
        cwd: 'backend',
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      this.processes.push({ name: 'Backend', process: backend });

      let backendReady = false;
      let startupTimeout;

      backend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[Backend] ${output.trim()}`);
        
        if (output.includes('Server running on') || output.includes('listening on')) {
          if (!backendReady) {
            backendReady = true;
            clearTimeout(startupTimeout);
            console.log('✅ Backend server started successfully');
            resolve();
          }
        }
      });

      backend.stderr.on('data', (data) => {
        const error = data.toString();
        if (!error.includes('Warning') && !error.includes('deprecated')) {
          console.error(`[Backend Error] ${error.trim()}`);
        }
      });

      backend.on('error', (error) => {
        console.error('❌ Backend startup error:', error.message);
        reject(error);
      });

      backend.on('exit', (code) => {
        if (code !== 0 && !this.isShuttingDown) {
          console.error(`❌ Backend exited with code ${code}`);
          reject(new Error(`Backend process failed with code ${code}`));
        }
      });

      // Timeout after 30 seconds
      startupTimeout = setTimeout(() => {
        if (!backendReady) {
          console.log('⚠️ Backend startup timeout, but continuing...');
          resolve();
        }
      }, 30000);
    });
  }

  async startFrontend() {
    return new Promise((resolve, reject) => {
      console.log('🔄 Starting frontend server...');
      
      const frontend = spawn('npm', ['start'], {
        stdio: ['pipe', 'pipe', 'pipe'],
        shell: true
      });

      this.processes.push({ name: 'Frontend', process: frontend });

      let frontendReady = false;
      let startupTimeout;

      frontend.stdout.on('data', (data) => {
        const output = data.toString();
        console.log(`[Frontend] ${output.trim()}`);
        
        if (output.includes('Local:') || output.includes('localhost:5173')) {
          if (!frontendReady) {
            frontendReady = true;
            clearTimeout(startupTimeout);
            console.log('✅ Frontend server started successfully');
            resolve();
          }
        }
      });

      frontend.stderr.on('data', (data) => {
        const error = data.toString();
        if (!error.includes('Warning') && !error.includes('deprecated')) {
          console.error(`[Frontend Error] ${error.trim()}`);
        }
      });

      frontend.on('error', (error) => {
        console.error('❌ Frontend startup error:', error.message);
        reject(error);
      });

      frontend.on('exit', (code) => {
        if (code !== 0 && !this.isShuttingDown) {
          console.error(`❌ Frontend exited with code ${code}`);
          reject(new Error(`Frontend process failed with code ${code}`));
        }
      });

      // Timeout after 30 seconds
      startupTimeout = setTimeout(() => {
        if (!frontendReady) {
          console.log('⚠️ Frontend startup timeout, but continuing...');
          resolve();
        }
      }, 30000);
    });
  }

  displayAccessInformation() {
    console.log('\n🎉 RESTROFLOW SYSTEM READY!');
    console.log('===========================');
    console.log('🌐 Main System: http://localhost:5173');
    console.log('🔧 Backend API: http://localhost:4000');
    console.log('');
    console.log('🔑 ACCESS CREDENTIALS:');
    console.log('======================');
    console.log('👑 Super Admin: PIN 123456');
    console.log('👤 Employee: PIN 111222');
    console.log('👨‍💼 Manager: PIN 567890');
    console.log('🏢 Tenant Admin: PIN 555666');
    console.log('🔄 Original Interfaces: PIN 999999');
    console.log('🔍 Debug Mode: PIN 000000');
    console.log('');
    console.log('🛡️ ENTERPRISE SECURITY CENTER:');
    console.log('===============================');
    console.log('To start security system: npm run super-admin');
    console.log('Security URL: http://localhost:5174');
    console.log('Security Login: PIN 123456');
    console.log('');
    console.log('📚 DOCUMENTATION:');
    console.log('=================');
    console.log('📖 System Guide: SYSTEM_COMPLETION_REPORT.md');
    console.log('🚀 Deployment: FINAL_DEPLOYMENT_GUIDE.md');
    console.log('🛡️ Security: ENTERPRISE_SECURITY_SYSTEM.md');
    console.log('');
    console.log('⚠️ To stop the system: Press Ctrl+C');
    console.log('✨ Your enterprise restaurant POS system is now running!');
  }

  setupGracefulShutdown() {
    const shutdown = async (signal) => {
      if (this.isShuttingDown) return;
      this.isShuttingDown = true;

      console.log(`\n⚠️ Received ${signal}, shutting down gracefully...`);
      await this.cleanup();
      console.log('✅ System shutdown complete');
      process.exit(0);
    };

    process.on('SIGINT', () => shutdown('SIGINT'));
    process.on('SIGTERM', () => shutdown('SIGTERM'));
    process.on('SIGQUIT', () => shutdown('SIGQUIT'));
  }

  async cleanup() {
    console.log('🧹 Cleaning up processes...');
    
    for (const { name, process } of this.processes) {
      try {
        console.log(`⏹️ Stopping ${name}...`);
        process.kill('SIGTERM');
        
        // Give process time to shut down gracefully
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        if (!process.killed) {
          console.log(`🔨 Force killing ${name}...`);
          process.kill('SIGKILL');
        }
      } catch (error) {
        console.error(`❌ Error stopping ${name}:`, error.message);
      }
    }
  }

  runCommand(command, options = {}) {
    return new Promise((resolve, reject) => {
      exec(command, options, (error, stdout, stderr) => {
        if (error) {
          reject(error);
        } else {
          resolve(stdout);
        }
      });
    });
  }
}

// Handle command line arguments
const args = process.argv.slice(2);
const showHelp = args.includes('--help') || args.includes('-h');

if (showHelp) {
  console.log(`
RESTROFLOW System Launcher

Usage: node launch-restroflow.js [options]

Options:
  --help, -h     Show this help message
  
Description:
  Automatically starts the complete RESTROFLOW system including:
  - Backend API server (port 4000)
  - Frontend application (port 5173)
  - Dependency verification
  - System health checks

Access URLs:
  Main System: http://localhost:5173
  Backend API: http://localhost:4000
  
Login Credentials:
  Super Admin: PIN 123456
  Employee: PIN 111222
  Manager: PIN 567890
  
To start enterprise security system:
  npm run super-admin (port 5174)
`);
  process.exit(0);
}

// Launch the system
const launcher = new RestroFlowLauncher();
launcher.launch().catch(error => {
  console.error('❌ System launch failed:', error);
  process.exit(1);
});
