// Performance Monitoring and Analytics for Authentication System
// Tracks login performance, system metrics, and user analytics

interface PerformanceMetric {
  id: string;
  timestamp: string;
  metricType: 'login_time' | 'api_response' | 'page_load' | 'database_query' | 'authentication_flow';
  value: number;
  unit: 'ms' | 'seconds' | 'bytes' | 'count';
  userId?: number;
  tenantId?: number;
  sessionId?: string;
  endpoint?: string;
  userAgent?: string;
  metadata: Record<string, any>;
}

interface LoginAnalytics {
  totalLogins: number;
  successfulLogins: number;
  failedLogins: number;
  averageLoginTime: number;
  peakLoginHours: number[];
  loginsByMethod: Record<string, number>;
  loginsByTenant: Record<string, number>;
  uniqueUsers: number;
  returningUsers: number;
  newUsers: number;
}

interface SystemMetrics {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkLatency: number;
  databaseConnections: number;
  activeUsers: number;
  requestsPerSecond: number;
  errorRate: number;
  uptime: number;
}

interface PerformanceReport {
  timeRange: { start: string; end: string };
  loginAnalytics: LoginAnalytics;
  systemMetrics: SystemMetrics;
  performanceTrends: Array<{
    timestamp: string;
    avgLoginTime: number;
    successRate: number;
    activeUsers: number;
  }>;
  recommendations: string[];
}

class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private maxMetrics = 10000;
  private batchSize = 100;
  private flushInterval = 60000; // 1 minute
  private pendingMetrics: PerformanceMetric[] = [];
  private performanceObserver?: PerformanceObserver;

  private constructor() {
    this.initializePerformanceObserver();
    this.startPeriodicFlush();
    this.startSystemMonitoring();
  }

  public static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  // Initialize Performance Observer for automatic metrics collection
  private initializePerformanceObserver(): void {
    if ('PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === 'navigation') {
            this.recordMetric({
              metricType: 'page_load',
              value: entry.duration,
              unit: 'ms',
              metadata: {
                type: entry.type,
                redirectCount: (entry as PerformanceNavigationTiming).redirectCount,
                domContentLoaded: (entry as PerformanceNavigationTiming).domContentLoadedEventEnd - entry.startTime
              }
            });
          } else if (entry.entryType === 'measure') {
            this.recordMetric({
              metricType: 'authentication_flow',
              value: entry.duration,
              unit: 'ms',
              metadata: {
                name: entry.name,
                startTime: entry.startTime
              }
            });
          }
        }
      });

      this.performanceObserver.observe({ entryTypes: ['navigation', 'measure'] });
    }
  }

  // Record a performance metric
  public recordMetric(data: {
    metricType: PerformanceMetric['metricType'];
    value: number;
    unit: PerformanceMetric['unit'];
    userId?: number;
    tenantId?: number;
    endpoint?: string;
    metadata?: Record<string, any>;
  }): void {
    const metric: PerformanceMetric = {
      id: `perf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      sessionId: this.getSessionId(),
      userAgent: navigator.userAgent,
      ...data,
      metadata: data.metadata || {}
    };

    this.metrics.push(metric);
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    this.pendingMetrics.push(metric);

    // Auto-flush if batch is full
    if (this.pendingMetrics.length >= this.batchSize) {
      this.flushMetrics();
    }
  }

  // Record login performance
  public recordLoginPerformance(data: {
    startTime: number;
    endTime: number;
    success: boolean;
    method: string;
    userId?: number;
    tenantId?: number;
    steps?: Array<{ name: string; duration: number }>;
  }): void {
    const totalTime = data.endTime - data.startTime;

    this.recordMetric({
      metricType: 'login_time',
      value: totalTime,
      unit: 'ms',
      userId: data.userId,
      tenantId: data.tenantId,
      metadata: {
        success: data.success,
        method: data.method,
        steps: data.steps
      }
    });

    // Record individual steps if provided
    if (data.steps) {
      data.steps.forEach(step => {
        this.recordMetric({
          metricType: 'authentication_flow',
          value: step.duration,
          unit: 'ms',
          userId: data.userId,
          tenantId: data.tenantId,
          metadata: {
            step: step.name,
            method: data.method
          }
        });
      });
    }
  }

  // Record API response time
  public recordAPIResponse(data: {
    endpoint: string;
    method: string;
    responseTime: number;
    statusCode: number;
    userId?: number;
    tenantId?: number;
  }): void {
    this.recordMetric({
      metricType: 'api_response',
      value: data.responseTime,
      unit: 'ms',
      endpoint: data.endpoint,
      userId: data.userId,
      tenantId: data.tenantId,
      metadata: {
        method: data.method,
        statusCode: data.statusCode,
        success: data.statusCode >= 200 && data.statusCode < 300
      }
    });
  }

  // Start system monitoring
  private startSystemMonitoring(): void {
    // Monitor memory usage
    setInterval(() => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        this.recordMetric({
          metricType: 'page_load',
          value: memory.usedJSHeapSize,
          unit: 'bytes',
          metadata: {
            type: 'memory_usage',
            totalHeapSize: memory.totalJSHeapSize,
            heapSizeLimit: memory.jsHeapSizeLimit
          }
        });
      }
    }, 30000); // Every 30 seconds

    // Monitor connection quality
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      this.recordMetric({
        metricType: 'api_response',
        value: connection.rtt || 0,
        unit: 'ms',
        metadata: {
          type: 'network_latency',
          effectiveType: connection.effectiveType,
          downlink: connection.downlink
        }
      });
    }
  }

  // Get session ID
  private getSessionId(): string {
    return localStorage.getItem('sessionId') || 'no_session';
  }

  // Flush metrics to backend
  private async flushMetrics(): Promise<void> {
    if (this.pendingMetrics.length === 0) return;

    const metricsToSend = [...this.pendingMetrics];
    this.pendingMetrics = [];

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/analytics/metrics', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify({ metrics: metricsToSend })
      });

      if (response.ok) {
        console.log(`✅ Flushed ${metricsToSend.length} performance metrics`);
      } else {
        console.error('❌ Failed to flush performance metrics');
        // Re-add failed metrics to pending queue
        this.pendingMetrics.unshift(...metricsToSend);
      }
    } catch (error) {
      console.error('❌ Error flushing performance metrics:', error);
      // Re-add failed metrics to pending queue
      this.pendingMetrics.unshift(...metricsToSend);
    }
  }

  // Start periodic flush
  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flushMetrics();
    }, this.flushInterval);
  }

  // Get login analytics
  public async getLoginAnalytics(timeRange?: { start: string; end: string }): Promise<LoginAnalytics | null> {
    try {
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams();
      
      if (timeRange) {
        queryParams.append('start', timeRange.start);
        queryParams.append('end', timeRange.end);
      }

      const response = await fetch(`http://localhost:4000/api/analytics/login-analytics?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to fetch login analytics');
        return null;
      }
    } catch (error) {
      console.error('Error fetching login analytics:', error);
      return null;
    }
  }

  // Get system metrics
  public async getSystemMetrics(): Promise<SystemMetrics | null> {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/analytics/system-metrics', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to fetch system metrics');
        return null;
      }
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      return null;
    }
  }

  // Generate performance report
  public async generatePerformanceReport(timeRange?: { start: string; end: string }): Promise<PerformanceReport | null> {
    try {
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams();
      
      if (timeRange) {
        queryParams.append('start', timeRange.start);
        queryParams.append('end', timeRange.end);
      }

      const response = await fetch(`http://localhost:4000/api/analytics/performance-report?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to generate performance report');
        return null;
      }
    } catch (error) {
      console.error('Error generating performance report:', error);
      return null;
    }
  }

  // Get local metrics for offline analysis
  public getLocalMetrics(filter: {
    metricType?: string;
    userId?: number;
    tenantId?: number;
    limit?: number;
  } = {}): PerformanceMetric[] {
    let metrics = [...this.metrics];

    if (filter.metricType) {
      metrics = metrics.filter(m => m.metricType === filter.metricType);
    }

    if (filter.userId) {
      metrics = metrics.filter(m => m.userId === filter.userId);
    }

    if (filter.tenantId) {
      metrics = metrics.filter(m => m.tenantId === filter.tenantId);
    }

    return metrics.slice(-(filter.limit || 1000));
  }

  // Calculate local performance statistics
  public getLocalPerformanceStats(): {
    averageLoginTime: number;
    averageAPIResponseTime: number;
    totalMetrics: number;
    metricsByType: Record<string, number>;
  } {
    const loginMetrics = this.metrics.filter(m => m.metricType === 'login_time');
    const apiMetrics = this.metrics.filter(m => m.metricType === 'api_response');

    const averageLoginTime = loginMetrics.length > 0
      ? loginMetrics.reduce((sum, m) => sum + m.value, 0) / loginMetrics.length
      : 0;

    const averageAPIResponseTime = apiMetrics.length > 0
      ? apiMetrics.reduce((sum, m) => sum + m.value, 0) / apiMetrics.length
      : 0;

    const metricsByType: Record<string, number> = {};
    this.metrics.forEach(metric => {
      metricsByType[metric.metricType] = (metricsByType[metric.metricType] || 0) + 1;
    });

    return {
      averageLoginTime: Math.round(averageLoginTime),
      averageAPIResponseTime: Math.round(averageAPIResponseTime),
      totalMetrics: this.metrics.length,
      metricsByType
    };
  }

  // Start performance measurement
  public startMeasurement(name: string): void {
    if ('performance' in window && 'mark' in performance) {
      performance.mark(`${name}-start`);
    }
  }

  // End performance measurement
  public endMeasurement(name: string): number {
    if ('performance' in window && 'mark' in performance && 'measure' in performance) {
      performance.mark(`${name}-end`);
      performance.measure(name, `${name}-start`, `${name}-end`);
      
      const measure = performance.getEntriesByName(name, 'measure')[0];
      return measure ? measure.duration : 0;
    }
    return 0;
  }

  // Clear local metrics
  public clearLocalMetrics(): void {
    this.metrics = [];
    console.log('🗑️ Local performance metrics cleared');
  }

  // Export metrics
  public exportMetrics(format: 'json' | 'csv' = 'json'): string {
    if (format === 'csv') {
      const headers = ['timestamp', 'metricType', 'value', 'unit', 'userId', 'tenantId', 'endpoint'];
      const rows = this.metrics.map(metric => [
        metric.timestamp,
        metric.metricType,
        metric.value.toString(),
        metric.unit,
        metric.userId?.toString() || '',
        metric.tenantId?.toString() || '',
        metric.endpoint || ''
      ]);
      
      return [headers, ...rows].map(row => row.join(',')).join('\n');
    } else {
      return JSON.stringify(this.metrics, null, 2);
    }
  }

  // Cleanup
  public cleanup(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    this.flushMetrics();
  }
}

// Export singleton instance
export const performanceMonitor = PerformanceMonitor.getInstance();

// Export types
export type { PerformanceMetric, LoginAnalytics, SystemMetrics, PerformanceReport };

// Utility functions
export const recordLoginTime = (startTime: number, endTime: number, success: boolean, method: string, userId?: number, tenantId?: number) => {
  performanceMonitor.recordLoginPerformance({
    startTime,
    endTime,
    success,
    method,
    userId,
    tenantId
  });
};

export const recordAPICall = (endpoint: string, method: string, responseTime: number, statusCode: number, userId?: number, tenantId?: number) => {
  performanceMonitor.recordAPIResponse({
    endpoint,
    method,
    responseTime,
    statusCode,
    userId,
    tenantId
  });
};

export const startPerformanceMeasurement = (name: string) => {
  performanceMonitor.startMeasurement(name);
};

export const endPerformanceMeasurement = (name: string) => {
  return performanceMonitor.endMeasurement(name);
};

export const getPerformanceStats = () => {
  return performanceMonitor.getLocalPerformanceStats();
};
