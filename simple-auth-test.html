<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #dc2626;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px 0;
        }
        button:hover {
            background: #b91c1c;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Simple Authentication Test</h1>
        <p>Testing super admin authentication with PIN 123456</p>
        
        <button onclick="testAuth()">🚀 Test Authentication</button>
        <button onclick="clearLog()">🗑️ Clear Log</button>
        
        <div id="log"></div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            if (!logDiv.querySelector('.result')) {
                const resultDiv = document.createElement('div');
                resultDiv.className = 'result';
                logDiv.appendChild(resultDiv);
            }
            
            const resultDiv = logDiv.querySelector('.result');
            resultDiv.textContent += logEntry;
            resultDiv.className = `result ${type}`;
            resultDiv.scrollTop = resultDiv.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        async function testAuth() {
            clearLog();
            log('🚀 Starting authentication test...');
            
            try {
                log('📡 Sending request to backend...');
                
                const requestData = {
                    pin: '123456',
                    tenant_slug: 'auto-detect',
                    admin_access: true
                };
                
                log(`📦 Request data: ${JSON.stringify(requestData, null, 2)}`);
                
                const response = await fetch('http://localhost:4000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });
                
                log(`📊 Response status: ${response.status} ${response.statusText}`);
                log(`✅ Response OK: ${response.ok}`);
                
                if (!response.ok) {
                    log(`❌ HTTP Error: ${response.status}`, 'error');
                    const errorText = await response.text();
                    log(`❌ Error response: ${errorText}`, 'error');
                    return;
                }
                
                log('📄 Parsing JSON response...');
                const data = await response.json();
                
                log(`📦 Response data: ${JSON.stringify(data, null, 2)}`);
                
                if (data.token) {
                    log('✅ Token received!', 'success');
                    log(`🔑 Token: ${data.token.substring(0, 50)}...`);
                    
                    if (data.user) {
                        log('✅ User data received!', 'success');
                        log(`👤 User: ${JSON.stringify(data.user, null, 2)}`);
                        
                        if (data.user.role === 'super_admin') {
                            log('🎉 SUPER ADMIN AUTHENTICATION SUCCESSFUL!', 'success');
                        } else {
                            log(`⚠️ Unexpected role: ${data.user.role}`, 'error');
                        }
                    } else {
                        log('⚠️ No user data in response', 'error');
                    }
                } else {
                    log('❌ No token in response', 'error');
                    if (data.error) {
                        log(`❌ Error: ${data.error}`, 'error');
                    }
                }
                
            } catch (error) {
                log(`❌ CATCH ERROR: ${error}`, 'error');
                log(`❌ Error type: ${typeof error}`, 'error');
                log(`❌ Error constructor: ${error?.constructor?.name}`, 'error');
                
                if (error instanceof Error) {
                    log(`❌ Error name: ${error.name}`, 'error');
                    log(`❌ Error message: ${error.message}`, 'error');
                    if (error.stack) {
                        log(`❌ Error stack: ${error.stack}`, 'error');
                    }
                } else {
                    log(`❌ Non-Error object: ${JSON.stringify(error)}`, 'error');
                }
            }
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            log('🌐 Page loaded, ready for testing');
        });
    </script>
</body>
</html>
