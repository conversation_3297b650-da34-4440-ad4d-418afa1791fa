import React, { useState } from 'react';
import { EnhancedAppProvider, useEnhancedAppContext } from './context/EnhancedAppContext';
import EnhancedLogin from './components/EnhancedLogin';

// Simple test component
const TestPOSInterface: React.FC = () => {
  const { state } = useEnhancedAppContext();

  return (
    <div className="h-screen flex flex-col bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200 px-4 py-3">
        <div className="flex items-center justify-between">
          <h1 className="text-xl font-semibold text-gray-900">
            🎉 Unified POS System Working!
          </h1>
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-500">
              {state.currentEmployee?.name || 'Test User'}
            </span>
            <span className="px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
              {state.currentEmployee?.role?.toUpperCase() || 'CONNECTED'}
            </span>
            <button
              onClick={() => window.location.reload()}
              className="px-3 py-1 text-sm text-gray-600 hover:text-gray-900 border border-gray-300 rounded-md hover:bg-gray-50"
            >
              Logout
            </button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">🚀</div>
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Unified POS System is Working!
          </h2>
          <p className="text-lg text-gray-600 mb-6">
            You have successfully logged in to the unified frontend system.
          </p>
          
          <div className="bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">System Status</h3>
            <div className="space-y-2 text-left">
              <div className="flex justify-between">
                <span className="text-gray-600">User:</span>
                <span className="font-medium">{state.currentEmployee?.name || 'Mock User'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Role:</span>
                <span className="font-medium">{state.currentEmployee?.role || 'mock_admin'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Tenant:</span>
                <span className="font-medium">{state.currentTenant?.name || 'Demo Restaurant'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Mode:</span>
                <span className="font-medium text-blue-600">Mock Backend</span>
              </div>
            </div>
          </div>

          <div className="mt-8 text-sm text-gray-500">
            <p>✅ Frontend: Working</p>
            <p>✅ Authentication: Mock Mode</p>
            <p>✅ Role-based Access: Implemented</p>
            <p>✅ Unified Interface: Ready</p>
          </div>
        </div>
      </main>

      {/* Status Bar */}
      <footer className="bg-white border-t border-gray-200 px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-500">
          <div className="flex items-center space-x-4">
            <span className="flex items-center">
              <div className="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
              System Online
            </span>
            <span>Mock Mode Active</span>
          </div>
          <div>
            {new Date().toLocaleString()}
          </div>
        </div>
      </footer>
    </div>
  );
};

// Main App Content Component
const TestPOSContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  const { state } = useEnhancedAppContext();

  // Update login state based on authentication
  React.useEffect(() => {
    setIsLoggedIn(state.isAuthenticated);
  }, [state.isAuthenticated, setIsLoggedIn]);

  if (!isLoggedIn) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full">
          <div className="text-center mb-8">
            <h1 className="text-3xl font-bold text-white mb-2">🧪 Test POS System</h1>
            <p className="text-blue-200">Enter your PIN to test the unified system</p>
          </div>
          <EnhancedLogin onLogin={setIsLoggedIn} />
        </div>
      </div>
    );
  }

  return <TestPOSInterface />;
};

// Main Test Unified POS System Component
const TestUnifiedPOS: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  return (
    <EnhancedAppProvider>
      <TestPOSContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
    </EnhancedAppProvider>
  );
};

export default TestUnifiedPOS;
