import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  TrendingUp,
  TrendingDown,
  BarChart3,
  LineChart,
  Calendar,
  Clock,
  DollarSign,
  Users,
  ShoppingCart,
  Target,
  Zap,
  Brain,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Eye,
  Download,
  Settings
} from 'lucide-react';

interface SalesForecast {
  id: string;
  period: string;
  timeframe: 'hourly' | 'daily' | 'weekly' | 'monthly';
  predictedRevenue: number;
  actualRevenue?: number;
  confidence: number;
  trend: 'up' | 'down' | 'stable';
  factors: string[];
  accuracy?: number;
  timestamp: Date;
}

interface ForecastMetrics {
  totalPredicted: number;
  actualTotal: number;
  accuracy: number;
  variance: number;
  trend: 'up' | 'down' | 'stable';
  confidence: number;
}

export function Phase3BPredictiveSalesForecasting() {
  const [salesForecasts, setSalesForecasts] = useState<SalesForecast[]>([]);
  const [forecastMetrics, setForecastMetrics] = useState<ForecastMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState<'hourly' | 'daily' | 'weekly' | 'monthly'>('daily');

  const mockSalesForecasts: SalesForecast[] = [
    {
      id: 'forecast-1',
      period: 'Today 6-8 PM',
      timeframe: 'hourly',
      predictedRevenue: 2850.00,
      actualRevenue: 2920.00,
      confidence: 94.5,
      trend: 'up',
      factors: ['weather', 'events', 'historical'],
      accuracy: 97.6,
      timestamp: new Date(Date.now() - 3600000)
    },
    {
      id: 'forecast-2',
      period: 'Tomorrow',
      timeframe: 'daily',
      predictedRevenue: 8450.00,
      confidence: 89.2,
      trend: 'up',
      factors: ['weekend', 'weather', 'promotions'],
      timestamp: new Date()
    },
    {
      id: 'forecast-3',
      period: 'This Weekend',
      timeframe: 'daily',
      predictedRevenue: 18900.00,
      confidence: 91.8,
      trend: 'up',
      factors: ['weekend', 'events', 'seasonal'],
      timestamp: new Date()
    },
    {
      id: 'forecast-4',
      period: 'Next Week',
      timeframe: 'weekly',
      predictedRevenue: 52300.00,
      confidence: 87.4,
      trend: 'stable',
      factors: ['historical', 'seasonal', 'trends'],
      timestamp: new Date()
    },
    {
      id: 'forecast-5',
      period: 'Next Month',
      timeframe: 'monthly',
      predictedRevenue: 234500.00,
      confidence: 83.6,
      trend: 'up',
      factors: ['seasonal', 'growth', 'marketing'],
      timestamp: new Date()
    },
    {
      id: 'forecast-6',
      period: 'Monday Lunch',
      timeframe: 'hourly',
      predictedRevenue: 1250.00,
      confidence: 92.1,
      trend: 'down',
      factors: ['weekday', 'lunch_rush', 'historical'],
      timestamp: new Date()
    }
  ];

  const mockForecastMetrics: ForecastMetrics = {
    totalPredicted: 318250.00,
    actualTotal: 295680.00,
    accuracy: 92.9,
    variance: 7.1,
    trend: 'up',
    confidence: 89.7
  };

  useEffect(() => {
    setTimeout(() => {
      setSalesForecasts(mockSalesForecasts);
      setForecastMetrics(mockForecastMetrics);
      setLoading(false);
    }, 1000);
  }, []);

  const filteredForecasts = salesForecasts.filter(forecast => 
    selectedTimeframe === 'hourly' ? forecast.timeframe === 'hourly' :
    selectedTimeframe === 'daily' ? forecast.timeframe === 'daily' :
    selectedTimeframe === 'weekly' ? forecast.timeframe === 'weekly' :
    forecast.timeframe === 'monthly'
  );

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-500" />;
      case 'down': return <TrendingDown className="h-4 w-4 text-red-500" />;
      case 'stable': return <BarChart3 className="h-4 w-4 text-blue-500" />;
      default: return <BarChart3 className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600';
      case 'down': return 'text-red-600';
      case 'stable': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600';
    if (confidence >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getAccuracyBadge = (accuracy?: number) => {
    if (!accuracy) return null;
    if (accuracy >= 95) return <Badge className="bg-green-100 text-green-800">Excellent</Badge>;
    if (accuracy >= 90) return <Badge className="bg-blue-100 text-blue-800">Good</Badge>;
    if (accuracy >= 85) return <Badge className="bg-yellow-100 text-yellow-800">Fair</Badge>;
    return <Badge className="bg-red-100 text-red-800">Poor</Badge>;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Predictive Sales Forecasting</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3B Predictive Sales Forecasting</h2>
          <p className="text-gray-600">AI-powered revenue predictions and demand forecasting</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Retrain Model
          </Button>
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Forecasts
          </Button>
        </div>
      </div>

      {/* Timeframe Filters */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        {(['hourly', 'daily', 'weekly', 'monthly'] as const).map((timeframe) => (
          <Button
            key={timeframe}
            variant={selectedTimeframe === timeframe ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setSelectedTimeframe(timeframe)}
            className="capitalize"
          >
            {timeframe}
          </Button>
        ))}
      </div>

      {/* Forecast Overview Metrics */}
      {forecastMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Predicted Revenue</p>
                  <p className="text-2xl font-bold text-blue-600">
                    ${forecastMetrics.totalPredicted.toLocaleString()}
                  </p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Forecast Accuracy</p>
                  <p className="text-2xl font-bold text-green-600">
                    {forecastMetrics.accuracy}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Avg Confidence</p>
                  <p className="text-2xl font-bold text-purple-600">
                    {forecastMetrics.confidence}%
                  </p>
                </div>
                <Brain className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Trend Direction</p>
                  <div className={`text-2xl font-bold ${getTrendColor(forecastMetrics.trend)}`}>
                    {getTrendIcon(forecastMetrics.trend)}
                    <span className="ml-2 capitalize">{forecastMetrics.trend}</span>
                  </div>
                </div>
                <BarChart3 className="h-8 w-8 text-gray-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Sales Forecasts */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">
          {selectedTimeframe.charAt(0).toUpperCase() + selectedTimeframe.slice(1)} Forecasts
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredForecasts.map((forecast) => (
            <Card key={forecast.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{forecast.period}</CardTitle>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline" className="capitalize">
                        {forecast.timeframe}
                      </Badge>
                      {forecast.accuracy && getAccuracyBadge(forecast.accuracy)}
                    </div>
                  </div>
                  <div className="flex items-center space-x-1">
                    {getTrendIcon(forecast.trend)}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <p className="text-sm text-gray-600">Predicted Revenue</p>
                    <p className="text-2xl font-bold text-blue-600">
                      ${forecast.predictedRevenue.toLocaleString()}
                    </p>
                  </div>

                  {forecast.actualRevenue && (
                    <div>
                      <p className="text-sm text-gray-600">Actual Revenue</p>
                      <p className="text-xl font-semibold text-green-600">
                        ${forecast.actualRevenue.toLocaleString()}
                      </p>
                      <div className="flex items-center space-x-2 mt-1">
                        <span className="text-xs text-gray-500">Variance:</span>
                        <span className={`text-xs font-medium ${
                          forecast.actualRevenue > forecast.predictedRevenue ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {((forecast.actualRevenue - forecast.predictedRevenue) / forecast.predictedRevenue * 100).toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )}

                  <div>
                    <p className="text-sm text-gray-600">Confidence Level</p>
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full transition-all duration-300 ${
                            forecast.confidence >= 90 ? 'bg-green-500' : 
                            forecast.confidence >= 80 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${forecast.confidence}%` }}
                        ></div>
                      </div>
                      <span className={`text-sm font-medium ${getConfidenceColor(forecast.confidence)}`}>
                        {forecast.confidence}%
                      </span>
                    </div>
                  </div>

                  <div>
                    <p className="text-sm text-gray-600 mb-2">Key Factors</p>
                    <div className="flex flex-wrap gap-1">
                      {forecast.factors.map((factor, index) => (
                        <Badge key={index} variant="outline" className="text-xs">
                          {factor.replace('_', ' ')}
                        </Badge>
                      ))}
                    </div>
                  </div>

                  <div className="flex space-x-2 pt-2">
                    <Button size="sm" variant="outline" className="flex-1">
                      <Eye className="h-3 w-3 mr-1" />
                      Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Forecast Accuracy Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <LineChart className="h-5 w-5 mr-2" />
            Forecast Accuracy Trends
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-12">
            <BarChart3 className="h-16 w-16 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Advanced Forecasting Analytics</h3>
            <p className="text-gray-600 mb-4">
              Detailed accuracy trends, model performance metrics, and forecasting insights will be displayed here.
            </p>
            <Button>
              <TrendingUp className="h-4 w-4 mr-2" />
              View Analytics
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
