// Enterprise Integration APIs for RestroFlow POS
// Supports ERP systems, accounting software, payment processors, and third-party services

interface IntegrationConfig {
  id: string;
  name: string;
  type: 'erp' | 'accounting' | 'payment' | 'inventory' | 'crm' | 'analytics' | 'delivery';
  enabled: boolean;
  credentials: Record<string, string>;
  settings: Record<string, any>;
  webhookUrl?: string;
  apiVersion?: string;
  lastSync?: string;
  syncInterval?: number; // minutes
}

interface SyncResult {
  success: boolean;
  recordsProcessed: number;
  errors: string[];
  timestamp: string;
  duration: number;
}

interface WebhookEvent {
  id: string;
  type: string;
  source: string;
  timestamp: string;
  data: any;
  signature?: string;
}

class EnterpriseIntegrationManager {
  private integrations: Map<string, IntegrationConfig> = new Map();
  private syncQueue: Map<string, any[]> = new Map();
  private webhookHandlers: Map<string, Function> = new Map();
  private rateLimiters: Map<string, { requests: number; resetTime: number }> = new Map();

  constructor() {
    this.initializeIntegrations();
    this.startSyncScheduler();
  }

  // Initialize supported integrations
  private initializeIntegrations(): void {
    console.log('🔗 Initializing enterprise integrations...');

    // QuickBooks Integration
    this.registerIntegration({
      id: 'quickbooks',
      name: 'QuickBooks Online',
      type: 'accounting',
      enabled: false,
      credentials: {
        clientId: '',
        clientSecret: '',
        accessToken: '',
        refreshToken: '',
        realmId: ''
      },
      settings: {
        syncCustomers: true,
        syncProducts: true,
        syncSales: true,
        syncExpenses: false,
        autoCreateCustomers: true
      },
      apiVersion: 'v3',
      syncInterval: 60 // 1 hour
    });

    // SAP Integration
    this.registerIntegration({
      id: 'sap',
      name: 'SAP Business One',
      type: 'erp',
      enabled: false,
      credentials: {
        serverUrl: '',
        database: '',
        username: '',
        password: '',
        companyDB: ''
      },
      settings: {
        syncInventory: true,
        syncCustomers: true,
        syncOrders: true,
        syncFinancials: true,
        realTimeSync: false
      },
      apiVersion: '10.0',
      syncInterval: 30
    });

    // Stripe Payment Integration
    this.registerIntegration({
      id: 'stripe',
      name: 'Stripe Payments',
      type: 'payment',
      enabled: false,
      credentials: {
        publishableKey: '',
        secretKey: '',
        webhookSecret: ''
      },
      settings: {
        captureMethod: 'automatic',
        currency: 'USD',
        enableApplePay: true,
        enableGooglePay: true,
        savePaymentMethods: true
      },
      webhookUrl: '/webhooks/stripe',
      apiVersion: '2023-10-16'
    });

    // Square Integration
    this.registerIntegration({
      id: 'square',
      name: 'Square Payments',
      type: 'payment',
      enabled: false,
      credentials: {
        applicationId: '',
        accessToken: '',
        locationId: '',
        webhookSignatureKey: ''
      },
      settings: {
        environment: 'sandbox',
        autocomplete: true,
        enableDigitalWallets: true,
        enableGiftCards: false
      },
      webhookUrl: '/webhooks/square',
      apiVersion: '2023-12-13'
    });

    // Salesforce CRM Integration
    this.registerIntegration({
      id: 'salesforce',
      name: 'Salesforce CRM',
      type: 'crm',
      enabled: false,
      credentials: {
        clientId: '',
        clientSecret: '',
        username: '',
        password: '',
        securityToken: '',
        instanceUrl: ''
      },
      settings: {
        syncContacts: true,
        syncAccounts: true,
        syncOpportunities: false,
        createLeads: true,
        trackCustomerJourney: true
      },
      apiVersion: 'v59.0',
      syncInterval: 120
    });

    // Xero Accounting Integration
    this.registerIntegration({
      id: 'xero',
      name: 'Xero Accounting',
      type: 'accounting',
      enabled: false,
      credentials: {
        clientId: '',
        clientSecret: '',
        accessToken: '',
        refreshToken: '',
        tenantId: ''
      },
      settings: {
        syncInvoices: true,
        syncPayments: true,
        syncContacts: true,
        syncItems: true,
        autoReconcile: false
      },
      apiVersion: '2.0',
      syncInterval: 90
    });

    // DoorDash Integration
    this.registerIntegration({
      id: 'doordash',
      name: 'DoorDash Delivery',
      type: 'delivery',
      enabled: false,
      credentials: {
        developerId: '',
        keyId: '',
        signingSecret: '',
        storeId: ''
      },
      settings: {
        autoAcceptOrders: false,
        preparationTime: 15,
        enableMenuSync: true,
        enableInventorySync: false
      },
      webhookUrl: '/webhooks/doordash',
      apiVersion: 'v1'
    });

    // Uber Eats Integration
    this.registerIntegration({
      id: 'ubereats',
      name: 'Uber Eats',
      type: 'delivery',
      enabled: false,
      credentials: {
        clientId: '',
        clientSecret: '',
        storeId: '',
        webhookSecret: ''
      },
      settings: {
        autoAcceptOrders: false,
        preparationTime: 20,
        enableMenuSync: true,
        pauseThreshold: 30
      },
      webhookUrl: '/webhooks/ubereats',
      apiVersion: 'v1'
    });

    console.log(`✅ Initialized ${this.integrations.size} enterprise integrations`);
  }

  // Register a new integration
  public registerIntegration(config: IntegrationConfig): void {
    this.integrations.set(config.id, config);
    
    if (config.webhookUrl) {
      this.setupWebhookHandler(config.id, config.webhookUrl);
    }
  }

  // Enable/disable integration
  public async toggleIntegration(integrationId: string, enabled: boolean): Promise<boolean> {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      throw new Error(`Integration ${integrationId} not found`);
    }

    integration.enabled = enabled;
    
    if (enabled) {
      // Test connection when enabling
      const testResult = await this.testConnection(integrationId);
      if (!testResult.success) {
        integration.enabled = false;
        throw new Error(`Failed to enable ${integrationId}: ${testResult.error}`);
      }
      
      // Start initial sync
      await this.syncIntegration(integrationId);
    }

    // Save configuration
    await this.saveIntegrationConfig(integrationId, integration);
    
    console.log(`${enabled ? '✅ Enabled' : '❌ Disabled'} integration: ${integration.name}`);
    return true;
  }

  // Test integration connection
  public async testConnection(integrationId: string): Promise<{ success: boolean; error?: string }> {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      return { success: false, error: 'Integration not found' };
    }

    try {
      switch (integration.type) {
        case 'accounting':
          return await this.testAccountingConnection(integration);
        case 'payment':
          return await this.testPaymentConnection(integration);
        case 'erp':
          return await this.testERPConnection(integration);
        case 'crm':
          return await this.testCRMConnection(integration);
        case 'delivery':
          return await this.testDeliveryConnection(integration);
        default:
          return { success: false, error: 'Unsupported integration type' };
      }
    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }

  // Sync data with integration
  public async syncIntegration(integrationId: string, force: boolean = false): Promise<SyncResult> {
    const integration = this.integrations.get(integrationId);
    if (!integration || !integration.enabled) {
      throw new Error(`Integration ${integrationId} not available`);
    }

    const startTime = Date.now();
    let recordsProcessed = 0;
    const errors: string[] = [];

    try {
      console.log(`🔄 Starting sync for ${integration.name}...`);

      // Check rate limits
      if (!this.checkRateLimit(integrationId)) {
        throw new Error('Rate limit exceeded');
      }

      switch (integration.type) {
        case 'accounting':
          recordsProcessed = await this.syncAccountingData(integration);
          break;
        case 'payment':
          recordsProcessed = await this.syncPaymentData(integration);
          break;
        case 'erp':
          recordsProcessed = await this.syncERPData(integration);
          break;
        case 'crm':
          recordsProcessed = await this.syncCRMData(integration);
          break;
        case 'delivery':
          recordsProcessed = await this.syncDeliveryData(integration);
          break;
      }

      integration.lastSync = new Date().toISOString();
      await this.saveIntegrationConfig(integrationId, integration);

      const result: SyncResult = {
        success: true,
        recordsProcessed,
        errors,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime
      };

      console.log(`✅ Sync completed for ${integration.name}: ${recordsProcessed} records`);
      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      errors.push(errorMessage);

      console.error(`❌ Sync failed for ${integration.name}:`, errorMessage);

      return {
        success: false,
        recordsProcessed,
        errors,
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime
      };
    }
  }

  // Process webhook events
  public async processWebhook(integrationId: string, event: WebhookEvent): Promise<void> {
    const integration = this.integrations.get(integrationId);
    if (!integration || !integration.enabled) {
      throw new Error(`Integration ${integrationId} not available`);
    }

    console.log(`📨 Processing webhook for ${integration.name}: ${event.type}`);

    try {
      // Verify webhook signature if available
      if (event.signature && integration.credentials.webhookSecret) {
        const isValid = await this.verifyWebhookSignature(
          event,
          integration.credentials.webhookSecret
        );
        if (!isValid) {
          throw new Error('Invalid webhook signature');
        }
      }

      // Process based on integration type
      switch (integration.id) {
        case 'stripe':
          await this.processStripeWebhook(event);
          break;
        case 'square':
          await this.processSquareWebhook(event);
          break;
        case 'doordash':
          await this.processDoorDashWebhook(event);
          break;
        case 'ubereats':
          await this.processUberEatsWebhook(event);
          break;
        default:
          console.warn(`No webhook handler for ${integrationId}`);
      }

      console.log(`✅ Webhook processed successfully for ${integration.name}`);
    } catch (error) {
      console.error(`❌ Webhook processing failed for ${integration.name}:`, error);
      throw error;
    }
  }

  // Get integration status
  public getIntegrationStatus(integrationId: string): any {
    const integration = this.integrations.get(integrationId);
    if (!integration) {
      return null;
    }

    return {
      id: integration.id,
      name: integration.name,
      type: integration.type,
      enabled: integration.enabled,
      lastSync: integration.lastSync,
      syncInterval: integration.syncInterval,
      status: integration.enabled ? 'active' : 'inactive',
      queueSize: this.syncQueue.get(integrationId)?.length || 0
    };
  }

  // Get all integrations status
  public getAllIntegrationsStatus(): any[] {
    return Array.from(this.integrations.keys()).map(id => this.getIntegrationStatus(id));
  }

  // Private helper methods
  private async testAccountingConnection(integration: IntegrationConfig): Promise<{ success: boolean; error?: string }> {
    // Implementation would test specific accounting system connection
    return { success: true };
  }

  private async testPaymentConnection(integration: IntegrationConfig): Promise<{ success: boolean; error?: string }> {
    // Implementation would test payment processor connection
    return { success: true };
  }

  private async testERPConnection(integration: IntegrationConfig): Promise<{ success: boolean; error?: string }> {
    // Implementation would test ERP system connection
    return { success: true };
  }

  private async testCRMConnection(integration: IntegrationConfig): Promise<{ success: boolean; error?: string }> {
    // Implementation would test CRM system connection
    return { success: true };
  }

  private async testDeliveryConnection(integration: IntegrationConfig): Promise<{ success: boolean; error?: string }> {
    // Implementation would test delivery platform connection
    return { success: true };
  }

  private async syncAccountingData(integration: IntegrationConfig): Promise<number> {
    // Implementation would sync accounting data
    return 0;
  }

  private async syncPaymentData(integration: IntegrationConfig): Promise<number> {
    // Implementation would sync payment data
    return 0;
  }

  private async syncERPData(integration: IntegrationConfig): Promise<number> {
    // Implementation would sync ERP data
    return 0;
  }

  private async syncCRMData(integration: IntegrationConfig): Promise<number> {
    // Implementation would sync CRM data
    return 0;
  }

  private async syncDeliveryData(integration: IntegrationConfig): Promise<number> {
    // Implementation would sync delivery platform data
    return 0;
  }

  private setupWebhookHandler(integrationId: string, webhookUrl: string): void {
    // Implementation would setup webhook endpoint
    console.log(`🔗 Setup webhook handler for ${integrationId}: ${webhookUrl}`);
  }

  private async verifyWebhookSignature(event: WebhookEvent, secret: string): Promise<boolean> {
    // Implementation would verify webhook signature
    return true;
  }

  private async processStripeWebhook(event: WebhookEvent): Promise<void> {
    // Implementation would process Stripe webhook events
    console.log('Processing Stripe webhook:', event.type);
  }

  private async processSquareWebhook(event: WebhookEvent): Promise<void> {
    // Implementation would process Square webhook events
    console.log('Processing Square webhook:', event.type);
  }

  private async processDoorDashWebhook(event: WebhookEvent): Promise<void> {
    // Implementation would process DoorDash webhook events
    console.log('Processing DoorDash webhook:', event.type);
  }

  private async processUberEatsWebhook(event: WebhookEvent): Promise<void> {
    // Implementation would process Uber Eats webhook events
    console.log('Processing Uber Eats webhook:', event.type);
  }

  private checkRateLimit(integrationId: string): boolean {
    const now = Date.now();
    const limiter = this.rateLimiters.get(integrationId);
    
    if (!limiter || now > limiter.resetTime) {
      this.rateLimiters.set(integrationId, {
        requests: 1,
        resetTime: now + 60000 // 1 minute
      });
      return true;
    }
    
    if (limiter.requests >= 100) { // 100 requests per minute
      return false;
    }
    
    limiter.requests++;
    return true;
  }

  private async saveIntegrationConfig(integrationId: string, config: IntegrationConfig): Promise<void> {
    try {
      await fetch('http://localhost:4000/api/integrations/config', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          integrationId,
          config
        })
      });
    } catch (error) {
      console.error('Error saving integration config:', error);
    }
  }

  private startSyncScheduler(): void {
    setInterval(() => {
      this.integrations.forEach(async (integration, id) => {
        if (!integration.enabled || !integration.syncInterval) return;
        
        const lastSync = integration.lastSync ? new Date(integration.lastSync) : new Date(0);
        const nextSync = new Date(lastSync.getTime() + integration.syncInterval * 60000);
        
        if (Date.now() >= nextSync.getTime()) {
          try {
            await this.syncIntegration(id);
          } catch (error) {
            console.error(`Scheduled sync failed for ${integration.name}:`, error);
          }
        }
      });
    }, 60000); // Check every minute
  }
}

// Export singleton instance
export const enterpriseIntegrations = new EnterpriseIntegrationManager();

// Export types
export type { IntegrationConfig, SyncResult, WebhookEvent };

// Utility functions
export const getIntegrationStatus = (id: string) => enterpriseIntegrations.getIntegrationStatus(id);
export const getAllIntegrationsStatus = () => enterpriseIntegrations.getAllIntegrationsStatus();
export const toggleIntegration = (id: string, enabled: boolean) => enterpriseIntegrations.toggleIntegration(id, enabled);
export const syncIntegration = (id: string) => enterpriseIntegrations.syncIntegration(id);
export const testConnection = (id: string) => enterpriseIntegrations.testConnection(id);
