#!/usr/bin/env node

/**
 * RESTROFLOW CLEAN BACKEND SERVER
 * 
 * Optimized, lightweight backend server focused on core POS functionality
 * without the memory-intensive features that were causing system overload.
 */

const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

const app = express();
const PORT = process.env.PORT || 4000;
const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret-key-2024';

// Middleware
app.use(cors({
    origin: ['http://localhost:5173', 'http://localhost:3000'],
    credentials: true
}));
app.use(express.json());
app.use(express.static('public'));

// Request logging middleware
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.path} - ${req.ip}`);
    next();
});

// In-memory data stores (for demo purposes)
let products = [
    { id: 1, name: 'Caesar Salad', price: 12.99, category: 'appetizers', stock: 50, image: 'https://via.placeholder.com/200x120/28a745/ffffff?text=Caesar+Salad' },
    { id: 2, name: 'Grilled Chicken', price: 18.99, category: 'mains', stock: 30, image: 'https://via.placeholder.com/200x120/007bff/ffffff?text=Grilled+Chicken' },
    { id: 3, name: 'Chocolate Cake', price: 8.99, category: 'desserts', stock: 20, image: 'https://via.placeholder.com/200x120/6f42c1/ffffff?text=Chocolate+Cake' },
    { id: 4, name: 'Coffee', price: 3.99, category: 'beverages', stock: 100, image: 'https://via.placeholder.com/200x120/fd7e14/ffffff?text=Coffee' },
    { id: 5, name: 'Margherita Pizza', price: 16.99, category: 'mains', stock: 25, image: 'https://via.placeholder.com/200x120/dc3545/ffffff?text=Pizza' },
    { id: 6, name: 'Garlic Bread', price: 6.99, category: 'appetizers', stock: 40, image: 'https://via.placeholder.com/200x120/ffc107/ffffff?text=Garlic+Bread' },
    { id: 7, name: 'Beef Burger', price: 14.99, category: 'mains', stock: 35, image: 'https://via.placeholder.com/200x120/17a2b8/ffffff?text=Beef+Burger' },
    { id: 8, name: 'Ice Cream', price: 5.99, category: 'desserts', stock: 60, image: 'https://via.placeholder.com/200x120/e83e8c/ffffff?text=Ice+Cream' },
    { id: 9, name: 'Orange Juice', price: 4.99, category: 'beverages', stock: 80, image: 'https://via.placeholder.com/200x120/fd7e14/ffffff?text=Orange+Juice' },
    { id: 10, name: 'Fish & Chips', price: 19.99, category: 'mains', stock: 20, image: 'https://via.placeholder.com/200x120/20c997/ffffff?text=Fish+Chips' }
];

let orders = [];
let employees = [
    { id: 1, name: 'Super Administrator', pin: '123456', role: 'super_admin', tenant: 'Demo Restaurant' },
    { id: 2, name: 'Manager', pin: '567890', role: 'manager', tenant: 'Demo Restaurant' },
    { id: 3, name: 'Employee 1', pin: '111222', role: 'employee', tenant: 'Demo Restaurant' },
    { id: 4, name: 'Employee 2', pin: '555666', role: 'employee', tenant: 'Demo Restaurant' }
];

let categories = [
    { id: 1, name: 'Appetizers', slug: 'appetizers' },
    { id: 2, name: 'Main Course', slug: 'mains' },
    { id: 3, name: 'Desserts', slug: 'desserts' },
    { id: 4, name: 'Beverages', slug: 'beverages' }
];

let dailyStats = {
    ordersToday: 0,
    revenueToday: 0,
    customersToday: 0,
    lastReset: new Date().toDateString()
};

// Authentication middleware
function authenticateToken(req, res, next) {
    const authHeader = req.headers['authorization'];
    const token = authHeader && authHeader.split(' ')[1];

    if (!token) {
        return res.status(401).json({ error: 'Access token required' });
    }

    jwt.verify(token, JWT_SECRET, (err, user) => {
        if (err) {
            return res.status(403).json({ error: 'Invalid or expired token' });
        }
        req.user = user;
        next();
    });
}

// Reset daily stats if it's a new day
function checkDailyReset() {
    const today = new Date().toDateString();
    if (dailyStats.lastReset !== today) {
        dailyStats = {
            ordersToday: 0,
            revenueToday: 0,
            customersToday: 0,
            lastReset: today
        };
    }
}

// Routes

// Health check
app.get('/api/health', (req, res) => {
    res.json({
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: '1.0.0'
    });
});

// Authentication
app.post('/api/auth/login', async (req, res) => {
    try {
        const { pin, tenant, admin_access, isAdmin } = req.body;
        
        console.log(`🔐 Login attempt - PIN: ${pin ? pin.length : 0} digits, Tenant: ${tenant || 'auto-detect'}, Admin: ${admin_access || isAdmin || false}`);

        if (!pin) {
            return res.status(400).json({ error: 'PIN is required' });
        }

        // Find employee by PIN
        const employee = employees.find(emp => emp.pin === pin);
        
        if (!employee) {
            console.log('❌ Invalid PIN provided');
            return res.status(401).json({ error: 'Invalid PIN' });
        }

        console.log(`✅ Login successful - ${employee.name} (${employee.role}) at ${employee.tenant}`);

        // Generate JWT token
        const token = jwt.sign(
            {
                employeeId: employee.id,
                name: employee.name,
                role: employee.role,
                tenant: employee.tenant
            },
            JWT_SECRET,
            { expiresIn: '8h' }
        );

        res.json({
            token,
            user: {
                id: employee.id,
                name: employee.name,
                role: employee.role,
                tenant: employee.tenant
            }
        });

    } catch (error) {
        console.error('❌ Login error:', error);
        res.status(500).json({ error: 'Internal server error' });
    }
});

// Products
app.get('/api/products', authenticateToken, (req, res) => {
    const { category } = req.query;
    let filteredProducts = products;
    
    if (category && category !== 'all') {
        filteredProducts = products.filter(p => p.category === category);
    }
    
    res.json(filteredProducts);
});

app.post('/api/products', authenticateToken, (req, res) => {
    if (req.user.role !== 'super_admin' && req.user.role !== 'manager') {
        return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    const { name, price, category, stock } = req.body;
    const newProduct = {
        id: products.length + 1,
        name,
        price: parseFloat(price),
        category,
        stock: parseInt(stock) || 0,
        image: `https://via.placeholder.com/200x120/007bff/ffffff?text=${encodeURIComponent(name)}`
    };
    
    products.push(newProduct);
    res.status(201).json(newProduct);
});

// Categories
app.get('/api/categories', authenticateToken, (req, res) => {
    res.json(categories);
});

// Orders
app.get('/api/orders', authenticateToken, (req, res) => {
    checkDailyReset();
    res.json(orders);
});

app.post('/api/orders', authenticateToken, (req, res) => {
    try {
        checkDailyReset();
        
        const { items, subtotal, tax, total, table, notes } = req.body;
        
        const newOrder = {
            id: orders.length + 1,
            items,
            subtotal: parseFloat(subtotal),
            tax: parseFloat(tax),
            total: parseFloat(total),
            table: table || '1',
            notes: notes || '',
            status: 'pending',
            timestamp: new Date().toISOString(),
            employee: req.user.name,
            employeeId: req.user.employeeId
        };
        
        orders.push(newOrder);
        
        // Update daily stats
        dailyStats.ordersToday += 1;
        dailyStats.revenueToday += newOrder.total;
        dailyStats.customersToday += 1;
        
        console.log(`📋 New order created: #${newOrder.id} - $${newOrder.total.toFixed(2)} by ${req.user.name}`);
        
        res.status(201).json(newOrder);
        
    } catch (error) {
        console.error('❌ Error creating order:', error);
        res.status(500).json({ error: 'Failed to create order' });
    }
});

// Update order status
app.patch('/api/orders/:id', authenticateToken, (req, res) => {
    const orderId = parseInt(req.params.id);
    const { status } = req.body;
    
    const order = orders.find(o => o.id === orderId);
    if (!order) {
        return res.status(404).json({ error: 'Order not found' });
    }
    
    order.status = status;
    order.updatedAt = new Date().toISOString();
    
    res.json(order);
});

// Analytics
app.get('/api/analytics/sales', authenticateToken, (req, res) => {
    checkDailyReset();
    
    const analytics = {
        ordersToday: dailyStats.ordersToday,
        revenueToday: dailyStats.revenueToday,
        customersToday: dailyStats.customersToday,
        averageOrderValue: dailyStats.ordersToday > 0 ? dailyStats.revenueToday / dailyStats.ordersToday : 0,
        topSellingItems: getTopSellingItems(),
        hourlyBreakdown: getHourlyBreakdown()
    };
    
    res.json(analytics);
});

app.get('/api/analytics/customers', authenticateToken, (req, res) => {
    checkDailyReset();
    
    res.json({
        totalCustomers: dailyStats.customersToday,
        newCustomers: Math.floor(dailyStats.customersToday * 0.3),
        returningCustomers: Math.floor(dailyStats.customersToday * 0.7),
        averageSpend: dailyStats.customersToday > 0 ? dailyStats.revenueToday / dailyStats.customersToday : 0
    });
});

// Employees
app.get('/api/employees', authenticateToken, (req, res) => {
    if (req.user.role !== 'super_admin' && req.user.role !== 'manager') {
        return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    const safeEmployees = employees.map(emp => ({
        id: emp.id,
        name: emp.name,
        role: emp.role,
        tenant: emp.tenant
        // PIN is excluded for security
    }));
    
    res.json(safeEmployees);
});

// Inventory
app.get('/api/inventory', authenticateToken, (req, res) => {
    const inventory = products.map(product => ({
        id: product.id,
        name: product.name,
        stock: product.stock,
        category: product.category,
        lowStock: product.stock < 10
    }));
    
    res.json(inventory);
});

// Kitchen orders
app.get('/api/kitchen/orders', authenticateToken, (req, res) => {
    const kitchenOrders = orders
        .filter(order => order.status === 'pending' || order.status === 'preparing')
        .map(order => ({
            id: order.id,
            items: order.items,
            table: order.table,
            status: order.status,
            timestamp: order.timestamp,
            notes: order.notes
        }));
    
    res.json(kitchenOrders);
});

// Helper functions
function getTopSellingItems() {
    const itemCounts = {};
    
    orders.forEach(order => {
        order.items.forEach(item => {
            itemCounts[item.name] = (itemCounts[item.name] || 0) + item.quantity;
        });
    });
    
    return Object.entries(itemCounts)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5)
        .map(([name, count]) => ({ name, count }));
}

function getHourlyBreakdown() {
    const hourlyData = Array(24).fill(0);
    
    orders.forEach(order => {
        const hour = new Date(order.timestamp).getHours();
        hourlyData[hour] += order.total;
    });
    
    return hourlyData;
}

// Error handling middleware
app.use((error, req, res, next) => {
    console.error('❌ Server error:', error);
    res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
    res.status(404).json({ error: 'Endpoint not found' });
});

// Start server
app.listen(PORT, () => {
    console.log('\n🚀 RESTROFLOW CLEAN BACKEND SERVER STARTED!');
    console.log('==================================================');
    console.log(`📡 Server running on: http://localhost:${PORT}`);
    console.log(`🕐 Started at: ${new Date().toISOString()}`);
    console.log('\n📊 Available API Endpoints:');
    console.log('  ✅ GET  /api/health');
    console.log('  🔐 POST /api/auth/login');
    console.log('  📦 GET  /api/products (auth required)');
    console.log('  📦 POST /api/products (auth required)');
    console.log('  📂 GET  /api/categories (auth required)');
    console.log('  🛒 GET  /api/orders (auth required)');
    console.log('  🛒 POST /api/orders (auth required)');
    console.log('  🛒 PATCH /api/orders/:id (auth required)');
    console.log('  📊 GET  /api/analytics/sales (auth required)');
    console.log('  📊 GET  /api/analytics/customers (auth required)');
    console.log('  👥 GET  /api/employees (auth required)');
    console.log('  📦 GET  /api/inventory (auth required)');
    console.log('  🍳 GET  /api/kitchen/orders (auth required)');
    console.log('\n🔑 Test Credentials:');
    console.log('  👑 Super Admin: PIN 123456');
    console.log('  👨‍💼 Manager: PIN 567890');
    console.log('  👤 Employee: PIN 111222, 555666');
    console.log('\n🌐 Frontend URLs:');
    console.log('  📱 Clean POS: http://localhost:5173/clean-pos-system.html');
    console.log('  👑 Super Admin: http://localhost:5173/project/super-admin.html');
    console.log('==================================================\n');
});

// Graceful shutdown
process.on('SIGINT', () => {
    console.log('\n🛑 Shutting down RESTROFLOW Clean Backend Server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n🛑 Shutting down RESTROFLOW Clean Backend Server...');
    process.exit(0);
});

module.exports = app;
