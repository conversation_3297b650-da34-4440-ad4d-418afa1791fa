<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTROFLOW - Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100">
    <!-- Navigation -->
    <nav class="bg-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <span class="text-2xl font-bold text-blue-600">🍽️ RESTROFLOW</span>
                </div>
                <div class="flex items-center space-x-4">
                    <span id="userInfo" class="text-gray-700"></span>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 text-white px-4 py-2 rounded-lg">
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto py-6 px-4">
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Welcome to RESTROFLOW</h1>
            <p class="text-gray-600">Select your interface to continue</p>
        </div>

        <!-- Interface Options -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <!-- Super Admin -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">👑</div>
                    <h3 class="text-xl font-semibold mb-2">Super Admin</h3>
                    <p class="text-gray-600 mb-4">Complete system administration and security monitoring</p>
                    <button onclick="accessInterface('/project/super-admin.html')" 
                            class="bg-red-600 hover:bg-red-700 text-white px-6 py-2 rounded-lg w-full">
                        Access Security Center
                    </button>
                </div>
            </div>

            <!-- POS System -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">🏪</div>
                    <h3 class="text-xl font-semibold mb-2">POS System</h3>
                    <p class="text-gray-600 mb-4">Restaurant point of sale operations</p>
                    <button onclick="accessInterface('/project/index.html')" 
                            class="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg w-full">
                        Access POS
                    </button>
                </div>
            </div>

            <!-- Original Interfaces -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">🔄</div>
                    <h3 class="text-xl font-semibold mb-2">Original Interfaces</h3>
                    <p class="text-gray-600 mb-4">Advanced features and industry-specific interfaces</p>
                    <button onclick="accessInterface('/project/index.html?mode=original')" 
                            class="bg-purple-600 hover:bg-purple-700 text-white px-6 py-2 rounded-lg w-full">
                        Access Advanced
                    </button>
                </div>
            </div>

            <!-- Debug Mode -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">🔍</div>
                    <h3 class="text-xl font-semibold mb-2">Debug Mode</h3>
                    <p class="text-gray-600 mb-4">System diagnostics and troubleshooting</p>
                    <button onclick="accessInterface('/project/index.html?mode=debug')" 
                            class="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg w-full">
                        Access Debug
                    </button>
                </div>
            </div>

            <!-- API Documentation -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">📚</div>
                    <h3 class="text-xl font-semibold mb-2">API Documentation</h3>
                    <p class="text-gray-600 mb-4">Backend API endpoints and testing</p>
                    <button onclick="window.open('http://localhost:4000/api/health', '_blank')" 
                            class="bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg w-full">
                        View API
                    </button>
                </div>
            </div>

            <!-- System Status -->
            <div class="bg-white rounded-lg shadow-md p-6 hover:shadow-lg transition-shadow">
                <div class="text-center">
                    <div class="text-4xl mb-4">📊</div>
                    <h3 class="text-xl font-semibold mb-2">System Status</h3>
                    <p class="text-gray-600 mb-4">Real-time system health monitoring</p>
                    <div id="systemStatus" class="text-sm text-gray-500 mb-4">
                        <div>Backend: <span id="backendStatus" class="text-green-600">Connected</span></div>
                        <div>Frontend: <span class="text-green-600">Online</span></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">Quick Actions</h2>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <button onclick="testBackend()" class="bg-blue-100 hover:bg-blue-200 text-blue-800 px-4 py-2 rounded-lg">
                    Test Backend
                </button>
                <button onclick="viewLogs()" class="bg-yellow-100 hover:bg-yellow-200 text-yellow-800 px-4 py-2 rounded-lg">
                    View Logs
                </button>
                <button onclick="clearCache()" class="bg-red-100 hover:bg-red-200 text-red-800 px-4 py-2 rounded-lg">
                    Clear Cache
                </button>
                <button onclick="refreshStatus()" class="bg-green-100 hover:bg-green-200 text-green-800 px-4 py-2 rounded-lg">
                    Refresh Status
                </button>
            </div>
        </div>
    </div>

    <script>
        // Check authentication on load
        window.addEventListener('load', function() {
            checkAuthentication();
            loadUserInfo();
            checkBackendStatus();
        });

        function checkAuthentication() {
            const token = localStorage.getItem('authToken');
            const user = localStorage.getItem('user');
            
            if (!token || !user) {
                window.location.href = '/login.html';
                return false;
            }
            
            return true;
        }

        function loadUserInfo() {
            const user = localStorage.getItem('user');
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    document.getElementById('userInfo').textContent = 
                        `Welcome, ${userData.name || userData.role || 'User'} (${userData.pin || 'N/A'})`;
                } catch (error) {
                    document.getElementById('userInfo').textContent = 'Welcome, User';
                }
            }
        }

        function accessInterface(url) {
            window.location.href = url;
        }

        function logout() {
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            localStorage.removeItem('loginTime');
            window.location.href = '/login.html';
        }

        async function checkBackendStatus() {
            try {
                const response = await fetch('http://localhost:4000/api/health');
                if (response.ok) {
                    document.getElementById('backendStatus').textContent = 'Connected';
                    document.getElementById('backendStatus').className = 'text-green-600';
                } else {
                    document.getElementById('backendStatus').textContent = 'Limited';
                    document.getElementById('backendStatus').className = 'text-yellow-600';
                }
            } catch (error) {
                document.getElementById('backendStatus').textContent = 'Offline';
                document.getElementById('backendStatus').className = 'text-red-600';
            }
        }

        async function testBackend() {
            try {
                const response = await fetch('http://localhost:4000/api/health');
                const data = await response.json();
                alert(`Backend Test Result:\nStatus: ${response.status}\nResponse: ${JSON.stringify(data, null, 2)}`);
            } catch (error) {
                alert(`Backend Test Failed:\n${error.message}`);
            }
        }

        function viewLogs() {
            alert('Logs feature would open system logs here');
        }

        function clearCache() {
            if (confirm('Clear all cached data? This will log you out.')) {
                localStorage.clear();
                window.location.href = '/login.html';
            }
        }

        function refreshStatus() {
            checkBackendStatus();
            alert('Status refreshed!');
        }

        // Event listeners
        document.getElementById('logoutBtn').addEventListener('click', logout);
    </script>
</body>
</html>
