import React, { useState, useEffect } from 'react';
import { sessionManager } from '../utils/SessionManager';
import TenantEmployeeLogin from './TenantEmployeeLogin';
import EnhancedSuperAdminLogin from './EnhancedSuperAdminLogin';
import TenantProfileManager from './TenantProfileManager';
import { 
  Building2, 
  Shield, 
  Users, 
  Settings,
  LogOut,
  RefreshCw,
  CheckCircle,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface UnifiedAuthenticationSystemProps {
  onAuthenticationSuccess: (userType: 'employee' | 'admin') => void;
  defaultMode?: 'employee' | 'admin';
}

const UnifiedAuthenticationSystem: React.FC<UnifiedAuthenticationSystemProps> = ({
  onAuthenticationSuccess,
  defaultMode = 'employee'
}) => {
  const [authMode, setAuthMode] = useState<'employee' | 'admin'>(defaultMode);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTenantSlug, setSelectedTenantSlug] = useState('');
  const [sessionStatus, setSessionStatus] = useState<'checking' | 'valid' | 'invalid'>('checking');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    checkExistingSession();
  }, []);

  const checkExistingSession = async () => {
    setIsLoading(true);
    setSessionStatus('checking');

    try {
      const hasValidSession = await sessionManager.autoLogin();
      
      if (hasValidSession) {
        const session = sessionManager.getSession();
        if (session) {
          setIsAuthenticated(true);
          setSessionStatus('valid');
          
          // Determine user type based on role and admin access
          const userType = session.adminAccess || session.employee.role === 'super_admin' ? 'admin' : 'employee';
          setAuthMode(userType);
          
          // Set tenant slug if available
          if (session.tenant.slug) {
            setSelectedTenantSlug(session.tenant.slug);
          }

          console.log('✅ Existing session restored:', {
            user: session.employee.name,
            role: session.employee.role,
            tenant: session.tenant.business_name || session.tenant.name,
            userType
          });

          onAuthenticationSuccess(userType);
        }
      } else {
        setIsAuthenticated(false);
        setSessionStatus('invalid');
      }
    } catch (error) {
      console.error('❌ Session check failed:', error);
      setIsAuthenticated(false);
      setSessionStatus('invalid');
    } finally {
      setIsLoading(false);
    }
  };

  const handleEmployeeLogin = (success: boolean) => {
    if (success) {
      const session = sessionManager.getSession();
      if (session) {
        sessionManager.createSession({
          token: session.token,
          employee: session.employee,
          tenant: session.tenant,
          location: session.location,
          adminAccess: false
        });

        setIsAuthenticated(true);
        setSessionStatus('valid');
        onAuthenticationSuccess('employee');
      }
    }
  };

  const handleAdminLogin = (success: boolean) => {
    if (success) {
      const session = sessionManager.getSession();
      if (session) {
        sessionManager.createSession({
          token: session.token,
          employee: session.employee,
          tenant: session.tenant,
          location: session.location,
          adminAccess: true
        });

        setIsAuthenticated(true);
        setSessionStatus('valid');
        onAuthenticationSuccess('admin');
      }
    }
  };

  const handleLogout = () => {
    sessionManager.logout();
    setIsAuthenticated(false);
    setSessionStatus('invalid');
    setSelectedTenantSlug('');
  };

  const handleRefreshSession = async () => {
    setSessionStatus('checking');
    const isValid = await sessionManager.refreshSession();
    setSessionStatus(isValid ? 'valid' : 'invalid');
    
    if (!isValid) {
      setIsAuthenticated(false);
    }
  };

  const toggleAuthMode = () => {
    setAuthMode(authMode === 'employee' ? 'admin' : 'employee');
  };

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  // Loading screen
  if (isLoading) {
    return (
      <div className={`min-h-screen flex items-center justify-center transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mb-4"></div>
          <p className={`text-lg font-medium ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Checking authentication...
          </p>
        </div>
      </div>
    );
  }

  // Authenticated state - show session info
  if (isAuthenticated) {
    const session = sessionManager.getSession();
    
    return (
      <div className={`min-h-screen transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        
        {/* Header */}
        <div className={`shadow-lg ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        }`}>
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-4">
              
              {/* User Info */}
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                  authMode === 'admin' 
                    ? 'bg-gradient-to-r from-red-500 to-pink-600' 
                    : 'bg-gradient-to-r from-blue-500 to-indigo-600'
                }`}>
                  {authMode === 'admin' ? (
                    <Shield className="w-6 h-6 text-white" />
                  ) : (
                    <Users className="w-6 h-6 text-white" />
                  )}
                </div>
                
                <div>
                  <h2 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {session?.employee.name}
                  </h2>
                  <p className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {session?.employee.role} • {session?.tenant.business_name || session?.tenant.name}
                  </p>
                </div>
              </div>

              {/* Session Status & Actions */}
              <div className="flex items-center space-x-4">
                
                {/* Session Status */}
                <div className={`flex items-center space-x-2 px-3 py-1 rounded-lg text-sm ${
                  sessionStatus === 'valid' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    : sessionStatus === 'checking'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                    : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                }`}>
                  {sessionStatus === 'valid' ? (
                    <CheckCircle className="w-4 h-4" />
                  ) : sessionStatus === 'checking' ? (
                    <Loader2 className="w-4 h-4 animate-spin" />
                  ) : (
                    <AlertCircle className="w-4 h-4" />
                  )}
                  <span className="font-medium">
                    {sessionStatus === 'valid' ? 'Session Active' : 
                     sessionStatus === 'checking' ? 'Checking...' : 'Session Invalid'}
                  </span>
                </div>

                {/* Refresh Session */}
                <button
                  onClick={handleRefreshSession}
                  className={`p-2 rounded-lg transition-colors ${
                    isDarkMode 
                      ? 'text-gray-400 hover:text-gray-200 hover:bg-gray-700' 
                      : 'text-gray-600 hover:text-gray-800 hover:bg-gray-100'
                  }`}
                  title="Refresh Session"
                >
                  <RefreshCw className="w-5 h-5" />
                </button>

                {/* Theme Toggle */}
                <button
                  onClick={toggleTheme}
                  className={`p-2 rounded-lg transition-colors ${
                    isDarkMode 
                      ? 'text-yellow-400 hover:bg-gray-700' 
                      : 'text-gray-600 hover:bg-gray-100'
                  }`}
                  title="Toggle Theme"
                >
                  {isDarkMode ? '☀️' : '🌙'}
                </button>

                {/* Logout */}
                <button
                  onClick={handleLogout}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-colors ${
                    isDarkMode 
                      ? 'bg-red-600 hover:bg-red-500 text-white' 
                      : 'bg-red-100 hover:bg-red-200 text-red-600'
                  }`}
                >
                  <LogOut className="w-4 h-4" />
                  <span>Logout</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {selectedTenantSlug && (
            <TenantProfileManager 
              tenantSlug={selectedTenantSlug}
              readOnly={!sessionManager.hasAnyRole(['super_admin', 'tenant_admin'])}
            />
          )}
        </div>
      </div>
    );
  }

  // Login screen
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900' 
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
    }`}>
      
      {/* Auth Mode Toggle */}
      <div className="fixed top-4 left-4 z-50">
        <div className={`flex rounded-lg p-1 ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        } shadow-lg`}>
          <button
            onClick={() => setAuthMode('employee')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              authMode === 'employee'
                ? 'bg-blue-600 text-white'
                : isDarkMode
                ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <Building2 className="w-4 h-4" />
            <span>Employee</span>
          </button>
          
          <button
            onClick={() => setAuthMode('admin')}
            className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
              authMode === 'admin'
                ? 'bg-red-600 text-white'
                : isDarkMode
                ? 'text-gray-300 hover:text-white hover:bg-gray-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
            }`}
          >
            <Shield className="w-4 h-4" />
            <span>Admin</span>
          </button>
        </div>
      </div>

      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className={`fixed top-4 right-4 p-3 rounded-full transition-all duration-300 z-50 ${
          isDarkMode 
            ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' 
            : 'bg-white text-gray-600 hover:bg-gray-100'
        } shadow-lg hover:shadow-xl`}
      >
        {isDarkMode ? '☀️' : '🌙'}
      </button>

      {/* Login Interface */}
      {authMode === 'employee' ? (
        <TenantEmployeeLogin
          onLogin={handleEmployeeLogin}
          tenantSlug={selectedTenantSlug}
          onTenantChange={setSelectedTenantSlug}
        />
      ) : (
        <EnhancedSuperAdminLogin
          onLogin={handleAdminLogin}
        />
      )}
    </div>
  );
};

export default UnifiedAuthenticationSystem;
