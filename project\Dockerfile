# syntax=docker/dockerfile:1

ARG NODE_VERSION=22.13.1

# --- Build Stage ---
FROM node:${NODE_VERSION}-slim AS builder
WORKDIR /app

# Install build dependencies only (no app code yet)
COPY --link package.json package.json
COPY --link package-lock.json package-lock.json

RUN --mount=type=cache,target=/root/.npm \
    npm ci

# Copy the rest of the app source code
COPY --link . .

# Build the TypeScript app (if build script exists)
RUN --mount=type=cache,target=/root/.npm \
    npm run build

# Remove dev dependencies, install only production deps
RUN --mount=type=cache,target=/root/.npm \
    rm -rf node_modules && npm ci --production

# --- Production Stage ---
FROM node:${NODE_VERSION}-slim AS final
WORKDIR /app

# Create a non-root user for security
RUN addgroup --system appgroup && adduser --system --ingroup appgroup appuser

# Copy built app and production node_modules from builder
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./
COPY --from=builder /app/package-lock.json ./

# If you need static assets, copy them as well (uncomment if needed)
# COPY --from=builder /app/public ./public

ENV NODE_ENV=production
ENV NODE_OPTIONS="--max-old-space-size=4096"

USER appuser

EXPOSE 4000

CMD ["npm", "start"]
