import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Beer, Wine, FileLock as Cocktail, GlassWater, Utensils, Flame, Coffee } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
}

const UnifiedProductGrid: React.FC = () => {
  const { state, dispatch, apiCall } = useEnhancedAppContext();
  const [selectedCategory, setSelectedCategory] = useState<string>('Beverages');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);

        console.log('🔄 Loading products and categories...');

        // Enhanced error handling with retry logic
        let productsData = [];
        let categoriesData = [];
        let backendConnected = false;

        try {
          // Load products with timeout
          const productsResponse = await Promise.race([
            apiCall('/api/products'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
          ]) as Response;

          if (productsResponse.ok) {
            productsData = await productsResponse.json();
            backendConnected = true;
            console.log('✅ Products loaded from backend:', productsData.length);
          }
        } catch (error) {
          console.log('⚠️ Backend products unavailable, using mock data');
        }

        try {
          // Load categories with timeout
          const categoriesResponse = await Promise.race([
            apiCall('/api/categories'),
            new Promise((_, reject) => setTimeout(() => reject(new Error('Timeout')), 5000))
          ]) as Response;

          if (categoriesResponse.ok) {
            categoriesData = await categoriesResponse.json();
            console.log('✅ Categories loaded from backend:', categoriesData.length);
          }
        } catch (error) {
          console.log('⚠️ Backend categories unavailable, using mock data');
        }

        // Use backend data if available, otherwise fallback to enhanced mock data
        if (backendConnected && productsData.length > 0) {
          setProducts(productsData);
          const categoryNames = categoriesData.map((cat: any) => cat.name || cat);
          setCategories(categoryNames.length > 0 ? categoryNames : ['Beverages', 'Food', 'Desserts']);

          if (categoryNames.length > 0) {
            setSelectedCategory(categoryNames[0]);
          }
        } else {
          // Enhanced mock data for better demo experience
          const mockProducts = [
            { id: '1', name: 'Espresso', price: 2.75, category: 'Beverages', description: 'Rich and bold espresso shot' },
            { id: '2', name: 'Cappuccino', price: 4.25, category: 'Beverages', description: 'Espresso with steamed milk foam' },
            { id: '3', name: 'Latte', price: 4.75, category: 'Beverages', description: 'Smooth espresso with steamed milk' },
            { id: '4', name: 'Americano', price: 3.50, category: 'Beverages', description: 'Espresso with hot water' },
            { id: '5', name: 'Club Sandwich', price: 12.99, category: 'Food', description: 'Triple-decker with turkey, bacon, lettuce' },
            { id: '6', name: 'Caesar Salad', price: 9.50, category: 'Food', description: 'Fresh romaine with parmesan and croutons' },
            { id: '7', name: 'Cheeseburger', price: 14.99, category: 'Food', description: 'Beef patty with cheese and fries' },
            { id: '8', name: 'Margherita Pizza', price: 16.99, category: 'Food', description: 'Fresh mozzarella, tomato, basil' },
            { id: '9', name: 'Chocolate Cake', price: 6.99, category: 'Desserts', description: 'Rich chocolate layer cake' },
            { id: '10', name: 'Tiramisu', price: 7.99, category: 'Desserts', description: 'Classic Italian coffee-flavored dessert' },
            { id: '11', name: 'Ice Cream', price: 4.99, category: 'Desserts', description: 'Vanilla, chocolate, or strawberry' },
            { id: '12', name: 'Fresh Juice', price: 3.99, category: 'Beverages', description: 'Orange, apple, or cranberry' }
          ];
          setProducts(mockProducts);
          setCategories(['Beverages', 'Food', 'Desserts']);
          setSelectedCategory('Beverages');
          setError(backendConnected ? null : 'Using demo data - Backend connection unavailable');
        }

      } catch (error) {
        console.error('❌ Critical error loading data:', error);
        setError('System error occurred. Please refresh the page.');
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [apiCall]);

  const getCategoryIcon = (category: string) => {
    switch(category.toLowerCase()) {
      case 'beverages':
      case 'drinks':
        return <Coffee className="h-5 w-5" />;
      case 'food':
        return <Utensils className="h-5 w-5" />;
      case 'beer':
        return <Beer className="h-5 w-5" />;
      case 'wine':
        return <Wine className="h-5 w-5" />;
      case 'cocktails':
        return <Cocktail className="h-5 w-5" />;
      case 'spirits':
        return <Wine className="h-5 w-5" />;
      case 'non-alcoholic':
        return <GlassWater className="h-5 w-5" />;
      default:
        return <Flame className="h-5 w-5" />;
    }
  };

  const handleAddToOrder = (product: Product) => {
    console.log('➕ Adding product to order:', product.name);
    
    // Add to current order using the correct action type
    dispatch({
      type: 'ADD_PRODUCT_TO_ORDER',
      payload: { ...product, inStock: true }
    });
  };

  const filteredProducts = products.filter(
    product => product.category === selectedCategory
  );

  const getCategoryCount = (category: string) => {
    return products.filter(p => p.category === category).length;
  };

  const getCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  };

  if (isLoading) {
    return (
      <div className="h-full flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
        <div className="bg-white border-b border-gray-200 p-4">
          <h2 className="text-xl font-semibold text-gray-900">Products</h2>
          <p className="text-sm text-gray-500">Loading fresh menu items...</p>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-6">
            <div className="relative">
              <div className="animate-spin rounded-full h-16 w-16 border-4 border-blue-200 border-t-blue-600"></div>
              <div className="absolute inset-0 flex items-center justify-center">
                <Coffee className="h-6 w-6 text-blue-600" />
              </div>
            </div>
            <div className="text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Loading Menu</h3>
              <p className="text-gray-600 mb-4">Fetching products and categories...</p>
              <div className="flex space-x-2 justify-center">
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }}></div>
                <div className="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }}></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-full flex flex-col bg-white">
        <div className="bg-white border-b border-gray-200 p-4">
          <h2 className="text-xl font-semibold text-gray-900">Products</h2>
          <div className="mt-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">⚠️ {error}</p>
          </div>
        </div>
        <div className="flex-1 flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-2">📦</div>
            <p className="text-lg font-medium mb-1">Using Mock Data</p>
            <p className="text-sm text-gray-500">Backend connection unavailable</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="text-xl font-semibold text-gray-900">Products</h2>
        <p className="text-sm text-gray-500">Select items to add to your order</p>
      </div>

      {/* Category tabs */}
      <div className="flex overflow-x-auto bg-gray-50 p-3 space-x-2 border-b border-gray-200">
        {categories.map(category => (
          <button
            key={category}
            className={`flex items-center space-x-2 py-2 px-4 rounded-lg transition-colors whitespace-nowrap ${
              selectedCategory === category
              ? 'bg-blue-600 text-white shadow-md'
              : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            {getCategoryIcon(category)}
            <span className="font-medium">{getCategoryName(category)}</span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              selectedCategory === category 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {getCategoryCount(category)}
            </span>
          </button>
        ))}
      </div>

      {/* Products grid */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredProducts.length === 0 ? (
            <div className="col-span-full flex items-center justify-center py-12 text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-2">📦</div>
                <p className="text-lg font-medium mb-1">No products available</p>
                <p className="text-sm">in {getCategoryName(selectedCategory)} category</p>
              </div>
            </div>
          ) : (
            filteredProducts.map(product => (
              <button
                key={product.id}
                className="bg-white hover:bg-gray-50 rounded-lg p-4 text-left transition-all duration-200 active:scale-95 hover:shadow-lg border border-gray-200 hover:border-blue-300 min-h-[140px] flex flex-col"
                onClick={() => handleAddToOrder(product)}
              >
                <div className="flex flex-col h-full">
                  {/* Product image placeholder */}
                  <div className="w-full h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-md mb-3 flex items-center justify-center">
                    {getCategoryIcon(product.category)}
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2 text-sm line-clamp-2">
                    {product.name}
                  </h3>
                  
                  {product.description && (
                    <p className="text-gray-600 text-xs mb-2 flex-grow line-clamp-2">
                      {product.description}
                    </p>
                  )}
                  
                  <div className="mt-auto">
                    <p className="text-blue-600 font-bold text-lg">
                      ${product.price.toFixed(2)}
                    </p>
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{filteredProducts.length} products in {getCategoryName(selectedCategory)}</span>
          <span>Tap any item to add to order</span>
        </div>
      </div>
    </div>
  );
};

export default UnifiedProductGrid;
