#!/usr/bin/env node

/**
 * Production Build Script for RESTROFLOW
 * Builds both main system and enterprise security system
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 RESTROFLOW PRODUCTION BUILD');
console.log('==============================');

const buildSteps = [
  {
    name: 'Clean Previous Builds',
    command: () => {
      if (fs.existsSync('dist')) {
        fs.rmSync('dist', { recursive: true, force: true });
        console.log('✅ Cleaned dist directory');
      }
      if (fs.existsSync('dist-super-admin')) {
        fs.rmSync('dist-super-admin', { recursive: true, force: true });
        console.log('✅ Cleaned dist-super-admin directory');
      }
    }
  },
  {
    name: 'Build Main System',
    command: 'npm run build'
  },
  {
    name: 'Build Enterprise Security System',
    command: 'npm run super-admin:build'
  },
  {
    name: 'Generate Service Worker',
    command: 'npm run generate-sw'
  },
  {
    name: 'Create Production Manifest',
    command: () => {
      const manifest = {
        name: 'RestroFlow Enterprise POS',
        version: '1.0.0',
        buildDate: new Date().toISOString(),
        components: {
          mainSystem: 'dist/',
          securitySystem: 'dist-super-admin/',
          serviceWorker: 'dist/sw.js'
        },
        features: [
          'Multi-tenant Restaurant POS',
          'Enterprise Security Center',
          'Advanced Analytics Dashboard',
          'Real-time Order Management',
          'AI-powered Fraud Detection',
          'Multi-currency Support',
          'Industry-specific Interfaces',
          'Progressive Web App'
        ],
        deployment: {
          mainPort: 5173,
          securityPort: 5174,
          apiPort: 4000,
          database: 'PostgreSQL'
        }
      };

      fs.writeFileSync('production-manifest.json', JSON.stringify(manifest, null, 2));
      console.log('✅ Created production manifest');
    }
  },
  {
    name: 'Optimize Assets',
    command: () => {
      // Copy critical assets to both builds
      const criticalAssets = [
        'public/favicon.ico',
        'public/manifest.json',
        'public/manifest-admin.json'
      ];

      criticalAssets.forEach(asset => {
        if (fs.existsSync(asset)) {
          const filename = path.basename(asset);
          if (fs.existsSync('dist')) {
            fs.copyFileSync(asset, `dist/${filename}`);
          }
          if (fs.existsSync('dist-super-admin')) {
            fs.copyFileSync(asset, `dist-super-admin/${filename}`);
          }
        }
      });

      console.log('✅ Optimized and copied critical assets');
    }
  },
  {
    name: 'Generate Deployment Guide',
    command: () => {
      const deploymentGuide = `# RESTROFLOW DEPLOYMENT GUIDE

## Production Build Complete

### Build Artifacts:
- **Main System**: \`dist/\` directory
- **Security System**: \`dist-super-admin/\` directory
- **Service Worker**: \`dist/sw.js\`
- **Manifests**: PWA manifests included

### Deployment Options:

#### Option 1: Static File Server
\`\`\`bash
# Serve main system
npx serve dist -p 5173

# Serve security system (separate terminal)
npx serve dist-super-admin -p 5174
\`\`\`

#### Option 2: Docker Deployment
\`\`\`bash
# Build Docker images
docker build -t restroflow-main .
docker build -t restroflow-security -f Dockerfile.security .

# Run containers
docker run -p 5173:80 restroflow-main
docker run -p 5174:80 restroflow-security
\`\`\`

#### Option 3: Cloud Deployment
- **AWS S3 + CloudFront**: Static hosting with CDN
- **Netlify**: Automatic deployment from Git
- **Vercel**: Serverless deployment
- **Azure Static Web Apps**: Enterprise hosting

### Backend Requirements:
- **Node.js**: v16+ required
- **PostgreSQL**: Database server
- **Environment**: Production environment variables

### Access URLs:
- **Main System**: http://your-domain:5173
- **Security System**: http://your-domain:5174
- **API Backend**: http://your-domain:4000

### Security Considerations:
- Enable HTTPS in production
- Configure proper CORS settings
- Set up SSL certificates
- Configure firewall rules
- Enable security headers

Build completed: ${new Date().toISOString()}
`;

      fs.writeFileSync('DEPLOYMENT.md', deploymentGuide);
      console.log('✅ Generated deployment guide');
    }
  }
];

async function runBuild() {
  console.log(`\n📦 Starting production build process...`);
  console.log(`⏰ Build started at: ${new Date().toLocaleString()}\n`);

  for (let i = 0; i < buildSteps.length; i++) {
    const step = buildSteps[i];
    console.log(`\n🔄 Step ${i + 1}/${buildSteps.length}: ${step.name}`);
    
    try {
      if (typeof step.command === 'function') {
        step.command();
      } else {
        console.log(`   Running: ${step.command}`);
        execSync(step.command, { stdio: 'inherit' });
      }
      console.log(`✅ Completed: ${step.name}`);
    } catch (error) {
      console.error(`❌ Failed: ${step.name}`);
      console.error(`   Error: ${error.message}`);
      process.exit(1);
    }
  }

  console.log('\n🎉 PRODUCTION BUILD COMPLETE!');
  console.log('=============================');
  console.log('✅ Main System: dist/');
  console.log('✅ Security System: dist-super-admin/');
  console.log('✅ Service Worker: dist/sw.js');
  console.log('✅ Production Manifest: production-manifest.json');
  console.log('✅ Deployment Guide: DEPLOYMENT.md');
  console.log('\n🚀 Your RESTROFLOW system is ready for production deployment!');
  console.log('\n📋 Next Steps:');
  console.log('1. Review DEPLOYMENT.md for deployment options');
  console.log('2. Configure your production environment');
  console.log('3. Set up SSL certificates');
  console.log('4. Deploy to your hosting platform');
  console.log('5. Configure your domain and DNS');
  console.log('\n✨ Happy deploying!');
}

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n⚠️ Build process interrupted');
  process.exit(1);
});

process.on('SIGTERM', () => {
  console.log('\n⚠️ Build process terminated');
  process.exit(1);
});

// Run the build
runBuild().catch(error => {
  console.error('\n❌ Build process failed:', error);
  process.exit(1);
});
