<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTROFLOW - Super Admin Security Test Suite</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5rem;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1rem;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            border: 2px solid #f1f2f6;
            border-radius: 10px;
            background: #f8f9fa;
        }

        .test-section h2 {
            color: #2c3e50;
            margin-bottom: 20px;
            font-size: 1.5rem;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #34495e;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .form-group input:focus, .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s, box-shadow 0.2s;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
        }

        .btn-success {
            background: linear-gradient(135deg, #2ecc71, #27ae60);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .response {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }

        .response.success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .response.error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .response.info {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online { background: #2ecc71; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }

        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .test-results {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-results h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }

        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }

        .test-item:last-child {
            border-bottom: none;
        }

        .badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            text-transform: uppercase;
        }

        .badge-success { background: #d4edda; color: #155724; }
        .badge-error { background: #f8d7da; color: #721c24; }
        .badge-warning { background: #fff3cd; color: #856404; }
        .badge-info { background: #d1ecf1; color: #0c5460; }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔒 RESTROFLOW Security Test Suite</h1>
            <p>Comprehensive Super Admin Authentication & Security Testing</p>
            <div style="margin-top: 15px;">
                <span class="status-indicator" id="serverStatus"></span>
                <span id="serverStatusText">Checking server status...</span>
            </div>
        </div>

        <div class="content">
            <!-- Authentication Testing Section -->
            <div class="test-section">
                <h2>🔐 Authentication Testing</h2>
                <div class="grid">
                    <div>
                        <form id="loginForm">
                            <div class="form-group">
                                <label for="pin">Super Admin PIN:</label>
                                <input type="password" id="pin" name="pin" placeholder="Enter PIN (default: 123456)" required>
                            </div>
                            <div class="form-group">
                                <label for="role">Test Role:</label>
                                <select id="role" name="role">
                                    <option value="super_admin">Super Admin</option>
                                    <option value="tenant_admin">Tenant Admin</option>
                                    <option value="employee">Employee</option>
                                </select>
                            </div>
                            <button type="submit" class="btn">🔑 Test Login</button>
                            <button type="button" class="btn btn-warning" onclick="testInvalidLogin()">❌ Test Invalid Login</button>
                            <button type="button" class="btn btn-danger" onclick="testBruteForce()">⚠️ Test Brute Force</button>
                        </form>
                    </div>
                    <div class="test-results">
                        <h3>Authentication Results</h3>
                        <div id="authResults">
                            <div class="test-item">
                                <span>Login Status</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>JWT Token</span>
                                <span class="badge badge-info">Not Generated</span>
                            </div>
                            <div class="test-item">
                                <span>Role Verification</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Session Duration</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="authResponse" class="response" style="display: none;"></div>
            </div>

            <!-- API Security Testing Section -->
            <div class="test-section">
                <h2>🛡️ API Security Testing</h2>
                <div class="grid">
                    <div>
                        <button class="btn" onclick="testHealthEndpoint()">❤️ Test Health Endpoint</button>
                        <button class="btn" onclick="testProtectedEndpoints()">🔒 Test Protected Endpoints</button>
                        <button class="btn btn-warning" onclick="testUnauthorizedAccess()">🚫 Test Unauthorized Access</button>
                        <button class="btn btn-danger" onclick="testSQLInjection()">💉 Test SQL Injection</button>
                        <button class="btn" onclick="testRateLimit()">⏱️ Test Rate Limiting</button>
                        <button class="btn" onclick="testCORS()">🌐 Test CORS Policy</button>
                    </div>
                    <div class="test-results">
                        <h3>API Security Results</h3>
                        <div id="apiResults">
                            <div class="test-item">
                                <span>Health Check</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Protected Endpoints</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Unauthorized Access</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>SQL Injection</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Rate Limiting</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>CORS Policy</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="apiResponse" class="response" style="display: none;"></div>
            </div>

            <!-- Performance & Monitoring Testing Section -->
            <div class="test-section">
                <h2>📊 Performance & Monitoring Testing</h2>
                <div class="grid">
                    <div>
                        <button class="btn" onclick="testPerformanceMetrics()">📈 Test Performance Metrics</button>
                        <button class="btn" onclick="testMonitoringAlerts()">🚨 Test Monitoring Alerts</button>
                        <button class="btn" onclick="testDatabasePerformance()">🗄️ Test Database Performance</button>
                        <button class="btn" onclick="testCachePerformance()">⚡ Test Cache Performance</button>
                        <button class="btn btn-warning" onclick="testLoadStress()">💪 Test Load Stress</button>
                    </div>
                    <div class="test-results">
                        <h3>Performance Results</h3>
                        <div id="performanceResults">
                            <div class="test-item">
                                <span>Performance Metrics</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Monitoring Alerts</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Database Performance</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Cache Performance</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Load Stress</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="performanceResponse" class="response" style="display: none;"></div>
            </div>

            <!-- System Integration Testing Section -->
            <div class="test-section">
                <h2>🔗 System Integration Testing</h2>
                <div class="grid">
                    <div>
                        <button class="btn" onclick="testTenantIsolation()">🏢 Test Tenant Isolation</button>
                        <button class="btn" onclick="testMultiTenantData()">📊 Test Multi-Tenant Data</button>
                        <button class="btn" onclick="testRoleBasedAccess()">👥 Test Role-Based Access</button>
                        <button class="btn" onclick="testSessionManagement()">🎫 Test Session Management</button>
                        <button class="btn btn-success" onclick="runFullSecuritySuite()">🚀 Run Full Security Suite</button>
                    </div>
                    <div class="test-results">
                        <h3>Integration Results</h3>
                        <div id="integrationResults">
                            <div class="test-item">
                                <span>Tenant Isolation</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Multi-Tenant Data</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Role-Based Access</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Session Management</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                            <div class="test-item">
                                <span>Full Security Suite</span>
                                <span class="badge badge-info">Not Tested</span>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="integrationResponse" class="response" style="display: none;"></div>
            </div>

            <!-- Test Summary Section -->
            <div class="test-section">
                <h2>📋 Test Summary & Report</h2>
                <div id="testSummary">
                    <div class="test-item">
                        <span><strong>Total Tests Run:</strong></span>
                        <span id="totalTests">0</span>
                    </div>
                    <div class="test-item">
                        <span><strong>Tests Passed:</strong></span>
                        <span id="passedTests" class="badge badge-success">0</span>
                    </div>
                    <div class="test-item">
                        <span><strong>Tests Failed:</strong></span>
                        <span id="failedTests" class="badge badge-error">0</span>
                    </div>
                    <div class="test-item">
                        <span><strong>Security Score:</strong></span>
                        <span id="securityScore" class="badge badge-info">Not Calculated</span>
                    </div>
                </div>
                <button class="btn btn-success" onclick="generateReport()" style="margin-top: 20px;">📄 Generate Security Report</button>
                <button class="btn btn-warning" onclick="clearAllTests()" style="margin-top: 20px;">🗑️ Clear All Tests</button>
            </div>
        </div>
    </div>

    <script>
        // Global variables for test management
        let authToken = null;
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            tests: {}
        };

        // API Base URL
        const API_BASE = 'http://localhost:4000/api';

        // Initialize the security test suite
        document.addEventListener('DOMContentLoaded', function() {
            checkServerStatus();
            setupEventListeners();
        });

        // Setup event listeners
        function setupEventListeners() {
            document.getElementById('loginForm').addEventListener('submit', handleLogin);
        }

        // Check server status
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (response.ok) {
                    updateServerStatus('online', `Server Online - ${data.status} (v${data.version})`);
                } else {
                    updateServerStatus('warning', 'Server Responding - Degraded Status');
                }
            } catch (error) {
                updateServerStatus('offline', 'Server Offline - Connection Failed');
            }
        }

        // Update server status indicator
        function updateServerStatus(status, message) {
            const indicator = document.getElementById('serverStatus');
            const text = document.getElementById('serverStatusText');

            indicator.className = `status-indicator status-${status}`;
            text.textContent = message;
        }

        // Handle login form submission
        async function handleLogin(event) {
            event.preventDefault();
            const pin = document.getElementById('pin').value;
            const role = document.getElementById('role').value;

            showLoading('authResponse');
            updateTestResult('authResults', 'Login Status', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pin, role })
                });

                const data = await response.json();

                if (response.ok && data.token) {
                    authToken = data.token;
                    showResponse('authResponse', 'Login successful!', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('authResults', 'Login Status', 'Success', 'success');
                    updateTestResult('authResults', 'JWT Token', 'Generated', 'success');
                    updateTestResult('authResults', 'Role Verification', data.user?.role || 'Unknown', 'success');
                    updateTestResult('authResults', 'Session Duration', '24 hours', 'success');
                    recordTest('authentication', true);
                } else {
                    showResponse('authResponse', 'Login failed!', JSON.stringify(data, null, 2), 'error');
                    updateTestResult('authResults', 'Login Status', 'Failed', 'error');
                    recordTest('authentication', false);
                }
            } catch (error) {
                showResponse('authResponse', 'Connection error!', error.message, 'error');
                updateTestResult('authResults', 'Login Status', 'Error', 'error');
                recordTest('authentication', false);
            }
        }

        // Test invalid login attempts
        async function testInvalidLogin() {
            showLoading('authResponse');
            const invalidPins = ['000000', '999999', 'abcdef', ''];
            let results = [];

            for (const pin of invalidPins) {
                try {
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ pin })
                    });

                    const data = await response.json();
                    results.push({
                        pin: pin || 'empty',
                        status: response.status,
                        blocked: !response.ok
                    });
                } catch (error) {
                    results.push({
                        pin: pin || 'empty',
                        error: error.message
                    });
                }
            }

            const allBlocked = results.every(r => r.blocked || r.error);
            showResponse('authResponse', 'Invalid Login Test Results', JSON.stringify(results, null, 2), allBlocked ? 'success' : 'error');
            recordTest('invalid_login', allBlocked);
        }

        // Test brute force protection
        async function testBruteForce() {
            showLoading('authResponse');
            const attempts = [];

            // Simulate rapid login attempts
            for (let i = 0; i < 10; i++) {
                try {
                    const start = Date.now();
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ pin: '000000' })
                    });

                    const end = Date.now();
                    attempts.push({
                        attempt: i + 1,
                        status: response.status,
                        responseTime: end - start,
                        rateLimited: response.status === 429
                    });

                    if (response.status === 429) break; // Rate limited
                } catch (error) {
                    attempts.push({
                        attempt: i + 1,
                        error: error.message
                    });
                }
            }

            const rateLimitTriggered = attempts.some(a => a.rateLimited);
            showResponse('authResponse', 'Brute Force Test Results', JSON.stringify(attempts, null, 2), rateLimitTriggered ? 'success' : 'error');
            recordTest('brute_force', rateLimitTriggered);
        }

        // Test health endpoint
        async function testHealthEndpoint() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'Health Check', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();

                if (response.ok) {
                    showResponse('apiResponse', 'Health Check Successful', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('apiResults', 'Health Check', 'Passed', 'success');
                    recordTest('health_check', true);
                } else {
                    showResponse('apiResponse', 'Health Check Failed', JSON.stringify(data, null, 2), 'error');
                    updateTestResult('apiResults', 'Health Check', 'Failed', 'error');
                    recordTest('health_check', false);
                }
            } catch (error) {
                showResponse('apiResponse', 'Health Check Error', error.message, 'error');
                updateTestResult('apiResults', 'Health Check', 'Error', 'error');
                recordTest('health_check', false);
            }
        }

        // Test protected endpoints
        async function testProtectedEndpoints() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'Protected Endpoints', 'Testing...', 'warning');

            const protectedEndpoints = [
                '/products',
                '/orders',
                '/tenants',
                '/employees',
                '/analytics/sales'
            ];

            let results = [];

            for (const endpoint of protectedEndpoints) {
                try {
                    // Test without token
                    const responseWithoutToken = await fetch(`${API_BASE}${endpoint}`);

                    // Test with token (if available)
                    let responseWithToken = null;
                    if (authToken) {
                        responseWithToken = await fetch(`${API_BASE}${endpoint}`, {
                            headers: {
                                'Authorization': `Bearer ${authToken}`
                            }
                        });
                    }

                    results.push({
                        endpoint,
                        withoutToken: {
                            status: responseWithoutToken.status,
                            protected: responseWithoutToken.status === 401
                        },
                        withToken: responseWithToken ? {
                            status: responseWithToken.status,
                            accessible: responseWithToken.ok
                        } : 'No token available'
                    });
                } catch (error) {
                    results.push({
                        endpoint,
                        error: error.message
                    });
                }
            }

            const allProtected = results.every(r => r.withoutToken?.protected);
            showResponse('apiResponse', 'Protected Endpoints Test Results', JSON.stringify(results, null, 2), allProtected ? 'success' : 'error');
            updateTestResult('apiResults', 'Protected Endpoints', allProtected ? 'Secured' : 'Vulnerable', allProtected ? 'success' : 'error');
            recordTest('protected_endpoints', allProtected);
        }

        // Test unauthorized access
        async function testUnauthorizedAccess() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'Unauthorized Access', 'Testing...', 'warning');

            const testCases = [
                { endpoint: '/tenants', method: 'GET', expectedStatus: 401 },
                { endpoint: '/products', method: 'POST', expectedStatus: 401 },
                { endpoint: '/orders', method: 'DELETE', expectedStatus: 401 },
                { endpoint: '/employees', method: 'PUT', expectedStatus: 401 }
            ];

            let results = [];

            for (const testCase of testCases) {
                try {
                    const response = await fetch(`${API_BASE}${testCase.endpoint}`, {
                        method: testCase.method,
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    results.push({
                        ...testCase,
                        actualStatus: response.status,
                        properlyBlocked: response.status === testCase.expectedStatus
                    });
                } catch (error) {
                    results.push({
                        ...testCase,
                        error: error.message
                    });
                }
            }

            const allBlocked = results.every(r => r.properlyBlocked);
            showResponse('apiResponse', 'Unauthorized Access Test Results', JSON.stringify(results, null, 2), allBlocked ? 'success' : 'error');
            updateTestResult('apiResults', 'Unauthorized Access', allBlocked ? 'Blocked' : 'Vulnerable', allBlocked ? 'success' : 'error');
            recordTest('unauthorized_access', allBlocked);
        }

        // Test SQL injection
        async function testSQLInjection() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'SQL Injection', 'Testing...', 'warning');

            const sqlInjectionPayloads = [
                "'; DROP TABLE products; --",
                "' OR '1'='1",
                "'; SELECT * FROM users; --",
                "' UNION SELECT * FROM employees --"
            ];

            let results = [];

            for (const payload of sqlInjectionPayloads) {
                try {
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ pin: payload })
                    });

                    const data = await response.json();
                    results.push({
                        payload,
                        status: response.status,
                        blocked: !response.ok,
                        response: data.message || 'No message'
                    });
                } catch (error) {
                    results.push({
                        payload,
                        error: error.message,
                        blocked: true
                    });
                }
            }

            const allBlocked = results.every(r => r.blocked);
            showResponse('apiResponse', 'SQL Injection Test Results', JSON.stringify(results, null, 2), allBlocked ? 'success' : 'error');
            updateTestResult('apiResults', 'SQL Injection', allBlocked ? 'Protected' : 'Vulnerable', allBlocked ? 'success' : 'error');
            recordTest('sql_injection', allBlocked);
        }

        // Test rate limiting
        async function testRateLimit() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'Rate Limiting', 'Testing...', 'warning');

            try {
                const requests = [];
                for (let i = 0; i < 20; i++) {
                    requests.push(fetch(`${API_BASE}/health`));
                }

                const results = await Promise.all(requests);
                const rateLimited = results.some(r => r.status === 429);

                showResponse('apiResponse', 'Rate Limiting Test', `Sent 20 requests, Rate limited: ${rateLimited}`, rateLimited ? 'success' : 'warning');
                updateTestResult('apiResults', 'Rate Limiting', rateLimited ? 'Active' : 'Not Detected', rateLimited ? 'success' : 'warning');
                recordTest('rate_limiting', rateLimited);
            } catch (error) {
                showResponse('apiResponse', 'Rate Limiting Error', error.message, 'error');
                updateTestResult('apiResults', 'Rate Limiting', 'Error', 'error');
                recordTest('rate_limiting', false);
            }
        }

        // Test CORS policy
        async function testCORS() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'CORS Policy', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/health`, { method: 'OPTIONS' });
                const corsHeaders = response.headers.get('Access-Control-Allow-Origin');
                const corsConfigured = corsHeaders !== null;

                showResponse('apiResponse', 'CORS Test', `CORS Headers: ${corsHeaders || 'Not configured'}`, corsConfigured ? 'success' : 'warning');
                updateTestResult('apiResults', 'CORS Policy', corsConfigured ? 'Configured' : 'Not Configured', corsConfigured ? 'success' : 'warning');
                recordTest('cors_policy', corsConfigured);
            } catch (error) {
                showResponse('apiResponse', 'CORS Error', error.message, 'error');
                updateTestResult('apiResults', 'CORS Policy', 'Error', 'error');
                recordTest('cors_policy', false);
            }
        }

        // Performance testing functions
        async function testPerformanceMetrics() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Performance Metrics', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/performance/stats`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('performanceResponse', 'Performance Metrics', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Performance Metrics', 'Available', 'success');
                    recordTest('performance_metrics', true);
                } else {
                    showResponse('performanceResponse', 'Performance Metrics Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('performanceResults', 'Performance Metrics', 'Failed', 'error');
                    recordTest('performance_metrics', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Performance Error', error.message, 'error');
                updateTestResult('performanceResults', 'Performance Metrics', 'Error', 'error');
                recordTest('performance_metrics', false);
            }
        }

        async function testMonitoringAlerts() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Monitoring Alerts', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/monitoring/alerts`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('performanceResponse', 'Monitoring Alerts', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Monitoring Alerts', 'Available', 'success');
                    recordTest('monitoring_alerts', true);
                } else {
                    showResponse('performanceResponse', 'Monitoring Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('performanceResults', 'Monitoring Alerts', 'Failed', 'error');
                    recordTest('monitoring_alerts', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Monitoring Error', error.message, 'error');
                updateTestResult('performanceResults', 'Monitoring Alerts', 'Error', 'error');
                recordTest('monitoring_alerts', false);
            }
        }

        async function testDatabasePerformance() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Database Performance', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/performance/database`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('performanceResponse', 'Database Performance', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Database Performance', 'Available', 'success');
                    recordTest('database_performance', true);
                } else {
                    showResponse('performanceResponse', 'Database Test Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('performanceResults', 'Database Performance', 'Failed', 'error');
                    recordTest('database_performance', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Database Error', error.message, 'error');
                updateTestResult('performanceResults', 'Database Performance', 'Error', 'error');
                recordTest('database_performance', false);
            }
        }

        async function testCachePerformance() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Cache Performance', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/performance/cache`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('performanceResponse', 'Cache Performance', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Cache Performance', 'Available', 'success');
                    recordTest('cache_performance', true);
                } else {
                    showResponse('performanceResponse', 'Cache Test Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('performanceResults', 'Cache Performance', 'Failed', 'error');
                    recordTest('cache_performance', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Cache Error', error.message, 'error');
                updateTestResult('performanceResults', 'Cache Performance', 'Error', 'error');
                recordTest('cache_performance', false);
            }
        }

        async function testLoadStress() {
            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Load Stress', 'Testing...', 'warning');

            try {
                const requests = [];
                const startTime = Date.now();

                for (let i = 0; i < 100; i++) {
                    requests.push(fetch(`${API_BASE}/health`));
                }

                const results = await Promise.all(requests);
                const endTime = Date.now();
                const duration = endTime - startTime;
                const successCount = results.filter(r => r.ok).length;

                const summary = {
                    totalRequests: 100,
                    successfulRequests: successCount,
                    failedRequests: 100 - successCount,
                    totalTime: duration,
                    requestsPerSecond: Math.round((100 / duration) * 1000)
                };

                showResponse('performanceResponse', 'Load Stress Test', JSON.stringify(summary, null, 2), successCount > 90 ? 'success' : 'warning');
                updateTestResult('performanceResults', 'Load Stress', `${successCount}/100 passed`, successCount > 90 ? 'success' : 'warning');
                recordTest('load_stress', successCount > 90);
            } catch (error) {
                showResponse('performanceResponse', 'Load Stress Error', error.message, 'error');
                updateTestResult('performanceResults', 'Load Stress', 'Error', 'error');
                recordTest('load_stress', false);
            }
        }

        // Integration testing functions
        async function testTenantIsolation() {
            if (!authToken) {
                showResponse('integrationResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('integrationResponse');
            updateTestResult('integrationResults', 'Tenant Isolation', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/tenants`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('integrationResponse', 'Tenant Isolation Test', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('integrationResults', 'Tenant Isolation', 'Verified', 'success');
                    recordTest('tenant_isolation', true);
                } else {
                    showResponse('integrationResponse', 'Tenant Test Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('integrationResults', 'Tenant Isolation', 'Failed', 'error');
                    recordTest('tenant_isolation', false);
                }
            } catch (error) {
                showResponse('integrationResponse', 'Tenant Error', error.message, 'error');
                updateTestResult('integrationResults', 'Tenant Isolation', 'Error', 'error');
                recordTest('tenant_isolation', false);
            }
        }

        async function testMultiTenantData() {
            if (!authToken) {
                showResponse('integrationResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('integrationResponse');
            updateTestResult('integrationResults', 'Multi-Tenant Data', 'Testing...', 'warning');

            try {
                const endpoints = ['/products', '/orders', '/employees'];
                const results = [];

                for (const endpoint of endpoints) {
                    const response = await fetch(`${API_BASE}${endpoint}`, {
                        headers: { 'Authorization': `Bearer ${authToken}` }
                    });
                    results.push({
                        endpoint,
                        status: response.status,
                        success: response.ok
                    });
                }

                const allSuccess = results.every(r => r.success);
                showResponse('integrationResponse', 'Multi-Tenant Data Test', JSON.stringify(results, null, 2), allSuccess ? 'success' : 'error');
                updateTestResult('integrationResults', 'Multi-Tenant Data', allSuccess ? 'Verified' : 'Failed', allSuccess ? 'success' : 'error');
                recordTest('multi_tenant_data', allSuccess);
            } catch (error) {
                showResponse('integrationResponse', 'Multi-Tenant Error', error.message, 'error');
                updateTestResult('integrationResults', 'Multi-Tenant Data', 'Error', 'error');
                recordTest('multi_tenant_data', false);
            }
        }

        async function testRoleBasedAccess() {
            showLoading('integrationResponse');
            updateTestResult('integrationResults', 'Role-Based Access', 'Testing...', 'warning');

            const testRoles = ['super_admin', 'tenant_admin', 'employee'];
            const results = [];

            for (const role of testRoles) {
                try {
                    const response = await fetch(`${API_BASE}/auth/login`, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ pin: '123456', role })
                    });

                    results.push({
                        role,
                        status: response.status,
                        success: response.ok
                    });
                } catch (error) {
                    results.push({
                        role,
                        error: error.message
                    });
                }
            }

            showResponse('integrationResponse', 'Role-Based Access Test', JSON.stringify(results, null, 2), 'info');
            updateTestResult('integrationResults', 'Role-Based Access', 'Tested', 'success');
            recordTest('role_based_access', true);
        }

        async function testSessionManagement() {
            if (!authToken) {
                showResponse('integrationResponse', 'Authentication Required', 'Please login first', 'error');
                return;
            }

            showLoading('integrationResponse');
            updateTestResult('integrationResults', 'Session Management', 'Testing...', 'warning');

            try {
                // Test token validation
                const response = await fetch(`${API_BASE}/auth/validate`, {
                    headers: { 'Authorization': `Bearer ${authToken}` }
                });

                if (response.ok) {
                    const data = await response.json();
                    showResponse('integrationResponse', 'Session Management Test', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('integrationResults', 'Session Management', 'Valid', 'success');
                    recordTest('session_management', true);
                } else {
                    showResponse('integrationResponse', 'Session Test Failed', `Status: ${response.status}`, 'error');
                    updateTestResult('integrationResults', 'Session Management', 'Failed', 'error');
                    recordTest('session_management', false);
                }
            } catch (error) {
                showResponse('integrationResponse', 'Session Error', error.message, 'error');
                updateTestResult('integrationResults', 'Session Management', 'Error', 'error');
                recordTest('session_management', false);
            }
        }

        // Run full security suite
        async function runFullSecuritySuite() {
            showLoading('integrationResponse');
            updateTestResult('integrationResults', 'Full Security Suite', 'Running...', 'warning');

            const tests = [
                testHealthEndpoint,
                testProtectedEndpoints,
                testUnauthorizedAccess,
                testSQLInjection,
                testRateLimit,
                testCORS
            ];

            try {
                for (const test of tests) {
                    await test();
                    await new Promise(resolve => setTimeout(resolve, 1000)); // Wait 1 second between tests
                }

                showResponse('integrationResponse', 'Full Security Suite', 'All security tests completed', 'success');
                updateTestResult('integrationResults', 'Full Security Suite', 'Completed', 'success');
                recordTest('full_security_suite', true);
                updateTestSummary();
            } catch (error) {
                showResponse('integrationResponse', 'Security Suite Error', error.message, 'error');
                updateTestResult('integrationResults', 'Full Security Suite', 'Error', 'error');
                recordTest('full_security_suite', false);
            }
        }

        // Utility functions
        function showLoading(elementId) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response info';
            element.textContent = 'Loading...';
        }

        function showResponse(elementId, title, content, type) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `response ${type}`;
            element.textContent = `${title}\n\n${content}`;
        }

        function updateTestResult(sectionId, testName, result, status) {
            const section = document.getElementById(sectionId);
            const testItems = section.querySelectorAll('.test-item');

            testItems.forEach(item => {
                const span = item.querySelector('span');
                if (span && span.textContent === testName) {
                    const badge = item.querySelector('.badge');
                    badge.textContent = result;
                    badge.className = `badge badge-${status}`;
                }
            });
        }

        function recordTest(testName, passed) {
            testResults.total++;
            if (passed) {
                testResults.passed++;
            } else {
                testResults.failed++;
            }
            testResults.tests[testName] = passed;
            updateTestSummary();
        }

        function updateTestSummary() {
            document.getElementById('totalTests').textContent = testResults.total;
            document.getElementById('passedTests').textContent = testResults.passed;
            document.getElementById('failedTests').textContent = testResults.failed;

            const score = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            const scoreElement = document.getElementById('securityScore');
            scoreElement.textContent = `${score}%`;
            scoreElement.className = `badge badge-${score >= 80 ? 'success' : score >= 60 ? 'warning' : 'error'}`;
        }

        function generateReport() {
            const report = {
                timestamp: new Date().toISOString(),
                summary: {
                    totalTests: testResults.total,
                    passed: testResults.passed,
                    failed: testResults.failed,
                    score: testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0
                },
                tests: testResults.tests,
                recommendations: []
            };

            // Add recommendations based on failed tests
            Object.entries(testResults.tests).forEach(([test, passed]) => {
                if (!passed) {
                    switch (test) {
                        case 'sql_injection':
                            report.recommendations.push('Implement parameterized queries to prevent SQL injection');
                            break;
                        case 'rate_limiting':
                            report.recommendations.push('Configure rate limiting to prevent abuse');
                            break;
                        case 'cors_policy':
                            report.recommendations.push('Configure CORS policy for security');
                            break;
                        default:
                            report.recommendations.push(`Review and fix ${test.replace('_', ' ')} implementation`);
                    }
                }
            });

            const reportWindow = window.open('', '_blank');
            reportWindow.document.write(`
                <html>
                    <head><title>RESTROFLOW Security Report</title></head>
                    <body>
                        <h1>RESTROFLOW Security Test Report</h1>
                        <pre>${JSON.stringify(report, null, 2)}</pre>
                    </body>
                </html>
            `);
        }

        function clearAllTests() {
            testResults = { total: 0, passed: 0, failed: 0, tests: {} };
            updateTestSummary();

            // Reset all test result displays
            const sections = ['authResults', 'apiResults', 'performanceResults', 'integrationResults'];
            sections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                const badges = section.querySelectorAll('.badge');
                badges.forEach(badge => {
                    badge.textContent = 'Not Tested';
                    badge.className = 'badge badge-info';
                });
            });

            // Hide response sections
            const responses = ['authResponse', 'apiResponse', 'performanceResponse', 'integrationResponse'];
            responses.forEach(responseId => {
                document.getElementById(responseId).style.display = 'none';
            });
        }
    </script>
</body>
</html>

        // Test rate limiting
        async function testRateLimit() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'Rate Limiting', 'Testing...', 'warning');

            const requests = [];
            const startTime = Date.now();

            // Send 50 rapid requests
            for (let i = 0; i < 50; i++) {
                requests.push(
                    fetch(`${API_BASE}/health`)
                        .then(response => ({
                            request: i + 1,
                            status: response.status,
                            rateLimited: response.status === 429
                        }))
                        .catch(error => ({
                            request: i + 1,
                            error: error.message
                        }))
                );
            }

            try {
                const results = await Promise.all(requests);
                const endTime = Date.now();
                const rateLimitTriggered = results.some(r => r.rateLimited);

                const summary = {
                    totalRequests: results.length,
                    rateLimitTriggered,
                    totalTime: endTime - startTime,
                    requestsPerSecond: Math.round((results.length / (endTime - startTime)) * 1000),
                    results: results.slice(0, 10) // Show first 10 results
                };

                showResponse('apiResponse', 'Rate Limiting Test Results', JSON.stringify(summary, null, 2), rateLimitTriggered ? 'success' : 'warning');
                updateTestResult('apiResults', 'Rate Limiting', rateLimitTriggered ? 'Active' : 'Not Detected', rateLimitTriggered ? 'success' : 'warning');
                recordTest('rate_limiting', rateLimitTriggered);
            } catch (error) {
                showResponse('apiResponse', 'Rate Limiting Test Error', error.message, 'error');
                updateTestResult('apiResults', 'Rate Limiting', 'Error', 'error');
                recordTest('rate_limiting', false);
            }
        }

        // Test CORS policy
        async function testCORS() {
            showLoading('apiResponse');
            updateTestResult('apiResults', 'CORS Policy', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'OPTIONS'
                });

                const corsHeaders = {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                };

                const corsConfigured = Object.values(corsHeaders).some(header => header !== null);

                showResponse('apiResponse', 'CORS Policy Test Results', JSON.stringify(corsHeaders, null, 2), corsConfigured ? 'success' : 'warning');
                updateTestResult('apiResults', 'CORS Policy', corsConfigured ? 'Configured' : 'Not Configured', corsConfigured ? 'success' : 'warning');
                recordTest('cors_policy', corsConfigured);
            } catch (error) {
                showResponse('apiResponse', 'CORS Policy Test Error', error.message, 'error');
                updateTestResult('apiResults', 'CORS Policy', 'Error', 'error');
                recordTest('cors_policy', false);
            }
        }

        // Test performance metrics
        async function testPerformanceMetrics() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first to test performance metrics', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Performance Metrics', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/performance/stats`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResponse('performanceResponse', 'Performance Metrics Retrieved', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Performance Metrics', 'Available', 'success');
                    recordTest('performance_metrics', true);
                } else {
                    showResponse('performanceResponse', 'Performance Metrics Failed', JSON.stringify(data, null, 2), 'error');
                    updateTestResult('performanceResults', 'Performance Metrics', 'Failed', 'error');
                    recordTest('performance_metrics', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Performance Metrics Error', error.message, 'error');
                updateTestResult('performanceResults', 'Performance Metrics', 'Error', 'error');
                recordTest('performance_metrics', false);
            }
        }

        // Test monitoring alerts
        async function testMonitoringAlerts() {
            if (!authToken) {
                showResponse('performanceResponse', 'Authentication Required', 'Please login first to test monitoring alerts', 'error');
                return;
            }

            showLoading('performanceResponse');
            updateTestResult('performanceResults', 'Monitoring Alerts', 'Testing...', 'warning');

            try {
                const response = await fetch(`${API_BASE}/monitoring/alerts`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                const data = await response.json();

                if (response.ok) {
                    showResponse('performanceResponse', 'Monitoring Alerts Retrieved', JSON.stringify(data, null, 2), 'success');
                    updateTestResult('performanceResults', 'Monitoring Alerts', 'Available', 'success');
                    recordTest('monitoring_alerts', true);
                } else {
                    showResponse('performanceResponse', 'Monitoring Alerts Failed', JSON.stringify(data, null, 2), 'error');
                    updateTestResult('performanceResults', 'Monitoring Alerts', 'Failed', 'error');
                    recordTest('monitoring_alerts', false);
                }
            } catch (error) {
                showResponse('performanceResponse', 'Monitoring Alerts Error', error.message, 'error');
                updateTestResult('performanceResults', 'Monitoring Alerts', 'Error', 'error');
                recordTest('monitoring_alerts', false);
            }
        }