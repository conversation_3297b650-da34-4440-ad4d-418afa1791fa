import React, { useState, useEffect } from 'react';
import { EnhancedAppProvider } from './context/EnhancedAppContext';
import RestructuredIndustryPOS from './components/RestructuredIndustryPOS';

const DirectRestructuredEntry: React.FC = () => {
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Force set localStorage flags
    localStorage.setItem('useRestructuredPOS', 'true');
    localStorage.setItem('useIndustryStandardPOS', 'true');
    
    // Check for dark mode
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
    
    console.log('🚀 DIRECT RESTRUCTURED ENTRY LOADED');
    console.log('This bypasses all routing logic and loads the restructured interface directly');
  }, []);

  const handleThemeToggle = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  return (
    <EnhancedAppProvider>
      <div className="direct-restructured-entry" data-direct-restructured="true">
        <RestructuredIndustryPOS
          isDarkMode={isDarkMode}
          onThemeToggle={handleThemeToggle}
        />
      </div>
    </EnhancedAppProvider>
  );
};

export default DirectRestructuredEntry;
