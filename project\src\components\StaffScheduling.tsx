import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { Shift, Schedule, Employee } from '../types';
import { Calendar, Clock, User, Plus, Edit3, Save, X, Check } from 'lucide-react';
import { v4 as uuidv4 } from 'uuid';

const StaffScheduling: React.FC = () => {
  const { state, fetchSchedules, addSchedule, updateSchedule } = useAppContext();
  const [currentWeek, setCurrentWeek] = useState(new Date());
  const [showShiftEditor, setShowShiftEditor] = useState(false);
  const [editingShift, setEditingShift] = useState<Shift | null>(null);
  const [selectedSchedule, setSelectedSchedule] = useState<Schedule | null>(null);

  // Get start of week (Monday)
  const getWeekStart = (date: Date) => {
    const d = new Date(date);
    const day = d.getDay();
    const diff = d.getDate() - day + (day === 0 ? -6 : 1);
    return new Date(d.setDate(diff));
  };

  // Get week dates
  const getWeekDates = (startDate: Date) => {
    const dates = [];
    for (let i = 0; i < 7; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      dates.push(date);
    }
    return dates;
  };

  const weekStart = getWeekStart(currentWeek);
  const weekDates = getWeekDates(weekStart);
  const weekStartString = weekStart.toISOString().split('T')[0];

  // Find current week's schedule
  const currentSchedule = state.schedules.find(s => s.weekStartDate === weekStartString);

  useEffect(() => {
    fetchSchedules();
  }, []);

  const positions = ['bartender', 'server', 'kitchen', 'manager'];
  const timeSlots = [
    '09:00', '10:00', '11:00', '12:00', '13:00', '14:00', '15:00', '16:00',
    '17:00', '18:00', '19:00', '20:00', '21:00', '22:00', '23:00'
  ];

  const getShiftsForDay = (date: Date) => {
    if (!currentSchedule) return [];
    const dateString = date.toISOString().split('T')[0];
    return currentSchedule.shifts.filter(shift => shift.date === dateString);
  };

  const getShiftColor = (position: string) => {
    switch (position) {
      case 'bartender':
        return 'bg-purple-600';
      case 'server':
        return 'bg-blue-600';
      case 'kitchen':
        return 'bg-green-600';
      case 'manager':
        return 'bg-red-600';
      default:
        return 'bg-gray-600';
    }
  };

  const handleAddShift = (date: Date) => {
    const newShift: Shift = {
      id: uuidv4(),
      employeeId: '',
      employeeName: '',
      date: date.toISOString().split('T')[0],
      startTime: '09:00',
      endTime: '17:00',
      position: 'server',
      status: 'scheduled'
    };
    setEditingShift(newShift);
    setShowShiftEditor(true);
  };

  const handleEditShift = (shift: Shift) => {
    setEditingShift(shift);
    setShowShiftEditor(true);
  };

  const handleSaveShift = async () => {
    if (!editingShift) return;

    try {
      let schedule = currentSchedule;
      
      if (!schedule) {
        // Create new schedule for this week
        schedule = {
          id: uuidv4(),
          weekStartDate: weekStartString,
          shifts: [],
          published: false,
          createdBy: state.currentEmployee?.id || ''
        };
      }

      const existingShiftIndex = schedule.shifts.findIndex(s => s.id === editingShift.id);
      
      if (existingShiftIndex >= 0) {
        // Update existing shift
        schedule.shifts[existingShiftIndex] = editingShift;
      } else {
        // Add new shift
        schedule.shifts.push(editingShift);
      }

      if (currentSchedule) {
        await updateSchedule(schedule);
      } else {
        await addSchedule(schedule);
      }

      setShowShiftEditor(false);
      setEditingShift(null);
    } catch (error) {
      console.error('Failed to save shift:', error);
    }
  };

  const handleDeleteShift = async (shiftId: string) => {
    if (!currentSchedule) return;

    try {
      const updatedSchedule = {
        ...currentSchedule,
        shifts: currentSchedule.shifts.filter(s => s.id !== shiftId)
      };
      await updateSchedule(updatedSchedule);
    } catch (error) {
      console.error('Failed to delete shift:', error);
    }
  };

  const handlePublishSchedule = async () => {
    if (!currentSchedule) return;

    try {
      const updatedSchedule = {
        ...currentSchedule,
        published: true
      };
      await updateSchedule(updatedSchedule);
    } catch (error) {
      console.error('Failed to publish schedule:', error);
    }
  };

  const ShiftEditor: React.FC = () => {
    if (!editingShift) return null;

    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-gray-800 rounded-lg p-6 w-96 max-h-[90vh] overflow-y-auto">
          <h3 className="text-xl font-semibold text-white mb-4">
            {editingShift.id ? 'Edit Shift' : 'Add Shift'}
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Employee
              </label>
              <select
                value={editingShift.employeeId}
                onChange={(e) => {
                  const employee = state.employees.find(emp => emp.id === e.target.value);
                  setEditingShift({
                    ...editingShift,
                    employeeId: e.target.value,
                    employeeName: employee?.name || ''
                  });
                }}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="">Select Employee</option>
                {state.employees.map(employee => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name} ({employee.role})
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Position
              </label>
              <select
                value={editingShift.position}
                onChange={(e) => setEditingShift({ ...editingShift, position: e.target.value })}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                {positions.map(position => (
                  <option key={position} value={position}>
                    {position.charAt(0).toUpperCase() + position.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  Start Time
                </label>
                <select
                  value={editingShift.startTime}
                  onChange={(e) => setEditingShift({ ...editingShift, startTime: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {timeSlots.map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-gray-300 text-sm font-medium mb-2">
                  End Time
                </label>
                <select
                  value={editingShift.endTime}
                  onChange={(e) => setEditingShift({ ...editingShift, endTime: e.target.value })}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                >
                  {timeSlots.map(time => (
                    <option key={time} value={time}>{time}</option>
                  ))}
                </select>
              </div>
            </div>

            <div>
              <label className="block text-gray-300 text-sm font-medium mb-2">
                Notes
              </label>
              <textarea
                value={editingShift.notes || ''}
                onChange={(e) => setEditingShift({ ...editingShift, notes: e.target.value })}
                className="w-full bg-gray-700 text-white px-3 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                rows={3}
                placeholder="Optional notes..."
              />
            </div>
          </div>

          <div className="flex justify-end space-x-3 mt-6">
            <button
              onClick={() => {
                setShowShiftEditor(false);
                setEditingShift(null);
              }}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
            <button
              onClick={handleSaveShift}
              disabled={!editingShift.employeeId}
              className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Save
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex justify-between items-center p-4 bg-gray-800 rounded-lg mb-4">
        <div className="flex items-center space-x-4">
          <h2 className="text-xl font-semibold text-white">Staff Schedule</h2>
          <div className="flex items-center space-x-2">
            <button
              onClick={() => {
                const prevWeek = new Date(currentWeek);
                prevWeek.setDate(prevWeek.getDate() - 7);
                setCurrentWeek(prevWeek);
              }}
              className="px-3 py-1 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              ←
            </button>
            <span className="text-white font-medium">
              {weekStart.toLocaleDateString()} - {weekDates[6].toLocaleDateString()}
            </span>
            <button
              onClick={() => {
                const nextWeek = new Date(currentWeek);
                nextWeek.setDate(nextWeek.getDate() + 7);
                setCurrentWeek(nextWeek);
              }}
              className="px-3 py-1 bg-gray-700 text-white rounded-md hover:bg-gray-600 transition-colors"
            >
              →
            </button>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          {currentSchedule && !currentSchedule.published && (
            <button
              onClick={handlePublishSchedule}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
            >
              <Check className="h-4 w-4" />
              <span>Publish Schedule</span>
            </button>
          )}
          {currentSchedule?.published && (
            <span className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-md">
              <Check className="h-4 w-4" />
              <span>Published</span>
            </span>
          )}
        </div>
      </div>

      {/* Schedule Grid */}
      <div className="flex-grow bg-gray-800 rounded-lg overflow-hidden">
        <div className="grid grid-cols-8 h-full">
          {/* Time column */}
          <div className="bg-gray-900 border-r border-gray-700">
            <div className="h-12 border-b border-gray-700 flex items-center justify-center">
              <Clock className="h-4 w-4 text-gray-400" />
            </div>
            {timeSlots.map(time => (
              <div key={time} className="h-16 border-b border-gray-700 flex items-center justify-center text-gray-400 text-sm">
                {time}
              </div>
            ))}
          </div>

          {/* Day columns */}
          {weekDates.map((date, dayIndex) => (
            <div key={dayIndex} className="border-r border-gray-700 last:border-r-0">
              {/* Day header */}
              <div className="h-12 border-b border-gray-700 flex flex-col items-center justify-center bg-gray-900">
                <div className="text-white font-medium text-sm">
                  {date.toLocaleDateString('en-US', { weekday: 'short' })}
                </div>
                <div className="text-gray-400 text-xs">
                  {date.getDate()}
                </div>
              </div>

              {/* Time slots */}
              <div className="relative">
                {timeSlots.map((time, timeIndex) => (
                  <div
                    key={time}
                    className="h-16 border-b border-gray-700 hover:bg-gray-700 cursor-pointer group"
                    onClick={() => handleAddShift(date)}
                  >
                    <div className="opacity-0 group-hover:opacity-100 flex items-center justify-center h-full">
                      <Plus className="h-4 w-4 text-gray-400" />
                    </div>
                  </div>
                ))}

                {/* Shifts for this day */}
                {getShiftsForDay(date).map(shift => {
                  const startHour = parseInt(shift.startTime.split(':')[0]);
                  const endHour = parseInt(shift.endTime.split(':')[0]);
                  const startMinute = parseInt(shift.startTime.split(':')[1]);
                  const endMinute = parseInt(shift.endTime.split(':')[1]);
                  
                  const startSlot = timeSlots.findIndex(t => parseInt(t.split(':')[0]) === startHour);
                  const duration = (endHour - startHour) + ((endMinute - startMinute) / 60);
                  
                  return (
                    <div
                      key={shift.id}
                      className={`absolute left-1 right-1 ${getShiftColor(shift.position)} rounded text-white text-xs p-1 cursor-pointer hover:opacity-90 transition-opacity`}
                      style={{
                        top: `${startSlot * 64 + 2}px`,
                        height: `${duration * 64 - 4}px`,
                        minHeight: '60px'
                      }}
                      onClick={() => handleEditShift(shift)}
                    >
                      <div className="font-medium truncate">{shift.employeeName}</div>
                      <div className="opacity-75 truncate">{shift.position}</div>
                      <div className="opacity-75">{shift.startTime}-{shift.endTime}</div>
                      {shift.status !== 'scheduled' && (
                        <div className="text-xs opacity-75 capitalize">{shift.status}</div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Legend */}
      <div className="mt-4 p-4 bg-gray-800 rounded-lg">
        <div className="flex flex-wrap gap-4">
          {positions.map(position => (
            <div key={position} className="flex items-center space-x-2">
              <div className={`w-4 h-4 ${getShiftColor(position)} rounded`}></div>
              <span className="text-gray-300 text-sm capitalize">{position}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Shift Editor Modal */}
      {showShiftEditor && <ShiftEditor />}
    </div>
  );
};

export default StaffScheduling;
