/* Enhanced POS System Custom Styles */

/* Global Improvements */
* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  line-height: 1.5;
}

/* Custom Scrollbar */
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Enhanced Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

@keyframes blob {
  0% {
    transform: translate(0px, 0px) scale(1);
  }
  33% {
    transform: translate(30px, -50px) scale(1.1);
  }
  66% {
    transform: translate(-20px, 20px) scale(0.9);
  }
  100% {
    transform: translate(0px, 0px) scale(1);
  }
}

/* Animation Classes */
.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce-custom {
  animation: bounce 1s infinite;
}

.animate-blob {
  animation: blob 7s infinite;
}

.animation-delay-2000 {
  animation-delay: 2s;
}

.animation-delay-4000 {
  animation-delay: 4s;
}

.animation-delay-6000 {
  animation-delay: 6s;
}

/* Enhanced Button Styles */
.btn-primary {
  background: linear-gradient(to right, #2563eb, #9333ea);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
}

.btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #7c3aed);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-primary:active {
  transform: scale(0.95);
}

.btn-secondary {
  background: white;
  color: #374151;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transform: scale(1);
  transition: all 0.2s;
}

.btn-secondary:hover {
  background: #f9fafb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-secondary:active {
  transform: scale(0.95);
}

.btn-success {
  background: linear-gradient(to right, #059669, #047857);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
}

.btn-success:hover {
  background: linear-gradient(to right, #047857, #065f46);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-success:active {
  transform: scale(0.95);
}

.btn-danger {
  background: linear-gradient(to right, #dc2626, #ec4899);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
}

.btn-danger:hover {
  background: linear-gradient(to right, #b91c1c, #db2777);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-danger:active {
  transform: scale(0.95);
}

/* Enhanced Card Styles */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
}

.card-body {
  padding: 1rem;
}

.card-footer {
  @apply p-4 border-t border-gray-200 bg-gray-50;
}

/* Enhanced Form Styles */
.form-input {
  @apply w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 bg-white;
}

.form-input:focus {
  @apply shadow-md;
}

.form-label {
  @apply block text-sm font-medium text-gray-700 mb-2;
}

.form-error {
  @apply text-sm text-red-600 mt-1;
}

/* Enhanced Status Indicators */
.status-online {
  @apply flex items-center space-x-2 text-green-700;
}

.status-offline {
  @apply flex items-center space-x-2 text-red-700;
}

.status-loading {
  @apply flex items-center space-x-2 text-yellow-700;
}

.status-dot {
  @apply w-2 h-2 rounded-full;
}

.status-dot-online {
  @apply bg-green-400 animate-pulse;
}

.status-dot-offline {
  @apply bg-red-400;
}

.status-dot-loading {
  @apply bg-yellow-400 animate-pulse;
}

/* Enhanced Navigation */
.nav-tab {
  @apply flex items-center space-x-2 py-3 px-4 border-b-2 font-medium text-sm transition-all duration-200 whitespace-nowrap cursor-pointer;
}

.nav-tab-active {
  @apply border-blue-500 text-blue-600 bg-blue-50;
}

.nav-tab-inactive {
  @apply border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 hover:bg-gray-50;
}

/* Enhanced Modals */
.modal-overlay {
  @apply fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50;
}

.modal-content {
  @apply bg-white rounded-xl shadow-2xl max-w-md w-full mx-4 overflow-hidden;
}

.modal-header {
  @apply p-6 border-b border-gray-200;
}

.modal-body {
  @apply p-6;
}

.modal-footer {
  @apply p-6 border-t border-gray-200 bg-gray-50;
}

/* Enhanced Tables */
.table {
  @apply w-full border-collapse;
}

.table th {
  @apply px-6 py-3 border-b border-gray-200 bg-gray-50 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
}

.table td {
  @apply px-6 py-4 border-b border-gray-200 text-sm text-gray-900;
}

.table tr:hover {
  @apply bg-gray-50;
}

/* Enhanced Loading States */
.loading-spinner {
  @apply inline-block w-4 h-4 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin;
}

.loading-skeleton {
  @apply bg-gray-200 rounded animate-pulse;
}

/* Enhanced Tooltips */
.tooltip {
  @apply absolute z-10 px-3 py-2 text-sm font-medium text-white bg-gray-900 rounded-lg shadow-sm opacity-0 transition-opacity duration-300;
}

.tooltip-arrow {
  @apply absolute w-2 h-2 bg-gray-900 transform rotate-45;
}

/* Enhanced Badges */
.badge {
  @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
}

.badge-primary {
  @apply bg-blue-100 text-blue-800;
}

.badge-success {
  @apply bg-green-100 text-green-800;
}

.badge-warning {
  @apply bg-yellow-100 text-yellow-800;
}

.badge-danger {
  @apply bg-red-100 text-red-800;
}

.badge-info {
  @apply bg-indigo-100 text-indigo-800;
}

/* Responsive Design Improvements */
@media (max-width: 768px) {
  .nav-tab span:not(.text-base) {
    @apply hidden;
  }
  
  .card {
    @apply mx-2;
  }
  
  .modal-content {
    @apply mx-2;
  }
}

/* Print Styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-only {
    display: block !important;
  }
}

/* Dark Mode Support (Future Enhancement) */
@media (prefers-color-scheme: dark) {
  .dark-mode {
    @apply bg-gray-900 text-white;
  }
  
  .dark-mode .card {
    @apply bg-gray-800 border-gray-700;
  }
  
  .dark-mode .form-input {
    @apply bg-gray-800 border-gray-600 text-white;
  }
}
