import React, { useState } from 'react';

const SimpleGlobalCurrency: React.FC = () => {
  const [fromCurrency, setFromCurrency] = useState('USD');
  const [toCurrency, setToCurrency] = useState('EUR');
  const [amount, setAmount] = useState('100');
  const [convertedAmount, setConvertedAmount] = useState('87.10');

  const currencies = [
    { code: 'USD', name: 'US Dollar', flag: '🇺🇸' },
    { code: 'EUR', name: 'Euro', flag: '🇪🇺' },
    { code: 'GBP', name: 'British Pound', flag: '🇬🇧' },
    { code: 'JPY', name: 'Japanese Yen', flag: '🇯🇵' },
    { code: 'CAD', name: 'Canadian Dollar', flag: '🇨🇦' },
    { code: 'AUD', name: 'Australian Dollar', flag: '🇦🇺' }
  ];

  const exchangeRates = {
    'USD-EUR': 0.871,
    'USD-GBP': 0.731,
    'USD-JPY': 110.0,
    'USD-CAD': 1.25,
    'USD-AUD': 1.35
  };

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-purple-50 to-pink-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            💱 Global Currency Manager
          </h1>
          <p className="text-gray-600 mt-2">
            Real-time currency conversion and multi-currency payment processing
          </p>
        </div>
        <div className="flex gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✅ Live Rates
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🌍 15+ Currencies
          </span>
        </div>
      </div>

      {/* Currency Converter */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Currency Converter</h3>
          </div>
          <div className="px-6 py-4 space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Amount</label>
              <input
                type="number"
                value={amount}
                onChange={(e) => setAmount(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Enter amount"
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">From</label>
                <select
                  value={fromCurrency}
                  onChange={(e) => setFromCurrency(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {currencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.flag} {currency.code}
                    </option>
                  ))}
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">To</label>
                <select
                  value={toCurrency}
                  onChange={(e) => setToCurrency(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {currencies.map((currency) => (
                    <option key={currency.code} value={currency.code}>
                      {currency.flag} {currency.code}
                    </option>
                  ))}
                </select>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {convertedAmount} {toCurrency}
                </div>
                <div className="text-sm text-gray-600 mt-1">
                  {amount} {fromCurrency} = {convertedAmount} {toCurrency}
                </div>
                <div className="text-xs text-gray-500 mt-1">
                  Rate: 1 {fromCurrency} = 0.871 {toCurrency}
                </div>
              </div>
            </div>

            <button className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
              🔄 Convert
            </button>
          </div>
        </div>

        {/* Exchange Rates */}
        <div className="bg-white rounded-lg shadow-md border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Live Exchange Rates</h3>
          </div>
          <div className="px-6 py-4">
            <div className="space-y-3">
              {Object.entries(exchangeRates).map(([pair, rate]) => {
                const [from, to] = pair.split('-');
                const fromCurrency = currencies.find(c => c.code === from);
                const toCurrency = currencies.find(c => c.code === to);
                
                return (
                  <div key={pair} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      <span>{fromCurrency?.flag}</span>
                      <span className="font-medium">{from}</span>
                      <span className="text-gray-400">→</span>
                      <span>{toCurrency?.flag}</span>
                      <span className="font-medium">{to}</span>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">{rate}</div>
                      <div className="text-xs text-gray-500">+0.5%</div>
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Global Payment Methods */}
      <div className="bg-white rounded-lg shadow-md border border-gray-200">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Global Payment Methods</h3>
        </div>
        <div className="px-6 py-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="p-4 bg-blue-50 rounded-lg text-center">
              <div className="text-2xl mb-2">💳</div>
              <div className="font-medium text-gray-900">Credit Cards</div>
              <div className="text-sm text-gray-600">Visa, Mastercard, Amex</div>
              <div className="text-xs text-green-600 mt-1">✅ Available</div>
            </div>
            
            <div className="p-4 bg-green-50 rounded-lg text-center">
              <div className="text-2xl mb-2">📱</div>
              <div className="font-medium text-gray-900">Digital Wallets</div>
              <div className="text-sm text-gray-600">Apple Pay, Google Pay</div>
              <div className="text-xs text-green-600 mt-1">✅ Available</div>
            </div>
            
            <div className="p-4 bg-purple-50 rounded-lg text-center">
              <div className="text-2xl mb-2">🏦</div>
              <div className="font-medium text-gray-900">Bank Transfers</div>
              <div className="text-sm text-gray-600">SEPA, ACH, Wire</div>
              <div className="text-xs text-green-600 mt-1">✅ Available</div>
            </div>
          </div>
        </div>
      </div>

      {/* Regional Compliance */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">🇪🇺 GDPR Compliance</h4>
          <p className="text-sm text-gray-600">European data protection</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ✅ Compliant
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">🇺🇸 CCPA Compliance</h4>
          <p className="text-sm text-gray-600">California privacy protection</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ✅ Compliant
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">🇨🇦 PIPEDA Compliance</h4>
          <p className="text-sm text-gray-600">Canadian privacy protection</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              ✅ Compliant
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleGlobalCurrency;
