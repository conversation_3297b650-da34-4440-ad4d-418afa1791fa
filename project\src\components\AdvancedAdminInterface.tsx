import React, { useState, useEffect } from 'react';
import {
  Settings,
  Shield,
  Users,
  Database,
  Activity,
  Bell,
  Lock,
  Key,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Save,
  RefreshCw,
  Download,
  Upload,
  Trash2,
  Edit,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Globe,
  Smartphone,
  Mail,
  Phone
} from 'lucide-react';

interface SecuritySettings {
  rateLimiting: {
    loginWindow: number;
    loginMaxAttempts: number;
    adminWindow: number;
    adminMaxAttempts: number;
    blockDuration: number;
  };
  mfa: {
    required: boolean;
    requiredRoles: string[];
    otpExpiry: number;
    backupCodeLength: number;
  };
  audit: {
    retentionDays: number;
    batchSize: number;
    flushInterval: number;
    enableRealTime: boolean;
  };
  emergency: {
    masterKeyEnabled: boolean;
    adminOverrideEnabled: boolean;
    offlineModeEnabled: boolean;
    supportContactEnabled: boolean;
  };
}

interface UserPermission {
  id: string;
  name: string;
  description: string;
  category: string;
  enabled: boolean;
}

interface SystemConfiguration {
  general: {
    systemName: string;
    version: string;
    environment: string;
    timezone: string;
    locale: string;
  };
  database: {
    host: string;
    port: number;
    name: string;
    poolSize: number;
    connectionTimeout: number;
  };
  email: {
    service: string;
    host: string;
    port: number;
    secure: boolean;
    from: string;
  };
  sms: {
    service: string;
    enabled: boolean;
    fromNumber: string;
  };
}

const AdvancedAdminInterface: React.FC = () => {
  const [activeTab, setActiveTab] = useState('security');
  const [securitySettings, setSecuritySettings] = useState<SecuritySettings | null>(null);
  const [systemConfig, setSystemConfig] = useState<SystemConfiguration | null>(null);
  const [userPermissions, setUserPermissions] = useState<UserPermission[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadAdminData();
  }, []);

  const loadAdminData = async () => {
    try {
      setIsLoading(true);

      // Load security settings
      const securityResponse = await fetch('http://localhost:4000/api/admin/security/settings', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (securityResponse.ok) {
        const securityData = await securityResponse.json();
        setSecuritySettings(securityData);
      }

      // Load system configuration
      const configResponse = await fetch('http://localhost:4000/api/admin/system/config', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (configResponse.ok) {
        const configData = await configResponse.json();
        setSystemConfig(configData);
      }

      // Load user permissions
      const permissionsResponse = await fetch('http://localhost:4000/api/admin/permissions', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (permissionsResponse.ok) {
        const permissionsData = await permissionsResponse.json();
        setUserPermissions(permissionsData);
      }

    } catch (error) {
      console.error('Error loading admin data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveSecuritySettings = async () => {
    if (!securitySettings) return;

    try {
      setIsSaving(true);

      const response = await fetch('http://localhost:4000/api/admin/security/settings', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(securitySettings)
      });

      if (response.ok) {
        console.log('✅ Security settings saved successfully');
      } else {
        console.error('❌ Failed to save security settings');
      }
    } catch (error) {
      console.error('Error saving security settings:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const saveSystemConfig = async () => {
    if (!systemConfig) return;

    try {
      setIsSaving(true);

      const response = await fetch('http://localhost:4000/api/admin/system/config', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(systemConfig)
      });

      if (response.ok) {
        console.log('✅ System configuration saved successfully');
      } else {
        console.error('❌ Failed to save system configuration');
      }
    } catch (error) {
      console.error('Error saving system configuration:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const updatePermission = async (permissionId: string, enabled: boolean) => {
    try {
      const response = await fetch(`http://localhost:4000/api/admin/permissions/${permissionId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        setUserPermissions(permissions =>
          permissions.map(p =>
            p.id === permissionId ? { ...p, enabled } : p
          )
        );
      }
    } catch (error) {
      console.error('Error updating permission:', error);
    }
  };

  const exportConfiguration = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/admin/export/config', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `restroflow-config-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting configuration:', error);
    }
  };

  const filteredPermissions = userPermissions.filter(permission => {
    const matchesSearch = permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         permission.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || permission.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  const permissionCategories = [...new Set(userPermissions.map(p => p.category))];

  const tabs = [
    { id: 'security', name: 'Security Settings', icon: Shield },
    { id: 'system', name: 'System Configuration', icon: Settings },
    { id: 'permissions', name: 'User Permissions', icon: Users },
    { id: 'monitoring', name: 'Monitoring & Alerts', icon: Activity },
    { id: 'backup', name: 'Backup & Recovery', icon: Database }
  ];

  if (isLoading) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <RefreshCw className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading admin interface...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>

      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Advanced Admin Interface
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Manage security settings, system configuration, and user permissions
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={exportConfiguration}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export Config</span>
              </button>

              <button
                onClick={loadAdminData}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
                }`}
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Tab Navigation */}
        <div className="mb-8">
          <div className="border-b border-gray-200 dark:border-gray-700">
            <nav className="-mb-px flex space-x-8">
              {tabs.map((tab) => {
                const Icon = tab.icon;
                return (
                  <button
                    key={tab.id}
                    onClick={() => setActiveTab(tab.id)}
                    className={`flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm transition-colors ${
                      activeTab === tab.id
                        ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{tab.name}</span>
                  </button>
                );
              })}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        <div className="space-y-6">

          {/* Security Settings Tab */}
          {activeTab === 'security' && securitySettings && (
            <div className="space-y-6">

              {/* Rate Limiting */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <h3 className={`text-lg font-semibold mb-4 ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Rate Limiting Configuration
                </h3>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-700'
                    }`}>
                      Login Window (ms)
                    </label>
                    <input
                      type="number"
                      value={securitySettings.rateLimiting.loginWindow}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        rateLimiting: {
                          ...securitySettings.rateLimiting,
                          loginWindow: parseInt(e.target.value)
                        }
                      })}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-700'
                    }`}>
                      Max Login Attempts
                    </label>
                    <input
                      type="number"
                      value={securitySettings.rateLimiting.loginMaxAttempts}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        rateLimiting: {
                          ...securitySettings.rateLimiting,
                          loginMaxAttempts: parseInt(e.target.value)
                        }
                      })}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>

                  <div>
                    <label className={`block text-sm font-medium mb-2 ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-700'
                    }`}>
                      Block Duration (ms)
                    </label>
                    <input
                      type="number"
                      value={securitySettings.rateLimiting.blockDuration}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        rateLimiting: {
                          ...securitySettings.rateLimiting,
                          blockDuration: parseInt(e.target.value)
                        }
                      })}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>
                </div>
              </div>

              {/* MFA Settings */}
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <h3 className={`text-lg font-semibold mb-4 ${
                  isDarkMode ? 'text-white' : 'text-gray-900'
                }`}>
                  Multi-Factor Authentication
                </h3>

                <div className="space-y-4">
                  <div className="flex items-center">
                    <input
                      type="checkbox"
                      id="mfa-required"
                      checked={securitySettings.mfa.required}
                      onChange={(e) => setSecuritySettings({
                        ...securitySettings,
                        mfa: {
                          ...securitySettings.mfa,
                          required: e.target.checked
                        }
                      })}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    />
                    <label htmlFor="mfa-required" className={`ml-2 text-sm ${
                      isDarkMode ? 'text-gray-200' : 'text-gray-700'
                    }`}>
                      Require MFA for all users
                    </label>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-700'
                      }`}>
                        OTP Expiry (ms)
                      </label>
                      <input
                        type="number"
                        value={securitySettings.mfa.otpExpiry}
                        onChange={(e) => setSecuritySettings({
                          ...securitySettings,
                          mfa: {
                            ...securitySettings.mfa,
                            otpExpiry: parseInt(e.target.value)
                          }
                        })}
                        className={`w-full px-3 py-2 border rounded-lg ${
                          isDarkMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      />
                    </div>

                    <div>
                      <label className={`block text-sm font-medium mb-2 ${
                        isDarkMode ? 'text-gray-200' : 'text-gray-700'
                      }`}>
                        Backup Code Length
                      </label>
                      <input
                        type="number"
                        value={securitySettings.mfa.backupCodeLength}
                        onChange={(e) => setSecuritySettings({
                          ...securitySettings,
                          mfa: {
                            ...securitySettings.mfa,
                            backupCodeLength: parseInt(e.target.value)
                          }
                        })}
                        className={`w-full px-3 py-2 border rounded-lg ${
                          isDarkMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      />
                    </div>
                  </div>
                </div>
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <button
                  onClick={saveSecuritySettings}
                  disabled={isSaving}
                  className="flex items-center space-x-2 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {isSaving ? (
                    <RefreshCw className="w-4 h-4 animate-spin" />
                  ) : (
                    <Save className="w-4 h-4" />
                  )}
                  <span>{isSaving ? 'Saving...' : 'Save Security Settings'}</span>
                </button>
              </div>
            </div>
          )}

          {/* User Permissions Tab */}
          {activeTab === 'permissions' && (
            <div className="space-y-6">

              {/* Search and Filter */}
              <div className={`p-4 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex flex-col sm:flex-row gap-4">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                      <input
                        type="text"
                        placeholder="Search permissions..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                          isDarkMode
                            ? 'bg-gray-700 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                      />
                    </div>
                  </div>

                  <div className="sm:w-48">
                    <select
                      value={selectedCategory}
                      onChange={(e) => setSelectedCategory(e.target.value)}
                      className={`w-full px-3 py-2 border rounded-lg ${
                        isDarkMode
                          ? 'bg-gray-700 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    >
                      <option value="all">All Categories</option>
                      {permissionCategories.map(category => (
                        <option key={category} value={category}>
                          {category.charAt(0).toUpperCase() + category.slice(1)}
                        </option>
                      ))}
                    </select>
                  </div>
                </div>
              </div>

              {/* Permissions List */}
              <div className={`rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg overflow-hidden`}>
                <div className="divide-y divide-gray-200 dark:divide-gray-700">
                  {filteredPermissions.map((permission) => (
                    <div key={permission.id} className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3">
                            <h4 className={`font-medium ${
                              isDarkMode ? 'text-white' : 'text-gray-900'
                            }`}>
                              {permission.name}
                            </h4>
                            <span className={`px-2 py-1 text-xs rounded-full ${
                              isDarkMode
                                ? 'bg-gray-700 text-gray-300'
                                : 'bg-gray-100 text-gray-600'
                            }`}>
                              {permission.category}
                            </span>
                          </div>
                          <p className={`text-sm mt-1 ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {permission.description}
                          </p>
                        </div>

                        <div className="flex items-center space-x-3">
                          <span className={`text-sm ${
                            permission.enabled
                              ? 'text-green-600 dark:text-green-400'
                              : 'text-red-600 dark:text-red-400'
                          }`}>
                            {permission.enabled ? 'Enabled' : 'Disabled'}
                          </span>

                          <button
                            onClick={() => updatePermission(permission.id, !permission.enabled)}
                            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                              permission.enabled
                                ? 'bg-blue-600'
                                : 'bg-gray-200 dark:bg-gray-700'
                            }`}
                          >
                            <span
                              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                                permission.enabled ? 'translate-x-6' : 'translate-x-1'
                              }`}
                            />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AdvancedAdminInterface;