<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Enhanced Tenant Management - BARPOS Super Admin</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-badge {
            @apply px-3 py-1 rounded-full text-xs font-medium;
        }
        .status-active { @apply bg-green-100 text-green-800; }
        .status-suspended { @apply bg-red-100 text-red-800; }
        .status-trial { @apply bg-yellow-100 text-yellow-800; }
        .status-inactive { @apply bg-gray-100 text-gray-800; }
        
        .plan-badge {
            @apply px-2 py-1 rounded text-xs font-medium;
        }
        .plan-starter { @apply bg-blue-100 text-blue-800; }
        .plan-pro { @apply bg-purple-100 text-purple-800; }
        .plan-enterprise { @apply bg-orange-100 text-orange-800; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <i data-lucide="building-2" class="w-8 h-8"></i>
                        <h1 class="text-2xl font-bold">Enhanced Tenant Management</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <span class="text-sm opacity-90">Super Admin Dashboard</span>
                    <button onclick="logout()" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="log-out" class="w-4 h-4"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white rounded-xl shadow-sm p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Total Tenants</p>
                        <p class="text-2xl font-bold text-gray-900" id="total-tenants">0</p>
                    </div>
                    <div class="bg-blue-100 p-3 rounded-lg">
                        <i data-lucide="building" class="w-6 h-6 text-blue-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Tenants</p>
                        <p class="text-2xl font-bold text-green-600" id="active-tenants">0</p>
                    </div>
                    <div class="bg-green-100 p-3 rounded-lg">
                        <i data-lucide="check-circle" class="w-6 h-6 text-green-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Trial Tenants</p>
                        <p class="text-2xl font-bold text-yellow-600" id="trial-tenants">0</p>
                    </div>
                    <div class="bg-yellow-100 p-3 rounded-lg">
                        <i data-lucide="clock" class="w-6 h-6 text-yellow-600"></i>
                    </div>
                </div>
            </div>
            
            <div class="bg-white rounded-xl shadow-sm p-6 card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Monthly Revenue</p>
                        <p class="text-2xl font-bold text-purple-600" id="monthly-revenue">$0</p>
                    </div>
                    <div class="bg-purple-100 p-3 rounded-lg">
                        <i data-lucide="dollar-sign" class="w-6 h-6 text-purple-600"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Search and Filters -->
        <div class="bg-white rounded-xl shadow-sm p-6 mb-8">
            <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0 lg:space-x-4">
                <!-- Search Bar -->
                <div class="flex-1 max-w-md">
                    <div class="relative">
                        <i data-lucide="search" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"></i>
                        <input 
                            type="text" 
                            id="search-input"
                            placeholder="Search tenants..." 
                            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        >
                    </div>
                </div>
                
                <!-- Filters -->
                <div class="flex flex-wrap gap-3">
                    <select id="status-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Status</option>
                        <option value="active">Active</option>
                        <option value="suspended">Suspended</option>
                        <option value="trial">Trial</option>
                        <option value="inactive">Inactive</option>
                    </select>
                    
                    <select id="plan-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="">All Plans</option>
                        <option value="starter">Starter</option>
                        <option value="pro">Pro</option>
                        <option value="enterprise">Enterprise</option>
                    </select>
                    
                    <select id="sort-filter" class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500">
                        <option value="created_at:DESC">Newest First</option>
                        <option value="created_at:ASC">Oldest First</option>
                        <option value="name:ASC">Name A-Z</option>
                        <option value="name:DESC">Name Z-A</option>
                        <option value="revenue_30d:DESC">Highest Revenue</option>
                    </select>
                </div>
                
                <!-- Action Buttons -->
                <div class="flex space-x-3">
                    <button onclick="openCreateTenantModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="plus" class="w-4 h-4"></i>
                        <span>Add Tenant</span>
                    </button>
                    
                    <button onclick="toggleBulkMode()" id="bulk-mode-btn" class="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="check-square" class="w-4 h-4"></i>
                        <span>Bulk Actions</span>
                    </button>
                    
                    <button onclick="exportTenants()" class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2 transition-colors">
                        <i data-lucide="download" class="w-4 h-4"></i>
                        <span>Export</span>
                    </button>
                </div>
            </div>
            
            <!-- Bulk Actions Bar (Hidden by default) -->
            <div id="bulk-actions-bar" class="hidden mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <span class="text-sm font-medium text-blue-800">
                            <span id="selected-count">0</span> tenants selected
                        </span>
                        <button onclick="selectAllTenants()" class="text-sm text-blue-600 hover:text-blue-800">Select All</button>
                        <button onclick="clearSelection()" class="text-sm text-blue-600 hover:text-blue-800">Clear Selection</button>
                    </div>
                    
                    <div class="flex space-x-2">
                        <button onclick="bulkActivate()" class="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm">
                            Activate
                        </button>
                        <button onclick="bulkSuspend()" class="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm">
                            Suspend
                        </button>
                        <button onclick="bulkUpdatePlan()" class="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm">
                            Update Plan
                        </button>
                        <button onclick="bulkExport()" class="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm">
                            Export Selected
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tenants Table -->
        <div class="bg-white rounded-xl shadow-sm overflow-hidden">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th id="bulk-select-header" class="hidden px-6 py-3 text-left">
                                <input type="checkbox" id="select-all-checkbox" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tenant</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Plan</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Metrics</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue (30d)</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Created</th>
                            <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody id="tenants-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Tenant rows will be populated here -->
                    </tbody>
                </table>
            </div>
            
            <!-- Loading State -->
            <div id="loading-state" class="flex items-center justify-center py-12">
                <div class="flex items-center space-x-2 text-gray-500">
                    <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <span>Loading tenants...</span>
                </div>
            </div>
            
            <!-- Empty State -->
            <div id="empty-state" class="hidden flex flex-col items-center justify-center py-12">
                <i data-lucide="building" class="w-12 h-12 text-gray-400 mb-4"></i>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No tenants found</h3>
                <p class="text-gray-500 mb-4">Get started by creating your first tenant.</p>
                <button onclick="openCreateTenantModal()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg">
                    Create Tenant
                </button>
            </div>
        </div>

        <!-- Pagination -->
        <div id="pagination-container" class="mt-6 flex items-center justify-between">
            <div class="text-sm text-gray-700">
                Showing <span id="showing-from">0</span> to <span id="showing-to">0</span> of <span id="total-count">0</span> results
            </div>
            <div class="flex space-x-2" id="pagination-buttons">
                <!-- Pagination buttons will be populated here -->
            </div>
        </div>
    </main>

    <!-- Modals will be added here -->
    <div id="modal-container"></div>

    <!-- Scripts -->
    <script>
        // Initialize Lucide icons
        lucide.createIcons();
        
        // Global state
        let currentPage = 1;
        let currentFilters = {};
        let selectedTenants = new Set();
        let bulkMode = false;
        let tenants = [];
        
        // Initialize the application
        document.addEventListener('DOMContentLoaded', function() {
            loadTenants();
            setupEventListeners();
        });
        
        function setupEventListeners() {
            // Search input
            document.getElementById('search-input').addEventListener('input', debounce(handleSearch, 300));
            
            // Filter changes
            document.getElementById('status-filter').addEventListener('change', handleFilterChange);
            document.getElementById('plan-filter').addEventListener('change', handleFilterChange);
            document.getElementById('sort-filter').addEventListener('change', handleFilterChange);
            
            // Select all checkbox
            document.getElementById('select-all-checkbox').addEventListener('change', handleSelectAll);
        }
        
        // Utility function for debouncing
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }
        
        // Load tenants with current filters
        async function loadTenants() {
            try {
                showLoading();
                
                const params = new URLSearchParams({
                    page: currentPage,
                    limit: 20,
                    ...currentFilters
                });
                
                const response = await fetch(`/api/admin/tenants?${params}`, {
                    headers: {
                        'Authorization': `Bearer ${localStorage.getItem('token')}`,
                        'Content-Type': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to load tenants');
                }
                
                const data = await response.json();
                
                if (data.success) {
                    tenants = data.data.tenants;
                    updateStatsOverview(data.data.summary);
                    renderTenantsTable(tenants);
                    renderPagination(data.data.pagination);
                } else {
                    // Fallback to old API format
                    tenants = data.tenants || data;
                    renderTenantsTable(tenants);
                }
                
                hideLoading();
                
            } catch (error) {
                console.error('Error loading tenants:', error);
                hideLoading();
                showError('Failed to load tenants');
            }
        }
        
        // Update stats overview
        function updateStatsOverview(summary) {
            if (summary) {
                document.getElementById('total-tenants').textContent = summary.total_tenants || 0;
                document.getElementById('active-tenants').textContent = summary.active_tenants || 0;
                document.getElementById('trial-tenants').textContent = summary.trial_tenants || 0;
            } else {
                // Calculate from tenants array
                document.getElementById('total-tenants').textContent = tenants.length;
                document.getElementById('active-tenants').textContent = tenants.filter(t => t.status === 'active').length;
                document.getElementById('trial-tenants').textContent = tenants.filter(t => t.is_trial_active).length;
            }

            // Calculate monthly revenue
            const monthlyRevenue = tenants.reduce((sum, tenant) => sum + (tenant.monthly_price || 0), 0);
            document.getElementById('monthly-revenue').textContent = `$${monthlyRevenue.toLocaleString()}`;
        }

        // Render tenants table
        function renderTenantsTable(tenants) {
            const tbody = document.getElementById('tenants-table-body');

            if (tenants.length === 0) {
                document.getElementById('empty-state').classList.remove('hidden');
                tbody.innerHTML = '';
                return;
            }

            document.getElementById('empty-state').classList.add('hidden');

            tbody.innerHTML = tenants.map(tenant => `
                <tr class="hover:bg-gray-50 transition-colors">
                    ${bulkMode ? `
                        <td class="px-6 py-4">
                            <input type="checkbox" class="tenant-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                                   value="${tenant.id}" onchange="handleTenantSelection(${tenant.id}, this.checked)">
                        </td>
                    ` : ''}
                    <td class="px-6 py-4 whitespace-nowrap">
                        <div class="flex items-center">
                            <div class="flex-shrink-0 h-10 w-10">
                                <div class="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-white font-medium">
                                    ${tenant.name.charAt(0).toUpperCase()}
                                </div>
                            </div>
                            <div class="ml-4">
                                <div class="text-sm font-medium text-gray-900">${tenant.name}</div>
                                <div class="text-sm text-gray-500">${tenant.email}</div>
                                <div class="text-xs text-gray-400">${tenant.business_type || 'Restaurant'}</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="status-badge status-${tenant.status}">
                            ${tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                        </span>
                        ${tenant.is_trial_active ? `
                            <div class="text-xs text-yellow-600 mt-1">
                                Trial: ${tenant.trial_days_left || 0} days left
                            </div>
                        ` : ''}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="plan-badge plan-${tenant.plan_type}">
                            ${tenant.plan_type.charAt(0).toUpperCase() + tenant.plan_type.slice(1)}
                        </span>
                        <div class="text-xs text-gray-500 mt-1">
                            $${tenant.monthly_price}/month
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="flex space-x-4">
                            <div class="text-center">
                                <div class="font-medium">${tenant.employee_count || 0}</div>
                                <div class="text-xs text-gray-500">Employees</div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium">${tenant.location_count || 0}</div>
                                <div class="text-xs text-gray-500">Locations</div>
                            </div>
                            <div class="text-center">
                                <div class="font-medium">${tenant.orders_30d || 0}</div>
                                <div class="text-xs text-gray-500">Orders</div>
                            </div>
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <div class="font-medium">$${(tenant.revenue_30d || 0).toLocaleString()}</div>
                        <div class="text-xs text-gray-500">
                            Avg: $${(tenant.avg_order_value || 0).toFixed(2)}
                        </div>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        ${new Date(tenant.created_at).toLocaleDateString()}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <div class="flex items-center justify-end space-x-2">
                            <button onclick="viewTenant(${tenant.id})" class="text-blue-600 hover:text-blue-900 p-1 rounded" title="View Details">
                                <i data-lucide="eye" class="w-4 h-4"></i>
                            </button>
                            <button onclick="editTenant(${tenant.id})" class="text-green-600 hover:text-green-900 p-1 rounded" title="Edit">
                                <i data-lucide="edit" class="w-4 h-4"></i>
                            </button>
                            ${tenant.status === 'active' ? `
                                <button onclick="suspendTenant(${tenant.id})" class="text-red-600 hover:text-red-900 p-1 rounded" title="Suspend">
                                    <i data-lucide="pause" class="w-4 h-4"></i>
                                </button>
                            ` : `
                                <button onclick="activateTenant(${tenant.id})" class="text-green-600 hover:text-green-900 p-1 rounded" title="Activate">
                                    <i data-lucide="play" class="w-4 h-4"></i>
                                </button>
                            `}
                            <button onclick="deleteTenant(${tenant.id})" class="text-red-600 hover:text-red-900 p-1 rounded" title="Delete">
                                <i data-lucide="trash-2" class="w-4 h-4"></i>
                            </button>
                        </div>
                    </td>
                </tr>
            `).join('');

            // Re-initialize Lucide icons for new content
            lucide.createIcons();
        }

        // Handle search
        function handleSearch(event) {
            currentFilters.search = event.target.value;
            currentPage = 1;
            loadTenants();
        }

        // Handle filter changes
        function handleFilterChange(event) {
            const filterName = event.target.id.replace('-filter', '').replace('-', '_');
            currentFilters[filterName] = event.target.value;
            currentPage = 1;
            loadTenants();
        }

        // Toggle bulk mode
        function toggleBulkMode() {
            bulkMode = !bulkMode;
            const bulkBtn = document.getElementById('bulk-mode-btn');
            const bulkBar = document.getElementById('bulk-actions-bar');
            const bulkHeader = document.getElementById('bulk-select-header');

            if (bulkMode) {
                bulkBtn.classList.remove('bg-gray-600', 'hover:bg-gray-700');
                bulkBtn.classList.add('bg-blue-600', 'hover:bg-blue-700');
                bulkBar.classList.remove('hidden');
                bulkHeader.classList.remove('hidden');
            } else {
                bulkBtn.classList.remove('bg-blue-600', 'hover:bg-blue-700');
                bulkBtn.classList.add('bg-gray-600', 'hover:bg-gray-700');
                bulkBar.classList.add('hidden');
                bulkHeader.classList.add('hidden');
                clearSelection();
            }

            renderTenantsTable(tenants);
        }

        // Handle tenant selection
        function handleTenantSelection(tenantId, isSelected) {
            if (isSelected) {
                selectedTenants.add(tenantId);
            } else {
                selectedTenants.delete(tenantId);
            }
            updateSelectedCount();
        }

        // Handle select all
        function handleSelectAll(event) {
            const checkboxes = document.querySelectorAll('.tenant-checkbox');
            checkboxes.forEach(checkbox => {
                checkbox.checked = event.target.checked;
                handleTenantSelection(parseInt(checkbox.value), checkbox.checked);
            });
        }

        // Update selected count
        function updateSelectedCount() {
            document.getElementById('selected-count').textContent = selectedTenants.size;
        }

        // Clear selection
        function clearSelection() {
            selectedTenants.clear();
            document.querySelectorAll('.tenant-checkbox').forEach(cb => cb.checked = false);
            document.getElementById('select-all-checkbox').checked = false;
            updateSelectedCount();
        }

        // Show/hide loading state
        function showLoading() {
            document.getElementById('loading-state').classList.remove('hidden');
            document.getElementById('tenants-table-body').innerHTML = '';
        }

        function hideLoading() {
            document.getElementById('loading-state').classList.add('hidden');
        }

        // Show error message
        function showError(message) {
            // You can implement a toast notification system here
            alert(message);
        }

        // Placeholder functions for actions (to be implemented)
        function openCreateTenantModal() {
            alert('Create tenant modal - to be implemented');
        }

        function viewTenant(id) {
            alert(`View tenant ${id} - to be implemented`);
        }

        function editTenant(id) {
            alert(`Edit tenant ${id} - to be implemented`);
        }

        function suspendTenant(id) {
            if (confirm('Are you sure you want to suspend this tenant?')) {
                alert(`Suspend tenant ${id} - to be implemented`);
            }
        }

        function activateTenant(id) {
            alert(`Activate tenant ${id} - to be implemented`);
        }

        function deleteTenant(id) {
            if (confirm('Are you sure you want to delete this tenant? This action cannot be undone.')) {
                alert(`Delete tenant ${id} - to be implemented`);
            }
        }

        function exportTenants() {
            alert('Export tenants - to be implemented');
        }

        function bulkActivate() {
            if (selectedTenants.size === 0) {
                alert('Please select tenants first');
                return;
            }
            alert(`Bulk activate ${selectedTenants.size} tenants - to be implemented`);
        }

        function bulkSuspend() {
            if (selectedTenants.size === 0) {
                alert('Please select tenants first');
                return;
            }
            alert(`Bulk suspend ${selectedTenants.size} tenants - to be implemented`);
        }

        function bulkUpdatePlan() {
            if (selectedTenants.size === 0) {
                alert('Please select tenants first');
                return;
            }
            alert(`Bulk update plan for ${selectedTenants.size} tenants - to be implemented`);
        }

        function bulkExport() {
            if (selectedTenants.size === 0) {
                alert('Please select tenants first');
                return;
            }
            alert(`Bulk export ${selectedTenants.size} tenants - to be implemented`);
        }

        function logout() {
            localStorage.removeItem('token');
            window.location.href = '/login.html';
        }
    </script>
</body>
</html>
