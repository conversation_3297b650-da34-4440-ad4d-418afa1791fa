## Running the Project with Docker

This project provides Dockerfiles for both the backend (`./backend`) and the main TypeScript project (`./project`), along with a Docker Compose configuration to orchestrate both services.

### Project-Specific Docker Requirements
- **Node.js Version:** Both services require Node.js version `22.13.1` (as specified by `ARG NODE_VERSION=22.13.1` in the Dockerfiles).
- **Production Dependencies:** The Dockerfiles install only production dependencies for security and efficiency.
- **Non-root User:** Containers run as a non-root user (`appuser`) for improved security.
- **Memory Limit:** `NODE_OPTIONS="--max-old-space-size=4096"` is set to limit Node.js memory usage.

### Environment Variables
- **NODE_ENV:** Set to `production` by default in both services.
- **Custom Environment Variables:** If you have additional environment variables, create `.env` files in `./backend` and/or `./project` and uncomment the `env_file` lines in the `docker-compose.yml`.

### Ports Exposed
- **Backend (`js-backend`):**
  - Exposes port `4000` (host:container mapping `4000:4000`)
- **Project (`ts-project`):**
  - Exposes port `4001` on the host, mapped to `4000` in the container (`4001:4000`)

### Build and Run Instructions
1. **Ensure Docker and Docker Compose are installed.**
2. **Build and start the services:**
   ```sh
   docker compose up --build
   ```
   This will build both the backend and project images using the provided Dockerfiles and start the containers.

3. **Access the services:**
   - Backend API: [http://localhost:4000](http://localhost:4000)
   - Project App: [http://localhost:4001](http://localhost:4001)

### Special Configuration Notes
- **Database:** The backend currently uses in-memory data but is designed for future PostgreSQL integration. No database service is included by default.
- **Static Assets:** If your project requires static assets (e.g., `public/`), uncomment the relevant `COPY` line in the `./project` Dockerfile.
- **Custom Build Contexts:** The Docker Compose file references Dockerfiles outside the build context (e.g., `../backendDockerfile`). Ensure the paths are correct relative to your compose file location.

---

*Update this section if you add a database or change port mappings in the future.*
