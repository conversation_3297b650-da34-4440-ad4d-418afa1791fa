import React, { useState, useEffect } from 'react';
import {
  Users,
  Search,
  Filter,
  Download,
  Upload,
  MoreVertical,
  CheckSquare,
  Square,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Activity,
  Calendar,
  DollarSign,
  Building,
  Mail,
  Phone,
  MapPin,
  Eye,
  Edit,
  Trash2,
  UserP<PERSON>,
  Settings,
  BarChart3
} from 'lucide-react';

interface Tenant {
  id: number;
  name: string;
  slug: string;
  email: string;
  phone?: string;
  address?: string;
  status: 'active' | 'suspended' | 'inactive';
  employeeCount: number;
  activeEmployees: number;
  locationCount: number;
  totalRevenue: number;
  orderCount: number;
  ordersToday: number;
  ordersWeek: number;
  lastActivity: string;
  planType: string;
  subscriptionStatus: string;
  createdAt: string;
  updatedAt: string;
  performanceScore?: number;
  growthRate?: number;
}

interface BulkAction {
  id: string;
  label: string;
  icon: React.ReactNode;
  action: (tenantIds: number[]) => void;
  requiresConfirmation?: boolean;
  destructive?: boolean;
}

export const EnhancedTenantManagement: React.FC = () => {
  const [tenants, setTenants] = useState<Tenant[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTenants, setSelectedTenants] = useState<Set<number>>(new Set());
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [showBulkActions, setShowBulkActions] = useState(false);

  const fetchTenants = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/admin/tenants', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch tenants');
      }

      const data = await response.json();
      
      // Calculate performance scores and growth rates
      const enhancedTenants = data.map((tenant: Tenant) => ({
        ...tenant,
        performanceScore: calculatePerformanceScore(tenant),
        growthRate: calculateGrowthRate(tenant)
      }));

      setTenants(enhancedTenants);
    } catch (error) {
      console.error('Error fetching tenants:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchTenants();
  }, []);

  const calculatePerformanceScore = (tenant: Tenant): number => {
    // Calculate performance score based on multiple factors
    const revenueScore = Math.min(tenant.totalRevenue / 10000, 1) * 30; // Max 30 points
    const orderScore = Math.min(tenant.orderCount / 1000, 1) * 25; // Max 25 points
    const employeeScore = Math.min(tenant.employeeCount / 50, 1) * 20; // Max 20 points
    const activityScore = tenant.ordersToday > 0 ? 25 : 0; // Max 25 points
    
    return Math.round(revenueScore + orderScore + employeeScore + activityScore);
  };

  const calculateGrowthRate = (tenant: Tenant): number => {
    // Simulate growth rate calculation (in real app, this would come from historical data)
    const weeklyOrders = tenant.ordersWeek || 0;
    const dailyAverage = weeklyOrders / 7;
    const todayOrders = tenant.ordersToday || 0;
    
    if (dailyAverage === 0) return 0;
    return Math.round(((todayOrders - dailyAverage) / dailyAverage) * 100);
  };

  const filteredAndSortedTenants = tenants
    .filter(tenant => {
      const matchesSearch = tenant.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           tenant.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                           tenant.slug.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesStatus = statusFilter === 'all' || tenant.status === statusFilter;
      return matchesSearch && matchesStatus;
    })
    .sort((a, b) => {
      let aValue: any = a[sortBy as keyof Tenant];
      let bValue: any = b[sortBy as keyof Tenant];
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

  const handleSelectAll = () => {
    if (selectedTenants.size === filteredAndSortedTenants.length) {
      setSelectedTenants(new Set());
    } else {
      setSelectedTenants(new Set(filteredAndSortedTenants.map(t => t.id)));
    }
  };

  const handleSelectTenant = (tenantId: number) => {
    const newSelected = new Set(selectedTenants);
    if (newSelected.has(tenantId)) {
      newSelected.delete(tenantId);
    } else {
      newSelected.add(tenantId);
    }
    setSelectedTenants(newSelected);
  };

  const bulkActions: BulkAction[] = [
    {
      id: 'activate',
      label: 'Activate Selected',
      icon: <CheckSquare className="h-4 w-4" />,
      action: (tenantIds) => handleBulkStatusChange(tenantIds, 'active')
    },
    {
      id: 'suspend',
      label: 'Suspend Selected',
      icon: <AlertTriangle className="h-4 w-4" />,
      action: (tenantIds) => handleBulkStatusChange(tenantIds, 'suspended'),
      requiresConfirmation: true
    },
    {
      id: 'export',
      label: 'Export Selected',
      icon: <Download className="h-4 w-4" />,
      action: handleBulkExport
    },
    {
      id: 'delete',
      label: 'Delete Selected',
      icon: <Trash2 className="h-4 w-4" />,
      action: handleBulkDelete,
      requiresConfirmation: true,
      destructive: true
    }
  ];

  const handleBulkStatusChange = async (tenantIds: number[], newStatus: string) => {
    try {
      const promises = tenantIds.map(id =>
        fetch(`/api/admin/tenants/${id}`, {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ status: newStatus })
        })
      );

      await Promise.all(promises);
      await fetchTenants();
      setSelectedTenants(new Set());
      alert(`Successfully updated ${tenantIds.length} tenants to ${newStatus}`);
    } catch (error) {
      console.error('Error updating tenants:', error);
      alert('Failed to update tenants');
    }
  };

  const handleBulkExport = (tenantIds: number[]) => {
    const selectedTenantsData = tenants.filter(t => tenantIds.includes(t.id));
    const csvContent = generateCSV(selectedTenantsData);
    downloadCSV(csvContent, `tenants-export-${new Date().toISOString().split('T')[0]}.csv`);
    setSelectedTenants(new Set());
  };

  const handleBulkDelete = async (tenantIds: number[]) => {
    if (!confirm(`Are you sure you want to delete ${tenantIds.length} tenants? This action cannot be undone.`)) {
      return;
    }

    try {
      const promises = tenantIds.map(id =>
        fetch(`/api/admin/tenants/${id}`, {
          method: 'DELETE',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ confirmDelete: true })
        })
      );

      await Promise.all(promises);
      await fetchTenants();
      setSelectedTenants(new Set());
      alert(`Successfully deleted ${tenantIds.length} tenants`);
    } catch (error) {
      console.error('Error deleting tenants:', error);
      alert('Failed to delete tenants');
    }
  };

  const generateCSV = (data: Tenant[]): string => {
    const headers = [
      'ID', 'Name', 'Slug', 'Email', 'Phone', 'Status', 'Employees', 
      'Revenue', 'Orders', 'Plan', 'Created', 'Performance Score'
    ];
    
    const rows = data.map(tenant => [
      tenant.id,
      tenant.name,
      tenant.slug,
      tenant.email,
      tenant.phone || '',
      tenant.status,
      tenant.employeeCount,
      tenant.totalRevenue,
      tenant.orderCount,
      tenant.planType,
      new Date(tenant.createdAt).toLocaleDateString(),
      tenant.performanceScore || 0
    ]);

    return [headers, ...rows].map(row => row.map(field => `"${field}"`).join(',')).join('\n');
  };

  const downloadCSV = (content: string, filename: string) => {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const getStatusBadge = (status: string) => {
    const baseClasses = "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium";
    switch (status) {
      case 'active':
        return `${baseClasses} bg-green-100 text-green-800`;
      case 'suspended':
        return `${baseClasses} bg-yellow-100 text-yellow-800`;
      case 'inactive':
        return `${baseClasses} bg-gray-100 text-gray-800`;
      default:
        return `${baseClasses} bg-gray-100 text-gray-800`;
    }
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getGrowthIcon = (rate: number) => {
    if (rate > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (rate < 0) return <TrendingDown className="h-4 w-4 text-red-500" />;
    return <Activity className="h-4 w-4 text-gray-500" />;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Tenant Management</h2>
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="animate-pulse space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Tenant Management</h2>
          <p className="text-gray-600 mt-1">
            Advanced tenant management with bulk operations and analytics
          </p>
        </div>
        <div className="flex items-center space-x-3">
          <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center">
            <UserPlus className="h-4 w-4 mr-2" />
            Add Tenant
          </button>
          <button className="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 flex items-center">
            <Upload className="h-4 w-4 mr-2" />
            Import
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white rounded-lg border border-gray-200 p-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row sm:items-center space-y-3 sm:space-y-0 sm:space-x-4">
            {/* Search */}
            <div className="relative">
              <Search className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                placeholder="Search tenants..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full sm:w-64"
              />
            </div>

            {/* Status Filter */}
            <div className="relative">
              <Filter className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <select
                value={statusFilter}
                onChange={(e) => setStatusFilter(e.target.value)}
                className="pl-10 pr-8 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                <option value="all">All Status</option>
                <option value="active">Active</option>
                <option value="suspended">Suspended</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            {/* Sort */}
            <div className="flex items-center space-x-2">
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              >
                <option value="name">Name</option>
                <option value="totalRevenue">Revenue</option>
                <option value="orderCount">Orders</option>
                <option value="employeeCount">Employees</option>
                <option value="performanceScore">Performance</option>
                <option value="createdAt">Created Date</option>
              </select>
              <button
                onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
                className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                {sortOrder === 'asc' ? '↑' : '↓'}
              </button>
            </div>
          </div>

          {/* Bulk Actions */}
          {selectedTenants.size > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-600">
                {selectedTenants.size} selected
              </span>
              <div className="relative">
                <button
                  onClick={() => setShowBulkActions(!showBulkActions)}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 flex items-center"
                >
                  Bulk Actions
                  <MoreVertical className="h-4 w-4 ml-2" />
                </button>
                {showBulkActions && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    {bulkActions.map(action => (
                      <button
                        key={action.id}
                        onClick={() => {
                          action.action(Array.from(selectedTenants));
                          setShowBulkActions(false);
                        }}
                        className={`w-full px-4 py-2 text-left hover:bg-gray-50 flex items-center ${
                          action.destructive ? 'text-red-600 hover:bg-red-50' : 'text-gray-700'
                        }`}
                      >
                        {action.icon}
                        <span className="ml-2">{action.label}</span>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Tenants Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left">
                  <button
                    onClick={handleSelectAll}
                    className="flex items-center"
                  >
                    {selectedTenants.size === filteredAndSortedTenants.length && filteredAndSortedTenants.length > 0 ? (
                      <CheckSquare className="h-5 w-5 text-blue-600" />
                    ) : (
                      <Square className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Tenant
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Revenue
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Orders
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Employees
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Growth
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredAndSortedTenants.map(tenant => (
                <tr key={tenant.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4">
                    <button
                      onClick={() => handleSelectTenant(tenant.id)}
                      className="flex items-center"
                    >
                      {selectedTenants.has(tenant.id) ? (
                        <CheckSquare className="h-5 w-5 text-blue-600" />
                      ) : (
                        <Square className="h-5 w-5 text-gray-400" />
                      )}
                    </button>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <Building className="h-5 w-5 text-blue-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{tenant.name}</div>
                        <div className="text-sm text-gray-500">{tenant.email}</div>
                        <div className="text-xs text-gray-400">/{tenant.slug}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <span className={getStatusBadge(tenant.status)}>
                      {tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                    </span>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      <div className={`text-sm font-medium ${getPerformanceColor(tenant.performanceScore || 0)}`}>
                        {tenant.performanceScore || 0}%
                      </div>
                      <div className="ml-2 w-16 bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            (tenant.performanceScore || 0) >= 80 ? 'bg-green-500' :
                            (tenant.performanceScore || 0) >= 60 ? 'bg-yellow-500' :
                            (tenant.performanceScore || 0) >= 40 ? 'bg-orange-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${tenant.performanceScore || 0}%` }}
                        ></div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      ${tenant.totalRevenue.toFixed(2)}
                    </div>
                    <div className="text-xs text-gray-500">
                      {tenant.orderCount} orders
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {tenant.ordersToday}
                    </div>
                    <div className="text-xs text-gray-500">
                      {tenant.ordersWeek} this week
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm font-medium text-gray-900">
                      {tenant.employeeCount}
                    </div>
                    <div className="text-xs text-gray-500">
                      {tenant.activeEmployees} active
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center">
                      {getGrowthIcon(tenant.growthRate || 0)}
                      <span className={`ml-1 text-sm font-medium ${
                        (tenant.growthRate || 0) > 0 ? 'text-green-600' :
                        (tenant.growthRate || 0) < 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {tenant.growthRate || 0}%
                      </span>
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex items-center space-x-2">
                      <button className="p-1 text-gray-400 hover:text-blue-600">
                        <Eye className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-green-600">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-red-600">
                        <Trash2 className="h-4 w-4" />
                      </button>
                      <button className="p-1 text-gray-400 hover:text-purple-600">
                        <BarChart3 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        {filteredAndSortedTenants.length === 0 && (
          <div className="text-center py-12">
            <Building className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No tenants found</h3>
            <p className="text-gray-500">
              {searchQuery || statusFilter !== 'all'
                ? 'Try adjusting your search or filter criteria.'
                : 'Get started by adding your first tenant.'
              }
            </p>
          </div>
        )}
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <Users className="h-8 w-8 text-blue-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">{tenants.length}</div>
              <div className="text-sm text-gray-600">Total Tenants</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <CheckSquare className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {tenants.filter(t => t.status === 'active').length}
              </div>
              <div className="text-sm text-gray-600">Active Tenants</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                ${tenants.reduce((sum, t) => sum + t.totalRevenue, 0).toFixed(2)}
              </div>
              <div className="text-sm text-gray-600">Total Revenue</div>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <div className="ml-4">
              <div className="text-2xl font-bold text-gray-900">
                {Math.round(tenants.reduce((sum, t) => sum + (t.performanceScore || 0), 0) / tenants.length) || 0}%
              </div>
              <div className="text-sm text-gray-600">Avg Performance</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
