import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import './App.css';

// API Configuration
const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:4000';

// Import the unified POS component
import UnifiedPOSSystem from './components/pos/UnifiedPOSSystem';

// Enhanced POS Main Interface Component
const EnhancedPOSInterface: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);

  useEffect(() => {
    // Check if user is already authenticated
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');

    if (token && userData) {
      console.log('🔍 Checking existing session...');

      // Verify token is still valid
      fetch(`${API_BASE_URL}/api/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      })
      .then(response => {
        if (response.ok) {
          console.log('✅ Session restored successfully');
          setIsAuthenticated(true);
          setUser(JSON.parse(userData));

          // Restore any saved order data
          const savedOrder = localStorage.getItem('currentOrder');
          const savedOrderData = localStorage.getItem('orderData');
          if (savedOrder || savedOrderData) {
            console.log('📋 Restored saved order data');
          }
        } else {
          console.log('❌ Token invalid, clearing session');
          // Token invalid, clear storage
          localStorage.removeItem('token');
          localStorage.removeItem('user');
          // Don't clear order data immediately - give user chance to re-login
        }
      })
      .catch(error => {
        console.error('Token verification failed:', error);
        console.log('⚠️ Backend unavailable, maintaining session');
        // If backend is unavailable, maintain session for better UX
        setIsAuthenticated(true);
        setUser(JSON.parse(userData));
      });
    }
  }, []);

  const handleLogin = async (pin: string): Promise<void> => {
    try {
      const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin,
          tenant_slug: 'demo-restaurant'
        }),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));

        // Restore any saved order data from previous session
        const savedOrderData = localStorage.getItem('savedOrderData');
        const orderSavedAt = localStorage.getItem('orderSavedAt');

        if (savedOrderData && orderSavedAt) {
          const savedTime = new Date(orderSavedAt);
          const now = new Date();
          const hoursDiff = (now.getTime() - savedTime.getTime()) / (1000 * 60 * 60);

          // Restore order if saved within last 24 hours
          if (hoursDiff < 24) {
            localStorage.setItem('currentOrder', savedOrderData);
            console.log('📋 Restored order from previous session');
            alert('Welcome back! Your previous order has been restored.');
          } else {
            // Clear old saved data
            localStorage.removeItem('savedOrderData');
            localStorage.removeItem('orderSavedAt');
            console.log('🗑️ Cleared old saved order data');
          }
        }

        setIsAuthenticated(true);
        setUser(data.user);

        console.log('✅ Login successful for user:', data.user.name);
      } else {
        const errorData = await response.json();
        alert(errorData.message || 'Invalid PIN. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      alert('Login failed. Please check your connection.');
    }
  };

  const handleLogout = () => {
    console.log('🚪 Logging out user...');

    // Save current order data before logout (if any exists)
    const currentOrderData = localStorage.getItem('currentOrder');
    if (currentOrderData) {
      console.log('💾 Preserving order data for next session');
      localStorage.setItem('savedOrderData', currentOrderData);
      localStorage.setItem('orderSavedAt', new Date().toISOString());
    }

    // Clear authentication data
    localStorage.removeItem('token');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);

    console.log('✅ Logout completed - order data preserved');
  };

  if (!isAuthenticated) {
    return <LoginScreen onLogin={handleLogin} />;
  }

  return <UnifiedPOSSystem onLogout={handleLogout} user={user} />;
};

// Enhanced Login Screen Component
const LoginScreen: React.FC<{ onLogin: (pin: string) => Promise<void> }> = ({ onLogin }) => {
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (pin.length >= 6) {
      setLoading(true);
      try {
        await onLogin(pin);
      } catch (error) {
        console.error('Login failed:', error);
      } finally {
        setLoading(false);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-800 flex items-center justify-center px-4">
      <div className="max-w-md w-full space-y-8">
        <div className="text-center">
          <div className="mx-auto h-20 w-20 bg-white rounded-full flex items-center justify-center mb-4">
            <span className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              B
            </span>
          </div>
          <h2 className="text-4xl font-bold text-white mb-2">BARPOS</h2>
          <p className="text-xl text-blue-100 mb-2">AI-Powered Global POS System</p>
          <p className="text-sm text-blue-200">Phase 7: Production Ready</p>
        </div>

        <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="pin" className="block text-sm font-medium text-white mb-2">
                Enter PIN to Access System
              </label>
              <input
                id="pin"
                type="password"
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                placeholder="Enter 6-digit PIN"
                maxLength={6}
                required
              />
              <p className="text-xs text-blue-200 mt-2">Demo PIN: 888888</p>
            </div>

            <button
              type="submit"
              disabled={pin.length < 6 || loading}
              className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
            >
              {loading ? (
                <span className="flex items-center justify-center">
                  <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Authenticating...
                </span>
              ) : (
                'Access Enhanced POS System'
              )}
            </button>
          </form>

          <div className="mt-6 text-center">
            <div className="text-sm text-blue-200 space-y-1">
              <p>✨ AI-Powered Fraud Detection</p>
              <p>🌍 Global Multi-Currency Support</p>
              <p>🛡️ Advanced Compliance Management</p>
              <p>⚡ Real-time Analytics & Automation</p>
            </div>
          </div>
        </div>

        <div className="text-center text-blue-200 text-sm">
          <p>Production Ready | Enterprise Grade | Global Scale</p>
        </div>
      </div>
    </div>
  );
};

// Main App Component
const App: React.FC = () => {
  return (
    <Router>
      <div className="App">
        <Routes>
          <Route path="/" element={<EnhancedPOSInterface />} />
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </div>
    </Router>
  );
};

export default App;
