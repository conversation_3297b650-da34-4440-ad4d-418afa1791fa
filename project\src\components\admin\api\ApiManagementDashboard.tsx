import React, { useState, useEffect } from 'react';
import { 
  Key, 
  Globe, 
  Activity, 
  Shield, 
  Plus, 
  Copy, 
  Eye, 
  EyeOff,
  Edit,
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  Bar<PERSON>hart3,
  <PERSON><PERSON><PERSON>,
  Zap
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON>hart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface ApiKey {
  id: number;
  tenant_id: number;
  key_name: string;
  api_key: string;
  permissions: string[];
  rate_limit_per_hour: number;
  rate_limit_per_day: number;
  is_active: boolean;
  last_used_at?: string;
  usage_count: number;
  created_at: string;
}

interface ApiEndpoint {
  id: number;
  endpoint_path: string;
  http_method: string;
  description: string;
  category: string;
  required_permissions: string[];
  is_public: boolean;
  is_deprecated: boolean;
  version: string;
}

interface ApiUsage {
  timestamp: string;
  endpoint: string;
  requests: number;
  avg_response_time: number;
  error_rate: number;
}

interface ApiMetrics {
  total_requests: number;
  avg_response_time: number;
  error_rate: number;
  active_keys: number;
  top_endpoints: Array<{endpoint: string, requests: number}>;
}

export function ApiManagementDashboard() {
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [endpoints, setEndpoints] = useState<ApiEndpoint[]>([]);
  const [usage, setUsage] = useState<ApiUsage[]>([]);
  const [metrics, setMetrics] = useState<ApiMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'keys' | 'endpoints' | 'usage' | 'analytics'>('keys');
  const [showCreateKey, setShowCreateKey] = useState(false);
  const [visibleKeys, setVisibleKeys] = useState<Set<number>>(new Set());

  useEffect(() => {
    fetchApiData();
  }, []);

  const fetchApiData = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock API keys
      const mockKeys: ApiKey[] = [
        {
          id: 1,
          tenant_id: 1,
          key_name: 'Production API Key',
          api_key: 'pk_live_51234567890abcdef',
          permissions: ['orders:read', 'orders:write', 'menu:read', 'analytics:read'],
          rate_limit_per_hour: 1000,
          rate_limit_per_day: 10000,
          is_active: true,
          last_used_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          usage_count: 15420,
          created_at: '2024-01-15T00:00:00Z'
        },
        {
          id: 2,
          tenant_id: 1,
          key_name: 'Development API Key',
          api_key: 'pk_test_51234567890abcdef',
          permissions: ['orders:read', 'menu:read'],
          rate_limit_per_hour: 100,
          rate_limit_per_day: 1000,
          is_active: true,
          last_used_at: new Date(Date.now() - 30 * 60 * 1000).toISOString(),
          usage_count: 2340,
          created_at: '2024-02-01T00:00:00Z'
        },
        {
          id: 3,
          tenant_id: 1,
          key_name: 'Analytics Only Key',
          api_key: 'pk_analytics_51234567890abcdef',
          permissions: ['analytics:read'],
          rate_limit_per_hour: 500,
          rate_limit_per_day: 5000,
          is_active: false,
          usage_count: 890,
          created_at: '2024-01-20T00:00:00Z'
        }
      ];

      // Mock endpoints
      const mockEndpoints: ApiEndpoint[] = [
        {
          id: 1,
          endpoint_path: '/api/v1/orders',
          http_method: 'GET',
          description: 'Retrieve orders with filtering and pagination',
          category: 'pos',
          required_permissions: ['orders:read'],
          is_public: false,
          is_deprecated: false,
          version: 'v1'
        },
        {
          id: 2,
          endpoint_path: '/api/v1/orders',
          http_method: 'POST',
          description: 'Create a new order',
          category: 'pos',
          required_permissions: ['orders:write'],
          is_public: false,
          is_deprecated: false,
          version: 'v1'
        },
        {
          id: 3,
          endpoint_path: '/api/v1/analytics/revenue',
          http_method: 'GET',
          description: 'Get revenue analytics and reports',
          category: 'analytics',
          required_permissions: ['analytics:read'],
          is_public: false,
          is_deprecated: false,
          version: 'v1'
        },
        {
          id: 4,
          endpoint_path: '/api/v1/menu/items',
          http_method: 'GET',
          description: 'Get menu items',
          category: 'pos',
          required_permissions: ['menu:read'],
          is_public: true,
          is_deprecated: false,
          version: 'v1'
        }
      ];

      // Mock usage data
      const mockUsage: ApiUsage[] = [];
      for (let i = 23; i >= 0; i--) {
        const timestamp = new Date(Date.now() - i * 60 * 60 * 1000);
        mockUsage.push({
          timestamp: timestamp.toISOString(),
          endpoint: '/api/v1/orders',
          requests: Math.floor(Math.random() * 100) + 50,
          avg_response_time: Math.floor(Math.random() * 200) + 100,
          error_rate: Math.random() * 5
        });
      }

      // Mock metrics
      const mockMetrics: ApiMetrics = {
        total_requests: 18750,
        avg_response_time: 245,
        error_rate: 1.2,
        active_keys: 2,
        top_endpoints: [
          { endpoint: '/api/v1/orders', requests: 8420 },
          { endpoint: '/api/v1/menu/items', requests: 5230 },
          { endpoint: '/api/v1/analytics/revenue', requests: 3100 },
          { endpoint: '/api/v1/payments', requests: 2000 }
        ]
      };

      setApiKeys(mockKeys);
      setEndpoints(mockEndpoints);
      setUsage(mockUsage);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching API data:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleKeyVisibility = (keyId: number) => {
    const newVisible = new Set(visibleKeys);
    if (newVisible.has(keyId)) {
      newVisible.delete(keyId);
    } else {
      newVisible.add(keyId);
    }
    setVisibleKeys(newVisible);
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const maskApiKey = (key: string) => {
    return key.substring(0, 8) + '...' + key.substring(key.length - 4);
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'text-green-700 bg-green-100';
      case 'POST': return 'text-blue-700 bg-blue-100';
      case 'PUT': return 'text-yellow-700 bg-yellow-100';
      case 'DELETE': return 'text-red-700 bg-red-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'pos': return 'text-purple-700 bg-purple-100';
      case 'analytics': return 'text-blue-700 bg-blue-100';
      case 'webhook': return 'text-orange-700 bg-orange-100';
      case 'admin': return 'text-red-700 bg-red-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-96 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">API Management</h2>
          <p className="text-gray-600 mt-1">
            Manage API keys, monitor usage, and configure endpoints
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <BarChart3 className="h-4 w-4 mr-2" />
            View Docs
          </button>
          <button
            onClick={() => setShowCreateKey(true)}
            className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            New API Key
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Requests</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {metrics.total_requests.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Avg Response Time</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.avg_response_time}ms</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Error Rate</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.error_rate}%</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Key className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Active Keys</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.active_keys}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('keys')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'keys'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Key className="h-4 w-4 inline mr-2" />
            API Keys ({apiKeys.length})
          </button>
          <button
            onClick={() => setActiveTab('endpoints')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'endpoints'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Globe className="h-4 w-4 inline mr-2" />
            Endpoints ({endpoints.length})
          </button>
          <button
            onClick={() => setActiveTab('usage')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'usage'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Activity className="h-4 w-4 inline mr-2" />
            Usage Analytics
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart3 className="h-4 w-4 inline mr-2" />
            Performance
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'keys' && (
        <div className="space-y-6">
          {apiKeys.map(key => (
            <div key={key.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-600">
                    <Key className="h-6 w-6" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{key.key_name}</h3>
                    <p className="text-sm text-gray-500">
                      Created {new Date(key.created_at).toLocaleDateString()}
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    key.is_active ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'
                  }`}>
                    {key.is_active ? 'Active' : 'Inactive'}
                  </span>
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-4">
                  <div className="flex-1">
                    <label className="block text-sm font-medium text-gray-700 mb-1">API Key</label>
                    <div className="flex items-center space-x-2">
                      <code className="flex-1 px-3 py-2 bg-gray-50 border border-gray-200 rounded-md text-sm font-mono">
                        {visibleKeys.has(key.id) ? key.api_key : maskApiKey(key.api_key)}
                      </code>
                      <button
                        onClick={() => toggleKeyVisibility(key.id)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                      >
                        {visibleKeys.has(key.id) ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </button>
                      <button
                        onClick={() => copyToClipboard(key.api_key)}
                        className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Usage Count</span>
                    <div className="font-medium">{key.usage_count.toLocaleString()}</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Rate Limit</span>
                    <div className="font-medium">{key.rate_limit_per_hour}/hour</div>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Used</span>
                    <div className="font-medium">
                      {key.last_used_at ? new Date(key.last_used_at).toLocaleString() : 'Never'}
                    </div>
                  </div>
                  <div>
                    <span className="text-gray-600">Permissions</span>
                    <div className="font-medium">{key.permissions.length} permissions</div>
                  </div>
                </div>

                <div>
                  <span className="text-sm text-gray-600 mb-2 block">Permissions</span>
                  <div className="flex flex-wrap gap-2">
                    {key.permissions.map((permission, index) => (
                      <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                        {permission}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'endpoints' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Endpoint
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Method
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Permissions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {endpoints.map(endpoint => (
                <tr key={endpoint.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900">{endpoint.endpoint_path}</div>
                      <div className="text-sm text-gray-500">{endpoint.description}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMethodColor(endpoint.http_method)}`}>
                      {endpoint.http_method}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(endpoint.category)}`}>
                      {endpoint.category}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex flex-wrap gap-1">
                      {endpoint.required_permissions.map((permission, index) => (
                        <span key={index} className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded">
                          {permission}
                        </span>
                      ))}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      {endpoint.is_public && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Public
                        </span>
                      )}
                      {endpoint.is_deprecated && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Deprecated
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'usage' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Request Volume Chart */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Request Volume (24h)</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={usage}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value: number) => [value, 'Requests']}
                />
                <Line 
                  type="monotone" 
                  dataKey="requests" 
                  stroke="#3B82F6" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Top Endpoints */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Top Endpoints</h3>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={metrics?.top_endpoints}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ endpoint, percent }) => `${endpoint.split('/').pop()} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="requests"
                >
                  {metrics?.top_endpoints.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Response Time Chart */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Response Time Trends</h3>
            <ResponsiveContainer width="100%" height={300}>
              <LineChart data={usage}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value: number) => [`${value}ms`, 'Response Time']}
                />
                <Line 
                  type="monotone" 
                  dataKey="avg_response_time" 
                  stroke="#10B981" 
                  strokeWidth={2}
                  dot={false}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>

          {/* Error Rate Chart */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Error Rate Monitoring</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={usage}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis 
                  dataKey="timestamp" 
                  tickFormatter={(value) => new Date(value).toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}
                />
                <YAxis />
                <Tooltip 
                  labelFormatter={(value) => new Date(value).toLocaleString()}
                  formatter={(value: number) => [`${value.toFixed(2)}%`, 'Error Rate']}
                />
                <Bar dataKey="error_rate" fill="#EF4444" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
}
