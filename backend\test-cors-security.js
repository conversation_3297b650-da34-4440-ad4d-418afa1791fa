#!/usr/bin/env node

/**
 * CORS Security Configuration Test
 * Tests the secure CORS policy and security headers implementation
 */

const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:4000';

async function testCORSConfiguration() {
  console.log('🔒 Testing CORS Security Configuration...\n');

  const tests = [
    {
      name: 'Valid Origin Request',
      origin: 'http://localhost:3000',
      expectedResult: 'ALLOWED'
    },
    {
      name: 'Invalid Origin Request', 
      origin: 'http://malicious-site.com',
      expectedResult: 'BLOCKED'
    },
    {
      name: 'No Origin Request (Mobile/API)',
      origin: null,
      expectedResult: 'ALLOWED'
    },
    {
      name: 'Localhost Variant',
      origin: 'http://127.0.0.1:3000',
      expectedResult: 'ALLOWED'
    }
  ];

  let passedTests = 0;
  let totalTests = tests.length;

  for (const test of tests) {
    try {
      console.log(`🧪 Testing: ${test.name}`);
      
      const headers = {
        'Content-Type': 'application/json'
      };
      
      if (test.origin) {
        headers['Origin'] = test.origin;
      }

      const response = await fetch(`${SERVER_URL}/api/health/public`, {
        method: 'GET',
        headers: headers
      });

      const corsHeader = response.headers.get('access-control-allow-origin');
      const securityHeaders = {
        'x-frame-options': response.headers.get('x-frame-options'),
        'x-content-type-options': response.headers.get('x-content-type-options'),
        'x-xss-protection': response.headers.get('x-xss-protection'),
        'referrer-policy': response.headers.get('referrer-policy'),
        'content-security-policy': response.headers.get('content-security-policy')
      };

      if (test.expectedResult === 'ALLOWED') {
        if (response.ok && (corsHeader === test.origin || corsHeader === '*' || !test.origin)) {
          console.log(`   ✅ PASS - Request allowed as expected`);
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Request should have been allowed`);
        }
      } else if (test.expectedResult === 'BLOCKED') {
        if (!corsHeader || corsHeader !== test.origin) {
          console.log(`   ✅ PASS - Request blocked as expected`);
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Request should have been blocked`);
        }
      }

      // Check security headers (only for first test to avoid spam)
      if (test.name === 'Valid Origin Request') {
        console.log(`   🛡️ Security Headers Check:`);
        console.log(`      X-Frame-Options: ${securityHeaders['x-frame-options'] || 'MISSING'}`);
        console.log(`      X-Content-Type-Options: ${securityHeaders['x-content-type-options'] || 'MISSING'}`);
        console.log(`      X-XSS-Protection: ${securityHeaders['x-xss-protection'] || 'MISSING'}`);
        console.log(`      Referrer-Policy: ${securityHeaders['referrer-policy'] || 'MISSING'}`);
        console.log(`      CSP: ${securityHeaders['content-security-policy'] ? 'PRESENT' : 'MISSING'}`);
      }

    } catch (error) {
      if (test.expectedResult === 'BLOCKED' && error.message.includes('CORS')) {
        console.log(`   ✅ PASS - CORS error as expected: ${error.message}`);
        passedTests++;
      } else {
        console.log(`   ❌ FAIL - Unexpected error: ${error.message}`);
      }
    }

    console.log('');
  }

  // Test preflight OPTIONS request
  console.log('🧪 Testing Preflight OPTIONS Request');
  try {
    const response = await fetch(`${SERVER_URL}/api/health/public`, {
      method: 'OPTIONS',
      headers: {
        'Origin': 'http://localhost:3000',
        'Access-Control-Request-Method': 'POST',
        'Access-Control-Request-Headers': 'Content-Type, Authorization'
      }
    });

    if (response.ok) {
      console.log('   ✅ PASS - Preflight request handled correctly');
      passedTests++;
    } else {
      console.log('   ❌ FAIL - Preflight request failed');
    }
    totalTests++;
  } catch (error) {
    console.log(`   ❌ FAIL - Preflight error: ${error.message}`);
    totalTests++;
  }

  console.log('\n📊 CORS Security Test Results:');
  console.log(`   Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`   Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('   🎉 All CORS security tests passed!');
  } else {
    console.log('   ⚠️ Some CORS security tests failed - review configuration');
  }

  return {
    passed: passedTests,
    total: totalTests,
    success_rate: Math.round((passedTests / totalTests) * 100)
  };
}

// Run the test
if (require.main === module) {
  testCORSConfiguration()
    .then(results => {
      process.exit(results.success_rate === 100 ? 0 : 1);
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error);
      process.exit(1);
    });
}

module.exports = { testCORSConfiguration };
