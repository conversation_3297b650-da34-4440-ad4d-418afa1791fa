import React from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface TestRestructuredPOSProps {
  isDarkMode?: boolean;
  onThemeToggle?: () => void;
}

const TestRestructuredPOS: React.FC<TestRestructuredPOSProps> = ({
  isDarkMode = false,
  onThemeToggle
}) => {
  const { state } = useEnhancedAppContext();

  console.log('🧪 TEST RESTRUCTURED POS COMPONENT LOADING');
  console.log('Props:', { isDarkMode, onThemeToggle });
  console.log('State:', state);

  return (
    <div className={`h-screen flex transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`} data-test-restructured="true">
      
      {/* Simple Sidebar */}
      <div className={`w-64 border-r flex flex-col transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      }`}>
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="px-3 py-1 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-full text-sm font-bold">
              🧪 TEST MODE
            </div>
          </div>
          <h1 className="text-xl font-bold mt-2">RestroFlow</h1>
          <p className="text-sm text-gray-500">Restructured Interface Test</p>
        </div>
        
        <div className="flex-1 p-4">
          <div className="space-y-3">
            <div className="p-3 bg-green-100 dark:bg-green-900 rounded-lg">
              <h3 className="font-semibold text-green-800 dark:text-green-200">✅ Component Loaded</h3>
              <p className="text-sm text-green-600 dark:text-green-300">TestRestructuredPOS is working</p>
            </div>
            
            <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-lg">
              <h3 className="font-semibold text-blue-800 dark:text-blue-200">🔧 Debug Info</h3>
              <div className="text-sm text-blue-600 dark:text-blue-300 space-y-1">
                <p>Theme: {isDarkMode ? 'Dark' : 'Light'}</p>
                <p>User: {state.currentEmployee?.name || 'Not logged in'}</p>
                <p>Authenticated: {state.isAuthenticated ? 'Yes' : 'No'}</p>
              </div>
            </div>
            
            <div className="p-3 bg-purple-100 dark:bg-purple-900 rounded-lg">
              <h3 className="font-semibold text-purple-800 dark:text-purple-200">🌐 URL Info</h3>
              <div className="text-sm text-purple-600 dark:text-purple-300 space-y-1">
                <p>URL: {window.location.href}</p>
                <p>Industry: {window.location.search.includes('industry=true') ? 'Yes' : 'No'}</p>
                <p>Restructured: {window.location.search.includes('restructured=true') ? 'Yes' : 'No'}</p>
              </div>
            </div>
            
            <div className="p-3 bg-yellow-100 dark:bg-yellow-900 rounded-lg">
              <h3 className="font-semibold text-yellow-800 dark:text-yellow-200">💾 localStorage</h3>
              <div className="text-sm text-yellow-600 dark:text-yellow-300 space-y-1">
                <p>useRestructuredPOS: {localStorage.getItem('useRestructuredPOS') || 'not set'}</p>
                <p>useIndustryStandardPOS: {localStorage.getItem('useIndustryStandardPOS') || 'not set'}</p>
              </div>
            </div>
          </div>
        </div>
        
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={onThemeToggle}
            className={`w-full px-3 py-2 rounded-lg transition-colors ${
              isDarkMode 
                ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
            }`}
          >
            {isDarkMode ? '☀️ Light Mode' : '🌙 Dark Mode'}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <header className={`px-6 py-4 border-b transition-colors duration-300 ${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        }`}>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="px-4 py-2 bg-gradient-to-r from-green-500 to-blue-600 text-white rounded-full font-bold">
                🧪 RESTRUCTURED TEST MODE
              </div>
              <h2 className="text-xl font-semibold">Component Test Successful</h2>
            </div>
          </div>
        </header>

        {/* Content Area */}
        <main className="flex-1 p-6">
          <div className={`h-full rounded-lg border-2 border-dashed p-8 text-center ${
            isDarkMode ? 'border-gray-600' : 'border-gray-300'
          }`}>
            <div className="max-w-2xl mx-auto">
              <div className="text-6xl mb-6">🧪</div>
              <h3 className="text-3xl font-bold mb-4 text-green-600">
                Test Component Working!
              </h3>
              <p className="text-lg mb-6">
                The TestRestructuredPOS component is loading successfully. This confirms that:
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
                <div className={`p-4 rounded-lg ${
                  isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
                }`}>
                  <h4 className="font-semibold mb-2 text-green-600">✅ Component System</h4>
                  <ul className="text-sm space-y-1 text-left">
                    <li>• React components loading</li>
                    <li>• TypeScript compilation working</li>
                    <li>• Context provider accessible</li>
                    <li>• Props passing correctly</li>
                  </ul>
                </div>
                
                <div className={`p-4 rounded-lg ${
                  isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
                }`}>
                  <h4 className="font-semibold mb-2 text-blue-600">🔧 System Status</h4>
                  <ul className="text-sm space-y-1 text-left">
                    <li>• Styling system working</li>
                    <li>• Theme switching functional</li>
                    <li>• URL parameters detected</li>
                    <li>• localStorage accessible</li>
                  </ul>
                </div>
              </div>
              
              <div className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-gray-100'
              }`}>
                <h4 className="font-semibold mb-3">🚀 Next Steps</h4>
                <p className="text-sm mb-4">
                  If you can see this test component, the restructured system should work. 
                  The issue might be with the routing logic or component imports.
                </p>
                <div className="space-y-2">
                  <button
                    onClick={() => {
                      localStorage.setItem('useRestructuredPOS', 'true');
                      localStorage.setItem('useIndustryStandardPOS', 'true');
                      window.location.href = 'http://localhost:5173/?industry=true&restructured=true';
                    }}
                    className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
                  >
                    🚀 Force Enable Restructured Mode
                  </button>
                  <button
                    onClick={() => {
                      console.log('🔧 Debug Info:');
                      console.log('localStorage:', {
                        useRestructuredPOS: localStorage.getItem('useRestructuredPOS'),
                        useIndustryStandardPOS: localStorage.getItem('useIndustryStandardPOS')
                      });
                      console.log('URL:', window.location.href);
                      console.log('State:', state);
                    }}
                    className="w-full px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
                  >
                    🔍 Log Debug Info
                  </button>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};

export default TestRestructuredPOS;
