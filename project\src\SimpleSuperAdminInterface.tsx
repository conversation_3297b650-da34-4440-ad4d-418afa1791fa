import React, { useState, useEffect } from 'react';

const SimpleSuperAdminInterface: React.FC = () => {
  const [currentEmployee, setCurrentEmployee] = useState<any>(null);
  const [currentTenant, setCurrentTenant] = useState<any>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    // Load user data from localStorage
    const employee = localStorage.getItem('currentEmployee');
    const tenant = localStorage.getItem('currentTenant');
    
    if (employee) {
      setCurrentEmployee(JSON.parse(employee));
    }
    if (tenant) {
      setCurrentTenant(JSON.parse(tenant));
    }

    // Check theme
    const darkMode = localStorage.getItem('adminTheme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  const toggleTheme = () => {
    const newTheme = !isDarkMode;
    setIsDarkMode(newTheme);
    localStorage.setItem('adminTheme', newTheme ? 'dark' : 'light');
  };

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('currentEmployee');
    localStorage.removeItem('currentTenant');
    window.location.reload();
  };

  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-gray-900 via-red-900 to-purple-900' 
        : 'bg-gradient-to-br from-red-50 via-pink-50 to-purple-50'
    }`}>
      {/* Header */}
      <header className={`${
        isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
      } border-b shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-gradient-to-r from-red-500 to-pink-600 rounded-lg flex items-center justify-center">
                <svg className="w-6 h-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
              </div>
              <div>
                <h1 className={`text-xl font-bold ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                  Super Admin Dashboard
                </h1>
                <p className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                  RestroFlow System Administration
                </p>
              </div>
            </div>

            {/* User Info and Actions */}
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <button
                onClick={toggleTheme}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode 
                    ? 'bg-gray-700 text-yellow-400 hover:bg-gray-600' 
                    : 'bg-gray-100 text-gray-600 hover:bg-gray-200'
                }`}
              >
                {isDarkMode ? '☀️' : '🌙'}
              </button>

              {/* User Info */}
              <div className={`text-right ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
                <p className="text-sm font-medium">{currentEmployee?.name || 'Super Administrator'}</p>
                <p className="text-xs opacity-75">{currentEmployee?.role || 'super_admin'}</p>
              </div>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Welcome Section */}
        <div className={`${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } rounded-xl border shadow-lg p-8 mb-8`}>
          <div className="text-center">
            <h2 className={`text-3xl font-bold mb-4 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
              🎉 Welcome to Super Admin Dashboard!
            </h2>
            <p className={`text-lg mb-6 ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
              You have successfully logged in as a Super Administrator.
            </p>
            
            {/* Status Cards */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
              <div className={`${
                isDarkMode ? 'bg-green-900/30 border-green-700' : 'bg-green-50 border-green-200'
              } border rounded-lg p-6`}>
                <div className="text-green-600 text-2xl mb-2">✅</div>
                <h3 className={`font-semibold ${isDarkMode ? 'text-green-300' : 'text-green-800'}`}>
                  Authentication
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-green-400' : 'text-green-600'}`}>
                  Super Admin access granted
                </p>
              </div>

              <div className={`${
                isDarkMode ? 'bg-blue-900/30 border-blue-700' : 'bg-blue-50 border-blue-200'
              } border rounded-lg p-6`}>
                <div className="text-blue-600 text-2xl mb-2">🔗</div>
                <h3 className={`font-semibold ${isDarkMode ? 'text-blue-300' : 'text-blue-800'}`}>
                  API Connection
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-blue-400' : 'text-blue-600'}`}>
                  Backend connected via proxy
                </p>
              </div>

              <div className={`${
                isDarkMode ? 'bg-purple-900/30 border-purple-700' : 'bg-purple-50 border-purple-200'
              } border rounded-lg p-6`}>
                <div className="text-purple-600 text-2xl mb-2">🏢</div>
                <h3 className={`font-semibold ${isDarkMode ? 'text-purple-300' : 'text-purple-800'}`}>
                  Tenant System
                </h3>
                <p className={`text-sm ${isDarkMode ? 'text-purple-400' : 'text-purple-600'}`}>
                  Multi-tenant ready
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className={`${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } rounded-xl border shadow-lg p-8`}>
          <h3 className={`text-xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            🚀 Quick Actions
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button className="bg-blue-600 hover:bg-blue-700 text-white p-4 rounded-lg text-left transition-colors">
              <div className="text-2xl mb-2">👥</div>
              <div className="font-semibold">User Management</div>
              <div className="text-sm opacity-90">Manage system users</div>
            </button>

            <button className="bg-green-600 hover:bg-green-700 text-white p-4 rounded-lg text-left transition-colors">
              <div className="text-2xl mb-2">🏢</div>
              <div className="font-semibold">Tenant Management</div>
              <div className="text-sm opacity-90">Manage tenants</div>
            </button>

            <button className="bg-purple-600 hover:bg-purple-700 text-white p-4 rounded-lg text-left transition-colors">
              <div className="text-2xl mb-2">📊</div>
              <div className="font-semibold">System Analytics</div>
              <div className="text-sm opacity-90">View system metrics</div>
            </button>

            <button className="bg-red-600 hover:bg-red-700 text-white p-4 rounded-lg text-left transition-colors">
              <div className="text-2xl mb-2">🛡️</div>
              <div className="font-semibold">Security Center</div>
              <div className="text-sm opacity-90">Security management</div>
            </button>
          </div>
        </div>

        {/* System Information */}
        <div className={`${
          isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
        } rounded-xl border shadow-lg p-8 mt-8`}>
          <h3 className={`text-xl font-bold mb-6 ${isDarkMode ? 'text-white' : 'text-gray-900'}`}>
            ℹ️ System Information
          </h3>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                Current Session
              </h4>
              <div className={`space-y-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <p><strong>User:</strong> {currentEmployee?.name || 'Super Administrator'}</p>
                <p><strong>Role:</strong> {currentEmployee?.role || 'super_admin'}</p>
                <p><strong>Tenant:</strong> {currentTenant?.name || 'System'}</p>
                <p><strong>Login Time:</strong> {new Date().toLocaleString()}</p>
              </div>
            </div>

            <div>
              <h4 className={`font-semibold mb-3 ${isDarkMode ? 'text-gray-300' : 'text-gray-700'}`}>
                System Status
              </h4>
              <div className={`space-y-2 text-sm ${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                <p><strong>Frontend:</strong> http://localhost:5173</p>
                <p><strong>Backend:</strong> http://localhost:4000 (via proxy)</p>
                <p><strong>Database:</strong> PostgreSQL BARPOS</p>
                <p><strong>Status:</strong> <span className="text-green-600">✅ All systems operational</span></p>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default SimpleSuperAdminInterface;
