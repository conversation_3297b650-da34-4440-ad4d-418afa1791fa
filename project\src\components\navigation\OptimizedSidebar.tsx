import React, { useState, useEffect } from 'react';
import {
  Home,
  ShoppingCart,
  Users,
  BarChart3,
  Settings,
  Package,
  Clock,
  Receipt,
  MapPin,
  Bell,
  Menu,
  X,
  ChevronLeft,
  ChevronRight,
  User,
  LogOut,
  Wifi,
  WifiOff,
  Battery,
  Signal,
  AlertCircle,
  CheckCircle,
  Zap,
  TrendingUp,
  Calendar,
  FileText
} from 'lucide-react';
import { <PERSON>ton, Badge, Flex, Text, Card } from '../ui/DesignSystem';

interface NavigationItem {
  id: string;
  name: string;
  icon: React.ReactNode;
  badge?: number;
  isActive?: boolean;
  permissions?: string[];
  category?: 'primary' | 'secondary' | 'admin';
  description?: string;
}

interface OptimizedSidebarProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  isDarkMode: boolean;
  currentUser?: {
    name: string;
    role: string;
    avatar?: string;
  };
  systemStatus?: {
    isOnline: boolean;
    printer: 'online' | 'offline' | 'error';
    payment: 'online' | 'offline' | 'error';
    kitchen: 'online' | 'offline' | 'error';
    inventory: 'synced' | 'syncing' | 'error';
  };
  notifications?: Array<{
    id: string;
    type: 'info' | 'warning' | 'error' | 'success';
    message: string;
    timestamp: Date;
  }>;
  onLogout?: () => void;
}

const OptimizedSidebar: React.FC<OptimizedSidebarProps> = ({
  activeTab,
  onTabChange,
  isCollapsed,
  onToggleCollapse,
  isDarkMode,
  currentUser,
  systemStatus = {
    isOnline: true,
    printer: 'online',
    payment: 'online',
    kitchen: 'online',
    inventory: 'synced'
  },
  notifications = [],
  onLogout
}) => {
  const [showNotifications, setShowNotifications] = useState(false);
  const [hoveredItem, setHoveredItem] = useState<string | null>(null);

  // Navigation items organized by category
  const navigationItems: NavigationItem[] = [
    // Primary Operations
    {
      id: 'pos',
      name: 'Point of Sale',
      icon: <ShoppingCart className="w-5 h-5" />,
      category: 'primary',
      description: 'Process orders and payments'
    },
    {
      id: 'floor',
      name: 'Floor Layout',
      icon: <MapPin className="w-5 h-5" />,
      category: 'primary',
      permissions: ['floor_management'],
      description: 'Manage tables and seating'
    },
    {
      id: 'orders',
      name: 'Order Queue',
      icon: <Clock className="w-5 h-5" />,
      badge: 25,
      category: 'primary',
      description: 'Track order status and kitchen'
    },

    // Business Management
    {
      id: 'inventory',
      name: 'Inventory',
      icon: <Package className="w-5 h-5" />,
      category: 'secondary',
      permissions: ['inventory_management'],
      description: 'Stock management and alerts'
    },
    {
      id: 'staff',
      name: 'Staff',
      icon: <Users className="w-5 h-5" />,
      category: 'secondary',
      permissions: ['staff_management'],
      description: 'Employee scheduling and management'
    },
    {
      id: 'analytics',
      name: 'Analytics',
      icon: <BarChart3 className="w-5 h-5" />,
      category: 'secondary',
      permissions: ['analytics'],
      description: 'Business insights and reports'
    },

    // Administrative
    {
      id: 'reports',
      name: 'Reports',
      icon: <FileText className="w-5 h-5" />,
      category: 'admin',
      permissions: ['reports'],
      description: 'Financial and operational reports'
    },
    {
      id: 'settings',
      name: 'Settings',
      icon: <Settings className="w-5 h-5" />,
      category: 'admin',
      permissions: ['settings'],
      description: 'System configuration'
    }
  ];

  // Filter items based on user permissions
  const availableItems = navigationItems.filter(item => {
    if (!item.permissions) return true;
    const userRole = currentUser?.role;
    if (userRole === 'super_admin' || userRole === 'tenant_admin') return true;
    // Add permission checking logic here
    return true;
  });

  // Group items by category
  const groupedItems = {
    primary: availableItems.filter(item => item.category === 'primary'),
    secondary: availableItems.filter(item => item.category === 'secondary'),
    admin: availableItems.filter(item => item.category === 'admin')
  };

  // Get status indicator color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online':
      case 'synced':
        return 'text-green-500';
      case 'syncing':
        return 'text-yellow-500';
      case 'offline':
      case 'error':
        return 'text-red-500';
      default:
        return 'text-gray-500';
    }
  };

  // Get status icon
  const getStatusIcon = (service: string, status: string) => {
    if (status === 'error') return <AlertCircle className="w-4 h-4" />;
    if (status === 'syncing') return <div className="w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin" />;
    return <CheckCircle className="w-4 h-4" />;
  };

  return (
    <div className={`${
      isCollapsed ? 'w-16' : 'w-64'
    } transition-all duration-300 ${
      isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'
    } border-r flex flex-col h-full`}>
      
      {/* Header */}
      <div className={`p-4 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        <Flex justify="between" align="center">
          {!isCollapsed && (
            <div className="flex items-center space-x-3">
              <div className={`p-2 rounded-lg ${
                isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
              }`}>
                <Home className="w-6 h-6 text-white" />
              </div>
              <div>
                <Text variant="body" className="font-bold text-lg">RestroFlow</Text>
                <Text variant="caption" color="muted">Industry POS</Text>
              </div>
            </div>
          )}
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggleCollapse}
            icon={isCollapsed ? ChevronRight : ChevronLeft}
            className={isCollapsed ? 'mx-auto' : ''}
          />
        </Flex>
      </div>

      {/* System Status */}
      {!isCollapsed && (
        <div className={`p-3 border-b ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <Card variant="filled" padding="sm">
            <Flex justify="between" align="center" className="mb-2">
              <Text variant="caption" className="font-medium">System Status</Text>
              <div className="flex items-center space-x-1">
                {systemStatus.isOnline ? (
                  <Wifi className="w-4 h-4 text-green-500" />
                ) : (
                  <WifiOff className="w-4 h-4 text-red-500" />
                )}
                <Signal className="w-4 h-4 text-green-500" />
                <Battery className="w-4 h-4 text-green-500" />
              </div>
            </Flex>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              {Object.entries(systemStatus).filter(([key]) => key !== 'isOnline').map(([service, status]) => (
                <Flex key={service} align="center" gap="sm">
                  <div className={getStatusColor(status)}>
                    {getStatusIcon(service, status)}
                  </div>
                  <Text variant="caption" className="capitalize">{service}</Text>
                </Flex>
              ))}
            </div>
          </Card>
        </div>
      )}

      {/* Navigation Items */}
      <div className="flex-1 overflow-y-auto py-4">
        {/* Primary Operations */}
        <div className="px-2 mb-6">
          {!isCollapsed && (
            <Text variant="caption" color="muted" className="px-3 mb-2 font-medium uppercase tracking-wide">
              Operations
            </Text>
          )}
          <nav className="space-y-1">
            {groupedItems.primary.map((item) => (
              <NavigationButton
                key={item.id}
                item={item}
                isActive={activeTab === item.id}
                isCollapsed={isCollapsed}
                isDarkMode={isDarkMode}
                onClick={() => onTabChange(item.id)}
                onHover={setHoveredItem}
                showTooltip={hoveredItem === item.id}
              />
            ))}
          </nav>
        </div>

        {/* Business Management */}
        <div className="px-2 mb-6">
          {!isCollapsed && (
            <Text variant="caption" color="muted" className="px-3 mb-2 font-medium uppercase tracking-wide">
              Management
            </Text>
          )}
          <nav className="space-y-1">
            {groupedItems.secondary.map((item) => (
              <NavigationButton
                key={item.id}
                item={item}
                isActive={activeTab === item.id}
                isCollapsed={isCollapsed}
                isDarkMode={isDarkMode}
                onClick={() => onTabChange(item.id)}
                onHover={setHoveredItem}
                showTooltip={hoveredItem === item.id}
              />
            ))}
          </nav>
        </div>

        {/* Administrative */}
        <div className="px-2">
          {!isCollapsed && (
            <Text variant="caption" color="muted" className="px-3 mb-2 font-medium uppercase tracking-wide">
              Administration
            </Text>
          )}
          <nav className="space-y-1">
            {groupedItems.admin.map((item) => (
              <NavigationButton
                key={item.id}
                item={item}
                isActive={activeTab === item.id}
                isCollapsed={isCollapsed}
                isDarkMode={isDarkMode}
                onClick={() => onTabChange(item.id)}
                onHover={setHoveredItem}
                showTooltip={hoveredItem === item.id}
              />
            ))}
          </nav>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className={`p-3 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowNotifications(!showNotifications)}
            icon={Bell}
            fullWidth={!isCollapsed}
            className="relative"
          >
            {!isCollapsed && 'Notifications'}
            {notifications.length > 0 && (
              <Badge variant="danger" size="sm" className="absolute -top-1 -right-1">
                {notifications.length}
              </Badge>
            )}
          </Button>
        </div>
      )}

      {/* User Profile */}
      <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
        {isCollapsed ? (
          <div className="flex flex-col items-center space-y-2">
            <div className={`p-2 rounded-full ${
              isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
            }`}>
              <User className="w-5 h-5" />
            </div>
            <Button
              variant="ghost"
              size="xs"
              onClick={onLogout}
              icon={LogOut}
            />
          </div>
        ) : (
          <Flex justify="between" align="center">
            <Flex align="center" gap="sm">
              <div className={`p-2 rounded-full ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
              }`}>
                <User className="w-5 h-5" />
              </div>
              <div>
                <Text variant="caption" className="font-medium">
                  {currentUser?.name || 'Employee'}
                </Text>
                <Text variant="caption" color="muted">
                  {currentUser?.role || 'Staff'}
                </Text>
              </div>
            </Flex>
            <Button
              variant="ghost"
              size="sm"
              onClick={onLogout}
              icon={LogOut}
            />
          </Flex>
        )}
      </div>
    </div>
  );
};

// Navigation Button Component
const NavigationButton: React.FC<{
  item: NavigationItem;
  isActive: boolean;
  isCollapsed: boolean;
  isDarkMode: boolean;
  onClick: () => void;
  onHover: (id: string | null) => void;
  showTooltip: boolean;
}> = ({ item, isActive, isCollapsed, isDarkMode, onClick, onHover, showTooltip }) => {
  return (
    <div className="relative">
      <button
        onClick={onClick}
        onMouseEnter={() => onHover(item.id)}
        onMouseLeave={() => onHover(null)}
        className={`w-full flex items-center space-x-3 px-3 py-3 rounded-lg transition-all duration-200 ${
          isActive
            ? isDarkMode 
              ? 'bg-blue-600 text-white' 
              : 'bg-blue-500 text-white'
            : isDarkMode 
              ? 'text-gray-300 hover:bg-gray-700' 
              : 'text-gray-700 hover:bg-gray-100'
        } ${isCollapsed ? 'justify-center' : ''}`}
      >
        <div className="relative">
          {item.icon}
          {item.badge && item.badge > 0 && (
            <Badge 
              variant="danger" 
              size="sm" 
              className="absolute -top-2 -right-2 min-w-5 h-5 text-xs"
            >
              {item.badge > 99 ? '99+' : item.badge}
            </Badge>
          )}
        </div>
        {!isCollapsed && (
          <span className="font-medium">{item.name}</span>
        )}
      </button>

      {/* Tooltip for collapsed state */}
      {isCollapsed && showTooltip && (
        <div className={`absolute left-full ml-2 top-1/2 transform -translate-y-1/2 z-50 ${
          isDarkMode ? 'bg-gray-900' : 'bg-gray-800'
        } text-white px-2 py-1 rounded text-sm whitespace-nowrap`}>
          {item.name}
          {item.description && (
            <div className="text-xs opacity-75">{item.description}</div>
          )}
        </div>
      )}
    </div>
  );
};

export default OptimizedSidebar;
