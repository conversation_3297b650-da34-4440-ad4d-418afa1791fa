import React from 'react';

const StyleTest: React.FC = () => {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 p-8">
      <div className="max-w-6xl mx-auto space-y-8">
        {/* Header Test */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-4">
            RESTROFLOW Style Test
          </h1>
          <p className="text-gray-600">Testing if Tailwind CSS and custom styles are working properly</p>
        </div>

        {/* Cards Test */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-xl shadow-lg">
            <div className="text-2xl mb-2">🎨</div>
            <h3 className="text-xl font-bold mb-2">Gradient Card</h3>
            <p className="text-blue-100">This should have a blue gradient background</p>
          </div>

          <div className="bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-xl shadow-lg">
            <div className="text-2xl mb-2">✅</div>
            <h3 className="text-xl font-bold mb-2">Green Card</h3>
            <p className="text-green-100">This should have a green gradient background</p>
          </div>

          <div className="bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-xl shadow-lg">
            <div className="text-2xl mb-2">🚀</div>
            <h3 className="text-xl font-bold mb-2">Purple Card</h3>
            <p className="text-purple-100">This should have a purple gradient background</p>
          </div>
        </div>

        {/* Buttons Test */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Button Styles Test</h2>
          <div className="flex flex-wrap gap-4">
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5">
              Primary Button
            </button>
            <button className="bg-gradient-to-r from-green-600 to-emerald-600 text-white px-6 py-3 rounded-lg font-medium hover:from-green-700 hover:to-emerald-700 transition-all duration-200">
              Success Button
            </button>
            <button className="bg-gradient-to-r from-gray-600 to-gray-700 text-white px-6 py-3 rounded-lg font-medium hover:from-gray-700 hover:to-gray-800 transition-all duration-200">
              Secondary Button
            </button>
          </div>
        </div>

        {/* Layout Test */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          <div className="xl:col-span-3 bg-white rounded-xl shadow-lg border border-gray-200 p-6">
            <h3 className="text-xl font-bold text-gray-900 mb-4">Main Content Area</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              {[1, 2, 3, 4, 5, 6, 7, 8].map((item) => (
                <div
                  key={item}
                  className="p-4 bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg hover:from-blue-50 hover:to-blue-100 transition-all duration-200 text-center border border-gray-200 hover:border-blue-300 hover:shadow-md transform hover:-translate-y-1 cursor-pointer"
                >
                  <div className="text-3xl mb-2">🍕</div>
                  <div className="font-medium text-gray-900">Item {item}</div>
                  <div className="text-sm text-gray-600">$9.99</div>
                </div>
              ))}
            </div>
          </div>

          <div className="xl:col-span-1 space-y-4">
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Sidebar</h3>
              <div className="space-y-3">
                <div className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="text-sm font-medium text-blue-900">Order Item 1</div>
                  <div className="text-xs text-blue-600">$9.99</div>
                </div>
                <div className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="text-sm font-medium text-green-900">Order Item 2</div>
                  <div className="text-xs text-green-600">$12.99</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Status Test */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Status Indicators</h2>
          <div className="flex flex-wrap gap-4">
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
              ✅ Online
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
              🔄 Processing
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
              ⚠️ Warning
            </span>
            <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
              ❌ Error
            </span>
          </div>
        </div>

        {/* Animation Test */}
        <div className="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Animation Test</h2>
          <div className="flex items-center space-x-4">
            <div className="w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-gray-600">Pulsing indicator</span>
            <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full"></div>
            <span className="text-gray-600">Spinning loader</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default StyleTest;
