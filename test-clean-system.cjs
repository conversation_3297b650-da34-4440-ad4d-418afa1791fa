/**
 * Test Clean RESTROFLOW System
 * Simple test for the essential components only
 */

const http = require('http');

async function testCleanSystem() {
  console.log('🧹 CLEAN RESTROFLOW SYSTEM TEST');
  console.log('===============================');

  // Test 1: Frontend
  console.log('\n🎨 Testing Frontend...');
  try {
    const frontendResponse = await makeRequest('http://localhost:3000');
    if (frontendResponse.status === 200) {
      console.log('✅ Frontend: WORKING on http://localhost:3000');
    } else {
      console.log('❌ Frontend: NOT WORKING');
    }
  } catch (error) {
    console.log('❌ Frontend: ERROR -', error.message);
  }

  // Test 2: Backend
  console.log('\n🔧 Testing Backend...');
  try {
    const healthResponse = await makeRequest('http://localhost:4000/api/health');
    if (healthResponse.status === 200) {
      console.log('✅ Backend: WORKING on http://localhost:4000');
      console.log(`   Status: ${healthResponse.data.status}`);
    } else {
      console.log('❌ Backend: NOT WORKING');
    }
  } catch (error) {
    console.log('❌ Backend: ERROR -', error.message);
  }

  // Test 3: Authentication
  console.log('\n🔐 Testing Authentication...');
  try {
    const authResponse = await makeRequest('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pin: '123456' })
    });

    if (authResponse.status === 200 && authResponse.data.token) {
      console.log('✅ Authentication: WORKING');
      console.log(`   User: ${authResponse.data.user?.name || 'Development User'}`);
      console.log(`   Token: ${authResponse.data.token.substring(0, 30)}...`);
    } else {
      console.log('❌ Authentication: NOT WORKING');
    }
  } catch (error) {
    console.log('❌ Authentication: ERROR -', error.message);
  }

  // Summary
  console.log('\n🎉 CLEAN SYSTEM STATUS');
  console.log('======================');
  console.log('✅ Backend API: http://localhost:4000');
  console.log('✅ Frontend POS: http://localhost:3000');
  console.log('✅ Login PIN: 123456');
  console.log('');
  console.log('🧹 CLEAN AND SIMPLE!');
  console.log('- No extra complexity');
  console.log('- No unnecessary features');
  console.log('- Just the working POS system');
  console.log('');
  console.log('🚀 Ready to use!');
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = require('http');
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

testCleanSystem();
