// Phase 3L: Global Compliance & Advanced Security System
// Global Compliance Dashboard Component

import React, { useState, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import {
  ShieldCheckIcon,
  GlobeAltIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  DocumentTextIcon,
  KeyIcon,
  EyeIcon,
  UserGroupIcon,
  CpuChipIcon,
  LockClosedIcon,
  ScaleIcon,
  BanknotesIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const GlobalComplianceDashboard = ({ className = '' }) => {
  const { t, formatNumber } = useTranslation();
  const [complianceData, setComplianceData] = useState(null);
  const [auditTrail, setAuditTrail] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedRegion, setSelectedRegion] = useState('eu');

  useEffect(() => {
    loadComplianceData();
    loadAuditTrail();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(() => {
      loadComplianceData();
      loadAuditTrail();
    }, 30000);
    
    return () => clearInterval(interval);
  }, [selectedRegion]);

  const loadComplianceData = async () => {
    try {
      setLoading(true);
      
      // Simulate comprehensive compliance data
      const mockComplianceData = {
        overview: {
          overallComplianceScore: 96.8,
          activeFrameworks: 5,
          complianceIssues: 2,
          lastAudit: '2025-06-01',
          nextAudit: '2025-12-01',
          certifications: ['ISO 27001', 'PCI DSS Level 1', 'SOC 2 Type II'],
          riskLevel: 'low'
        },
        frameworks: {
          'GDPR': {
            region: 'European Union',
            complianceScore: 98.2,
            status: 'compliant',
            lastReview: '2025-05-15',
            issues: 0,
            requirements: {
              dataSubjectRights: 100,
              consentManagement: 98,
              dataBreachNotification: 100,
              privacyByDesign: 95,
              dataPortability: 97
            },
            penalties: '€20M or 4% global turnover',
            nextDeadline: '2025-07-01'
          },
          'CCPA': {
            region: 'California, USA',
            complianceScore: 94.7,
            status: 'compliant',
            lastReview: '2025-05-20',
            issues: 1,
            requirements: {
              consumerRights: 96,
              privacyPolicy: 100,
              optOutMechanism: 92,
              dataDisclosure: 98,
              verificationProcess: 90
            },
            penalties: '$7,500 per violation',
            nextDeadline: '2025-08-15'
          },
          'PIPEDA': {
            region: 'Canada',
            complianceScore: 97.1,
            status: 'compliant',
            lastReview: '2025-05-10',
            issues: 0,
            requirements: {
              privacyPrinciples: 98,
              consentRequirements: 96,
              dataBreachNotification: 100,
              crossBorderTransfer: 95,
              privacyImpactAssessment: 97
            },
            penalties: 'Administrative + Reputational',
            nextDeadline: '2025-09-01'
          },
          'PCI_DSS': {
            region: 'Global',
            complianceScore: 99.1,
            status: 'compliant',
            lastReview: '2025-06-01',
            issues: 0,
            requirements: {
              secureNetwork: 100,
              protectCardholderData: 99,
              vulnerabilityManagement: 98,
              accessControl: 100,
              monitoring: 99,
              informationSecurity: 98
            },
            penalties: 'Fines + Card Brand Penalties',
            nextDeadline: '2025-06-01'
          },
          'ISO_27001': {
            region: 'Global',
            complianceScore: 95.8,
            status: 'compliant',
            lastReview: '2025-04-15',
            issues: 1,
            requirements: {
              informationSecurityPolicy: 98,
              riskManagement: 94,
              assetManagement: 96,
              accessControl: 97,
              incidentManagement: 95,
              businessContinuity: 93
            },
            penalties: 'Certification Loss',
            nextDeadline: '2025-10-15'
          }
        },
        security: {
          threatLevel: 'low',
          securityIncidents: 0,
          lastSecurityAudit: '2025-05-25',
          encryptionStatus: 'Quantum-Resistant',
          biometricAccuracy: 99.95,
          zeroTrustScore: 94.2,
          vulnerabilities: {
            critical: 0,
            high: 1,
            medium: 3,
            low: 7
          }
        },
        dataProtection: {
          dataSubjectRequests: {
            total: 47,
            access: 23,
            deletion: 15,
            rectification: 6,
            portability: 3,
            averageResponseTime: 2.3 // days
          },
          consentManagement: {
            totalConsents: 15847,
            activeConsents: 14923,
            withdrawnConsents: 924,
            consentRate: 94.2
          },
          dataBreaches: {
            total: 0,
            lastBreach: 'None',
            averageDetectionTime: 'N/A',
            averageContainmentTime: 'N/A'
          }
        }
      };
      
      setComplianceData(mockComplianceData);
      setError(null);
    } catch (error) {
      console.error('Failed to load compliance data:', error);
      setError('Failed to load compliance data');
    } finally {
      setLoading(false);
    }
  };

  const loadAuditTrail = async () => {
    try {
      const response = await fetch('/api/audit/trail?limit=10');
      const data = await response.json();
      setAuditTrail(data.auditTrail || []);
    } catch (error) {
      console.error('Failed to load audit trail:', error);
    }
  };

  const ComplianceFrameworkCard = ({ framework, data }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          <ScaleIcon className="w-5 h-5 text-blue-500" />
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">{framework}</h4>
        </div>
        <div className="flex items-center space-x-2">
          <span className={`px-2 py-1 text-xs rounded-full ${
            data.status === 'compliant' 
              ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
          }`}>
            {data.status}
          </span>
          <span className="text-lg font-bold text-blue-600 dark:text-blue-400">
            {data.complianceScore}%
          </span>
        </div>
      </div>
      
      <div className="space-y-2 mb-3">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Region</span>
          <span className="text-gray-900 dark:text-white">{data.region}</span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Issues</span>
          <span className={`font-medium ${data.issues === 0 ? 'text-green-600' : 'text-red-600'}`}>
            {data.issues}
          </span>
        </div>
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Last Review</span>
          <span className="text-gray-900 dark:text-white">{data.lastReview}</span>
        </div>
      </div>
      
      <div className="space-y-1">
        {Object.entries(data.requirements).slice(0, 3).map(([req, score]) => (
          <div key={req} className="flex items-center justify-between">
            <span className="text-xs text-gray-600 dark:text-gray-400 capitalize">
              {req.replace(/([A-Z])/g, ' $1').trim()}
            </span>
            <div className="flex items-center space-x-2">
              <div className="w-16 bg-gray-200 dark:bg-gray-700 rounded-full h-1">
                <div
                  className="bg-blue-500 h-1 rounded-full transition-all duration-300"
                  style={{ width: `${score}%` }}
                ></div>
              </div>
              <span className="text-xs font-medium text-gray-900 dark:text-white w-8 text-right">
                {score}%
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const SecurityMetricsCard = ({ security }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-2 mb-3">
        <ShieldCheckIcon className="w-5 h-5 text-green-500" />
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          {t('compliance.security_metrics', 'Security Metrics')}
        </h4>
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-green-600 dark:text-green-400">
            {security.threatLevel.toUpperCase()}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Threat Level</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
            {security.biometricAccuracy}%
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Biometric Accuracy</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-purple-600 dark:text-purple-400">
            {security.zeroTrustScore}%
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Zero Trust Score</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-emerald-600 dark:text-emerald-400">
            {security.securityIncidents}
          </div>
          <div className="text-xs text-gray-600 dark:text-gray-400">Security Incidents</div>
        </div>
      </div>
      
      <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600 dark:text-gray-400">Encryption</span>
          <span className="text-green-600 dark:text-green-400 font-medium">
            {security.encryptionStatus}
          </span>
        </div>
      </div>
    </div>
  );

  const AuditTrailCard = ({ events }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-2 mb-3">
        <DocumentTextIcon className="w-5 h-5 text-gray-500" />
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          {t('compliance.recent_audit_events', 'Recent Audit Events')}
        </h4>
      </div>
      
      <div className="space-y-2 max-h-64 overflow-y-auto">
        {events.slice(0, 5).map((event, index) => (
          <div key={index} className="flex items-start space-x-3 p-2 bg-gray-50 dark:bg-gray-700 rounded">
            <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
              event.success ? 'bg-green-500' : 'bg-red-500'
            }`}></div>
            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {event.action.replace(/_/g, ' ')}
                </span>
                <span className={`text-xs px-2 py-1 rounded ${
                  event.riskLevel === 'low' 
                    ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
                    : event.riskLevel === 'medium'
                    ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
                    : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
                }`}>
                  {event.riskLevel}
                </span>
              </div>
              <div className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                {event.userId} • {event.resource} • {new Date(event.timestamp).toLocaleTimeString()}
              </div>
              {event.complianceFlags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {event.complianceFlags.slice(0, 2).map((flag, flagIndex) => (
                    <span key={flagIndex} className="text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-1 py-0.5 rounded">
                      {flag}
                    </span>
                  ))}
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <ShieldCheckIcon className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">
            {t('compliance.loading', 'Loading compliance data...')}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2 text-red-500">
          <ExclamationTriangleIcon className="w-5 h-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  if (!complianceData) return null;

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {t('compliance.dashboard', 'Global Compliance Dashboard')}
          </h2>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
            {t('compliance.subtitle', 'Enterprise-grade compliance monitoring and security oversight')}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <select
            value={selectedRegion}
            onChange={(e) => setSelectedRegion(e.target.value)}
            className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
          >
            <option value="eu">European Union</option>
            <option value="us-ca">California, USA</option>
            <option value="ca">Canada</option>
            <option value="global">Global</option>
          </select>
        </div>
      </div>

      {/* Overview Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Overall Compliance</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400">
                {complianceData.overview.overallComplianceScore}%
              </p>
            </div>
            <CheckCircleIcon className="w-8 h-8 text-green-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Active Frameworks</p>
              <p className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                {complianceData.overview.activeFrameworks}
              </p>
            </div>
            <ScaleIcon className="w-8 h-8 text-blue-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Open Issues</p>
              <p className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                {complianceData.overview.complianceIssues}
              </p>
            </div>
            <ExclamationTriangleIcon className="w-8 h-8 text-orange-500" />
          </div>
        </div>
        
        <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Risk Level</p>
              <p className="text-2xl font-bold text-green-600 dark:text-green-400 capitalize">
                {complianceData.overview.riskLevel}
              </p>
            </div>
            <ShieldCheckIcon className="w-8 h-8 text-green-500" />
          </div>
        </div>
      </div>

      {/* Compliance Frameworks */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {t('compliance.frameworks', 'Compliance Frameworks')}
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Object.entries(complianceData.frameworks).map(([framework, data]) => (
            <ComplianceFrameworkCard key={framework} framework={framework} data={data} />
          ))}
        </div>
      </div>

      {/* Security and Audit */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <SecurityMetricsCard security={complianceData.security} />
        <AuditTrailCard events={auditTrail} />
      </div>
    </div>
  );
};

export default GlobalComplianceDashboard;
