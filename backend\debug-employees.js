const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function checkEmployees() {
  try {
    // Check tenant ID 3 (BARPOS System)
    const result = await pool.query('SELECT id, name, role, pin, pin_hash, is_active FROM employees WHERE tenant_id = $1', [3]);
    console.log('=== EMPLOYEES IN TENANT ID 3 (BARPOS System) ===');
    console.log('Total employees found:', result.rows.length);
    result.rows.forEach((emp, i) => {
      console.log(`${i+1}. ID: ${emp.id}, Name: ${emp.name}, Role: ${emp.role}`);
      console.log(`   PIN: ${emp.pin || 'none'}, PIN Hash: ${emp.pin_hash ? emp.pin_hash.substring(0, 20) + '...' : 'none'}`);
      console.log(`   is_active: ${emp.is_active}`);
      console.log('');
    });
    
    // Check the exact query from server
    const serverQuery = await pool.query(`
      SELECT e.*
      FROM employees e
      WHERE e.tenant_id = $1 AND (e.is_active = true OR e.is_active IS NULL)
    `, [3]);
    console.log('=== SERVER QUERY RESULT ===');
    console.log('Employees returned by server query:', serverQuery.rows.length);
    serverQuery.rows.forEach((emp, i) => {
      console.log(`${i+1}. ${emp.name} (${emp.role}) - is_active: ${emp.is_active}`);
    });
    
    await pool.end();
  } catch (error) {
    console.error('Error:', error);
    await pool.end();
  }
}

checkEmployees();
