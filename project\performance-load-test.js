#!/usr/bin/env node

/**
 * Performance & Load Testing Suite
 * Tests system performance under various load conditions
 */

import axios from 'axios';
import colors from 'colors';

const BASE_URL = 'http://localhost:4000';
const TEST_RESULTS = {
  passed: 0,
  failed: 0,
  total: 0,
  details: [],
  metrics: {}
};

// Test configuration
const LOAD_TEST_CONFIG = {
  concurrent_users: [1, 5, 10, 20],
  test_duration: 10000, // 10 seconds
  request_interval: 100 // 100ms between requests
};

let authToken = null;

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  switch(type) {
    case 'success': console.log(`[${timestamp}] ✅ ${message}`.green); break;
    case 'error': console.log(`[${timestamp}] ❌ ${message}`.red); break;
    case 'warning': console.log(`[${timestamp}] ⚠️  ${message}`.yellow); break;
    case 'info': console.log(`[${timestamp}] ℹ️  ${message}`.blue); break;
    default: console.log(`[${timestamp}] ${message}`);
  }
};

const recordTest = (testName, passed, details = '', metrics = {}) => {
  TEST_RESULTS.total++;
  if (passed) {
    TEST_RESULTS.passed++;
    log(`${testName} - PASSED ${details}`, 'success');
  } else {
    TEST_RESULTS.failed++;
    log(`${testName} - FAILED ${details}`, 'error');
  }
  TEST_RESULTS.details.push({ testName, passed, details, metrics, timestamp: new Date().toISOString() });
  
  // Store metrics
  if (Object.keys(metrics).length > 0) {
    TEST_RESULTS.metrics[testName] = metrics;
  }
};

const makeRequest = async (method, endpoint, data = null, headers = {}) => {
  const startTime = Date.now();
  try {
    const config = {
      method,
      url: `${BASE_URL}${endpoint}`,
      headers: {
        'Content-Type': 'application/json',
        ...headers
      },
      timeout: 10000 // 10 second timeout
    };
    
    if (data) config.data = data;
    
    const response = await axios(config);
    const responseTime = Date.now() - startTime;
    
    return { 
      success: true, 
      data: response.data, 
      status: response.status,
      responseTime,
      size: JSON.stringify(response.data).length
    };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    return { 
      success: false, 
      error: error.response?.data || error.message, 
      status: error.response?.status || 500,
      responseTime
    };
  }
};

const authenticate = async () => {
  const result = await makeRequest('POST', '/api/auth/login', {
    pin: '888888',
    tenant: 'demo-restaurant'
  });
  
  if (result.success && result.data.token) {
    authToken = result.data.token;
    return true;
  }
  return false;
};

const testResponseTimes = async () => {
  log('⏱️ Testing Response Times...', 'info');
  
  const endpoints = [
    { endpoint: '/api/health', method: 'GET', requiresAuth: false, name: 'Health Check' },
    { endpoint: '/api/products', method: 'GET', requiresAuth: true, name: 'Products' },
    { endpoint: '/api/categories', method: 'GET', requiresAuth: true, name: 'Categories' },
    { endpoint: '/api/floor/tables', method: 'GET', requiresAuth: true, name: 'Floor Tables' },
    { endpoint: '/api/admin/metrics', method: 'GET', requiresAuth: true, name: 'Admin Metrics' }
  ];
  
  for (const test of endpoints) {
    const headers = test.requiresAuth ? { 'Authorization': `Bearer ${authToken}` } : {};
    
    // Test multiple times to get average
    const results = [];
    for (let i = 0; i < 5; i++) {
      const result = await makeRequest(test.method, test.endpoint, null, headers);
      results.push(result);
    }
    
    const successfulResults = results.filter(r => r.success);
    if (successfulResults.length > 0) {
      const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
      const maxResponseTime = Math.max(...successfulResults.map(r => r.responseTime));
      const minResponseTime = Math.min(...successfulResults.map(r => r.responseTime));
      
      recordTest(`Response Time - ${test.name}`, avgResponseTime < 1000, 
        `Avg: ${avgResponseTime.toFixed(0)}ms, Min: ${minResponseTime}ms, Max: ${maxResponseTime}ms`,
        { avgResponseTime, maxResponseTime, minResponseTime, successRate: successfulResults.length / results.length }
      );
    } else {
      recordTest(`Response Time - ${test.name}`, false, 'All requests failed');
    }
  }
};

const testConcurrentUsers = async () => {
  log('👥 Testing Concurrent User Load...', 'info');
  
  for (const userCount of LOAD_TEST_CONFIG.concurrent_users) {
    const startTime = Date.now();
    const promises = [];
    
    // Create concurrent requests
    for (let i = 0; i < userCount; i++) {
      const promise = makeRequest('GET', '/api/health');
      promises.push(promise);
    }
    
    const results = await Promise.all(promises);
    const endTime = Date.now();
    
    const successfulRequests = results.filter(r => r.success);
    const totalTime = endTime - startTime;
    const avgResponseTime = successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length;
    
    recordTest(`Concurrent Users - ${userCount} users`, successfulRequests.length === userCount,
      `${successfulRequests.length}/${userCount} successful, Avg response: ${avgResponseTime.toFixed(0)}ms`,
      { 
        userCount, 
        successRate: successfulRequests.length / userCount,
        avgResponseTime,
        totalTime
      }
    );
  }
};

const testSustainedLoad = async () => {
  log('🔄 Testing Sustained Load...', 'info');
  
  const duration = 15000; // 15 seconds
  const interval = 200; // 200ms between requests
  const startTime = Date.now();
  const results = [];
  
  while (Date.now() - startTime < duration) {
    const result = await makeRequest('GET', '/api/health');
    results.push(result);
    
    // Wait for interval
    await new Promise(resolve => setTimeout(resolve, interval));
  }
  
  const successfulRequests = results.filter(r => r.success);
  const totalRequests = results.length;
  const avgResponseTime = successfulRequests.reduce((sum, r) => sum + r.responseTime, 0) / successfulRequests.length;
  const requestsPerSecond = (totalRequests / (duration / 1000)).toFixed(2);
  
  recordTest('Sustained Load Test', successfulRequests.length / totalRequests > 0.95,
    `${successfulRequests.length}/${totalRequests} successful (${requestsPerSecond} req/s), Avg: ${avgResponseTime.toFixed(0)}ms`,
    {
      totalRequests,
      successfulRequests: successfulRequests.length,
      successRate: successfulRequests.length / totalRequests,
      avgResponseTime,
      requestsPerSecond: parseFloat(requestsPerSecond)
    }
  );
};

const testMemoryUsage = async () => {
  log('💾 Testing Memory Usage...', 'info');
  
  // Get initial memory usage
  const initialMemory = process.memoryUsage();
  
  // Perform memory-intensive operations
  const largeRequests = [];
  for (let i = 0; i < 50; i++) {
    const headers = { 'Authorization': `Bearer ${authToken}` };
    largeRequests.push(makeRequest('GET', '/api/admin/tenants', null, headers));
  }
  
  await Promise.all(largeRequests);
  
  // Get final memory usage
  const finalMemory = process.memoryUsage();
  
  const memoryIncrease = (finalMemory.heapUsed - initialMemory.heapUsed) / 1024 / 1024; // MB
  const totalMemory = finalMemory.heapUsed / 1024 / 1024; // MB
  
  recordTest('Memory Usage', memoryIncrease < 50, // Less than 50MB increase
    `Memory increase: ${memoryIncrease.toFixed(2)}MB, Total: ${totalMemory.toFixed(2)}MB`,
    {
      initialMemoryMB: initialMemory.heapUsed / 1024 / 1024,
      finalMemoryMB: finalMemory.heapUsed / 1024 / 1024,
      memoryIncreaseMB: memoryIncrease
    }
  );
};

const testDatabasePerformance = async () => {
  log('🗄️ Testing Database Performance...', 'info');
  
  const headers = { 'Authorization': `Bearer ${authToken}` };
  
  // Test database-heavy endpoints
  const dbEndpoints = [
    { endpoint: '/api/admin/tenants', name: 'Tenant Query' },
    { endpoint: '/api/admin/metrics', name: 'Metrics Aggregation' },
    { endpoint: '/api/products', name: 'Product Listing' }
  ];
  
  for (const test of dbEndpoints) {
    const results = [];
    
    // Run multiple queries
    for (let i = 0; i < 10; i++) {
      const result = await makeRequest('GET', test.endpoint, null, headers);
      results.push(result);
    }
    
    const successfulResults = results.filter(r => r.success);
    const avgResponseTime = successfulResults.reduce((sum, r) => sum + r.responseTime, 0) / successfulResults.length;
    
    recordTest(`DB Performance - ${test.name}`, avgResponseTime < 500, // Less than 500ms
      `Avg response: ${avgResponseTime.toFixed(0)}ms, Success: ${successfulResults.length}/10`,
      { avgResponseTime, successRate: successfulResults.length / results.length }
    );
  }
};

const testErrorRecovery = async () => {
  log('🔧 Testing Error Recovery...', 'info');
  
  // Test recovery from invalid requests
  const invalidRequests = [
    makeRequest('GET', '/api/nonexistent'),
    makeRequest('POST', '/api/auth/login', { invalid: 'data' }),
    makeRequest('GET', '/api/admin/metrics') // No auth
  ];
  
  await Promise.all(invalidRequests);
  
  // Test that system still responds normally after errors
  const recoveryResult = await makeRequest('GET', '/api/health');
  
  recordTest('Error Recovery', recoveryResult.success,
    recoveryResult.success ? 'System recovered successfully' : 'System failed to recover',
    { recoveryTime: recoveryResult.responseTime }
  );
};

const runPerformanceTests = async () => {
  console.log('⚡ Starting Performance & Load Testing Suite...'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  
  try {
    // Authenticate first
    const authSuccess = await authenticate();
    if (!authSuccess) {
      console.error('❌ Failed to authenticate for performance tests');
      return;
    }
    
    await testResponseTimes();
    await testConcurrentUsers();
    await testSustainedLoad();
    await testMemoryUsage();
    await testDatabasePerformance();
    await testErrorRecovery();
    
  } catch (error) {
    console.error('💥 Performance test suite failed:', error.message);
  }
  
  // Print results
  console.log('\n' + '=' .repeat(60).cyan);
  console.log('📊 PERFORMANCE TEST RESULTS'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  console.log(`Total Tests: ${TEST_RESULTS.total}`.white);
  console.log(`Passed: ${TEST_RESULTS.passed}`.green);
  console.log(`Failed: ${TEST_RESULTS.failed}`.red);
  console.log(`Success Rate: ${((TEST_RESULTS.passed / TEST_RESULTS.total) * 100).toFixed(1)}%`.yellow);
  
  // Print performance metrics summary
  console.log('\n📈 PERFORMANCE METRICS SUMMARY:'.cyan.bold);
  for (const [testName, metrics] of Object.entries(TEST_RESULTS.metrics)) {
    if (metrics.avgResponseTime) {
      console.log(`  ${testName}: ${metrics.avgResponseTime.toFixed(0)}ms avg response`.white);
    }
  }
  
  if (TEST_RESULTS.failed > 0) {
    console.log('\n❌ FAILED TESTS:'.red.bold);
    TEST_RESULTS.details.filter(t => !t.passed).forEach(test => {
      console.log(`  - ${test.testName}: ${test.details}`.red);
    });
  }
  
  console.log('\n' + (TEST_RESULTS.failed === 0 ? '✅ ALL PERFORMANCE TESTS PASSED!'.green.bold : '⚠️ SOME PERFORMANCE TESTS FAILED'.red.bold));
};

// Run tests
runPerformanceTests();
