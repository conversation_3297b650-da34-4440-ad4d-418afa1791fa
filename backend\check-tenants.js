const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function checkTenants() {
  try {
    console.log('Checking all tenants:');
    const result = await pool.query('SELECT * FROM tenants ORDER BY id');
    console.table(result.rows);
    
    console.log('\nChecking tenant with ID 3:');
    const tenant3 = await pool.query('SELECT * FROM tenants WHERE id = 3');
    console.table(tenant3.rows);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

checkTenants();
