import React, { useState, useEffect } from 'react';
import { endpointConnector, EndpointInfo } from '../services/endpointConnector';

interface EndpointDashboardProps {
  onBack: () => void;
}

const EndpointDashboard: React.FC<EndpointDashboardProps> = ({ onBack }) => {
  const [endpoints, setEndpoints] = useState<EndpointInfo[]>([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [testResults, setTestResults] = useState<Record<string, any>>({});
  const [testing, setTesting] = useState(false);
  const [stats, setStats] = useState<Record<string, number>>({});

  useEffect(() => {
    loadEndpoints();
    loadStats();
  }, []);

  const loadEndpoints = () => {
    const allEndpoints = endpointConnector.getAllEndpoints();
    setEndpoints(allEndpoints);
  };

  const loadStats = () => {
    const endpointStats = endpointConnector.getEndpointStats();
    setStats(endpointStats);
  };

  const filteredEndpoints = selectedCategory === 'all' 
    ? endpoints 
    : endpoints.filter(endpoint => endpoint.category === selectedCategory);

  const categories = ['all', ...endpointConnector.getCategories()];

  const testAllEndpoints = async () => {
    setTesting(true);
    try {
      const results = await endpointConnector.testAllEndpoints();
      setTestResults(results);
    } catch (error) {
      console.error('Error testing endpoints:', error);
    } finally {
      setTesting(false);
    }
  };

  const testSingleEndpoint = async (endpoint: EndpointInfo) => {
    const key = `${endpoint.method} ${endpoint.path}`;
    setTestResults(prev => ({ ...prev, [key]: { testing: true } }));
    
    try {
      const result = await endpointConnector.testEndpoint(endpoint);
      setTestResults(prev => ({ ...prev, [key]: result }));
    } catch (error) {
      setTestResults(prev => ({ 
        ...prev, 
        [key]: { success: false, error: 'Test failed' } 
      }));
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-green-100 text-green-800';
      case 'POST': return 'bg-blue-100 text-blue-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      case 'PATCH': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system': return '⚙️';
      case 'auth': return '🔐';
      case 'products': return '📦';
      case 'orders': return '📋';
      case 'payments': return '💳';
      case 'admin': return '👑';
      case 'analytics': return '📊';
      case 'inventory': return '📦';
      case 'kitchen': return '🍳';
      case 'floor': return '🏢';
      case 'hardware': return '🖥️';
      case 'ai': return '🤖';
      case 'global': return '🌍';
      case 'settings': return '⚙️';
      default: return '📡';
    }
  };

  const getTestResultIcon = (result: any) => {
    if (result?.testing) return '⏳';
    if (result?.success) return '✅';
    if (result?.success === false) return '❌';
    return '⚪';
  };

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-indigo-700 text-white shadow-lg">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <button 
                onClick={onBack}
                className="bg-white/20 hover:bg-white/30 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 mr-4"
              >
                ← Back to Dashboard
              </button>
              <h1 className="text-3xl font-bold inline">📡 Endpoint Dashboard</h1>
              <p className="text-blue-100 mt-1">Scan, test, and monitor all API endpoints</p>
            </div>
            <div className="text-right">
              <button
                onClick={testAllEndpoints}
                disabled={testing}
                className="bg-green-600 hover:bg-green-700 disabled:bg-gray-500 text-white px-4 py-2 rounded-lg font-medium transition-colors duration-200 mb-2"
              >
                {testing ? '🔄 Testing...' : '🧪 Test All Endpoints'}
              </button>
              <div className="text-sm text-blue-100">
                <div>📡 {stats.total} Total Endpoints</div>
                <div>🔐 {stats.requiresAuth} Require Auth</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">📡</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Total Endpoints</p>
                <p className="text-2xl font-bold text-gray-900">{stats.total}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">🔓</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Public Endpoints</p>
                <p className="text-2xl font-bold text-gray-900">{stats.public}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">🔐</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Auth Required</p>
                <p className="text-2xl font-bold text-gray-900">{stats.requiresAuth}</p>
              </div>
            </div>
          </div>

          <div className="bg-white rounded-lg shadow-md p-6">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                  <span className="text-white font-bold">👑</span>
                </div>
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-500">Admin Only</p>
                <p className="text-2xl font-bold text-gray-900">{stats.adminOnly}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="bg-white rounded-lg shadow-md p-6 mb-8">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Filter by Category</h3>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                onClick={() => setSelectedCategory(category)}
                className={`px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
                  selectedCategory === category
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {category === 'all' ? '📡 All' : `${getCategoryIcon(category)} ${category}`}
                <span className="ml-2 bg-white/20 px-2 py-1 rounded-full text-xs">
                  {category === 'all' ? stats.total : stats[category] || 0}
                </span>
              </button>
            ))}
          </div>
        </div>

        {/* Endpoints Table */}
        <div className="bg-white rounded-lg shadow-md overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">
              API Endpoints {selectedCategory !== 'all' && `- ${selectedCategory}`}
            </h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Auth</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Test</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredEndpoints.map((endpoint, index) => {
                  const key = `${endpoint.method} ${endpoint.path}`;
                  const testResult = testResults[key];
                  
                  return (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getMethodColor(endpoint.method)}`}>
                          {endpoint.method}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">
                          {endpoint.path}
                        </code>
                      </td>
                      <td className="px-6 py-4">
                        <div className="text-sm text-gray-900">{endpoint.description}</div>
                        {endpoint.parameters && (
                          <div className="text-xs text-gray-500 mt-1">
                            Params: {endpoint.parameters.join(', ')}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          endpoint.requiresAuth ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                        }`}>
                          {endpoint.requiresAuth ? '🔐 Required' : '🔓 Public'}
                        </span>
                        {endpoint.requiredRole && (
                          <div className="text-xs text-gray-500 mt-1">
                            Role: {endpoint.requiredRole}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          endpoint.status === 'active' ? 'bg-green-100 text-green-800' : 
                          endpoint.status === 'testing' ? 'bg-yellow-100 text-yellow-800' : 
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {endpoint.status}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <span className="text-lg mr-2">{getTestResultIcon(testResult)}</span>
                          {testResult && !testResult.testing && (
                            <div className="text-xs">
                              {testResult.success ? (
                                <span className="text-green-600">
                                  {testResult.status} ({testResult.responseTime}ms)
                                </span>
                              ) : (
                                <span className="text-red-600">
                                  {testResult.status || 'Failed'}
                                </span>
                              )}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button
                          onClick={() => testSingleEndpoint(endpoint)}
                          disabled={testResult?.testing}
                          className="text-blue-600 hover:text-blue-900 disabled:text-gray-400"
                        >
                          🧪 Test
                        </button>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>

        {filteredEndpoints.length === 0 && (
          <div className="text-center py-12">
            <div className="text-6xl mb-4">📡</div>
            <h3 className="text-xl font-medium text-gray-900 mb-2">No endpoints found</h3>
            <p className="text-gray-600">
              {selectedCategory === 'all' 
                ? 'No endpoints are available.' 
                : `No endpoints found in the "${selectedCategory}" category.`}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default EndpointDashboard;
