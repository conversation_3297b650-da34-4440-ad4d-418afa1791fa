import React, { useState, useEffect } from 'react';
import {
  Users,
  UserPlus,
  Edit3,
  Trash2,
  Shield,
  Key,
  Mail,
  Phone,
  CheckCircle,
  XCircle,
  AlertTriangle,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  Settings,
  Eye,
  EyeOff,
  Lock,
  Unlock,
  User<PERSON>heck,
  UserX,
  Crown,
  Star,
  Clock,
  X
} from 'lucide-react';
import { comprehensiveAdminApiService } from '../services/comprehensiveAdminApiService';

interface User {
  id: string;
  name: string;
  email: string;
  role: string;
  status: 'active' | 'inactive' | 'suspended';
  tenant_id: string;
  tenant_name: string;
  last_login: string;
  created_at: string;
  permissions: string[];
  phone?: string;
  avatar?: string;
}

interface UserAction {
  id: string;
  type: 'edit' | 'role' | 'permissions' | 'status' | 'password' | 'delete';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresConfirmation?: boolean;
  adminOnly?: boolean;
}

interface EnhancedUserManagerProps {
  users?: any[];
  metrics?: any;
  isDarkMode?: boolean;
}

interface UserStatistics {
  overview: {
    totalUsers: number;
    activeUsers: number;
    inactiveUsers: number;
    activeToday: number;
    activeThisWeek: number;
    newUsersThisMonth: number;
  };
  roleDistribution: {
    superAdmins: number;
    tenantAdmins: number;
    managers: number;
    employees: number;
  };
  recentActivity: Array<{
    id: string;
    name: string;
    email: string;
    role: string;
    lastLogin: string;
    tenantName: string;
    isNewUser: boolean;
  }>;
}

interface UserActivity {
  type: string;
  date: string;
  description: string;
  metadata: any;
}

export const EnhancedUserManager: React.FC<EnhancedUserManagerProps> = ({
  users: propUsers = [],
  metrics: propMetrics = null,
  isDarkMode = false
}) => {
  const [users, setUsers] = useState<User[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  // Enhanced state for new features
  const [userStatistics, setUserStatistics] = useState<UserStatistics | null>(null);
  const [selectedUserActivity, setSelectedUserActivity] = useState<UserActivity[]>([]);
  const [showUserProfile, setShowUserProfile] = useState<string | null>(null);
  const [showActivityModal, setShowActivityModal] = useState<string | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [advancedFilters, setAdvancedFilters] = useState({
    dateRange: '',
    lastLoginDays: '',
    createdDateRange: ''
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterTenant, setFilterTenant] = useState<string>('all');
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState<string | null>(null);
  const [showRoleModal, setShowRoleModal] = useState<string | null>(null);
  const [showPermissionsModal, setShowPermissionsModal] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form states
  const [createUserForm, setCreateUserForm] = useState({
    name: '',
    email: '',
    role: 'employee',
    tenant_id: '',
    phone: '',
    permissions: [] as string[]
  });

  const [editUserForm, setEditUserForm] = useState<Partial<User>>({});
  const [selectedUserForRole, setSelectedUserForRole] = useState<User | null>(null);
  const [selectedUserForPermissions, setSelectedUserForPermissions] = useState<User | null>(null);

  // Available tenants for user assignment
  const [tenants, setTenants] = useState<Array<{id: string, name: string, slug?: string, status?: string}>>([]);

  // Available permissions
  const [availablePermissions, setAvailablePermissions] = useState([
    'pos_access',
    'reports_view',
    'user_management',
    'inventory_management',
    'financial_reports',
    'system_settings',
    'tenant_admin',
    'analytics_view'
  ]);

  // User actions configuration
  const userActions: UserAction[] = [
    {
      id: 'edit_user',
      type: 'edit',
      label: 'Edit User',
      icon: Edit3,
      color: 'blue'
    },
    {
      id: 'change_role',
      type: 'role',
      label: 'Change Role',
      icon: Shield,
      color: 'purple',
      adminOnly: true
    },
    {
      id: 'manage_permissions',
      type: 'permissions',
      label: 'Permissions',
      icon: Key,
      color: 'orange',
      adminOnly: true
    },
    {
      id: 'reset_password',
      type: 'password',
      label: 'Reset Password',
      icon: Lock,
      color: 'yellow',
      requiresConfirmation: true
    },
    {
      id: 'toggle_status',
      type: 'status',
      label: 'Toggle Status',
      icon: UserCheck,
      color: 'green',
      requiresConfirmation: true
    },
    {
      id: 'delete_user',
      type: 'delete',
      label: 'Delete User',
      icon: Trash2,
      color: 'red',
      requiresConfirmation: true,
      adminOnly: true
    }
  ];

  const roles = [
    { id: 'super_admin', name: 'Super Admin', color: 'purple' },
    { id: 'tenant_admin', name: 'Tenant Admin', color: 'blue' },
    { id: 'manager', name: 'Manager', color: 'green' },
    { id: 'employee', name: 'Employee', color: 'gray' }
  ];

  // Enhanced data loading - use props if available, otherwise fetch from API
  useEffect(() => {
    console.log('🚀 Enhanced UserManager component mounted, loading comprehensive data...');
    if (propUsers && propUsers.length > 0) {
      console.log('📊 Using provided users data:', propUsers);
      loadUsersFromProps();
    } else {
      console.log('📡 No props data, fetching from API...');
      loadUsersData();
    }
    loadTenantsData();
    loadUserStatistics();
  }, [propUsers]);

  // Load comprehensive user statistics
  const loadUserStatistics = async () => {
    try {
      console.log('📊 Loading user statistics...');
      const response = await fetch('http://localhost:4000/api/admin/users/statistics', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const stats = await response.json();
        setUserStatistics(stats);
        console.log('✅ User statistics loaded:', stats);
      } else {
        console.warn('⚠️ Failed to load user statistics');
      }
    } catch (error) {
      console.error('💥 Error loading user statistics:', error);
    }
  };

  // Load user activity timeline
  const loadUserActivity = async (userId: string) => {
    try {
      console.log(`📋 Loading activity for user ${userId}...`);
      const response = await fetch(`http://localhost:4000/api/admin/users/${userId}/activity`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setSelectedUserActivity(data.activities);
        console.log(`✅ Loaded ${data.activities.length} activities for user ${userId}`);
      } else {
        console.warn(`⚠️ Failed to load activity for user ${userId}`);
        setSelectedUserActivity([]);
      }
    } catch (error) {
      console.error('💥 Error loading user activity:', error);
      setSelectedUserActivity([]);
    }
  };

  const loadTenantsData = async () => {
    try {
      console.log('🏢 Loading tenants for dropdown...');
      const tenantsData = await comprehensiveAdminApiService.getTenants();
      console.log('✅ Loaded tenants:', tenantsData);

      // Transform tenants data for dropdown
      const dropdownTenants = tenantsData.map((tenant: any) => ({
        id: tenant.id.toString(),
        name: tenant.name,
        slug: tenant.slug,
        status: tenant.status
      }));

      setTenants(dropdownTenants);
    } catch (error) {
      console.error('💥 Error loading tenants:', error);
      // Keep empty array if API fails
      setTenants([]);
    }
  };

  // Filter users
  useEffect(() => {
    let filtered = users.filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           user.tenant_name.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesRole = filterRole === 'all' || user.role === filterRole;
      const matchesStatus = filterStatus === 'all' || user.status === filterStatus;
      const matchesTenant = filterTenant === 'all' || user.tenant_id === filterTenant;
      
      return matchesSearch && matchesRole && matchesStatus && matchesTenant;
    });

    setFilteredUsers(filtered);
  }, [users, searchTerm, filterRole, filterStatus, filterTenant]);

  const loadUsersFromProps = () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('📊 Loading users from props:', propUsers);

      // Transform the prop data to match frontend expectations
      const transformedUsers = propUsers.map((user: any) => ({
        id: user.id.toString(),
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status || (user.isActive ? 'active' : 'inactive'),
        tenant_id: user.tenantId?.toString() || user.tenant_id?.toString() || '',
        tenant_name: user.tenantName || user.tenant_name || 'Unknown',
        last_login: user.lastLogin || user.last_login || '',
        created_at: user.createdAt || user.created_at || '',
        updated_at: user.updatedAt || user.updated_at || '',
        permissions: user.permissions || [],
        phone: user.phone || ''
      }));

      console.log('🔄 Transformed users from props:', transformedUsers);
      setUsers(transformedUsers);
    } catch (error) {
      console.error('💥 Error loading users from props:', error);
      setError('Failed to load users data from props.');
    } finally {
      setIsLoading(false);
    }
  };

  const loadUsersData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      console.log('👥 Loading users data...');
      const usersData = await comprehensiveAdminApiService.getUsers();
      console.log('📊 Raw API response:', usersData);

      // Transform the data to match frontend expectations
      const transformedUsers = usersData.map((user: any) => ({
        id: user.id.toString(),
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status || (user.isActive ? 'active' : 'inactive'), // Handle both formats
        tenant_id: user.tenantId?.toString() || user.tenant_id?.toString() || '',
        tenant_name: user.tenantName || user.tenant_name || 'Unknown',
        last_login: user.lastLogin || user.last_login || '',
        created_at: user.createdAt || user.created_at || '',
        updated_at: user.updatedAt || user.updated_at || '',
        permissions: user.permissions || [],
        phone: user.phone || ''
      }));

      console.log('🔄 Transformed users:', transformedUsers);
      setUsers(transformedUsers);
    } catch (error) {
      console.error('💥 Error loading users:', error);
      setError('Failed to load users data. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleUserAction = async (action: UserAction, userId: string) => {
    console.log('🔧 User action clicked:', { action: action.id, userId, type: action.type });

    if (action.requiresConfirmation) {
      console.log('⚠️ Action requires confirmation, showing dialog');
      setShowConfirmDialog(`${action.id}_${userId}`);
      return;
    }

    console.log('▶️ Executing action directly');
    await executeUserAction(action, userId);
  };

  const executeUserAction = async (action: UserAction, userId: string) => {
    console.log('🚀 Executing user action:', { action: action.id, userId, type: action.type });
    setActionLoading(`${action.id}_${userId}`);
    setError(null);

    try {
      switch (action.type) {
        case 'edit':
          console.log('✏️ Executing edit action');
          await handleEditUser(userId);
          break;

        case 'role':
          console.log('👤 Executing role change action');
          await handleChangeRole(userId);
          break;

        case 'permissions':
          console.log('🔐 Executing permissions action');
          await handleManagePermissions(userId);
          break;

        case 'password':
          console.log('🔑 Executing password reset action');
          await handlePasswordReset(userId);
          break;

        case 'status':
          console.log('🔄 Executing status toggle action');
          await handleStatusToggle(userId);
          break;

        case 'delete':
          console.log('🗑️ Executing delete action');
          await handleDeleteUser(userId);
          break;

        default:
          console.warn('❓ Unknown action type:', action.type);
      }
      console.log('✅ Action completed successfully');
    } catch (error) {
      console.error('💥 Error executing action:', error);
      setError(`Failed to ${action.label.toLowerCase()}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setActionLoading(null);
      setShowConfirmDialog(null);
    }
  };

  // Create new user
  const handleCreateUser = async () => {
    setActionLoading('create_user');
    setError(null);
    setSuccess(null);

    try {
      // Validate form
      if (!createUserForm.name || !createUserForm.email || !createUserForm.tenant_id) {
        throw new Error('Please fill in all required fields');
      }

      console.log('🆕 Creating new user:', createUserForm);
      const newUser = await comprehensiveAdminApiService.createUser(createUserForm);

      // Transform the new user data to match frontend expectations
      const transformedUser = {
        id: newUser.id.toString(),
        name: newUser.name,
        email: newUser.email,
        role: newUser.role,
        status: newUser.status || 'active',
        tenant_id: newUser.tenantId?.toString() || newUser.tenant_id?.toString() || '',
        tenant_name: newUser.tenantName || newUser.tenant_name || 'Unknown',
        last_login: newUser.lastLogin || newUser.last_login || '',
        created_at: newUser.createdAt || newUser.created_at || '',
        updated_at: newUser.updatedAt || newUser.updated_at || '',
        permissions: newUser.permissions || [],
        phone: newUser.phone || ''
      };

      setUsers(prev => [...prev, transformedUser]);
      setCreateUserForm({
        name: '',
        email: '',
        role: 'employee',
        tenant_id: '',
        phone: '',
        permissions: []
      });
      setShowCreateModal(false);
      setSuccess('User created successfully');
    } catch (error) {
      console.error('💥 Error creating user:', error);
      setError(error instanceof Error ? error.message : 'Failed to create user');
    } finally {
      setActionLoading(null);
    }
  };

  // Edit user
  const handleEditUser = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    setEditUserForm(user);
    setShowEditModal(userId);
  };

  const handleUpdateUser = async () => {
    if (!showEditModal) return;

    setActionLoading('update_user');
    setError(null);
    setSuccess(null);

    try {
      console.log('✏️ Updating user:', showEditModal, editUserForm);
      const updatedUser = await comprehensiveAdminApiService.updateUser(showEditModal, editUserForm);

      // Transform the updated user data
      const transformedUser = {
        id: updatedUser.id.toString(),
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status || 'active',
        tenant_id: updatedUser.tenantId?.toString() || updatedUser.tenant_id?.toString() || '',
        tenant_name: updatedUser.tenantName || updatedUser.tenant_name || 'Unknown',
        last_login: updatedUser.lastLogin || updatedUser.last_login || '',
        created_at: updatedUser.createdAt || updatedUser.created_at || '',
        updated_at: updatedUser.updatedAt || updatedUser.updated_at || '',
        permissions: updatedUser.permissions || [],
        phone: updatedUser.phone || ''
      };

      setUsers(prev => prev.map(u =>
        u.id === showEditModal ? { ...u, ...transformedUser } : u
      ));
      setShowEditModal(null);
      setEditUserForm({});
      setSuccess('User updated successfully');
    } catch (error) {
      console.error('💥 Error updating user:', error);
      setError(error instanceof Error ? error.message : 'Failed to update user');
    } finally {
      setActionLoading(null);
    }
  };

  // Change user role
  const handleChangeRole = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    setSelectedUserForRole(user);
    setShowRoleModal(userId);
  };

  const handleRoleUpdate = async (newRole: string) => {
    if (!selectedUserForRole) return;

    setActionLoading('change_role');
    setError(null);
    setSuccess(null);

    try {
      console.log('👤 Updating user role:', selectedUserForRole.id, newRole);
      await comprehensiveAdminApiService.updateUser(selectedUserForRole.id, { role: newRole });

      setUsers(prev => prev.map(u =>
        u.id === selectedUserForRole.id ? { ...u, role: newRole } : u
      ));
      setShowRoleModal(null);
      setSelectedUserForRole(null);
      setSuccess('User role updated successfully');
    } catch (error) {
      console.error('💥 Error updating role:', error);
      setError(error instanceof Error ? error.message : 'Failed to update user role');
    } finally {
      setActionLoading(null);
    }
  };

  // Manage user permissions
  const handleManagePermissions = async (userId: string) => {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    setSelectedUserForPermissions(user);
    setShowPermissionsModal(userId);
  };

  const handlePermissionsUpdate = async (newPermissions: string[]) => {
    if (!selectedUserForPermissions) return;

    setActionLoading('update_permissions');
    setError(null);
    setSuccess(null);

    try {
      console.log('🔐 Updating user permissions:', selectedUserForPermissions.id, newPermissions);
      await comprehensiveAdminApiService.updateUser(selectedUserForPermissions.id, { permissions: newPermissions });

      setUsers(prev => prev.map(u =>
        u.id === selectedUserForPermissions.id ? { ...u, permissions: newPermissions } : u
      ));
      setShowPermissionsModal(null);
      setSelectedUserForPermissions(null);
      setSuccess('User permissions updated successfully');
    } catch (error) {
      console.error('💥 Error updating permissions:', error);
      setError(error instanceof Error ? error.message : 'Failed to update permissions');
    } finally {
      setActionLoading(null);
    }
  };

  const handlePasswordReset = async (userId: string) => {
    try {
      console.log('🔑 Resetting password for user:', userId);
      const result = await comprehensiveAdminApiService.resetUserPassword(userId);
      setSuccess(`Password reset successfully. New temporary password: ${result.temporary_password || 'Check email for new password'}`);
    } catch (error) {
      console.error('💥 Error resetting password:', error);
      throw new Error('Failed to reset password');
    }
  };

  const handleStatusToggle = async (userId: string) => {
    console.log('🔄 Status toggle called for user:', userId);
    const user = users.find(u => u.id === userId);
    if (!user) {
      console.error('❌ User not found:', userId);
      return;
    }

    console.log('👤 Current user:', user);
    const newStatus = user.status === 'active' ? 'inactive' : 'active';
    const newIsActive = newStatus === 'active';
    console.log('🔄 Changing status from', user.status, 'to', newStatus, '(isActive:', newIsActive, ')');

    try {
      console.log('📡 Updating user status via API...');
      await comprehensiveAdminApiService.updateUser(userId, {
        status: newStatus,
        is_active: newIsActive
      });

      setUsers(prev => prev.map(u =>
        u.id === userId ? { ...u, status: newStatus } : u
      ));
      setSuccess(`User status updated to ${newStatus}`);
      console.log('✅ Status updated successfully');
    } catch (error) {
      console.error('💥 Status toggle error:', error);
      throw error;
    }
  };

  const handleDeleteUser = async (userId: string) => {
    try {
      console.log('🗑️ Deleting user:', userId);
      await comprehensiveAdminApiService.deleteUser(userId);

      setUsers(prev => prev.filter(u => u.id !== userId));
      setSuccess('User deleted successfully');

      // Refresh statistics after deletion
      loadUserStatistics();
    } catch (error) {
      console.error('💥 Error deleting user:', error);
      throw new Error('Failed to delete user');
    }
  };

  // Enhanced user profile view
  const handleViewUserProfile = async (userId: string) => {
    console.log('👤 Opening user profile for:', userId);
    setShowUserProfile(userId);
    await loadUserActivity(userId);
  };

  // Enhanced user activity view
  const handleViewUserActivity = async (userId: string) => {
    console.log('📋 Opening activity timeline for:', userId);
    setShowActivityModal(userId);
    await loadUserActivity(userId);
  };

  // Enhanced bulk operations
  const handleEnhancedBulkAction = async (action: string, userIds: string[], additionalData?: any) => {
    try {
      setActionLoading(`bulk_${action}`);
      console.log(`📦 Enhanced bulk action: ${action} on ${userIds.length} users`);

      const response = await fetch('http://localhost:4000/api/admin/users/bulk-actions', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          action,
          userIds,
          ...additionalData
        })
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Bulk action completed:', result);

        // Refresh data
        if (propUsers && propUsers.length > 0) {
          loadUsersFromProps();
        } else {
          loadUsersData();
        }
        loadUserStatistics();

        setSuccess(`Bulk ${action} completed: ${result.summary.successful} successful, ${result.summary.failed} failed`);
        setSelectedUsers([]);
      } else {
        throw new Error('Bulk action failed');
      }
    } catch (error) {
      console.error('💥 Error in enhanced bulk action:', error);
      setError(`Failed to perform bulk ${action}`);
    } finally {
      setActionLoading(null);
    }
  };

  // Export users functionality
  const handleExportUsers = async (format: string = 'csv') => {
    try {
      setActionLoading('export');
      console.log(`📤 Exporting users in ${format} format...`);

      const response = await fetch(`http://localhost:4000/api/admin/users/export?format=${format}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        if (format === 'csv') {
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `users-export-${new Date().toISOString().split('T')[0]}.csv`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        } else {
          const data = await response.json();
          console.log('📊 Export data:', data);
        }

        setSuccess(`Users exported successfully in ${format} format`);
      } else {
        throw new Error('Export failed');
      }
    } catch (error) {
      console.error('💥 Error exporting users:', error);
      setError('Failed to export users');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedUsers.length === 0) {
      setError('Please select users to perform bulk action');
      return;
    }

    setActionLoading(action);
    setError(null);
    setSuccess(null);

    try {
      console.log('📦 Performing bulk action:', action, 'on users:', selectedUsers);
      await comprehensiveAdminApiService.bulkUserAction(action, selectedUsers);

      await loadUsersData();
      setSelectedUsers([]);
      setSuccess(`Bulk ${action} completed successfully`);
    } catch (error) {
      console.error('💥 Error performing bulk action:', error);
      setError(error instanceof Error ? error.message : `Failed to perform ${action}`);
    } finally {
      setActionLoading(null);
    }
  };



  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'suspended': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleColor = (role: string) => {
    const roleConfig = roles.find(r => r.id === role);
    switch (roleConfig?.color) {
      case 'purple': return 'bg-purple-100 text-purple-800';
      case 'blue': return 'bg-blue-100 text-blue-800';
      case 'green': return 'bg-green-100 text-green-800';
      case 'gray': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin': return <Crown className="h-4 w-4" />;
      case 'tenant_admin': return <Star className="h-4 w-4" />;
      case 'manager': return <Shield className="h-4 w-4" />;
      case 'employee': return <Users className="h-4 w-4" />;
      default: return <Users className="h-4 w-4" />;
    }
  };

  const getActionButtonClass = (action: UserAction) => {
    const baseClass = "flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed";
    
    switch (action.color) {
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'yellow': return `${baseClass} bg-yellow-600 hover:bg-yellow-700 text-white`;
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'red': return `${baseClass} bg-red-600 hover:bg-red-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading users...</span>
      </div>
    );
  }

  return (
    <div className={`rounded-lg shadow-lg p-6 transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gray-800 border border-gray-700'
        : 'bg-white'
    }`}>
      {/* Enhanced Header with Statistics */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className={`text-2xl font-bold transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Enhanced User Management
          </h2>
          <p className={`transition-colors duration-300 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Comprehensive user management with analytics, activity tracking, and advanced operations
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowBulkActions(!showBulkActions)}
            className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors duration-200 ${
              showBulkActions
                ? 'bg-blue-600 text-white'
                : isDarkMode
                  ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            <Settings className="h-4 w-4" />
            <span>Bulk Actions</span>
          </button>
          <button
            onClick={() => handleExportUsers('csv')}
            disabled={actionLoading === 'export'}
            className="flex items-center space-x-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            {actionLoading === 'export' ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <Download className="h-4 w-4" />
            )}
            <span>Export CSV</span>
          </button>
          <button
            onClick={() => setShowCreateModal(true)}
            className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <UserPlus className="h-4 w-4" />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Real-time Statistics Dashboard */}
      {userStatistics && (
        <div className="mb-6">
          <h3 className={`text-lg font-semibold mb-4 transition-colors duration-300 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            User Statistics Dashboard
          </h3>

          {/* Overview Cards */}
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-4">
            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-blue-50 border-blue-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Users className="h-8 w-8 text-blue-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.overview.totalUsers}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Total Users
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-green-50 border-green-200'
            }`}>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.overview.activeUsers}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Active Users
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-yellow-50 border-yellow-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.overview.activeToday}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Active Today
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-purple-50 border-purple-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Crown className="h-8 w-8 text-purple-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.roleDistribution.superAdmins}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Super Admins
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-orange-50 border-orange-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Shield className="h-8 w-8 text-orange-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.roleDistribution.tenantAdmins}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Tenant Admins
                  </p>
                </div>
              </div>
            </div>

            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-indigo-50 border-indigo-200'
            }`}>
              <div className="flex items-center space-x-3">
                <Star className="h-8 w-8 text-indigo-600" />
                <div>
                  <p className={`text-2xl font-bold transition-colors duration-300 ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {userStatistics.overview.newUsersThisMonth}
                  </p>
                  <p className={`text-sm transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    New This Month
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          {userStatistics.recentActivity.length > 0 && (
            <div className={`p-4 rounded-lg border transition-colors duration-300 ${
              isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
            }`}>
              <h4 className={`font-semibold mb-3 transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Recent User Activity
              </h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                {userStatistics.recentActivity.slice(0, 6).map((activity, index) => (
                  <div key={index} className={`p-3 rounded border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
                  }`}>
                    <div className="flex items-center space-x-2">
                      <div className={`w-2 h-2 rounded-full ${activity.isNewUser ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                      <span className={`font-medium transition-colors duration-300 ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {activity.name}
                      </span>
                    </div>
                    <p className={`text-sm transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      {activity.role} • {activity.tenantName}
                    </p>
                    <p className={`text-xs transition-colors duration-300 ${
                      isDarkMode ? 'text-gray-500' : 'text-gray-500'
                    }`}>
                      Last login: {activity.lastLogin ? new Date(activity.lastLogin).toLocaleDateString() : 'Never'}
                    </p>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Enhanced Filters */}
      <div className="mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
          {/* Search */}
          <div className="relative">
            <Search className={`absolute left-3 top-3 h-4 w-4 transition-colors duration-300 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-400'
            }`} />
            <input
              type="text"
              placeholder="Search users by name, email..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
                isDarkMode
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400'
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
            />
          </div>

          {/* Role Filter */}
          <select
            value={filterRole}
            onChange={(e) => setFilterRole(e.target.value)}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="all">All Roles</option>
            {roles.map(role => (
              <option key={role.id} value={role.id}>{role.name}</option>
            ))}
          </select>

          {/* Status Filter */}
          <select
            value={filterStatus}
            onChange={(e) => setFilterStatus(e.target.value)}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="all">All Status</option>
            <option value="active">Active</option>
            <option value="inactive">Inactive</option>
            <option value="suspended">Suspended</option>
          </select>

          {/* Tenant Filter */}
          <select
            value={filterTenant}
            onChange={(e) => setFilterTenant(e.target.value)}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="all">All Tenants</option>
            {tenants.map(tenant => (
              <option key={tenant.id} value={tenant.id}>{tenant.name}</option>
            ))}
          </select>
        </div>

        {/* Advanced Filters */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <select
            value={advancedFilters.lastLoginDays}
            onChange={(e) => setAdvancedFilters(prev => ({ ...prev, lastLoginDays: e.target.value }))}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="">Last Login (All)</option>
            <option value="1">Last 24 hours</option>
            <option value="7">Last 7 days</option>
            <option value="30">Last 30 days</option>
            <option value="90">Last 90 days</option>
            <option value="never">Never logged in</option>
          </select>

          <select
            value={advancedFilters.createdDateRange}
            onChange={(e) => setAdvancedFilters(prev => ({ ...prev, createdDateRange: e.target.value }))}
            className={`px-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-white'
                : 'bg-white border-gray-300 text-gray-900'
            }`}
          >
            <option value="">Created Date (All)</option>
            <option value="today">Today</option>
            <option value="week">This week</option>
            <option value="month">This month</option>
            <option value="quarter">This quarter</option>
          </select>

          <button
            onClick={() => {
              setSearchTerm('');
              setFilterRole('all');
              setFilterStatus('all');
              setFilterTenant('all');
              setAdvancedFilters({ dateRange: '', lastLoginDays: '', createdDateRange: '' });
            }}
            className={`px-4 py-2 border rounded-lg transition-colors duration-200 ${
              isDarkMode
                ? 'bg-gray-700 border-gray-600 text-gray-300 hover:bg-gray-600'
                : 'bg-gray-100 border-gray-300 text-gray-700 hover:bg-gray-200'
            }`}
          >
            Clear Filters
          </button>
        </div>
      </div>

      {/* Enhanced Bulk Actions */}
      {showBulkActions && selectedUsers.length > 0 && (
        <div className={`border rounded-lg p-4 mb-6 transition-colors duration-300 ${
          isDarkMode
            ? 'bg-gray-700 border-gray-600'
            : 'bg-blue-50 border-blue-200'
        }`}>
          <div className="flex items-center justify-between mb-3">
            <span className={`font-medium transition-colors duration-300 ${
              isDarkMode ? 'text-white' : 'text-blue-800'
            }`}>
              {selectedUsers.length} user(s) selected
            </span>
            <button
              onClick={() => setSelectedUsers([])}
              className={`text-sm transition-colors duration-200 ${
                isDarkMode ? 'text-gray-400 hover:text-white' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Clear Selection
            </button>
          </div>
          <div className="flex flex-wrap gap-2">
            <button
              onClick={() => handleEnhancedBulkAction('activate', selectedUsers)}
              disabled={actionLoading?.startsWith('bulk_')}
              className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700 disabled:opacity-50"
            >
              Activate All
            </button>
            <button
              onClick={() => handleEnhancedBulkAction('deactivate', selectedUsers)}
              disabled={actionLoading?.startsWith('bulk_')}
              className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700 disabled:opacity-50"
            >
              Deactivate All
            </button>
            <button
              onClick={() => handleEnhancedBulkAction('reset_pin', selectedUsers)}
              disabled={actionLoading?.startsWith('bulk_')}
              className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700 disabled:opacity-50"
            >
              Reset PINs
            </button>
            <button
              onClick={() => {
                const newRole = prompt('Enter new role (super_admin, tenant_admin, manager, employee):');
                if (newRole && ['super_admin', 'tenant_admin', 'manager', 'employee'].includes(newRole)) {
                  handleEnhancedBulkAction('change_role', selectedUsers, { role: newRole });
                }
              }}
              disabled={actionLoading?.startsWith('bulk_')}
              className="px-3 py-1 bg-purple-600 text-white rounded text-sm hover:bg-purple-700 disabled:opacity-50"
            >
              Change Role
            </button>
            <button
              onClick={() => {
                if (confirm(`Are you sure you want to delete ${selectedUsers.length} users? This action cannot be undone.`)) {
                  handleEnhancedBulkAction('delete', selectedUsers);
                }
              }}
              disabled={actionLoading?.startsWith('bulk_')}
              className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700 disabled:opacity-50"
            >
              Delete All
            </button>
          </div>
        </div>
      )}

      {/* Bulk Actions */}
      {selectedUsers.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-blue-800 font-medium">
              {selectedUsers.length} user(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkAction('activate')}
                className="px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700"
              >
                Activate
              </button>
              <button
                onClick={() => handleBulkAction('deactivate')}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Deactivate
              </button>
              <button
                onClick={() => handleBulkAction('reset_passwords')}
                className="px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700"
              >
                Reset Passwords
              </button>
              <button
                onClick={() => setSelectedUsers([])}
                className="px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 font-medium text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Success Message */}
      {success && (
        <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
            <span className="text-green-800">{success}</span>
          </div>
          <button
            onClick={() => setSuccess(null)}
            className="mt-2 text-green-600 hover:text-green-800 font-medium text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Users Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-200">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-200 px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedUsers.length === filteredUsers.length && filteredUsers.length > 0}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedUsers(filteredUsers.map(user => user.id));
                    } else {
                      setSelectedUsers([]);
                    }
                  }}
                  className="rounded"
                />
              </th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">User</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Role</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Status</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Tenant</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Last Login</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredUsers.map((user) => (
              <tr key={user.id} className="hover:bg-gray-50">
                <td className="border border-gray-200 px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedUsers.includes(user.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(prev => [...prev, user.id]);
                      } else {
                        setSelectedUsers(prev => prev.filter(id => id !== user.id));
                      }
                    }}
                    className="rounded"
                  />
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                      <Users className="h-4 w-4 text-gray-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{user.name}</div>
                      <div className="text-sm text-gray-500">{user.email}</div>
                      {user.phone && (
                        <div className="text-sm text-gray-500">{user.phone}</div>
                      )}
                    </div>
                  </div>
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="flex items-center space-x-2">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRoleColor(user.role)}`}>
                      <span className="flex items-center space-x-1">
                        {getRoleIcon(user.role)}
                        <span>{roles.find(r => r.id === user.role)?.name || user.role}</span>
                      </span>
                    </span>
                  </div>
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                    {user.status.toUpperCase()}
                  </span>
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                  {user.tenant_name}
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                  {new Date(user.last_login).toLocaleDateString()}
                </td>
                <td className={`border px-4 py-3 transition-colors duration-300 ${
                  isDarkMode ? 'border-gray-600' : 'border-gray-200'
                }`}>
                  <div className="flex flex-wrap gap-1">
                    {/* Enhanced Action Buttons */}
                    <button
                      onClick={() => handleViewUserProfile(user.id)}
                      className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-blue-600 hover:bg-blue-700 text-white"
                      title="View Profile"
                    >
                      <Eye className="h-3 w-3" />
                      <span className="hidden sm:inline">Profile</span>
                    </button>

                    <button
                      onClick={() => handleViewUserActivity(user.id)}
                      className="flex items-center space-x-1 px-2 py-1 rounded text-xs font-medium transition-all duration-200 bg-indigo-600 hover:bg-indigo-700 text-white"
                      title="View Activity"
                    >
                      <Clock className="h-3 w-3" />
                      <span className="hidden sm:inline">Activity</span>
                    </button>

                    {/* Original Action Buttons */}
                    {userActions.map((action) => (
                      <button
                        key={action.id}
                        onClick={() => {
                          console.log('🖱️ Button clicked:', { action: action.id, userId: user.id, actionLabel: action.label });
                          handleUserAction(action, user.id);
                        }}
                        disabled={actionLoading === `${action.id}_${user.id}`}
                        className={getActionButtonClass(action)}
                        title={action.label}
                      >
                        {actionLoading === `${action.id}_${user.id}` ? (
                          <RefreshCw className="h-3 w-3 animate-spin" />
                        ) : (
                          <action.icon className="h-3 w-3" />
                        )}
                        <span className="hidden sm:inline">{action.label}</span>
                      </button>
                    ))}
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredUsers.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Users className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p>No users found</p>
        </div>
      )}

      {/* Enhanced User Profile Modal */}
      {showUserProfile && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-colors duration-300 ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-semibold transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                User Profile Details
              </h3>
              <button
                onClick={() => setShowUserProfile(null)}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {(() => {
              const user = users.find(u => u.id === showUserProfile);
              if (!user) return <div>User not found</div>;

              return (
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  {/* User Information */}
                  <div className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-semibold mb-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Basic Information
                    </h4>
                    <div className="space-y-3">
                      <div className="flex items-center space-x-3">
                        <div className="w-12 h-12 bg-gray-300 rounded-full flex items-center justify-center">
                          <Users className="h-6 w-6 text-gray-600" />
                        </div>
                        <div>
                          <p className={`font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {user.name}
                          </p>
                          <p className={`text-sm transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-400' : 'text-gray-600'
                          }`}>
                            {user.email}
                          </p>
                        </div>
                      </div>

                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Role
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {roles.find(r => r.id === user.role)?.name || user.role}
                          </p>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Status
                          </label>
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(user.status)}`}>
                            {user.status.toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Tenant
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {user.tenant_name}
                          </p>
                        </div>
                        <div>
                          <label className={`text-sm font-medium transition-colors duration-300 ${
                            isDarkMode ? 'text-gray-300' : 'text-gray-700'
                          }`}>
                            Last Login
                          </label>
                          <p className={`transition-colors duration-300 ${
                            isDarkMode ? 'text-white' : 'text-gray-900'
                          }`}>
                            {user.last_login ? new Date(user.last_login).toLocaleDateString() : 'Never'}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Activity Timeline */}
                  <div className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <h4 className={`font-semibold mb-4 transition-colors duration-300 ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Recent Activity
                    </h4>
                    <div className="space-y-3 max-h-64 overflow-y-auto">
                      {selectedUserActivity.length > 0 ? (
                        selectedUserActivity.map((activity, index) => (
                          <div key={index} className={`p-3 rounded border transition-colors duration-300 ${
                            isDarkMode ? 'bg-gray-800 border-gray-600' : 'bg-white border-gray-200'
                          }`}>
                            <div className="flex items-center space-x-2">
                              <div className={`w-2 h-2 rounded-full ${
                                activity.type === 'login' ? 'bg-green-500' :
                                activity.type === 'order_created' ? 'bg-blue-500' : 'bg-yellow-500'
                              }`}></div>
                              <span className={`font-medium transition-colors duration-300 ${
                                isDarkMode ? 'text-white' : 'text-gray-900'
                              }`}>
                                {activity.description}
                              </span>
                            </div>
                            <p className={`text-sm mt-1 transition-colors duration-300 ${
                              isDarkMode ? 'text-gray-400' : 'text-gray-600'
                            }`}>
                              {new Date(activity.date).toLocaleString()}
                            </p>
                          </div>
                        ))
                      ) : (
                        <p className={`text-center transition-colors duration-300 ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          No recent activity
                        </p>
                      )}
                    </div>
                  </div>
                </div>
              );
            })()}
          </div>
        </div>
      )}

      {/* User Activity Modal */}
      {showActivityModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className={`rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto transition-colors duration-300 ${
            isDarkMode ? 'bg-gray-800' : 'bg-white'
          }`}>
            <div className="flex items-center justify-between mb-6">
              <h3 className={`text-xl font-semibold transition-colors duration-300 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                User Activity Timeline
              </h3>
              <button
                onClick={() => setShowActivityModal(null)}
                className={`p-2 rounded-lg transition-colors duration-200 ${
                  isDarkMode ? 'hover:bg-gray-700 text-gray-400' : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-4">
              {selectedUserActivity.length > 0 ? (
                selectedUserActivity.map((activity, index) => (
                  <div key={index} className={`p-4 rounded-lg border transition-colors duration-300 ${
                    isDarkMode ? 'bg-gray-700 border-gray-600' : 'bg-gray-50 border-gray-200'
                  }`}>
                    <div className="flex items-start space-x-3">
                      <div className={`w-3 h-3 rounded-full mt-2 ${
                        activity.type === 'login' ? 'bg-green-500' :
                        activity.type === 'order_created' ? 'bg-blue-500' :
                        activity.type === 'profile_update' ? 'bg-yellow-500' : 'bg-gray-500'
                      }`}></div>
                      <div className="flex-1">
                        <h4 className={`font-medium transition-colors duration-300 ${
                          isDarkMode ? 'text-white' : 'text-gray-900'
                        }`}>
                          {activity.description}
                        </h4>
                        <p className={`text-sm mt-1 transition-colors duration-300 ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {new Date(activity.date).toLocaleString()}
                        </p>
                        {activity.metadata && Object.keys(activity.metadata).length > 0 && (
                          <div className={`mt-2 p-2 rounded text-xs transition-colors duration-300 ${
                            isDarkMode ? 'bg-gray-800 text-gray-300' : 'bg-gray-100 text-gray-700'
                          }`}>
                            <pre>{JSON.stringify(activity.metadata, null, 2)}</pre>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8">
                  <Clock className={`h-12 w-12 mx-auto mb-3 transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-600' : 'text-gray-400'
                  }`} />
                  <p className={`transition-colors duration-300 ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    No activity records found
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Create User Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Create New User</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={createUserForm.name}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter full name"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={createUserForm.email}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter email address"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={createUserForm.phone}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter phone number"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Role *
                </label>
                <select
                  value={createUserForm.role}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, role: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  {roles.map(role => (
                    <option key={role.id} value={role.id}>{role.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Tenant *
                </label>
                <select
                  value={createUserForm.tenant_id}
                  onChange={(e) => setCreateUserForm(prev => ({ ...prev, tenant_id: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select a tenant</option>
                  {tenants.map(tenant => (
                    <option key={tenant.id} value={tenant.id}>{tenant.name}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Permissions
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                  {availablePermissions.map(permission => (
                    <label key={permission} className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={createUserForm.permissions.includes(permission)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setCreateUserForm(prev => ({
                              ...prev,
                              permissions: [...prev.permissions, permission]
                            }));
                          } else {
                            setCreateUserForm(prev => ({
                              ...prev,
                              permissions: prev.permissions.filter(p => p !== permission)
                            }));
                          }
                        }}
                        className="rounded"
                      />
                      <span className="text-sm text-gray-700">{permission.replace(/_/g, ' ')}</span>
                    </label>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCreateModal(false);
                  setCreateUserForm({
                    name: '',
                    email: '',
                    role: 'employee',
                    tenant_id: '',
                    phone: '',
                    permissions: []
                  });
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleCreateUser}
                disabled={actionLoading === 'create_user'}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {actionLoading === 'create_user' ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Create User'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Edit User Modal */}
      {showEditModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Edit User</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name
                </label>
                <input
                  type="text"
                  value={editUserForm.name || ''}
                  onChange={(e) => setEditUserForm(prev => ({ ...prev, name: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  value={editUserForm.email || ''}
                  onChange={(e) => setEditUserForm(prev => ({ ...prev, email: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Phone Number
                </label>
                <input
                  type="tel"
                  value={editUserForm.phone || ''}
                  onChange={(e) => setEditUserForm(prev => ({ ...prev, phone: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowEditModal(null);
                  setEditUserForm({});
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={handleUpdateUser}
                disabled={actionLoading === 'update_user'}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {actionLoading === 'update_user' ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Update User'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Role Management Modal */}
      {showRoleModal && selectedUserForRole && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Change Role for {selectedUserForRole.name}
            </h3>

            <div className="space-y-3">
              <p className="text-sm text-gray-600">
                Current Role: <span className="font-medium">{roles.find(r => r.id === selectedUserForRole.role)?.name}</span>
              </p>

              <div className="space-y-2">
                {roles.map(role => (
                  <label key={role.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50">
                    <input
                      type="radio"
                      name="role"
                      value={role.id}
                      checked={selectedUserForRole.role === role.id}
                      onChange={() => {}}
                      className="text-blue-600"
                    />
                    <div>
                      <div className="font-medium text-gray-900">{role.name}</div>
                      <div className="text-sm text-gray-500">
                        {role.id === 'super_admin' && 'Full system access'}
                        {role.id === 'tenant_admin' && 'Tenant administration access'}
                        {role.id === 'manager' && 'Management level access'}
                        {role.id === 'employee' && 'Basic POS access'}
                      </div>
                    </div>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowRoleModal(null);
                  setSelectedUserForRole(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  const selectedRole = document.querySelector('input[name="role"]:checked') as HTMLInputElement;
                  if (selectedRole) {
                    handleRoleUpdate(selectedRole.value);
                  }
                }}
                disabled={actionLoading === 'change_role'}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {actionLoading === 'change_role' ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Update Role'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Permissions Management Modal */}
      {showPermissionsModal && selectedUserForPermissions && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Manage Permissions for {selectedUserForPermissions.name}
            </h3>

            <div className="space-y-4">
              <p className="text-sm text-gray-600">
                Current Role: <span className="font-medium">{roles.find(r => r.id === selectedUserForPermissions.role)?.name}</span>
              </p>

              <div className="space-y-2 max-h-64 overflow-y-auto border border-gray-200 rounded-md p-3">
                {availablePermissions.map(permission => (
                  <label key={permission} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedUserForPermissions.permissions.includes(permission)}
                      onChange={(e) => {
                        const newPermissions = e.target.checked
                          ? [...selectedUserForPermissions.permissions, permission]
                          : selectedUserForPermissions.permissions.filter(p => p !== permission);

                        setSelectedUserForPermissions(prev => prev ? {
                          ...prev,
                          permissions: newPermissions
                        } : null);
                      }}
                      className="rounded"
                    />
                    <span className="text-sm text-gray-700">{permission.replace(/_/g, ' ')}</span>
                  </label>
                ))}
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowPermissionsModal(null);
                  setSelectedUserForPermissions(null);
                }}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => {
                  if (selectedUserForPermissions) {
                    handlePermissionsUpdate(selectedUserForPermissions.permissions);
                  }
                }}
                disabled={actionLoading === 'update_permissions'}
                className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 flex items-center justify-center"
              >
                {actionLoading === 'update_permissions' ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  'Update Permissions'
                )}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Confirmation Dialog */}
      {showConfirmDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Action</h3>
            <p className="text-gray-600 mb-6">
              Are you sure you want to perform this action? This may affect user access and permissions.
            </p>
            <div className="flex space-x-3">
              <button
                onClick={() => setShowConfirmDialog(null)}
                className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={async () => {
                  console.log('🔄 Confirm button clicked, showConfirmDialog:', showConfirmDialog);
                  try {
                    // Split on the last underscore to handle action IDs with underscores
                    const lastUnderscoreIndex = showConfirmDialog.lastIndexOf('_');
                    const actionId = showConfirmDialog.substring(0, lastUnderscoreIndex);
                    const userId = showConfirmDialog.substring(lastUnderscoreIndex + 1);
                    console.log('🎯 Parsed action:', { actionId, userId, original: showConfirmDialog });

                    const action = userActions.find(a => a.id === actionId);
                    console.log('🔍 Found action:', action);
                    console.log('📋 Available actions:', userActions.map(a => a.id));

                    if (action) {
                      console.log('▶️ Executing action...');
                      await executeUserAction(action, userId);
                      console.log('✅ Action completed successfully');
                    } else {
                      console.error('❌ Action not found:', actionId);
                      console.error('📋 Available action IDs:', userActions.map(a => a.id));
                      setError(`Action not found: ${actionId}`);
                      setShowConfirmDialog(null);
                    }
                  } catch (error) {
                    console.error('💥 Error in confirm button:', error);
                    setError(`Failed to execute action: ${error instanceof Error ? error.message : 'Unknown error'}`);
                    setShowConfirmDialog(null);
                  }
                }}
                disabled={actionLoading !== null}
                className={`flex-1 px-4 py-2 text-white rounded-lg transition-all duration-200 font-medium ${
                  actionLoading !== null
                    ? 'bg-gray-400 cursor-not-allowed opacity-50'
                    : 'bg-red-600 hover:bg-red-700 active:bg-red-800 cursor-pointer hover:shadow-lg'
                }`}
              >
                {actionLoading !== null ? (
                  <div className="flex items-center justify-center space-x-2">
                    <RefreshCw className="h-4 w-4 animate-spin" />
                    <span>Processing...</span>
                  </div>
                ) : (
                  'Confirm'
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
