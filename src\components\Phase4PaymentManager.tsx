import React, { useState, useEffect } from 'react';
import { 
  CreditCard, 
  Smartphone, 
  Printer, 
  Wifi, 
  CheckCircle, 
  AlertCircle,
  DollarSign,
  TrendingUp,
  Settings,
  RefreshCw,
  Plus,
  Edit,
  Trash2,
  Activity
} from 'lucide-react';

interface PaymentMethod {
  id: string;
  name: string;
  provider: string;
  isActive: boolean;
  processingFee: number;
  supportsTips: boolean;
  supportsSplit: boolean;
}

interface HardwareDevice {
  id: string;
  name: string;
  type: string;
  status: 'online' | 'offline' | 'error';
  lastHeartbeat: string;
  location: string;
}

interface PaymentStats {
  totalTransactions: number;
  totalVolume: number;
  averageTransaction: number;
  successRate: number;
  processingFees: number;
}

const Phase4PaymentManager: React.FC = () => {
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [hardwareDevices, setHardwareDevices] = useState<HardwareDevice[]>([]);
  const [paymentStats, setPaymentStats] = useState<PaymentStats>({
    totalTransactions: 0,
    totalVolume: 0,
    averageTransaction: 0,
    successRate: 0,
    processingFees: 0
  });
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadPaymentData();
  }, []);

  const loadPaymentData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      
      // Load payment methods
      const methodsResponse = await fetch('/api/payments/methods/enhanced', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      // Load hardware devices
      const devicesResponse = await fetch('/api/hardware/devices/enhanced', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (methodsResponse.ok && devicesResponse.ok) {
        const methodsData = await methodsResponse.json();
        const devicesData = await devicesResponse.json();
        
        setPaymentMethods(methodsData.methods || []);
        setHardwareDevices(devicesData.devices || []);
      } else {
        // Fallback to mock data
        setPaymentMethods([
          { id: '1', name: 'Cash', provider: 'cash', isActive: true, processingFee: 0, supportsTips: true, supportsSplit: true },
          { id: '2', name: 'Credit Card', provider: 'stripe', isActive: true, processingFee: 2.9, supportsTips: true, supportsSplit: true },
          { id: '3', name: 'Debit Card', provider: 'moneris', isActive: true, processingFee: 1.5, supportsTips: true, supportsSplit: true },
          { id: '4', name: 'Apple Pay', provider: 'stripe', isActive: true, processingFee: 2.9, supportsTips: false, supportsSplit: false },
          { id: '5', name: 'Google Pay', provider: 'stripe', isActive: false, processingFee: 2.9, supportsTips: false, supportsSplit: false }
        ]);
        
        setHardwareDevices([
          { id: '1', name: 'Main Terminal', type: 'payment_terminal', status: 'online', lastHeartbeat: '2024-01-15T10:30:00Z', location: 'Counter 1' },
          { id: '2', name: 'Receipt Printer', type: 'printer', status: 'online', lastHeartbeat: '2024-01-15T10:29:45Z', location: 'Kitchen' },
          { id: '3', name: 'Barcode Scanner', type: 'scanner', status: 'online', lastHeartbeat: '2024-01-15T10:30:15Z', location: 'Counter 1' },
          { id: '4', name: 'Cash Drawer', type: 'cash_drawer', status: 'offline', lastHeartbeat: '2024-01-15T09:45:00Z', location: 'Counter 1' },
          { id: '5', name: 'Mobile Terminal', type: 'payment_terminal', status: 'error', lastHeartbeat: '2024-01-15T10:15:00Z', location: 'Table Service' }
        ]);
      }

      // Mock payment stats
      setPaymentStats({
        totalTransactions: 1247,
        totalVolume: 125430.50,
        averageTransaction: 100.58,
        successRate: 98.7,
        processingFees: 3632.89
      });

    } catch (error) {
      console.error('Failed to load payment data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'offline':
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
      case 'error':
        return <AlertCircle className="h-5 w-5 text-red-500" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-400" />;
    }
  };

  const getDeviceIcon = (type: string) => {
    switch (type) {
      case 'payment_terminal':
        return <CreditCard className="h-6 w-6 text-blue-600" />;
      case 'printer':
        return <Printer className="h-6 w-6 text-green-600" />;
      case 'scanner':
        return <Activity className="h-6 w-6 text-purple-600" />;
      case 'cash_drawer':
        return <DollarSign className="h-6 w-6 text-yellow-600" />;
      default:
        return <Settings className="h-6 w-6 text-gray-600" />;
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Payment Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Transactions</p>
              <p className="text-2xl font-semibold text-gray-900">{paymentStats.totalTransactions.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Volume</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(paymentStats.totalVolume)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Transaction</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(paymentStats.averageTransaction)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Success Rate</p>
              <p className="text-2xl font-semibold text-gray-900">{paymentStats.successRate}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Plus className="h-8 w-8 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Add Payment Method</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Wifi className="h-8 w-8 text-green-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Test Connections</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <Settings className="h-8 w-8 text-purple-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Configure Hardware</span>
            </button>
            <button className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors">
              <RefreshCw className="h-8 w-8 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Sync All Devices</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderPaymentMethods = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Payment Methods</h3>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Add Method</span>
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Method</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Provider</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Fee</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Features</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {paymentMethods.map((method) => (
              <tr key={method.id}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <CreditCard className="h-5 w-5 text-gray-400 mr-3" />
                    <span className="text-sm font-medium text-gray-900">{method.name}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 capitalize">
                  {method.provider}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {method.processingFee}%
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  <div className="flex space-x-2">
                    {method.supportsTips && <span className="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Tips</span>}
                    {method.supportsSplit && <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs">Split</span>}
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    method.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {method.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-red-600 hover:text-red-900">
                      <Trash2 className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderHardwareDevices = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Hardware Devices</h3>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Register Device</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {hardwareDevices.map((device) => (
          <div key={device.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                {getDeviceIcon(device.type)}
                <div className="ml-3">
                  <h4 className="text-lg font-medium text-gray-900">{device.name}</h4>
                  <p className="text-sm text-gray-500 capitalize">{device.type.replace('_', ' ')}</p>
                </div>
              </div>
              {getStatusIcon(device.status)}
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Location:</span>
                <span className="text-gray-900">{device.location}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Last Seen:</span>
                <span className="text-gray-900">
                  {new Date(device.lastHeartbeat).toLocaleTimeString()}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Status:</span>
                <span className={`capitalize ${
                  device.status === 'online' ? 'text-green-600' :
                  device.status === 'offline' ? 'text-gray-600' : 'text-red-600'
                }`}>
                  {device.status}
                </span>
              </div>
            </div>

            <div className="mt-4 flex space-x-2">
              <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Configure
              </button>
              <button className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Test
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-green-600" />
          <span className="text-gray-600">Loading payment systems...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Phase 4: Enhanced Payment & Hardware</h2>
        <p className="text-gray-600">Advanced payment processing and hardware integration</p>
      </div>

      {/* Sub Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: Activity },
            { id: 'methods', name: 'Payment Methods', icon: CreditCard },
            { id: 'hardware', name: 'Hardware Devices', icon: Settings }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-green-500 text-green-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'methods' && renderPaymentMethods()}
      {activeTab === 'hardware' && renderHardwareDevices()}
    </div>
  );
};

export default Phase4PaymentManager;
