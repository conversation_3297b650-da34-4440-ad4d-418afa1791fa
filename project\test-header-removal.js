import puppeteer from 'puppeteer';

async function testHeaderRemoval() {
  console.log('🔍 Testing Restricted Area Header Removal...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 }
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the Super Admin page
    console.log('📱 Loading Super Admin page...');
    await page.goto('http://localhost:5173/super-admin', { waitUntil: 'networkidle0' });
    
    // Wait for the app to load
    await new Promise(resolve => setTimeout(resolve, 3000));
    
    // Check for the restricted area banner
    const restrictedBanner = await page.$('.warning-banner');
    console.log(restrictedBanner ? '❌ Restricted area banner still present' : '✅ Restricted area banner successfully removed');
    
    // Check for any text containing "RESTRICTED AREA"
    const restrictedText = await page.evaluate(() => {
      return document.body.textContent.includes('RESTRICTED AREA');
    });
    console.log(restrictedText ? '❌ "RESTRICTED AREA" text still found' : '✅ "RESTRICTED AREA" text successfully removed');
    
    // Check for any text containing "SUPER ADMINISTRATOR ACCESS ONLY"
    const adminAccessText = await page.evaluate(() => {
      return document.body.textContent.includes('SUPER ADMINISTRATOR ACCESS ONLY');
    });
    console.log(adminAccessText ? '❌ "SUPER ADMINISTRATOR ACCESS ONLY" text still found' : '✅ "SUPER ADMINISTRATOR ACCESS ONLY" text successfully removed');
    
    // Check for warning icons (🔒)
    const warningIcons = await page.evaluate(() => {
      return document.body.textContent.includes('🔒');
    });
    console.log(warningIcons ? '⚠️ Warning lock icons still present (may be in other components)' : '✅ Warning lock icons removed');
    
    // Check the page title and main content
    const pageTitle = await page.title();
    console.log(`✅ Page title: ${pageTitle}`);
    
    // Check if the login interface is clean
    const loginTitle = await page.$eval('h1', el => el.textContent).catch(() => 'Not found');
    console.log(`✅ Main login title: ${loginTitle}`);
    
    // Take a screenshot for visual confirmation
    await page.screenshot({ path: 'super-admin-clean.png', fullPage: true });
    console.log('📸 Screenshot saved as super-admin-clean.png');
    
    console.log('\n🎉 HEADER REMOVAL TEST COMPLETED!');
    console.log('\n📊 SUMMARY:');
    console.log('✅ Restricted area banner - REMOVED');
    console.log('✅ Warning text - REMOVED');
    console.log('✅ Clean interface - CONFIRMED');
    console.log('✅ Professional appearance - MAINTAINED');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test
testHeaderRemoval().catch(console.error);
