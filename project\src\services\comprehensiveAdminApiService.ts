// Comprehensive Super Admin API Service with PostgreSQL Integration
// Database: BARPOS, Host: localhost, User: BARPOS, Password: Chaand@0319, Port: 5432

export interface SystemMetrics {
  totalTenants: number;
  activeTenants: number;
  totalUsers: number;
  activeUsers: number;
  monthlyRevenue: number;
  systemUptime: number;
  databaseConnections: number;
  apiRequests: number;
  errorRate: number;
  responseTime: number;
  memoryUsage: number;
  cpuUsage: number;
  diskUsage: number;
  lastUpdated: string;
}

export interface Tenant {
  id: number;
  name: string;
  slug: string;
  status: 'active' | 'inactive' | 'suspended';
  plan: 'basic' | 'pro' | 'enterprise';
  createdAt: string;
  lastLogin: string;
  userCount: number;
  monthlyRevenue: number;
  locations: number;
  features: string[];
}

export interface User {
  id: number;
  name: string;
  email: string;
  role: 'super_admin' | 'tenant_admin' | 'manager' | 'employee';
  tenantId: number;
  tenantName: string;
  status: 'active' | 'inactive' | 'suspended';
  lastLogin: string;
  createdAt: string;
  permissions: string[];
}

export interface SystemAnalytics {
  dailyActiveUsers: number;
  monthlyActiveUsers: number;
  totalTransactions: number;
  totalRevenue: number;
  averageOrderValue: number;
  conversionRate: number;
  churnRate: number;
  growthRate: number;
  popularFeatures: Array<{ name: string; usage: number }>;
  performanceMetrics: {
    avgResponseTime: number;
    uptime: number;
    errorRate: number;
    throughput: number;
  };
}

export interface SecurityAudit {
  id: number;
  timestamp: string;
  event: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId: number;
  userEmail: string;
  ipAddress: string;
  details: string;
  status: 'resolved' | 'investigating' | 'open';
}

export interface AIAnalytics {
  predictedRevenue: number;
  forecastAccuracy: number;
  inventoryOptimization: {
    overstockedItems: number;
    understockedItems: number;
    optimizationSavings: number;
  };
  customerBehavior: {
    segmentCount: number;
    churnPrediction: number;
    lifetimeValue: number;
  };
  pricingOptimization: {
    recommendedChanges: number;
    potentialIncrease: number;
    competitiveAnalysis: string;
  };
  staffOptimization: {
    optimalScheduling: number;
    laborCostSavings: number;
    productivityIncrease: number;
  };
}

export interface SystemActivity {
  id: number;
  timestamp: string;
  action: string;
  user: string;
  tenant: string;
  type: 'success' | 'warning' | 'error' | 'info';
  details: string;
}

class ComprehensiveAdminApiService {
  private baseUrl = 'http://localhost:4000/api/admin';
  private cache = new Map<string, { data: any; timestamp: number }>();
  private cacheTimeout = 30000; // 30 seconds
  private autoRefreshInterval: NodeJS.Timeout | null = null;
  private authToken: string | null = null;

  // Authentication setup
  async authenticate(): Promise<boolean> {
    try {
      // Check if we already have a valid token in localStorage
      const existingToken = localStorage.getItem('authToken');
      if (existingToken) {
        this.authToken = existingToken;
        console.log('✅ Using existing authentication token');
        return true;
      }

      console.log('🔐 No existing token found, authentication required');
      console.log('⚠️ Please log in through the Super Admin interface first');
      return false;
    } catch (error) {
      console.error('💥 Authentication failed:', error);
      return false;
    }
  }

  // Get authenticated headers
  private getAuthHeaders(): HeadersInit {
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    return headers;
  }

  // Database connection test (public endpoint, no authentication required)
  async testDatabaseConnection(): Promise<boolean> {
    try {
      // Use public health endpoint that doesn't require authentication
      const response = await fetch('http://localhost:4000/api/health/database');

      if (!response.ok) return false;
      const result = await response.json();
      console.log('🔍 Database connection test result:', result);
      // Handle different response formats from backend
      return result.database?.connected === true || result.connected === true;
    } catch (error) {
      console.error('Database connection test failed:', error);
      return false;
    }
  }

  // Cache management
  private getCachedData<T>(key: string): T | null {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheTimeout) {
      return cached.data as T;
    }
    return null;
  }

  private setCachedData(key: string, data: any): void {
    this.cache.set(key, { data, timestamp: Date.now() });
  }

  private clearCache(): void {
    this.cache.clear();
  }

  // System Metrics (Phase 1)
  async getSystemMetrics(forceRefresh = false): Promise<SystemMetrics> {
    const cacheKey = 'system-metrics';

    if (!forceRefresh) {
      const cached = this.getCachedData<SystemMetrics>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/metrics/system`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/metrics/system`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        this.setCachedData(cacheKey, data);
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      console.log('📊 System metrics received:', data);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch system metrics:', error);
      // Return fallback data
      return {
        totalTenants: 0,
        activeTenants: 0,
        totalUsers: 0,
        activeUsers: 0,
        monthlyRevenue: 0,
        systemUptime: 0,
        databaseConnections: 0,
        apiRequests: 0,
        errorRate: 0,
        responseTime: 0,
        memoryUsage: 0,
        cpuUsage: 0,
        diskUsage: 0,
        lastUpdated: new Date().toISOString()
      };
    }
  }

  // Tenant Management (Phase 1)
  async getTenants(forceRefresh = false): Promise<Tenant[]> {
    const cacheKey = 'tenants';

    if (!forceRefresh) {
      const cached = this.getCachedData<Tenant[]>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const result = await retryResponse.json();

        // Handle paginated response format and map to expected format
        const rawTenants = result.tenants || result;
        const tenants = rawTenants.map((tenant: any) => ({
          id: tenant.id,
          name: tenant.name,
          slug: tenant.slug,
          status: tenant.status || 'active',
          plan: tenant.plan || 'basic',
          createdAt: tenant.createdAt || tenant.created_at || '',
          lastLogin: tenant.lastLogin || tenant.last_login || '',
          userCount: tenant.userCount || tenant.user_count || 0,
          monthlyRevenue: tenant.monthlyRevenue || tenant.monthly_revenue || 0,
          locations: tenant.locations || 1,
          features: tenant.features || []
        }));
        console.log('🏢 Tenants received and mapped:', tenants);
        this.setCachedData(cacheKey, tenants);
        return tenants;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const result = await response.json();
      // Handle paginated response format and map to expected format
      const rawTenants = result.tenants || result;
      const tenants = rawTenants.map((tenant: any) => ({
        id: tenant.id,
        name: tenant.name,
        slug: tenant.slug,
        status: tenant.status || 'active',
        plan: tenant.plan || 'basic',
        createdAt: tenant.createdAt || tenant.created_at || '',
        lastLogin: tenant.lastLogin || tenant.last_login || '',
        userCount: tenant.userCount || tenant.user_count || 0,
        monthlyRevenue: tenant.monthlyRevenue || tenant.monthly_revenue || 0,
        locations: tenant.locations || 1,
        features: tenant.features || []
      }));
      console.log('🏢 Tenants received and mapped:', tenants);
      this.setCachedData(cacheKey, tenants);
      return tenants;
    } catch (error) {
      console.error('Failed to fetch tenants:', error);
      return [];
    }
  }

  async createTenant(tenantData: Partial<Tenant>): Promise<Tenant> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tenantData)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(tenantData)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        this.clearCache();
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      this.clearCache(); // Clear cache to force refresh
      return data;
    } catch (error) {
      console.error('Failed to create tenant:', error);
      throw error;
    }
  }

  async updateTenant(id: number, tenantData: Partial<Tenant>): Promise<Tenant> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants/${id}`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(tenantData)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants/${id}`, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(tenantData)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        this.clearCache();
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      this.clearCache();
      return data;
    } catch (error) {
      console.error('Failed to update tenant:', error);
      throw error;
    }
  }

  async deleteTenant(id: number, options: { confirmDelete: boolean }): Promise<void> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants/${id}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(options)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants/${id}`, {
          method: 'DELETE',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(options)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        this.clearCache();
        return;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      this.clearCache();
    } catch (error) {
      console.error('Failed to delete tenant:', error);
      throw error;
    }
  }

  async activateTenant(id: number): Promise<Tenant> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants/${id}/activate`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants/${id}/activate`, {
          method: 'POST',
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        this.clearCache();
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      this.clearCache();
      return data;
    } catch (error) {
      console.error('Failed to activate tenant:', error);
      throw error;
    }
  }

  async suspendTenant(id: number, options: { reason: string }): Promise<Tenant> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/tenants/${id}/suspend`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(options)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/tenants/${id}/suspend`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(options)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        this.clearCache();
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      this.clearCache();
      return data;
    } catch (error) {
      console.error('Failed to suspend tenant:', error);
      throw error;
    }
  }

  // User Management (Phase 1)
  async getUsers(forceRefresh = false): Promise<User[]> {
    const cacheKey = 'users';

    if (!forceRefresh) {
      const cached = this.getCachedData<User[]>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const result = await retryResponse.json();

        // Handle paginated response format and map to expected format
        const users = (result.users || result).map((user: any) => ({
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role,
          tenantId: user.tenantId || user.tenant_id,
          tenantName: user.tenantName || 'Unknown',
          status: user.isActive ? 'active' : 'inactive',
          lastLogin: user.lastLogin || user.last_login || '',
          createdAt: user.createdAt || user.created_at || '',
          permissions: []
        }));

        console.log('👥 Users received:', users);
        this.setCachedData(cacheKey, users);
        return users;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const result = await response.json();
      // Handle paginated response format and map to expected format
      const users = (result.users || result).map((user: any) => ({
        id: user.id,
        name: user.name,
        email: user.email,
        role: user.role,
        tenantId: user.tenantId || user.tenant_id,
        tenantName: user.tenantName || 'Unknown',
        status: user.isActive ? 'active' : 'inactive',
        lastLogin: user.lastLogin || user.last_login || '',
        createdAt: user.createdAt || user.created_at || '',
        permissions: []
      }));

      console.log('👥 Users received:', users);
      this.setCachedData(cacheKey, users);
      return users;
    } catch (error) {
      console.error('Failed to fetch users:', error);
      return [];
    }
  }

  // User Management CRUD Operations
  async createUser(userData: any): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(userData)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      this.clearCache();
      return await response.json();
    } catch (error) {
      console.error('Failed to create user:', error);
      throw error;
    }
  }

  async updateUser(userId: string, userData: any): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/${userId}`, {
        method: 'PATCH',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/${userId}`, {
          method: 'PATCH',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(userData)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      this.clearCache();
      return await response.json();
    } catch (error) {
      console.error('Failed to update user:', error);
      throw error;
    }
  }

  async deleteUser(userId: string): Promise<void> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/${userId}`, {
        method: 'DELETE',
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/${userId}`, {
          method: 'DELETE',
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
      } else if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      this.clearCache();
    } catch (error) {
      console.error('Failed to delete user:', error);
      throw error;
    }
  }

  async resetUserPassword(userId: string): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/${userId}/reset-password`, {
        method: 'POST',
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/${userId}/reset-password`, {
          method: 'POST',
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.json();
    } catch (error) {
      console.error('Failed to reset user password:', error);
      throw error;
    }
  }

  async bulkUserAction(action: string, userIds: string[]): Promise<void> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/bulk-action`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ action, user_ids: userIds })
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/bulk-action`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ action, user_ids: userIds })
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
      } else if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      this.clearCache();
    } catch (error) {
      console.error('Failed to perform bulk user action:', error);
      throw error;
    }
  }

  async exportUsers(options: any): Promise<Blob> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/export`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(options)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/export`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(options)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.blob();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.blob();
    } catch (error) {
      console.error('Failed to export users:', error);
      throw error;
    }
  }

  // Enhanced User Management Methods
  async getUserStatistics(): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/statistics`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/statistics`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.json();
    } catch (error) {
      console.error('Failed to fetch user statistics:', error);
      throw error;
    }
  }

  async getUserActivity(userId: string, limit: number = 50): Promise<any[]> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/${userId}/activity?limit=${limit}`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/${userId}/activity?limit=${limit}`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        return data.activities || [];
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      return data.activities || [];
    } catch (error) {
      console.error('Failed to fetch user activity:', error);
      return [];
    }
  }

  async enhancedBulkUserAction(action: string, userIds: string[], additionalData?: any): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/users/bulk-actions`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({
          action,
          userIds,
          ...additionalData
        })
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/bulk-actions`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({
            action,
            userIds,
            ...additionalData
          })
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      this.clearCache();
      return await response.json();
    } catch (error) {
      console.error('Failed to perform enhanced bulk user action:', error);
      throw error;
    }
  }

  async exportUsersEnhanced(options: { format?: string; filters?: any } = {}): Promise<Blob> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const queryParams = new URLSearchParams();
      if (options.format) {
        queryParams.append('format', options.format);
      }
      if (options.filters) {
        Object.keys(options.filters).forEach(key => {
          if (options.filters![key] && options.filters![key] !== 'all') {
            queryParams.append(key, options.filters![key]);
          }
        });
      }

      const response = await fetch(`${this.baseUrl}/users/export?${queryParams.toString()}`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/users/export?${queryParams.toString()}`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.blob();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.blob();
    } catch (error) {
      console.error('Failed to export users (enhanced):', error);
      throw error;
    }
  }

  // Super Admin Profile Management
  async getProfile(): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/profile`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.json();
    } catch (error) {
      console.error('Failed to fetch profile:', error);
      throw error;
    }
  }

  async updateProfile(profileData: any): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/profile`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData)
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile`, {
          method: 'PUT',
          headers: this.getAuthHeaders(),
          body: JSON.stringify(profileData)
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      this.clearCache();
      return await response.json();
    } catch (error) {
      console.error('Failed to update profile:', error);
      throw error;
    }
  }

  async changePin(currentPin: string, newPin: string): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/profile/change-pin`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ currentPin, newPin })
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile/change-pin`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ currentPin, newPin })
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Failed to change PIN:', error);
      throw error;
    }
  }

  async verifyPin(pin: string): Promise<boolean> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/profile/verify-pin`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify({ pin })
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile/verify-pin`, {
          method: 'POST',
          headers: this.getAuthHeaders(),
          body: JSON.stringify({ pin })
        });
        return retryResponse.ok;
      }

      return response.ok;
    } catch (error) {
      console.error('Failed to verify PIN:', error);
      return false;
    }
  }

  async getProfileActivityLogs(): Promise<any[]> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/profile/activity-logs`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile/activity-logs`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      return await response.json();
    } catch (error) {
      console.error('Failed to fetch activity logs:', error);
      return [];
    }
  }

  async uploadProfilePicture(file: File): Promise<any> {
    try {
      if (!this.authToken) {
        await this.authenticate();
      }

      const formData = new FormData();
      formData.append('profilePicture', file);

      const response = await fetch(`${this.baseUrl}/profile/upload-picture`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.authToken}`
        },
        body: formData
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/profile/upload-picture`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.authToken}`
          },
          body: formData
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        return await retryResponse.json();
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      this.clearCache();
      return await response.json();
    } catch (error) {
      console.error('Failed to upload profile picture:', error);
      throw error;
    }
  }

  // System Analytics (Phase 2)
  async getSystemAnalytics(forceRefresh = false): Promise<SystemAnalytics> {
    const cacheKey = 'system-analytics';

    if (!forceRefresh) {
      const cached = this.getCachedData<SystemAnalytics>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      // Use statistics endpoint since analytics/system doesn't exist
      const response = await fetch(`${this.baseUrl}/statistics`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/statistics`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();

        // Map statistics response to SystemAnalytics format
        const analytics: SystemAnalytics = {
          dailyActiveUsers: data.users?.active || 0,
          monthlyActiveUsers: data.users?.total || 0,
          totalTransactions: data.orders?.total || 0,
          totalRevenue: data.revenue?.total || 0,
          averageOrderValue: data.revenue?.average_order_value || 0,
          conversionRate: 0.85, // Mock data
          churnRate: 0.05, // Mock data
          growthRate: 0.15, // Mock data
          popularFeatures: [
            { name: 'POS System', usage: 95 },
            { name: 'Inventory Management', usage: 78 },
            { name: 'Analytics', usage: 65 }
          ],
          performanceMetrics: {
            avgResponseTime: 120,
            uptime: 99.9,
            errorRate: 0.1,
            throughput: 1000
          }
        };

        console.log('📈 System analytics received:', analytics);
        this.setCachedData(cacheKey, analytics);
        return analytics;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();

      // Map statistics response to SystemAnalytics format
      const analytics: SystemAnalytics = {
        dailyActiveUsers: data.users?.active || 0,
        monthlyActiveUsers: data.users?.total || 0,
        totalTransactions: data.orders?.total || 0,
        totalRevenue: data.revenue?.total || 0,
        averageOrderValue: data.revenue?.average_order_value || 0,
        conversionRate: 0.85, // Mock data
        churnRate: 0.05, // Mock data
        growthRate: 0.15, // Mock data
        popularFeatures: [
          { name: 'POS System', usage: 95 },
          { name: 'Inventory Management', usage: 78 },
          { name: 'Analytics', usage: 65 }
        ],
        performanceMetrics: {
          avgResponseTime: 120,
          uptime: 99.9,
          errorRate: 0.1,
          throughput: 1000
        }
      };

      console.log('📈 System analytics received:', analytics);
      this.setCachedData(cacheKey, analytics);
      return analytics;
    } catch (error) {
      console.error('Failed to fetch system analytics:', error);
      return {
        dailyActiveUsers: 0,
        monthlyActiveUsers: 0,
        totalTransactions: 0,
        totalRevenue: 0,
        averageOrderValue: 0,
        conversionRate: 0,
        churnRate: 0,
        growthRate: 0,
        popularFeatures: [],
        performanceMetrics: {
          avgResponseTime: 0,
          uptime: 0,
          errorRate: 0,
          throughput: 0
        }
      };
    }
  }

  // Security Audit (Phase 2)
  async getSecurityAudits(forceRefresh = false): Promise<SecurityAudit[]> {
    const cacheKey = 'security-audits';

    if (!forceRefresh) {
      const cached = this.getCachedData<SecurityAudit[]>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/security/audits`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/security/audits`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        console.log('🛡️ Security audits received:', data);
        this.setCachedData(cacheKey, data);
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      console.log('🛡️ Security audits received:', data);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch security audits:', error);
      return [];
    }
  }

  // AI Analytics (Phase 3)
  async getAIAnalytics(forceRefresh = false): Promise<AIAnalytics> {
    const cacheKey = 'ai-analytics';

    if (!forceRefresh) {
      const cached = this.getCachedData<AIAnalytics>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/ai/analytics`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/ai/analytics`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        console.log('🤖 AI Analytics received:', data);
        this.setCachedData(cacheKey, data);
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      console.log('🤖 AI Analytics received:', data);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch AI analytics:', error);
      return {
        predictedRevenue: 0,
        forecastAccuracy: 0,
        inventoryOptimization: {
          overstockedItems: 0,
          understockedItems: 0,
          optimizationSavings: 0
        },
        customerBehavior: {
          segmentCount: 0,
          churnPrediction: 0,
          lifetimeValue: 0
        },
        pricingOptimization: {
          recommendedChanges: 0,
          potentialIncrease: 0,
          competitiveAnalysis: 'No data available'
        },
        staffOptimization: {
          optimalScheduling: 0,
          laborCostSavings: 0,
          productivityIncrease: 0
        }
      };
    }
  }

  // System Activity (All Phases)
  async getSystemActivity(forceRefresh = false): Promise<SystemActivity[]> {
    const cacheKey = 'system-activity';

    if (!forceRefresh) {
      const cached = this.getCachedData<SystemActivity[]>(cacheKey);
      if (cached) return cached;
    }

    try {
      // Ensure authentication
      if (!this.authToken) {
        await this.authenticate();
      }

      const response = await fetch(`${this.baseUrl}/activity`, {
        headers: this.getAuthHeaders()
      });

      if (response.status === 401 || response.status === 403) {
        await this.authenticate();
        const retryResponse = await fetch(`${this.baseUrl}/activity`, {
          headers: this.getAuthHeaders()
        });
        if (!retryResponse.ok) throw new Error(`HTTP ${retryResponse.status}`);
        const data = await retryResponse.json();
        console.log('📋 System activity received:', data);
        this.setCachedData(cacheKey, data);
        return data;
      }

      if (!response.ok) throw new Error(`HTTP ${response.status}`);

      const data = await response.json();
      console.log('📋 System activity received:', data);
      this.setCachedData(cacheKey, data);
      return data;
    } catch (error) {
      console.error('Failed to fetch system activity:', error);
      return [];
    }
  }

  // Auto-refresh functionality
  startAutoRefresh(intervalMs = 120000): () => void { // Default 2 minutes to reduce server load
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
    }

    this.autoRefreshInterval = setInterval(() => {
      console.log('🔄 Auto-refreshing admin data...');
      this.clearCache();
    }, intervalMs);

    return () => {
      if (this.autoRefreshInterval) {
        clearInterval(this.autoRefreshInterval);
        this.autoRefreshInterval = null;
      }
    };
  }

  // Cleanup
  cleanup(): void {
    if (this.autoRefreshInterval) {
      clearInterval(this.autoRefreshInterval);
      this.autoRefreshInterval = null;
    }
    this.clearCache();
  }
}

export const comprehensiveAdminApiService = new ComprehensiveAdminApiService();
