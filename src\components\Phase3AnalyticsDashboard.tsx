import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  DollarSign, 
  ShoppingCart,
  Clock,
  MapPin,
  Target,
  RefreshCw,
  Download,
  Filter,
  Calendar
} from 'lucide-react';

interface AnalyticsData {
  totalRevenue: number;
  totalOrders: number;
  averageOrderValue: number;
  customerCount: number;
  topProducts: Array<{
    name: string;
    sales: number;
    revenue: number;
  }>;
  salesTrends: Array<{
    date: string;
    revenue: number;
    orders: number;
  }>;
  locationPerformance: Array<{
    location: string;
    revenue: number;
    orders: number;
    growth: number;
  }>;
}

const Phase3AnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData>({
    totalRevenue: 0,
    totalOrders: 0,
    averageOrderValue: 0,
    customerCount: 0,
    topProducts: [],
    salesTrends: [],
    locationPerformance: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d');
  const [selectedLocation, setSelectedLocation] = useState('all');

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, selectedLocation]);

  const loadAnalyticsData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch(`/api/analytics/dashboard?range=${timeRange}&location=${selectedLocation}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setAnalyticsData(data);
      } else {
        // Fallback to mock data for demonstration
        setAnalyticsData({
          totalRevenue: 125430.50,
          totalOrders: 1247,
          averageOrderValue: 100.58,
          customerCount: 892,
          topProducts: [
            { name: 'Signature Burger', sales: 234, revenue: 3510.00 },
            { name: 'Caesar Salad', sales: 189, revenue: 2268.00 },
            { name: 'Craft Beer', sales: 156, revenue: 936.00 },
            { name: 'Chocolate Cake', sales: 98, revenue: 686.00 },
            { name: 'Fish & Chips', sales: 87, revenue: 1305.00 }
          ],
          salesTrends: [
            { date: '2024-01-01', revenue: 15420, orders: 142 },
            { date: '2024-01-02', revenue: 18230, orders: 167 },
            { date: '2024-01-03', revenue: 16890, orders: 154 },
            { date: '2024-01-04', revenue: 21340, orders: 198 },
            { date: '2024-01-05', revenue: 19560, orders: 178 },
            { date: '2024-01-06', revenue: 22180, orders: 203 },
            { date: '2024-01-07', revenue: 20810, orders: 189 }
          ],
          locationPerformance: [
            { location: 'Downtown', revenue: 45230, orders: 423, growth: 12.5 },
            { location: 'Mall Location', revenue: 38940, orders: 367, growth: 8.3 },
            { location: 'Airport', revenue: 28760, orders: 289, growth: -2.1 },
            { location: 'Suburban', revenue: 12500, orders: 168, growth: 15.7 }
          ]
        });
      }
    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`;
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
          <span className="text-gray-600">Loading analytics data...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Controls */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3: Advanced Analytics</h2>
          <p className="text-gray-600">Real-time insights and performance metrics</p>
        </div>
        <div className="mt-4 sm:mt-0 flex items-center space-x-4">
          <select 
            value={timeRange} 
            onChange={(e) => setTimeRange(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="1d">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
            <option value="90d">Last 90 Days</option>
          </select>
          <select 
            value={selectedLocation} 
            onChange={(e) => setSelectedLocation(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Locations</option>
            <option value="downtown">Downtown</option>
            <option value="mall">Mall Location</option>
            <option value="airport">Airport</option>
            <option value="suburban">Suburban</option>
          </select>
          <button 
            onClick={loadAnalyticsData}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(analyticsData.totalRevenue)}</p>
              <p className="text-sm text-green-600">+12.5% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <ShoppingCart className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <p className="text-2xl font-semibold text-gray-900">{analyticsData.totalOrders.toLocaleString()}</p>
              <p className="text-sm text-blue-600">+8.3% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Average Order Value</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(analyticsData.averageOrderValue)}</p>
              <p className="text-sm text-purple-600">+3.7% from last period</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-orange-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Unique Customers</p>
              <p className="text-2xl font-semibold text-gray-900">{analyticsData.customerCount.toLocaleString()}</p>
              <p className="text-sm text-orange-600">+15.2% from last period</p>
            </div>
          </div>
        </div>
      </div>

      {/* Charts and Tables */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Top Products */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Top Performing Products</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.topProducts.map((product, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-3">
                      {index + 1}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{product.name}</p>
                      <p className="text-sm text-gray-500">{product.sales} sales</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(product.revenue)}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Location Performance */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-medium text-gray-900">Location Performance</h3>
          </div>
          <div className="p-6">
            <div className="space-y-4">
              {analyticsData.locationPerformance.map((location, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center">
                    <MapPin className="h-5 w-5 text-gray-400 mr-3" />
                    <div>
                      <p className="font-medium text-gray-900">{location.location}</p>
                      <p className="text-sm text-gray-500">{location.orders} orders</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold text-gray-900">{formatCurrency(location.revenue)}</p>
                    <p className={`text-sm ${location.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatPercentage(location.growth)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Sales Trends Chart Placeholder */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Sales Trends</h3>
        </div>
        <div className="p-6">
          <div className="h-64 flex items-center justify-center border-2 border-dashed border-gray-300 rounded-lg">
            <div className="text-center">
              <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">Interactive sales chart will be displayed here</p>
              <p className="text-sm text-gray-400 mt-2">Chart.js or D3.js integration coming soon</p>
            </div>
          </div>
        </div>
      </div>

      {/* Export Options */}
      <div className="bg-white rounded-lg shadow p-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Export Analytics</h3>
            <p className="text-gray-600">Download detailed reports and data</p>
          </div>
          <div className="flex space-x-3">
            <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export CSV</span>
            </button>
            <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
              <Download className="h-4 w-4" />
              <span>Export PDF</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Phase3AnalyticsDashboard;
