version: '3.8'

# RESTROFLOW Enterprise Production Deployment
# Complete multi-container setup with security and monitoring

services:
  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    container_name: restroflow-database
    restart: unless-stopped
    environment:
      POSTGRES_DB: RESTROFLOW
      POSTGRES_USER: BARPOS
      POSTGRES_PASSWORD: Chaand@0319
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/database:/docker-entrypoint-initdb.d:ro
    ports:
      - "5432:5432"
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U BARPOS -d RESTROFLOW"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # Backend API Server
  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: restroflow-backend
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: *********************************************/RESTROFLOW
      JWT_SECRET: restroflow-super-secret-jwt-key-production
      PORT: 4000
      CORS_ORIGIN: http://localhost:5173,http://localhost:5174,https://localhost:5173,https://localhost:5174
      SECURITY_LEVEL: maximum
    ports:
      - "4000:4000"
    depends_on:
      database:
        condition: service_healthy
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:4000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    volumes:
      - ./logs:/app/logs

  # Main POS System Frontend
  frontend-main:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: restroflow-main
    restart: unless-stopped
    ports:
      - "5173:80"
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      NGINX_HOST: localhost
      NGINX_PORT: 80

  # Enterprise Security System Frontend
  frontend-security:
    build:
      context: .
      dockerfile: Dockerfile.security
    container_name: restroflow-security
    restart: unless-stopped
    ports:
      - "5174:443"
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "-k", "https://localhost/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    environment:
      SECURITY_MODE: maximum
      SSL_ENABLED: true

  # Redis for Caching and Sessions
  redis:
    image: redis:7-alpine
    container_name: restroflow-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass restroflow-redis-password
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx Load Balancer and Reverse Proxy
  nginx:
    image: nginx:alpine
    container_name: restroflow-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend-main
      - frontend-security
      - backend
    networks:
      - restroflow-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  restroflow-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
