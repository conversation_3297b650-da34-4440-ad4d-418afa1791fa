# RESTROFLOW Enterprise Security Nginx Configuration
# Enhanced security configuration for maximum protection

user nginx;
worker_processes auto;
error_log /var/log/nginx/error.log warn;
pid /var/run/nginx.pid;

# Security: Limit worker connections
events {
    worker_connections 1024;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    
    # Security: Hide nginx version
    server_tokens off;
    
    # Security: Prevent clickjacking
    add_header X-Frame-Options "DENY" always;
    
    # Security: Prevent MIME type sniffing
    add_header X-Content-Type-Options "nosniff" always;
    
    # Security: Enable XSS protection
    add_header X-XSS-Protection "1; mode=block" always;
    
    # Security: Strict transport security (HTTPS only)
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
    
    # Security: Content Security Policy
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' http://localhost:4000 ws://localhost:* wss://localhost:*; frame-ancestors 'none';" always;
    
    # Security: Referrer policy
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    
    # Security: Permissions policy
    add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
    
    # Performance and security settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    types_hash_max_size 2048;
    client_max_body_size 10M;
    
    # Security: Rate limiting
    limit_req_zone $binary_remote_addr zone=security:10m rate=10r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=100r/m;
    
    # Logging format with security information
    log_format security '$remote_addr - $remote_user [$time_local] '
                       '"$request" $status $body_bytes_sent '
                       '"$http_referer" "$http_user_agent" '
                       '$request_time $upstream_response_time '
                       '$ssl_protocol $ssl_cipher';
    
    access_log /var/log/nginx/access.log security;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;
    
    # Security server block
    server {
        listen 443 ssl http2 default_server;
        listen [::]:443 ssl http2 default_server;
        server_name localhost;
        
        # SSL Configuration
        ssl_certificate /etc/nginx/ssl/nginx-selfsigned.crt;
        ssl_certificate_key /etc/nginx/ssl/nginx-selfsigned.key;
        
        # SSL Security settings
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
        ssl_prefer_server_ciphers off;
        ssl_session_cache shared:SSL:10m;
        ssl_session_timeout 10m;
        
        # Security: OCSP stapling
        ssl_stapling on;
        ssl_stapling_verify on;
        
        # Document root
        root /usr/share/nginx/html;
        index index.html;
        
        # Security: Rate limiting for main pages
        location / {
            limit_req zone=security burst=20 nodelay;
            try_files $uri $uri/ /index.html;
            
            # Security headers for HTML files
            location ~* \.html$ {
                add_header Cache-Control "no-cache, no-store, must-revalidate";
                add_header Pragma "no-cache";
                add_header Expires "0";
            }
        }
        
        # Security: Block access to sensitive files
        location ~* \.(env|log|ini|conf|sql|bak|backup|old)$ {
            deny all;
            return 404;
        }
        
        # Security: Block access to hidden files
        location ~ /\. {
            deny all;
            return 404;
        }
        
        # Security: Block access to backup files
        location ~* ~$ {
            deny all;
            return 404;
        }
        
        # Static assets with caching
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            add_header X-Content-Type-Options "nosniff";
        }
        
        # API proxy with rate limiting
        location /api/ {
            limit_req zone=api burst=200 nodelay;
            
            # CORS headers for API
            add_header Access-Control-Allow-Origin "https://localhost:5174";
            add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
            add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With";
            add_header Access-Control-Allow-Credentials "true";
            
            # Handle preflight requests
            if ($request_method = 'OPTIONS') {
                add_header Access-Control-Allow-Origin "https://localhost:5174";
                add_header Access-Control-Allow-Methods "GET, POST, PUT, DELETE, OPTIONS";
                add_header Access-Control-Allow-Headers "Authorization, Content-Type, X-Requested-With";
                add_header Access-Control-Max-Age 1728000;
                add_header Content-Type "text/plain charset=UTF-8";
                add_header Content-Length 0;
                return 204;
            }
            
            proxy_pass http://localhost:4000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;
            
            # Security timeouts
            proxy_connect_timeout 30s;
            proxy_send_timeout 30s;
            proxy_read_timeout 30s;
        }
        
        # Security: Custom error pages
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /404.html {
            internal;
            add_header Content-Type "text/html";
            return 404 "<!DOCTYPE html><html><head><title>404 Not Found</title></head><body><h1>404 - Page Not Found</h1><p>The requested resource was not found on this server.</p></body></html>";
        }
        
        location = /50x.html {
            internal;
            add_header Content-Type "text/html";
            return 500 "<!DOCTYPE html><html><head><title>Server Error</title></head><body><h1>500 - Internal Server Error</h1><p>The server encountered an internal error.</p></body></html>";
        }
    }
    
    # Redirect HTTP to HTTPS
    server {
        listen 80 default_server;
        listen [::]:80 default_server;
        server_name _;
        return 301 https://$host$request_uri;
    }
}
