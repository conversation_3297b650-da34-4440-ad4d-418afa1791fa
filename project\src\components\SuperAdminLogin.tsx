import React, { useState } from 'react';

interface SuperAdminLoginProps {
  onLogin: (success: boolean) => void;
}

const SuperAdminLogin: React.FC<SuperAdminLoginProps> = ({ onLogin }) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin.trim()) {
      setError('Please enter your Super Admin PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Simple Super Admin authentication
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin,
          tenant_slug: 'barpos-system'
        }),
      });

      const data = await response.json();

      if (response.ok && data.employee && data.employee.role === 'super_admin') {
        // Store authentication data
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
        localStorage.setItem('currentTenant', JSON.stringify(data.tenant));

        console.log('✅ Super Admin login successful');
        onLogin(true);
      } else {
        setError('Invalid Super Admin PIN. Access denied.');
      }
    } catch (err) {
      console.error('❌ Super Admin login error:', err);
      setError('Connection failed. Please ensure the backend server is running.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">

        {/* Header */}
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            BARPOS Super Admin
          </h1>
          <p className="text-gray-600">
            System Administration Portal
          </p>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
            {error}
          </div>
        )}

        {/* PIN Input Form */}
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Super Admin PIN
            </label>
            <input
              type="password"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter your PIN"
              maxLength={6}
              autoComplete="off"
            />
          </div>

          {/* Number Pad */}
          <div className="grid grid-cols-3 gap-3">
            {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((digit) => (
              <button
                key={digit}
                type="button"
                onClick={() => handlePinInput(digit.toString())}
                disabled={isLoading}
                className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                  isDarkMode 
                    ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600' 
                    : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
                } hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed`}
              >
                {digit}
              </button>
            ))}
            
            <button
              type="button"
              onClick={handleClear}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium text-sm transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-red-600 hover:bg-red-500 text-white' 
                  : 'bg-red-100 hover:bg-red-200 text-red-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              Clear
            </button>
            
            <button
              type="button"
              onClick={() => handlePinInput('0')}
              disabled={isLoading}
              className={`h-12 rounded-lg font-semibold text-lg transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-gray-700 hover:bg-gray-600 text-white border border-gray-600' 
                  : 'bg-gray-100 hover:bg-gray-200 text-gray-700 border border-gray-300'
              } hover:scale-105 active:scale-95 disabled:opacity-50`}
            >
              0
            </button>
            
            <button
              type="button"
              onClick={handleBackspace}
              disabled={isLoading}
              className={`h-12 rounded-lg font-medium transition-all duration-150 ${
                isDarkMode 
                  ? 'bg-yellow-600 hover:bg-yellow-500 text-white' 
                  : 'bg-yellow-100 hover:bg-yellow-200 text-yellow-600'
              } hover:scale-105 active:scale-95 disabled:opacity-50 flex items-center justify-center`}
            >
              ⌫
            </button>
          </div>

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 ${
              isDarkMode 
                ? 'bg-red-900/50 border border-red-700 text-red-300' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Login Button */}
          <button
            type="submit"
            disabled={pin.length === 0 || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              pin.length === 0 || isLoading
                ? isDarkMode 
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl'
            } transform hover:scale-105 active:scale-95`}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Authenticating...</span>
              </>
            ) : (
              <>
                <Shield className="w-5 h-5" />
                <span>Access Admin Dashboard</span>
                <ArrowRight className="w-5 h-5" />
              </>
            )}
          </button>
        </form>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>Super Administrator Portal</p>
            <p className="mt-1">© 2024 RestroFlow. All rights reserved.</p>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob ${
          isDarkMode ? 'bg-red-600' : 'bg-red-300'
        }`}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000 ${
          isDarkMode ? 'bg-pink-600' : 'bg-pink-300'
        }`}></div>
        <div className={`absolute top-40 left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000 ${
          isDarkMode ? 'bg-purple-600' : 'bg-purple-300'
        }`}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob { animation: blob 7s infinite; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-4000 { animation-delay: 4s; }
      `}</style>
    </div>
  );
};

export default SuperAdminLogin;
