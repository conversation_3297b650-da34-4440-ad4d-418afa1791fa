import React, { useState, useEffect } from 'react';
import { 
  Music, 
  Clock, 
  Users, 
  CreditCard, 
  Shield,
  Wine,
  Beer,
  Martini,
  Calendar,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Timer,
  Volume2,
  Star,
  UserCheck
} from 'lucide-react';

interface DrinkItem {
  id: string;
  name: string;
  category: 'beer' | 'wine' | 'spirits' | 'cocktails' | 'non-alcoholic';
  price: number;
  alcoholContent?: number;
  volume: number; // in ml
  description?: string;
  ingredients?: string[];
  isHappyHour?: boolean;
  happyHourPrice?: number;
}

interface TabOrder {
  id: string;
  tabName: string;
  tableNumber?: number;
  items: {
    drink: DrinkItem;
    quantity: number;
    timestamp: Date;
    price: number;
  }[];
  openedAt: Date;
  lastActivity: Date;
  status: 'open' | 'ready-to-close' | 'closed';
  total: number;
  ageVerified: boolean;
  customerId?: string;
}

interface Customer {
  id: string;
  name: string;
  age: number;
  idVerified: boolean;
  verificationTime: Date;
  loyaltyTier: 'Regular' | 'VIP' | 'Premium';
  visitCount: number;
}

interface HappyHourSchedule {
  id: string;
  name: string;
  startTime: string;
  endTime: string;
  days: string[];
  discount: number;
  applicableCategories: string[];
  isActive: boolean;
}

const BarInterface: React.FC = () => {
  const [activeTabs, setActiveTabs] = useState<TabOrder[]>([]);
  const [selectedTab, setSelectedTab] = useState<TabOrder | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('beer');
  const [currentCustomer, setCurrentCustomer] = useState<Customer | null>(null);
  const [showAgeVerification, setShowAgeVerification] = useState<boolean>(false);
  const [happyHourActive, setHappyHourActive] = useState<boolean>(false);
  const [currentEvent, setCurrentEvent] = useState<string>('Live Jazz Night');

  // Mock drink menu
  const drinkMenu: Record<string, DrinkItem[]> = {
    beer: [
      {
        id: '1',
        name: 'IPA Draft',
        category: 'beer',
        price: 7.50,
        alcoholContent: 6.2,
        volume: 473,
        description: 'Hoppy and citrusy',
        isHappyHour: true,
        happyHourPrice: 5.50
      },
      {
        id: '2',
        name: 'Lager',
        category: 'beer',
        price: 6.50,
        alcoholContent: 4.8,
        volume: 473,
        description: 'Crisp and refreshing',
        isHappyHour: true,
        happyHourPrice: 4.50
      },
      {
        id: '3',
        name: 'Stout',
        category: 'beer',
        price: 8.00,
        alcoholContent: 5.5,
        volume: 473,
        description: 'Rich and creamy'
      }
    ],
    wine: [
      {
        id: '4',
        name: 'House Red',
        category: 'wine',
        price: 9.00,
        alcoholContent: 13.5,
        volume: 150,
        description: 'Cabernet Sauvignon blend',
        isHappyHour: true,
        happyHourPrice: 6.50
      },
      {
        id: '5',
        name: 'Chardonnay',
        category: 'wine',
        price: 10.50,
        alcoholContent: 12.5,
        volume: 150,
        description: 'Oaked white wine'
      },
      {
        id: '6',
        name: 'Prosecco',
        category: 'wine',
        price: 12.00,
        alcoholContent: 11.0,
        volume: 150,
        description: 'Italian sparkling wine'
      }
    ],
    spirits: [
      {
        id: '7',
        name: 'Whiskey (Single)',
        category: 'spirits',
        price: 12.00,
        alcoholContent: 40.0,
        volume: 44,
        description: 'Premium bourbon'
      },
      {
        id: '8',
        name: 'Vodka (Single)',
        category: 'spirits',
        price: 10.00,
        alcoholContent: 40.0,
        volume: 44,
        description: 'Premium vodka'
      },
      {
        id: '9',
        name: 'Rum (Single)',
        category: 'spirits',
        price: 11.00,
        alcoholContent: 37.5,
        volume: 44,
        description: 'Aged rum'
      }
    ],
    cocktails: [
      {
        id: '10',
        name: 'Old Fashioned',
        category: 'cocktails',
        price: 14.00,
        alcoholContent: 35.0,
        volume: 88,
        description: 'Whiskey, bitters, sugar',
        ingredients: ['Bourbon', 'Angostura bitters', 'Sugar', 'Orange peel']
      },
      {
        id: '11',
        name: 'Mojito',
        category: 'cocktails',
        price: 12.50,
        alcoholContent: 20.0,
        volume: 200,
        description: 'Rum, mint, lime, soda',
        ingredients: ['White rum', 'Fresh mint', 'Lime juice', 'Soda water', 'Sugar']
      },
      {
        id: '12',
        name: 'Martini',
        category: 'cocktails',
        price: 15.00,
        alcoholContent: 38.0,
        volume: 75,
        description: 'Gin or vodka with vermouth',
        ingredients: ['Gin', 'Dry vermouth', 'Olive or lemon twist']
      }
    ],
    'non-alcoholic': [
      {
        id: '13',
        name: 'Virgin Mojito',
        category: 'non-alcoholic',
        price: 6.50,
        alcoholContent: 0,
        volume: 200,
        description: 'Mint, lime, soda water'
      },
      {
        id: '14',
        name: 'Craft Soda',
        category: 'non-alcoholic',
        price: 4.50,
        alcoholContent: 0,
        volume: 330,
        description: 'Artisanal sodas'
      }
    ]
  };

  // Mock happy hour schedule
  const happyHourSchedule: HappyHourSchedule = {
    id: '1',
    name: 'Daily Happy Hour',
    startTime: '17:00',
    endTime: '19:00',
    days: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],
    discount: 25,
    applicableCategories: ['beer', 'wine'],
    isActive: true
  };

  const openNewTab = () => {
    const newTab: TabOrder = {
      id: Date.now().toString(),
      tabName: `Tab ${activeTabs.length + 1}`,
      items: [],
      openedAt: new Date(),
      lastActivity: new Date(),
      status: 'open',
      total: 0,
      ageVerified: false
    };
    
    setActiveTabs([...activeTabs, newTab]);
    setSelectedTab(newTab);
    setShowAgeVerification(true);
  };

  const addDrinkToTab = (drink: DrinkItem) => {
    if (!selectedTab || !selectedTab.ageVerified) {
      if (drink.alcoholContent && drink.alcoholContent > 0) {
        setShowAgeVerification(true);
        return;
      }
    }

    const price = happyHourActive && drink.isHappyHour ? drink.happyHourPrice || drink.price : drink.price;
    
    const updatedTab = {
      ...selectedTab!,
      items: [
        ...selectedTab!.items,
        {
          drink,
          quantity: 1,
          timestamp: new Date(),
          price
        }
      ],
      lastActivity: new Date(),
      total: selectedTab!.total + price
    };

    const updatedTabs = activeTabs.map(tab => 
      tab.id === selectedTab!.id ? updatedTab : tab
    );
    
    setActiveTabs(updatedTabs);
    setSelectedTab(updatedTab);
  };

  const verifyAge = () => {
    if (selectedTab) {
      const updatedTab = {
        ...selectedTab,
        ageVerified: true
      };
      
      const updatedTabs = activeTabs.map(tab => 
        tab.id === selectedTab.id ? updatedTab : tab
      );
      
      setActiveTabs(updatedTabs);
      setSelectedTab(updatedTab);
    }
    setShowAgeVerification(false);
  };

  const closeTab = (tabId: string) => {
    setActiveTabs(activeTabs.filter(tab => tab.id !== tabId));
    if (selectedTab?.id === tabId) {
      setSelectedTab(null);
    }
  };

  const splitTab = (tabId: string, splitCount: number) => {
    const tab = activeTabs.find(t => t.id === tabId);
    if (!tab) return;

    const itemsPerSplit = Math.ceil(tab.items.length / splitCount);
    const newTabs: TabOrder[] = [];

    for (let i = 0; i < splitCount; i++) {
      const startIndex = i * itemsPerSplit;
      const endIndex = Math.min(startIndex + itemsPerSplit, tab.items.length);
      const splitItems = tab.items.slice(startIndex, endIndex);
      const splitTotal = splitItems.reduce((sum, item) => sum + item.price, 0);

      newTabs.push({
        ...tab,
        id: `${tab.id}-split-${i + 1}`,
        tabName: `${tab.tabName} (Split ${i + 1})`,
        items: splitItems,
        total: splitTotal,
        status: 'ready-to-close'
      });
    }

    const updatedTabs = activeTabs.filter(t => t.id !== tabId).concat(newTabs);
    setActiveTabs(updatedTabs);
    setSelectedTab(newTabs[0]);
  };

  const categories = [
    { id: 'beer', name: 'Beer', icon: Beer, color: 'amber' },
    { id: 'wine', name: 'Wine', icon: Wine, color: 'purple' },
    { id: 'spirits', name: 'Spirits', icon: Martini, color: 'blue' },
    { id: 'cocktails', name: 'Cocktails', icon: Martini, color: 'pink' },
    { id: 'non-alcoholic', name: 'Non-Alcoholic', icon: CheckCircle, color: 'green' }
  ];

  // Check if it's happy hour
  useEffect(() => {
    const now = new Date();
    const currentTime = now.getHours() * 100 + now.getMinutes();
    const startTime = parseInt(happyHourSchedule.startTime.replace(':', ''));
    const endTime = parseInt(happyHourSchedule.endTime.replace(':', ''));
    const currentDay = now.toLocaleDateString('en-US', { weekday: 'long' });
    
    setHappyHourActive(
      happyHourSchedule.isActive &&
      happyHourSchedule.days.includes(currentDay) &&
      currentTime >= startTime &&
      currentTime <= endTime
    );
  }, []);

  return (
    <div className="h-full bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-800 to-indigo-700 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Music className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">The Blue Note Bar</h1>
              <p className="text-blue-100">Premium cocktails & live entertainment</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{activeTabs.length}</div>
              <div className="text-blue-200">Open Tabs</div>
            </div>
            <div className="text-center">
              <div className={`text-2xl font-bold ${happyHourActive ? 'text-yellow-300' : 'text-blue-200'}`}>
                {happyHourActive ? '🍻' : '🎵'}
              </div>
              <div className="text-blue-200">
                {happyHourActive ? 'Happy Hour' : currentEvent}
              </div>
            </div>
          </div>
        </div>
        
        {happyHourActive && (
          <div className="mt-4 p-3 bg-yellow-500/20 rounded-lg border border-yellow-400/30">
            <div className="flex items-center space-x-2">
              <Star className="w-5 h-5 text-yellow-300" />
              <span className="font-semibold text-yellow-100">
                Happy Hour Active! 25% off beer and wine until 7:00 PM
              </span>
            </div>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Active Tabs */}
        <div className="lg:col-span-1">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6 h-full">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-3">
                <CreditCard className="w-6 h-6 text-white" />
                <h2 className="text-xl font-bold text-white">Active Tabs</h2>
              </div>
              <button
                onClick={openNewTab}
                className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200"
              >
                New Tab
              </button>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {activeTabs.map((tab) => (
                <div
                  key={tab.id}
                  onClick={() => setSelectedTab(tab)}
                  className={`p-4 rounded-lg border-2 cursor-pointer transition-all duration-200 ${
                    selectedTab?.id === tab.id
                      ? 'border-blue-400 bg-blue-500/20'
                      : 'border-white/20 bg-white/5 hover:border-white/40'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-bold text-white">{tab.tabName}</span>
                    <div className="flex items-center space-x-2">
                      {tab.ageVerified ? (
                        <Shield className="w-4 h-4 text-green-400" />
                      ) : (
                        <AlertTriangle className="w-4 h-4 text-yellow-400" />
                      )}
                      <span className="text-sm font-medium text-white">
                        ${tab.total.toFixed(2)}
                      </span>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-300 mb-2">
                    {tab.items.length} items • Opened {tab.openedAt.toLocaleTimeString()}
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className={`text-xs px-2 py-1 rounded-full ${
                      tab.status === 'open' ? 'bg-green-500/20 text-green-300' :
                      tab.status === 'ready-to-close' ? 'bg-yellow-500/20 text-yellow-300' :
                      'bg-gray-500/20 text-gray-300'
                    }`}>
                      {tab.status.replace('-', ' ')}
                    </span>
                    
                    {tab.items.length > 1 && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          splitTab(tab.id, 2);
                        }}
                        className="text-xs text-blue-300 hover:text-blue-100 transition-colors"
                      >
                        Split
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Drink Menu */}
        <div className="lg:col-span-2">
          <div className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Wine className="w-6 h-6 text-white" />
              <h2 className="text-xl font-bold text-white">Drink Menu</h2>
            </div>
            
            {/* Category Tabs */}
            <div className="flex space-x-2 mb-6 overflow-x-auto">
              {categories.map((category) => {
                const Icon = category.icon;
                return (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
                      selectedCategory === category.id
                        ? 'bg-blue-600 text-white shadow-lg'
                        : 'bg-white/10 text-white/80 hover:bg-white/20'
                    }`}
                  >
                    <Icon className="w-4 h-4" />
                    <span>{category.name}</span>
                  </button>
                );
              })}
            </div>
            
            {/* Drink Items */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {drinkMenu[selectedCategory]?.map((drink) => (
                <div
                  key={drink.id}
                  onClick={() => addDrinkToTab(drink)}
                  className="p-4 bg-white/5 border border-white/20 rounded-lg hover:border-white/40 hover:bg-white/10 transition-all duration-200 cursor-pointer"
                >
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2">
                        <h3 className="font-semibold text-white">{drink.name}</h3>
                        {happyHourActive && drink.isHappyHour && (
                          <Star className="w-4 h-4 text-yellow-400 fill-current" />
                        )}
                      </div>
                      {drink.description && (
                        <p className="text-sm text-gray-300 mt-1">{drink.description}</p>
                      )}
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-white">
                        ${happyHourActive && drink.isHappyHour ? drink.happyHourPrice : drink.price}
                      </div>
                      {happyHourActive && drink.isHappyHour && drink.happyHourPrice && (
                        <div className="text-xs text-gray-400 line-through">
                          ${drink.price}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-400">
                    <span>{drink.volume}ml</span>
                    {drink.alcoholContent && drink.alcoholContent > 0 && (
                      <span>{drink.alcoholContent}% ABV</span>
                    )}
                  </div>
                  
                  {drink.ingredients && (
                    <div className="mt-2 text-xs text-gray-400">
                      {drink.ingredients.join(', ')}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Tab Details & Controls */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Age Verification */}
            {showAgeVerification && (
              <div className="bg-red-500/20 backdrop-blur-sm rounded-2xl shadow-lg border border-red-400/30 p-6">
                <div className="flex items-center space-x-3 mb-4">
                  <Shield className="w-6 h-6 text-red-300" />
                  <h3 className="text-lg font-bold text-white">Age Verification Required</h3>
                </div>
                
                <p className="text-red-100 mb-4">
                  Customer must be 21+ to purchase alcoholic beverages
                </p>
                
                <div className="space-y-3">
                  <button
                    onClick={verifyAge}
                    className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-200"
                  >
                    ✓ ID Verified - Customer is 21+
                  </button>
                  <button
                    onClick={() => setShowAgeVerification(false)}
                    className="w-full bg-red-600 hover:bg-red-700 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-200"
                  >
                    ✗ Cancel Order
                  </button>
                </div>
              </div>
            )}

            {/* Selected Tab Details */}
            {selectedTab && (
              <div className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-bold text-white">{selectedTab.tabName}</h3>
                  <div className="flex items-center space-x-2">
                    {selectedTab.ageVerified ? (
                      <Shield className="w-5 h-5 text-green-400" />
                    ) : (
                      <AlertTriangle className="w-5 h-5 text-yellow-400" />
                    )}
                  </div>
                </div>
                
                <div className="space-y-3 max-h-48 overflow-y-auto">
                  {selectedTab.items.length === 0 ? (
                    <p className="text-gray-400 text-center py-4">No items in tab</p>
                  ) : (
                    selectedTab.items.map((item, index) => (
                      <div key={index} className="flex items-center justify-between p-3 bg-white/5 rounded-lg">
                        <div>
                          <div className="font-medium text-white">{item.drink.name}</div>
                          <div className="text-xs text-gray-400">
                            {item.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                        <span className="font-bold text-white">${item.price.toFixed(2)}</span>
                      </div>
                    ))
                  )}
                </div>
                
                {selectedTab.items.length > 0 && (
                  <div className="mt-4 pt-4 border-t border-white/20">
                    <div className="flex items-center justify-between mb-4">
                      <span className="text-lg font-bold text-white">Total:</span>
                      <span className="text-xl font-bold text-white">${selectedTab.total.toFixed(2)}</span>
                    </div>
                    
                    <div className="space-y-2">
                      <button
                        onClick={() => closeTab(selectedTab.id)}
                        className="w-full bg-green-600 hover:bg-green-700 text-white py-3 px-4 rounded-lg font-semibold transition-all duration-200"
                      >
                        Close Tab & Pay
                      </button>
                      
                      {selectedTab.items.length > 1 && (
                        <button
                          onClick={() => splitTab(selectedTab.id, 2)}
                          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200"
                        >
                          Split Tab
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            )}

            {/* Bar Stats */}
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl shadow-lg border border-white/20 p-6">
              <h3 className="text-lg font-bold text-white mb-4">Tonight's Stats</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Tabs Opened</span>
                  <span className="font-bold text-white">23</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Revenue</span>
                  <span className="font-bold text-green-400">$1,247</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Most Popular</span>
                  <span className="font-bold text-white">IPA Draft</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-300">Event</span>
                  <span className="font-bold text-purple-300">{currentEvent}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BarInterface;
