import React, { useState, useEffect } from 'react';
import {
  Store,
  Package,
  Users,
  BarChart3,
  Settings,
  MapPin,
  Calendar,
  DollarSign,
  TrendingUp,
  AlertTriangle,
  Bell,
  Menu,
  X,
  ChevronRight,
  Plus,
  Search,
  Filter,
  LogOut,
  User,
  Shield,
  Globe,
  CreditCard,
  FileText,
  Clock,
  Target,
  Zap
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import TenantAdminDashboard from './TenantAdminDashboard';
import ProductManagementInterface from './ProductManagementInterface';
import MultiLocationBackendManager from './MultiLocationBackendManager';

interface TenantAdminLandingPageProps {
  onSwitchToPOS: () => void;
  onLogout: () => void;
}

const TenantAdminLandingPage: React.FC<TenantAdminLandingPageProps> = ({ onSwitchToPOS, onLogout }) => {
  const { user } = useEnhancedAppContext();
  const [currentTab, setCurrentTab] = useState('dashboard');
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [notifications, setNotifications] = useState<any[]>([]);
  const [showNotifications, setShowNotifications] = useState(false);

  const navigationItems = [
    {
      id: 'dashboard',
      name: 'Dashboard',
      icon: BarChart3,
      description: 'Overview and quick actions'
    },
    {
      id: 'product-management',
      name: 'Product Management',
      icon: Package,
      description: 'Manage menu items and categories'
    },
    {
      id: 'location-management',
      name: 'Location Management',
      icon: MapPin,
      description: 'Multi-location backend management'
    },
    {
      id: 'staff-management',
      name: 'Staff Management',
      icon: Users,
      description: 'Employee management across locations'
    },
    {
      id: 'financial-reports',
      name: 'Financial Reports',
      icon: DollarSign,
      description: 'Revenue and financial analytics'
    },
    {
      id: 'inventory-management',
      name: 'Inventory Management',
      icon: Package,
      description: 'Stock management and transfers'
    },
    {
      id: 'tenant-settings',
      name: 'Settings',
      icon: Settings,
      description: 'Business configuration and preferences'
    }
  ];

  useEffect(() => {
    // Fetch notifications
    fetchNotifications();
  }, []);

  const fetchNotifications = async () => {
    // Mock notifications - in production, fetch from API
    setNotifications([
      {
        id: 1,
        title: 'Low Stock Alert',
        message: 'Chicken Breast is running low at Downtown location',
        type: 'warning',
        time: '5 minutes ago',
        unread: true
      },
      {
        id: 2,
        title: 'New Staff Member',
        message: 'John Doe has been added to Airport location',
        type: 'info',
        time: '1 hour ago',
        unread: true
      },
      {
        id: 3,
        title: 'Daily Report Ready',
        message: 'Yesterday\'s financial report is available',
        type: 'success',
        time: '2 hours ago',
        unread: false
      }
    ]);
  };

  const StatCard: React.FC<{ title: string; value: string | number; icon: React.ReactNode; color: string; trend?: string }> = 
    ({ title, value, icon, color, trend }) => (
    <div className="bg-white rounded-lg shadow p-6">
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm font-medium text-gray-600">{title}</p>
          <p className="text-2xl font-semibold text-gray-900">{value}</p>
          {trend && (
            <p className="text-sm text-green-600 flex items-center mt-1">
              <TrendingUp className="h-3 w-3 mr-1" />
              {trend}
            </p>
          )}
        </div>
        <div className={`p-3 rounded-full ${color}`}>
          {icon}
        </div>
      </div>
    </div>
  );

  const renderDashboard = () => (
    <div className="space-y-6">
      {/* Business Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Orders Today"
          value={dashboardData?.business_overview.total_orders_today || 0}
          icon={<ShoppingBag className="h-6 w-6 text-white" />}
          color="bg-blue-500"
          trend="+12% from yesterday"
        />
        <StatCard
          title="Revenue Today"
          value={`$${dashboardData?.business_overview.revenue_today?.toFixed(2) || '0.00'}`}
          icon={<DollarSign className="h-6 w-6 text-white" />}
          color="bg-green-500"
          trend="+8% from yesterday"
        />
        <StatCard
          title="Active Staff"
          value={dashboardData?.business_overview.active_staff || 0}
          icon={<Users className="h-6 w-6 text-white" />}
          color="bg-purple-500"
        />
        <StatCard
          title="Menu Items"
          value={dashboardData?.business_overview.menu_items || 0}
          icon={<MenuIcon className="h-6 w-6 text-white" />}
          color="bg-orange-500"
        />
      </div>

      {/* Quick Stats */}
      <div className="bg-white rounded-lg shadow p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Quick Stats</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              ${dashboardData?.quick_stats.avg_order_value?.toFixed(2) || '0.00'}
            </div>
            <div className="text-sm text-gray-600">Avg Order Value</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {dashboardData?.quick_stats.peak_hour || 'N/A'}
            </div>
            <div className="text-sm text-gray-600">Peak Hour</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">
              {dashboardData?.quick_stats.top_item || 'N/A'}
            </div>
            <div className="text-sm text-gray-600">Top Selling Item</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {dashboardData?.quick_stats.customer_satisfaction || 0}/5
            </div>
            <div className="text-sm text-gray-600">Customer Rating</div>
          </div>
        </div>
      </div>

      {/* Recent Orders & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Orders */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Orders</h3>
          <div className="space-y-3">
            {dashboardData?.recent_orders?.map((order) => (
              <div key={order.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                  <div className="font-medium text-gray-900">Order #{order.id.slice(-6)}</div>
                  <div className="text-sm text-gray-600">{order.items_count} items</div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-gray-900">${order.total?.toFixed(2)}</div>
                  <div className="text-sm text-gray-600">
                    {new Date(order.timestamp).toLocaleTimeString()}
                  </div>
                </div>
              </div>
            )) || (
              <div className="text-center text-gray-500 py-4">No recent orders</div>
            )}
          </div>
        </div>

        {/* Alerts */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Alerts & Notifications</h3>
          <div className="space-y-3">
            {dashboardData?.alerts?.map((alert, index) => (
              <div key={index} className={`flex items-start p-3 rounded-lg ${
                alert.type === 'warning' ? 'bg-yellow-50 border border-yellow-200' : 'bg-blue-50 border border-blue-200'
              }`}>
                <AlertCircle className={`h-5 w-5 mt-0.5 mr-3 ${
                  alert.type === 'warning' ? 'text-yellow-600' : 'text-blue-600'
                }`} />
                <div className="text-sm text-gray-700">{alert.message}</div>
              </div>
            )) || (
              <div className="text-center text-gray-500 py-4">No alerts</div>
            )}
          </div>
        </div>
      </div>
    </div>
  );

  const renderMenu = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Menu Management</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="h-4 w-4" />
          Add Item
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Item
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {menuItems.map((item) => (
                <tr key={item.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{item.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {item.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${item.price.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      item.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {item.is_active ? 'Active' : 'Inactive'}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Eye className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  const renderStaff = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold text-gray-900">Staff Management</h2>
        <button className="bg-blue-600 text-white px-4 py-2 rounded-lg flex items-center gap-2 hover:bg-blue-700">
          <Plus className="h-4 w-4" />
          Add Staff
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {staff.map((member) => (
                <tr key={member.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{member.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {member.role}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      member.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                    }`}>
                      {member.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {new Date(member.last_login).toLocaleDateString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button className="text-blue-600 hover:text-blue-900 mr-3">
                      <Edit className="h-4 w-4" />
                    </button>
                    <button className="text-gray-600 hover:text-gray-900">
                      <Settings className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className="mt-4 text-gray-600">Loading Tenant Dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-100">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">
                {dashboardData?.business_overview.business_name || 'Restaurant Dashboard'}
              </h1>
            </div>
            <div className="flex items-center space-x-4">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <Bell className="h-6 w-6" />
              </button>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white text-sm font-medium">TA</span>
                </div>
                <span className="text-sm font-medium text-gray-700">Tenant Admin</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {[
              { id: 'dashboard', label: 'Dashboard', icon: BarChart3 },
              { id: 'menu', label: 'Menu', icon: Package },
              { id: 'staff', label: 'Staff', icon: UserCheck },
              { id: 'orders', label: 'Orders', icon: ShoppingBag },
              { id: 'devices', label: 'Devices', icon: Printer },
              { id: 'reports', label: 'Reports', icon: TrendingUp }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {activeTab === 'dashboard' && renderDashboard()}
          {activeTab === 'menu' && renderMenu()}
          {activeTab === 'staff' && renderStaff()}
          {['orders', 'devices', 'reports'].includes(activeTab) && (
            <div className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900">{activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Coming Soon</h3>
              <p className="text-gray-500">This feature will be available soon.</p>
            </div>
          )}
        </div>
      </main>
    </div>
  );
};

export default TenantAdminLandingPage;
