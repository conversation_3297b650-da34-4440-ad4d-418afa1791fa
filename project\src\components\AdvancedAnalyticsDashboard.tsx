import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { BarChart3, TrendingUp, Calendar, Clock, Users, DollarSign, Target, Zap, RefreshCw } from 'lucide-react';

interface AnalyticsData {
  sales_heatmap: Array<{
    hour: number;
    day: string;
    sales: number;
    orders: number;
    avg_order_value: number;
  }>;
  location_performance: Array<{
    location_id: string;
    location_name: string;
    revenue: number;
    orders: number;
    growth_rate: number;
    efficiency_score: number;
  }>;
  predictive_insights: {
    next_week_forecast: {
      revenue: number;
      orders: number;
      confidence: number;
    };
    inventory_predictions: Array<{
      product_name: string;
      predicted_demand: number;
      reorder_date: string;
      confidence: number;
    }>;
    peak_hours_forecast: Array<{
      day: string;
      peak_hour: number;
      expected_orders: number;
    }>;
  };
  customer_insights: {
    segments: Array<{
      segment: string;
      count: number;
      avg_spend: number;
      frequency: number;
    }>;
    retention_rate: number;
    churn_risk: number;
  };
  operational_metrics: {
    avg_prep_time: number;
    kitchen_efficiency: number;
    staff_productivity: number;
    waste_percentage: number;
  };
}

const AdvancedAnalyticsDashboard: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [selectedLocation, setSelectedLocation] = useState<string>('all');
  const [activeView, setActiveView] = useState<'overview' | 'heatmap' | 'predictions' | 'customers'>('overview');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load advanced analytics data
  useEffect(() => {
    const loadAnalyticsData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('📊 Loading advanced analytics...');
        
        const response = await apiCall(`/api/enterprise/analytics?range=${selectedTimeRange}&location=${selectedLocation}`);
        if (response.ok) {
          const data = await response.json();
          setAnalyticsData(data);
          console.log('✅ Advanced analytics loaded');
        }
      } catch (error) {
        console.error('❌ Error loading advanced analytics:', error);
        setError('Failed to load analytics. Using mock data.');
        
        // Fallback to mock data
        const mockData: AnalyticsData = {
          sales_heatmap: generateMockHeatmapData(),
          location_performance: [
            {
              location_id: 'loc_1',
              location_name: 'Downtown Restaurant',
              revenue: 125000,
              orders: 2450,
              growth_rate: 12.5,
              efficiency_score: 87
            },
            {
              location_id: 'loc_2',
              location_name: 'Airport Branch',
              revenue: 180000,
              orders: 3200,
              growth_rate: 8.3,
              efficiency_score: 92
            },
            {
              location_id: 'loc_3',
              location_name: 'Mall Food Court',
              revenue: 95000,
              orders: 1800,
              growth_rate: -2.1,
              efficiency_score: 78
            }
          ],
          predictive_insights: {
            next_week_forecast: {
              revenue: 28500,
              orders: 520,
              confidence: 85
            },
            inventory_predictions: [
              {
                product_name: 'Coffee Beans',
                predicted_demand: 150,
                reorder_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
                confidence: 92
              },
              {
                product_name: 'Burger Patties',
                predicted_demand: 300,
                reorder_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(),
                confidence: 88
              }
            ],
            peak_hours_forecast: [
              { day: 'Monday', peak_hour: 12, expected_orders: 45 },
              { day: 'Tuesday', peak_hour: 13, expected_orders: 52 },
              { day: 'Wednesday', peak_hour: 12, expected_orders: 48 }
            ]
          },
          customer_insights: {
            segments: [
              { segment: 'Regular Customers', count: 1250, avg_spend: 35.50, frequency: 2.3 },
              { segment: 'Occasional Visitors', count: 3200, avg_spend: 28.75, frequency: 0.8 },
              { segment: 'New Customers', count: 850, avg_spend: 32.25, frequency: 1.0 }
            ],
            retention_rate: 68.5,
            churn_risk: 15.2
          },
          operational_metrics: {
            avg_prep_time: 14.5,
            kitchen_efficiency: 89.2,
            staff_productivity: 85.7,
            waste_percentage: 3.2
          }
        };
        setAnalyticsData(mockData);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadAnalyticsData();
  }, [apiCall, selectedTimeRange, selectedLocation]);

  // Generate mock heatmap data
  function generateMockHeatmapData() {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    const data = [];
    
    for (const day of days) {
      for (let hour = 6; hour <= 23; hour++) {
        let sales = 0;
        let orders = 0;
        
        // Simulate realistic patterns
        if (hour >= 7 && hour <= 9) { // Breakfast
          sales = Math.random() * 800 + 400;
          orders = Math.random() * 25 + 15;
        } else if (hour >= 11 && hour <= 14) { // Lunch
          sales = Math.random() * 1200 + 800;
          orders = Math.random() * 40 + 30;
        } else if (hour >= 17 && hour <= 21) { // Dinner
          sales = Math.random() * 1500 + 1000;
          orders = Math.random() * 50 + 35;
        } else {
          sales = Math.random() * 300 + 100;
          orders = Math.random() * 10 + 5;
        }
        
        // Weekend boost
        if (day === 'Saturday' || day === 'Sunday') {
          sales *= 1.3;
          orders *= 1.2;
        }
        
        data.push({
          hour,
          day,
          sales: Math.round(sales),
          orders: Math.round(orders),
          avg_order_value: Math.round((sales / orders) * 100) / 100
        });
      }
    }
    
    return data;
  }

  const getHeatmapColor = (value: number, max: number) => {
    const intensity = value / max;
    if (intensity > 0.8) return 'bg-red-500';
    if (intensity > 0.6) return 'bg-orange-500';
    if (intensity > 0.4) return 'bg-yellow-500';
    if (intensity > 0.2) return 'bg-green-500';
    return 'bg-blue-500';
  };

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading advanced analytics...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="text-center">
          <BarChart3 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <p className="text-gray-600">No analytics data available</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Advanced Analytics Dashboard</h2>
            <p className="text-sm text-gray-500">AI-powered insights and predictions</p>
          </div>
          <div className="flex items-center space-x-3">
            <select
              value={selectedTimeRange}
              onChange={(e) => setSelectedTimeRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="7d">Last 7 Days</option>
              <option value="30d">Last 30 Days</option>
              <option value="90d">Last 90 Days</option>
            </select>
            <select
              value={selectedLocation}
              onChange={(e) => setSelectedLocation(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Locations</option>
              <option value="loc_1">Downtown Restaurant</option>
              <option value="loc_2">Airport Branch</option>
              <option value="loc_3">Mall Food Court</option>
            </select>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'overview', label: 'Overview', icon: BarChart3 },
            { id: 'heatmap', label: 'Sales Heatmap', icon: Calendar },
            { id: 'predictions', label: 'Predictions', icon: Zap },
            { id: 'customers', label: 'Customer Insights', icon: Users }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'overview' && (
          <div className="space-y-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Avg Prep Time</p>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.operational_metrics.avg_prep_time}m</p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Kitchen Efficiency</p>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.operational_metrics.kitchen_efficiency}%</p>
                  </div>
                  <Target className="h-8 w-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Staff Productivity</p>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.operational_metrics.staff_productivity}%</p>
                  </div>
                  <Users className="h-8 w-8 text-purple-500" />
                </div>
              </div>
              <div className="bg-white p-4 rounded-lg border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Waste Percentage</p>
                    <p className="text-2xl font-bold text-gray-900">{analyticsData.operational_metrics.waste_percentage}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-500" />
                </div>
              </div>
            </div>

            {/* Location Performance */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Location Performance</h3>
              <div className="space-y-3">
                {analyticsData.location_performance.map((location) => (
                  <div key={location.location_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{location.location_name}</h4>
                      <p className="text-sm text-gray-600">{location.orders} orders</p>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Revenue</p>
                        <p className="font-semibold text-gray-900">${location.revenue.toLocaleString()}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Growth</p>
                        <p className={`font-semibold ${location.growth_rate >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {location.growth_rate >= 0 ? '+' : ''}{location.growth_rate}%
                        </p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Efficiency</p>
                        <p className="font-semibold text-gray-900">{location.efficiency_score}%</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeView === 'heatmap' && (
          <div className="bg-white rounded-lg border border-gray-200 p-4">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Sales Heatmap</h3>
            <div className="overflow-x-auto">
              <div className="grid grid-cols-19 gap-1 min-w-max">
                {/* Header row */}
                <div></div>
                {Array.from({ length: 18 }, (_, i) => i + 6).map(hour => (
                  <div key={hour} className="text-xs text-gray-500 text-center p-1">
                    {hour}:00
                  </div>
                ))}
                
                {/* Data rows */}
                {['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'].map(day => (
                  <React.Fragment key={day}>
                    <div className="text-xs text-gray-500 p-1 font-medium">{day.slice(0, 3)}</div>
                    {Array.from({ length: 18 }, (_, i) => i + 6).map(hour => {
                      const dataPoint = analyticsData.sales_heatmap.find(d => d.day === day && d.hour === hour);
                      const maxSales = Math.max(...analyticsData.sales_heatmap.map(d => d.sales));
                      return (
                        <div
                          key={`${day}-${hour}`}
                          className={`w-8 h-8 rounded ${getHeatmapColor(dataPoint?.sales || 0, maxSales)} opacity-80 hover:opacity-100 transition-opacity cursor-pointer`}
                          title={`${day} ${hour}:00 - $${dataPoint?.sales || 0} (${dataPoint?.orders || 0} orders)`}
                        />
                      );
                    })}
                  </React.Fragment>
                ))}
              </div>
            </div>
            <div className="mt-4 flex items-center space-x-4 text-xs text-gray-500">
              <span>Low</span>
              <div className="flex space-x-1">
                <div className="w-4 h-4 bg-blue-500 rounded"></div>
                <div className="w-4 h-4 bg-green-500 rounded"></div>
                <div className="w-4 h-4 bg-yellow-500 rounded"></div>
                <div className="w-4 h-4 bg-orange-500 rounded"></div>
                <div className="w-4 h-4 bg-red-500 rounded"></div>
              </div>
              <span>High</span>
            </div>
          </div>
        )}

        {activeView === 'predictions' && (
          <div className="space-y-6">
            {/* Next Week Forecast */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Next Week Forecast</h3>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <p className="text-gray-500 text-sm">Predicted Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">${analyticsData.predictive_insights.next_week_forecast.revenue.toLocaleString()}</p>
                  <p className="text-sm text-green-600">{analyticsData.predictive_insights.next_week_forecast.confidence}% confidence</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm">Predicted Orders</p>
                  <p className="text-2xl font-bold text-gray-900">{analyticsData.predictive_insights.next_week_forecast.orders}</p>
                  <p className="text-sm text-green-600">{analyticsData.predictive_insights.next_week_forecast.confidence}% confidence</p>
                </div>
                <div className="text-center">
                  <p className="text-gray-500 text-sm">Avg Order Value</p>
                  <p className="text-2xl font-bold text-gray-900">
                    ${(analyticsData.predictive_insights.next_week_forecast.revenue / analyticsData.predictive_insights.next_week_forecast.orders).toFixed(2)}
                  </p>
                  <p className="text-sm text-green-600">Calculated</p>
                </div>
              </div>
            </div>

            {/* Inventory Predictions */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Inventory Predictions</h3>
              <div className="space-y-3">
                {analyticsData.predictive_insights.inventory_predictions.map((prediction, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{prediction.product_name}</h4>
                      <p className="text-sm text-gray-600">Reorder by {new Date(prediction.reorder_date).toLocaleDateString()}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">{prediction.predicted_demand} units</p>
                      <p className="text-sm text-green-600">{prediction.confidence}% confidence</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Peak Hours Forecast */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Peak Hours Forecast</h3>
              <div className="grid grid-cols-3 gap-4">
                {analyticsData.predictive_insights.peak_hours_forecast.map((forecast, index) => (
                  <div key={index} className="text-center p-3 bg-gray-50 rounded-lg">
                    <h4 className="font-medium text-gray-900">{forecast.day}</h4>
                    <p className="text-lg font-bold text-blue-600">{forecast.peak_hour}:00</p>
                    <p className="text-sm text-gray-600">{forecast.expected_orders} orders</p>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeView === 'customers' && (
          <div className="space-y-6">
            {/* Customer Segments */}
            <div className="bg-white rounded-lg border border-gray-200 p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Segments</h3>
              <div className="space-y-3">
                {analyticsData.customer_insights.segments.map((segment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <h4 className="font-medium text-gray-900">{segment.segment}</h4>
                      <p className="text-sm text-gray-600">{segment.count} customers</p>
                    </div>
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Avg Spend</p>
                        <p className="font-semibold text-gray-900">${segment.avg_spend}</p>
                      </div>
                      <div className="text-center">
                        <p className="text-sm text-gray-500">Frequency</p>
                        <p className="font-semibold text-gray-900">{segment.frequency}x/month</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Retention Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Retention Rate</p>
                    <p className="text-3xl font-bold text-green-600">{analyticsData.customer_insights.retention_rate}%</p>
                  </div>
                  <Users className="h-8 w-8 text-green-500" />
                </div>
              </div>
              <div className="bg-white rounded-lg border border-gray-200 p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-500 text-sm">Churn Risk</p>
                    <p className="text-3xl font-bold text-red-600">{analyticsData.customer_insights.churn_risk}%</p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-red-500" />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdvancedAnalyticsDashboard;
