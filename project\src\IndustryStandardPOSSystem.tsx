import React, { useState, useEffect } from 'react';
import { EnhancedAppProvider, useEnhancedAppContext } from './context/EnhancedAppContext';
import ModernLogin from './components/ModernLogin';
import IndustryStandardPOSLayout from './components/IndustryStandardPOSLayout';
import RestructuredIndustryPOS from './components/RestructuredIndustryPOS';
import TestRestructuredPOS from './components/TestRestructuredPOS';

/**
 * Industry Standard POS System
 * 
 * A complete redesign of the restaurant POS system built to meet industry standards
 * with modern UI/UX design, responsive layout, and comprehensive functionality.
 * 
 * Features:
 * - Modern, intuitive interface design
 * - Mobile-first responsive layout
 * - Industry-standard POS functionality
 * - Advanced payment processing
 * - Real-time order management
 * - Comprehensive inventory tracking
 * - Staff management and scheduling
 * - Business analytics and reporting
 * - Multi-tenant support
 * - Role-based access control
 * - Dark/Light theme support
 * - Session persistence
 * - Offline capability indicators
 * - Real-time notifications
 * - Hardware integration ready
 * 
 * Technical Stack:
 * - React with TypeScript
 * - Node.js/Express backend
 * - PostgreSQL database
 * - WebSocket real-time communication
 * - Modern CSS with Tailwind
 * - Responsive design patterns
 * - Industry-standard security
 */

interface IndustryStandardPOSSystemProps {
  // Optional props for customization
  initialTheme?: 'light' | 'dark';
  companyBranding?: {
    logo?: string;
    primaryColor?: string;
    secondaryColor?: string;
    companyName?: string;
  };
}

const IndustryStandardPOSSystemContent: React.FC<IndustryStandardPOSSystemProps> = ({
  initialTheme = 'light',
  companyBranding
}) => {
  const { state, dispatch } = useEnhancedAppContext();
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check localStorage for saved theme preference
    const savedTheme = localStorage.getItem('industryPOSTheme');
    if (savedTheme) {
      return savedTheme === 'dark';
    }
    return initialTheme === 'dark';
  });

  // Apply theme to document root for global styling
  useEffect(() => {
    const root = document.documentElement;
    if (isDarkMode) {
      root.classList.add('dark');
      root.style.colorScheme = 'dark';
    } else {
      root.classList.remove('dark');
      root.style.colorScheme = 'light';
    }
    
    // Save theme preference
    localStorage.setItem('industryPOSTheme', isDarkMode ? 'dark' : 'light');
    
    console.log(`🎨 Industry POS Theme: ${isDarkMode ? 'Dark' : 'Light'} Mode`);
  }, [isDarkMode]);

  // Apply company branding if provided
  useEffect(() => {
    if (companyBranding) {
      const root = document.documentElement;
      if (companyBranding.primaryColor) {
        root.style.setProperty('--primary-color', companyBranding.primaryColor);
      }
      if (companyBranding.secondaryColor) {
        root.style.setProperty('--secondary-color', companyBranding.secondaryColor);
      }
      
      // Update document title
      if (companyBranding.companyName) {
        document.title = `${companyBranding.companyName} - RestroFlow POS`;
      }
    }
  }, [companyBranding]);

  // Handle theme toggle
  const handleThemeToggle = () => {
    setIsDarkMode(prev => !prev);
  };

  // Auto-save current state periodically
  useEffect(() => {
    const interval = setInterval(() => {
      if (state.isAuthenticated && state.currentOrder) {
        // Auto-save order to prevent data loss
        const orderData = {
          ...state.currentOrder,
          lastSaved: new Date().toISOString()
        };
        localStorage.setItem('industryPOS_currentOrder', JSON.stringify(orderData));
      }
    }, 30000); // Save every 30 seconds

    return () => clearInterval(interval);
  }, [state.isAuthenticated, state.currentOrder]);

  // Restore saved order on login
  useEffect(() => {
    if (state.isAuthenticated && !state.currentOrder) {
      const savedOrder = localStorage.getItem('industryPOS_currentOrder');
      if (savedOrder) {
        try {
          const orderData = JSON.parse(savedOrder);
          // Only restore if saved within last 24 hours
          const savedTime = new Date(orderData.lastSaved);
          const now = new Date();
          const hoursDiff = (now.getTime() - savedTime.getTime()) / (1000 * 60 * 60);
          
          if (hoursDiff < 24) {
            dispatch({ type: 'SET_CURRENT_ORDER', payload: orderData });
            console.log('🔄 Restored saved order from localStorage');
          } else {
            localStorage.removeItem('industryPOS_currentOrder');
          }
        } catch (error) {
          console.error('Error restoring saved order:', error);
          localStorage.removeItem('industryPOS_currentOrder');
        }
      }
    }
  }, [state.isAuthenticated, dispatch]);

  // Handle system-wide keyboard shortcuts
  useEffect(() => {
    const handleKeyboardShortcuts = (event: KeyboardEvent) => {
      // Only handle shortcuts when authenticated
      if (!state.isAuthenticated) return;

      // Ctrl/Cmd + K: Focus search
      if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        const searchInput = document.querySelector('input[placeholder*="Search"]') as HTMLInputElement;
        if (searchInput) {
          searchInput.focus();
        }
      }

      // Ctrl/Cmd + Shift + T: Toggle theme
      if ((event.ctrlKey || event.metaKey) && event.shiftKey && event.key === 'T') {
        event.preventDefault();
        handleThemeToggle();
      }

      // F1: Help (future implementation)
      if (event.key === 'F1') {
        event.preventDefault();
        console.log('Help system - Coming soon');
      }

      // Escape: Clear current action/modal
      if (event.key === 'Escape') {
        // This will be handled by individual components
      }
    };

    document.addEventListener('keydown', handleKeyboardShortcuts);
    return () => document.removeEventListener('keydown', handleKeyboardShortcuts);
  }, [state.isAuthenticated]);

  // Performance monitoring
  useEffect(() => {
    if (state.isAuthenticated) {
      const startTime = performance.now();
      
      return () => {
        const endTime = performance.now();
        const sessionDuration = (endTime - startTime) / 1000; // seconds
        console.log(`📊 POS Session Duration: ${sessionDuration.toFixed(2)} seconds`);
      };
    }
  }, [state.isAuthenticated]);

  // Show login screen if not authenticated
  if (!state.isAuthenticated) {
    return (
      <div className={`min-h-screen transition-colors duration-300 ${
        isDarkMode 
          ? 'bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900' 
          : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
      }`}>
        <ModernLogin
          onLogin={(success) => {
            if (success) {
              // Login successful, component will re-render due to context state change
            }
          }}
          loginType="pos"
        />
      </div>
    );
  }

  // Main POS Interface - Check for restructured version preference (MANDATORY)
  const useRestructured = localStorage.getItem('useRestructuredPOS') === 'true' ||
                          window.location.search.includes('restructured=true');

  // Debug logging
  console.log('🔧 RESTRUCTURED CHECK:');
  console.log('localStorage.useRestructuredPOS:', localStorage.getItem('useRestructuredPOS'));
  console.log('window.location.search:', window.location.search);
  console.log('useRestructured:', useRestructured);
  console.log('state.isAuthenticated:', state.isAuthenticated);

  return (
    <div className={`transition-colors duration-300 ${
      isDarkMode ? 'dark' : ''
    }`}>
      {useRestructured ? (
        <>
          {console.log('🚀 RENDERING RESTRUCTURED INTERFACE')}
          <RestructuredIndustryPOS
            isDarkMode={isDarkMode}
            onThemeToggle={handleThemeToggle}
          />
        </>
      ) : (
        <>
          {console.log('📱 RENDERING STANDARD INTERFACE')}
          <IndustryStandardPOSLayout
            isDarkMode={isDarkMode}
            onThemeToggle={handleThemeToggle}
          />
        </>
      )}
    </div>
  );
};

// Main component with provider wrapper
const IndustryStandardPOSSystem: React.FC<IndustryStandardPOSSystemProps> = (props) => {
  return (
    <EnhancedAppProvider>
      <IndustryStandardPOSSystemContent {...props} />
    </EnhancedAppProvider>
  );
};

export default IndustryStandardPOSSystem;

// Export individual components for flexibility
export { IndustryStandardPOSLayout } from './components/IndustryStandardPOSLayout';
export { default as IndustryStandardPOS } from './components/IndustryStandardPOS';
export { default as ModernPaymentProcessor } from './components/ModernPaymentProcessor';

// Export types for external use
export type { IndustryStandardPOSSystemProps };

/**
 * Usage Examples:
 * 
 * Basic Usage:
 * ```tsx
 * import IndustryStandardPOSSystem from './IndustryStandardPOSSystem';
 * 
 * function App() {
 *   return <IndustryStandardPOSSystem />;
 * }
 * ```
 * 
 * With Custom Branding:
 * ```tsx
 * import IndustryStandardPOSSystem from './IndustryStandardPOSSystem';
 * 
 * function App() {
 *   return (
 *     <IndustryStandardPOSSystem
 *       initialTheme="dark"
 *       companyBranding={{
 *         companyName: "My Restaurant",
 *         primaryColor: "#3B82F6",
 *         secondaryColor: "#10B981",
 *         logo: "/path/to/logo.png"
 *       }}
 *     />
 *   );
 * }
 * ```
 * 
 * Integration with Existing Systems:
 * ```tsx
 * import { IndustryStandardPOSLayout } from './IndustryStandardPOSSystem';
 * 
 * function CustomPOSApp() {
 *   return (
 *     <MyAppProvider>
 *       <IndustryStandardPOSLayout isDarkMode={true} />
 *     </MyAppProvider>
 *   );
 * }
 * ```
 */
