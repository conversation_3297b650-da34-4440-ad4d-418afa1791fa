const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432
});

async function checkUsers() {
  try {
    console.log('Checking users with PIN 123456:');
    const result = await pool.query("SELECT id, name, pin, role, tenant_id FROM employees WHERE pin = '123456'");
    console.table(result.rows);
    
    console.log('\nChecking all employees:');
    const allUsers = await pool.query("SELECT id, name, pin, role, tenant_id FROM employees ORDER BY id");
    console.table(allUsers.rows);
    
    pool.end();
  } catch (error) {
    console.error('Error:', error);
    pool.end();
  }
}

checkUsers();
