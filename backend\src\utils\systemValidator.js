// System Validation Utility for RESTROFLOW
const fs = require('fs');
const path = require('path');
const { pool } = require('../database/config/connection');

class SystemValidator {
  constructor() {
    this.results = {
      database: { status: 'pending', tests: [] },
      backend: { status: 'pending', tests: [] },
      frontend: { status: 'pending', tests: [] },
      services: { status: 'pending', tests: [] },
      overall: { status: 'pending', score: 0 }
    };
  }

  async runAllValidations() {
    console.log('🔍 Starting comprehensive system validation...\n');
    
    try {
      await this.validateDatabase();
      await this.validateBackendStructure();
      await this.validateFrontendStructure();
      await this.validateServices();
      
      this.calculateOverallScore();
      this.generateReport();
      
    } catch (error) {
      console.error('💥 Validation failed:', error);
      this.results.overall.status = 'failed';
    }
  }

  async validateDatabase() {
    console.log('📊 Validating database structure...');
    
    try {
      // Test database connection
      const connectionTest = await this.testDatabaseConnection();
      this.results.database.tests.push(connectionTest);
      
      // Test essential tables
      const tablesTest = await this.testEssentialTables();
      this.results.database.tests.push(tablesTest);
      
      // Test migration system
      const migrationTest = await this.testMigrationSystem();
      this.results.database.tests.push(migrationTest);
      
      const passedTests = this.results.database.tests.filter(t => t.passed).length;
      this.results.database.status = passedTests === this.results.database.tests.length ? 'passed' : 'partial';
      
      console.log(`✅ Database validation: ${passedTests}/${this.results.database.tests.length} tests passed\n`);
      
    } catch (error) {
      console.error('💥 Database validation failed:', error);
      this.results.database.status = 'failed';
    }
  }

  async testDatabaseConnection() {
    try {
      const result = await pool.query('SELECT NOW()');
      return {
        name: 'Database Connection',
        passed: true,
        message: 'Successfully connected to PostgreSQL database'
      };
    } catch (error) {
      return {
        name: 'Database Connection',
        passed: false,
        message: `Connection failed: ${error.message}`
      };
    }
  }

  async testEssentialTables() {
    const essentialTables = [
      'tenants', 'users', 'employees', 'products', 
      'orders', 'order_items', 'payments', 'tables'
    ];
    
    try {
      const existingTables = await pool.query(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_schema = 'public'
      `);
      
      const tableNames = existingTables.rows.map(row => row.table_name);
      const missingTables = essentialTables.filter(table => !tableNames.includes(table));
      
      return {
        name: 'Essential Tables',
        passed: missingTables.length === 0,
        message: missingTables.length === 0 ? 
          'All essential tables exist' : 
          `Missing tables: ${missingTables.join(', ')}`
      };
    } catch (error) {
      return {
        name: 'Essential Tables',
        passed: false,
        message: `Table check failed: ${error.message}`
      };
    }
  }

  async testMigrationSystem() {
    try {
      const migrationFile = path.join(__dirname, '../database/migrations/migrate.js');
      const exists = fs.existsSync(migrationFile);
      
      return {
        name: 'Migration System',
        passed: exists,
        message: exists ? 'Migration system is properly organized' : 'Migration system not found'
      };
    } catch (error) {
      return {
        name: 'Migration System',
        passed: false,
        message: `Migration test failed: ${error.message}`
      };
    }
  }

  async validateBackendStructure() {
    console.log('🏗️ Validating backend structure...');
    
    const requiredPaths = [
      'src/server.js',
      'src/database/config/connection.js',
      'src/middleware/auth.js',
      'src/middleware/errorHandler.js',
      'src/routes/api/auth.js',
      'src/routes/api/pos.js',
      'src/routes/api/admin.js',
      'src/routes/api/tenant.js',
      'src/services/aiService.js',
      'src/services/globalService.js'
    ];
    
    const backendRoot = path.join(__dirname, '../../');
    
    for (const requiredPath of requiredPaths) {
      const fullPath = path.join(backendRoot, requiredPath);
      const exists = fs.existsSync(fullPath);
      
      this.results.backend.tests.push({
        name: `Backend File: ${requiredPath}`,
        passed: exists,
        message: exists ? 'File exists' : 'File missing'
      });
    }
    
    const passedTests = this.results.backend.tests.filter(t => t.passed).length;
    this.results.backend.status = passedTests === this.results.backend.tests.length ? 'passed' : 'partial';
    
    console.log(`✅ Backend validation: ${passedTests}/${this.results.backend.tests.length} tests passed\n`);
  }

  async validateFrontendStructure() {
    console.log('⚛️ Validating frontend structure...');
    
    const requiredPaths = [
      'src/components/pos/UnifiedPOSSystem.tsx',
      'src/components/admin/SuperAdminDashboard.tsx',
      'src/components/tenant/TenantAdminDashboard.tsx',
      'src/components/ui/button.tsx',
      'src/components/ui/card.tsx',
      'src/App.tsx'
    ];
    
    const frontendRoot = path.join(__dirname, '../../../frontend');
    
    for (const requiredPath of requiredPaths) {
      const fullPath = path.join(frontendRoot, requiredPath);
      const exists = fs.existsSync(fullPath);
      
      this.results.frontend.tests.push({
        name: `Frontend File: ${requiredPath}`,
        passed: exists,
        message: exists ? 'File exists' : 'File missing'
      });
    }
    
    const passedTests = this.results.frontend.tests.filter(t => t.passed).length;
    this.results.frontend.status = passedTests === this.results.frontend.tests.length ? 'passed' : 'partial';
    
    console.log(`✅ Frontend validation: ${passedTests}/${this.results.frontend.tests.length} tests passed\n`);
  }

  async validateServices() {
    console.log('🔧 Validating services...');
    
    try {
      // Test AI Service
      const aiService = require('../services/aiService');
      const aiHealthCheck = await aiService.healthCheck();
      
      this.results.services.tests.push({
        name: 'AI Service',
        passed: aiHealthCheck.status === 'healthy' || aiHealthCheck.status === 'initializing',
        message: `Status: ${aiHealthCheck.status}`
      });
      
      // Test Global Service
      const globalService = require('../services/globalService');
      const globalHealthCheck = await globalService.healthCheck();
      
      this.results.services.tests.push({
        name: 'Global Service',
        passed: globalHealthCheck.status === 'healthy' || globalHealthCheck.status === 'initializing',
        message: `Status: ${globalHealthCheck.status}`
      });
      
      const passedTests = this.results.services.tests.filter(t => t.passed).length;
      this.results.services.status = passedTests === this.results.services.tests.length ? 'passed' : 'partial';
      
      console.log(`✅ Services validation: ${passedTests}/${this.results.services.tests.length} tests passed\n`);
      
    } catch (error) {
      console.error('💥 Services validation failed:', error);
      this.results.services.status = 'failed';
    }
  }

  calculateOverallScore() {
    const categories = ['database', 'backend', 'frontend', 'services'];
    let totalScore = 0;
    let maxScore = 0;
    
    categories.forEach(category => {
      const categoryResults = this.results[category];
      const passed = categoryResults.tests.filter(t => t.passed).length;
      const total = categoryResults.tests.length;
      
      totalScore += passed;
      maxScore += total;
    });
    
    this.results.overall.score = Math.round((totalScore / maxScore) * 100);
    this.results.overall.status = this.results.overall.score >= 90 ? 'excellent' :
                                  this.results.overall.score >= 75 ? 'good' :
                                  this.results.overall.score >= 50 ? 'needs_improvement' : 'critical';
  }

  generateReport() {
    console.log('📋 SYSTEM VALIDATION REPORT');
    console.log('=' .repeat(50));
    console.log(`Overall Score: ${this.results.overall.score}% (${this.results.overall.status.toUpperCase()})`);
    console.log('=' .repeat(50));
    
    const categories = ['database', 'backend', 'frontend', 'services'];
    
    categories.forEach(category => {
      const categoryResults = this.results[category];
      const passed = categoryResults.tests.filter(t => t.passed).length;
      const total = categoryResults.tests.length;
      const percentage = Math.round((passed / total) * 100);
      
      console.log(`\n${category.toUpperCase()}: ${passed}/${total} (${percentage}%) - ${categoryResults.status.toUpperCase()}`);
      
      categoryResults.tests.forEach(test => {
        const icon = test.passed ? '✅' : '❌';
        console.log(`  ${icon} ${test.name}: ${test.message}`);
      });
    });
    
    console.log('\n' + '=' .repeat(50));
    console.log('🎉 System validation completed!');
    
    if (this.results.overall.score >= 90) {
      console.log('🌟 Excellent! Your system restructuring was successful.');
    } else if (this.results.overall.score >= 75) {
      console.log('👍 Good! Minor issues detected but system is functional.');
    } else {
      console.log('⚠️ Issues detected. Please review failed tests above.');
    }
  }
}

// Export for use in other modules
module.exports = SystemValidator;

// CLI interface
if (require.main === module) {
  const validator = new SystemValidator();
  validator.runAllValidations()
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Validation failed:', error);
      process.exit(1);
    });
}
