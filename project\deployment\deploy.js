#!/usr/bin/env node

/**
 * RestroFlow Production Deployment Script
 * =======================================
 * 
 * This script handles the complete deployment process for RestroFlow
 * including database setup, environment configuration, and service startup.
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');
const config = require('./production-config');

class RestroFlowDeployer {
  constructor() {
    this.startTime = Date.now();
    this.steps = [];
    this.errors = [];
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '📋',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      step: '🔄'
    }[type] || '📋';

    console.log(`${prefix} [${timestamp}] ${message}`);
    
    this.steps.push({
      timestamp,
      type,
      message
    });
  }

  async executeCommand(command, description) {
    this.log(`${description}...`, 'step');
    try {
      const output = execSync(command, { encoding: 'utf8', stdio: 'pipe' });
      this.log(`${description} completed successfully`, 'success');
      return output;
    } catch (error) {
      this.log(`${description} failed: ${error.message}`, 'error');
      this.errors.push({ command, description, error: error.message });
      throw error;
    }
  }

  async checkPrerequisites() {
    this.log('Checking deployment prerequisites...', 'step');

    // Check Node.js version
    const nodeVersion = process.version;
    this.log(`Node.js version: ${nodeVersion}`, 'info');
    
    if (parseInt(nodeVersion.slice(1)) < 16) {
      throw new Error('Node.js version 16 or higher is required');
    }

    // Check if PostgreSQL is available
    try {
      await this.executeCommand('psql --version', 'Checking PostgreSQL');
    } catch (error) {
      this.log('PostgreSQL not found. Please install PostgreSQL 12 or higher', 'warning');
    }

    // Check if required directories exist
    const requiredDirs = ['./logs', './uploads', './backups'];
    for (const dir of requiredDirs) {
      if (!fs.existsSync(dir)) {
        fs.mkdirSync(dir, { recursive: true });
        this.log(`Created directory: ${dir}`, 'success');
      }
    }

    this.log('Prerequisites check completed', 'success');
  }

  async setupEnvironment() {
    this.log('Setting up environment configuration...', 'step');

    const envTemplate = `# RestroFlow Production Environment Configuration
# Generated on ${new Date().toISOString()}

# Server Configuration
NODE_ENV=production
PORT=${config.server.port}
HOST=${config.server.host}

# Database Configuration
DB_HOST=${config.database.host}
DB_PORT=${config.database.port}
DB_NAME=${config.database.database}
DB_USER=${config.database.user}
DB_PASSWORD=${config.database.password}

# Authentication
JWT_SECRET=${config.auth.jwtSecret}
JWT_EXPIRATION=${config.auth.jwtExpiration}

# WebSocket Configuration
WS_PORT=${config.websocket.port}

# Redis Configuration (Optional)
REDIS_ENABLED=${config.redis.enabled}
REDIS_HOST=${config.redis.host}
REDIS_PORT=${config.redis.port}

# Email Configuration
EMAIL_SERVICE=${config.email.service}
EMAIL_HOST=${config.email.host}
EMAIL_PORT=${config.email.port}
EMAIL_USER=${config.email.auth.user}
EMAIL_PASS=${config.email.auth.pass}
EMAIL_FROM=${config.email.from}

# Payment Gateways
STRIPE_ENABLED=${config.payments.stripe.enabled}
STRIPE_PUBLISHABLE_KEY=${config.payments.stripe.publishableKey}
STRIPE_SECRET_KEY=${config.payments.stripe.secretKey}

MONERIS_ENABLED=${config.payments.moneris.enabled}
MONERIS_STORE_ID=${config.payments.moneris.storeId}
MONERIS_API_TOKEN=${config.payments.moneris.apiToken}

# Monitoring
LOG_LEVEL=${config.logging.level}

# CORS Configuration
CORS_ORIGIN=${Array.isArray(config.server.cors.origin) ? config.server.cors.origin.join(',') : config.server.cors.origin}
`;

    fs.writeFileSync('.env.production', envTemplate);
    this.log('Environment configuration created: .env.production', 'success');
  }

  async installDependencies() {
    this.log('Installing production dependencies...', 'step');
    
    // Install backend dependencies
    await this.executeCommand('npm ci --only=production', 'Installing backend dependencies');
    
    // Build frontend if needed
    if (fs.existsSync('package.json')) {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      if (packageJson.scripts && packageJson.scripts.build) {
        await this.executeCommand('npm run build', 'Building frontend assets');
      }
    }

    this.log('Dependencies installation completed', 'success');
  }

  async setupDatabase() {
    this.log('Setting up database...', 'step');

    const dbSetupSQL = `
-- RestroFlow Database Setup Script
-- Generated on ${new Date().toISOString()}

-- Create database if not exists
SELECT 'CREATE DATABASE ${config.database.database}'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '${config.database.database}');

-- Connect to the database
\\c ${config.database.database};

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create tables if they don't exist
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    logo_url VARCHAR(500),
    theme VARCHAR(20) DEFAULT 'light',
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS employees (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    password VARCHAR(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'staff',
    phone VARCHAR(50),
    is_active BOOLEAN DEFAULT true,
    last_login TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(tenant_id, email)
);

CREATE TABLE IF NOT EXISTS orders (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    order_number VARCHAR(50) NOT NULL,
    table_number VARCHAR(20),
    customer_name VARCHAR(255),
    items JSONB NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    payment_status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS products (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    category VARCHAR(100),
    image_url VARCHAR(500),
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenants_slug ON tenants(slug);
CREATE INDEX IF NOT EXISTS idx_employees_tenant_email ON employees(tenant_id, email);
CREATE INDEX IF NOT EXISTS idx_orders_tenant_status ON orders(tenant_id, status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_products_tenant_category ON products(tenant_id, category);

-- Insert sample data if tables are empty
INSERT INTO tenants (name, slug, email, phone, address) 
SELECT 'Demo Restaurant', 'demo-restaurant', '<EMAIL>', '******-0123', '123 Main St, City, State 12345'
WHERE NOT EXISTS (SELECT 1 FROM tenants WHERE slug = 'demo-restaurant');

INSERT INTO employees (tenant_id, name, email, password, role)
SELECT 1, 'Admin User', '<EMAIL>', 'admin123', 'admin'
WHERE NOT EXISTS (SELECT 1 FROM employees WHERE email = '<EMAIL>');

-- Update timestamps trigger
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply triggers to all tables
DROP TRIGGER IF EXISTS update_tenants_updated_at ON tenants;
CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_employees_updated_at ON employees;
CREATE TRIGGER update_employees_updated_at BEFORE UPDATE ON employees FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_orders_updated_at ON orders;
CREATE TRIGGER update_orders_updated_at BEFORE UPDATE ON orders FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_products_updated_at ON products;
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
`;

    // Write SQL setup script
    fs.writeFileSync('./deployment/setup-database.sql', dbSetupSQL);
    this.log('Database setup script created', 'success');

    // Try to run the setup script
    try {
      await this.executeCommand(
        `psql -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} -f ./deployment/setup-database.sql`,
        'Running database setup script'
      );
    } catch (error) {
      this.log('Database setup failed. Please run the setup script manually:', 'warning');
      this.log(`psql -h ${config.database.host} -p ${config.database.port} -U ${config.database.user} -f ./deployment/setup-database.sql`, 'info');
    }
  }

  async createSystemdService() {
    this.log('Creating systemd service...', 'step');

    const serviceFile = `[Unit]
Description=RestroFlow Restaurant Management System
After=network.target postgresql.service
Wants=postgresql.service

[Service]
Type=simple
User=restroflow
WorkingDirectory=${process.cwd()}
Environment=NODE_ENV=production
EnvironmentFile=${process.cwd()}/.env.production
ExecStart=${process.execPath} ${path.join(process.cwd(), 'backend/working-server.js')}
Restart=always
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=restroflow

# Security settings
NoNewPrivileges=yes
PrivateTmp=yes
ProtectSystem=strict
ProtectHome=yes
ReadWritePaths=${process.cwd()}

[Install]
WantedBy=multi-user.target
`;

    fs.writeFileSync('./deployment/restroflow.service', serviceFile);
    this.log('Systemd service file created: ./deployment/restroflow.service', 'success');
    this.log('To install the service, run as root:', 'info');
    this.log('sudo cp ./deployment/restroflow.service /etc/systemd/system/', 'info');
    this.log('sudo systemctl daemon-reload', 'info');
    this.log('sudo systemctl enable restroflow', 'info');
    this.log('sudo systemctl start restroflow', 'info');
  }

  async createNginxConfig() {
    this.log('Creating Nginx configuration...', 'step');

    const nginxConfig = `# RestroFlow Nginx Configuration
# Generated on ${new Date().toISOString()}

upstream restroflow_backend {
    server 127.0.0.1:${config.server.port};
    keepalive 32;
}

upstream restroflow_websocket {
    server 127.0.0.1:${config.websocket.port};
}

server {
    listen 80;
    server_name restroflow.com www.restroflow.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name restroflow.com www.restroflow.com;

    # SSL Configuration
    ssl_certificate /etc/ssl/certs/restroflow.crt;
    ssl_certificate_key /etc/ssl/private/restroflow.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # Static Files
    location /static/ {
        alias ${process.cwd()}/public/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # WebSocket
    location /ws/ {
        proxy_pass http://restroflow_websocket;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # API Routes
    location /api/ {
        proxy_pass http://restroflow_backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }

    # Frontend Application
    location / {
        try_files $uri $uri/ @backend;
    }

    location @backend {
        proxy_pass http://restroflow_backend;
        proxy_http_version 1.1;
        proxy_set_header Connection "";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Error Pages
    error_page 404 /404.html;
    error_page 500 502 503 504 /50x.html;
}
`;

    fs.writeFileSync('./deployment/nginx.conf', nginxConfig);
    this.log('Nginx configuration created: ./deployment/nginx.conf', 'success');
  }

  async generateDeploymentReport() {
    const endTime = Date.now();
    const duration = Math.round((endTime - this.startTime) / 1000);

    const report = {
      deployment: {
        timestamp: new Date().toISOString(),
        duration: `${duration} seconds`,
        version: config.version,
        environment: config.environment,
        status: this.errors.length === 0 ? 'SUCCESS' : 'PARTIAL_SUCCESS'
      },
      configuration: {
        server: {
          port: config.server.port,
          host: config.server.host
        },
        database: {
          host: config.database.host,
          port: config.database.port,
          database: config.database.database
        },
        features: config.features
      },
      steps: this.steps,
      errors: this.errors,
      nextSteps: [
        'Review the generated configuration files',
        'Set up SSL certificates for production',
        'Configure your domain DNS settings',
        'Set up monitoring and alerting',
        'Perform security audit',
        'Set up automated backups',
        'Configure log rotation'
      ]
    };

    fs.writeFileSync('./deployment/deployment-report.json', JSON.stringify(report, null, 2));
    this.log('Deployment report generated: ./deployment/deployment-report.json', 'success');

    return report;
  }

  async deploy() {
    try {
      this.log('🚀 Starting RestroFlow deployment process...', 'info');
      this.log(`Version: ${config.version}`, 'info');
      this.log(`Environment: ${config.environment}`, 'info');

      await this.checkPrerequisites();
      await this.setupEnvironment();
      await this.installDependencies();
      await this.setupDatabase();
      await this.createSystemdService();
      await this.createNginxConfig();

      const report = await this.generateDeploymentReport();

      this.log('🎉 RestroFlow deployment completed successfully!', 'success');
      this.log(`Total time: ${Math.round((Date.now() - this.startTime) / 1000)} seconds`, 'info');

      if (this.errors.length > 0) {
        this.log(`⚠️  ${this.errors.length} warnings/errors occurred during deployment`, 'warning');
        this.log('Please review the deployment report for details', 'info');
      }

      return report;

    } catch (error) {
      this.log(`💥 Deployment failed: ${error.message}`, 'error');
      this.errors.push({ step: 'deployment', error: error.message });
      
      const report = await this.generateDeploymentReport();
      throw error;
    }
  }
}

// Run deployment if called directly
if (require.main === module) {
  const deployer = new RestroFlowDeployer();
  deployer.deploy()
    .then(report => {
      console.log('\n📋 Deployment Summary:');
      console.log(`Status: ${report.deployment.status}`);
      console.log(`Duration: ${report.deployment.duration}`);
      console.log(`Steps completed: ${report.steps.length}`);
      console.log(`Errors: ${report.errors.length}`);
      
      if (report.errors.length === 0) {
        console.log('\n🎉 RestroFlow is ready for production!');
        console.log('\nNext steps:');
        report.nextSteps.forEach((step, index) => {
          console.log(`${index + 1}. ${step}`);
        });
      }
      
      process.exit(0);
    })
    .catch(error => {
      console.error('\n💥 Deployment failed:', error.message);
      process.exit(1);
    });
}

module.exports = RestroFlowDeployer;
