<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BARPOS - Super Admin Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/lucide@latest/dist/umd/lucide.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .metric-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        .success-card {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .warning-card {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .danger-card {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <!-- Header -->
    <header class="gradient-bg text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center py-6">
                <div class="flex items-center space-x-4">
                    <div class="bg-white/20 p-3 rounded-lg">
                        <i data-lucide="shield-check" class="w-8 h-8"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">BARPOS Super Admin</h1>
                        <p class="text-blue-100">Enterprise Management Dashboard</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button id="themeToggle" class="bg-white/20 hover:bg-white/30 p-2 rounded-lg transition-colors">
                        <i data-lucide="moon" class="w-5 h-5"></i>
                    </button>
                    <button id="refreshBtn" class="bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="refresh-cw" class="w-4 h-4 mr-2"></i>
                        Refresh
                    </button>
                    <button id="logoutBtn" class="bg-red-500 hover:bg-red-600 px-4 py-2 rounded-lg transition-colors">
                        <i data-lucide="log-out" class="w-4 h-4 mr-2"></i>
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="metric-card text-white p-6 rounded-xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm">Total Tenants</p>
                        <p id="totalTenants" class="text-3xl font-bold">-</p>
                    </div>
                    <i data-lucide="building" class="w-12 h-12 text-white/60"></i>
                </div>
            </div>
            <div class="success-card text-white p-6 rounded-xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm">Active Users</p>
                        <p id="activeUsers" class="text-3xl font-bold">-</p>
                    </div>
                    <i data-lucide="users" class="w-12 h-12 text-white/60"></i>
                </div>
            </div>
            <div class="warning-card text-white p-6 rounded-xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm">Monthly Revenue</p>
                        <p id="monthlyRevenue" class="text-3xl font-bold">-</p>
                    </div>
                    <i data-lucide="dollar-sign" class="w-12 h-12 text-white/60"></i>
                </div>
            </div>
            <div class="danger-card text-white p-6 rounded-xl card-hover">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-white/80 text-sm">System Health</p>
                        <p id="systemHealth" class="text-3xl font-bold">-</p>
                    </div>
                    <i data-lucide="activity" class="w-12 h-12 text-white/60"></i>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="bg-white rounded-xl shadow-sm mb-8">
            <div class="border-b border-gray-200">
                <nav class="flex space-x-8 px-6">
                    <button class="tab-btn active py-4 px-2 border-b-2 border-blue-500 text-blue-600 font-medium" data-tab="overview">
                        <i data-lucide="home" class="w-4 h-4 mr-2"></i>
                        Overview
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="tenants">
                        <i data-lucide="building-2" class="w-4 h-4 mr-2"></i>
                        Tenant Management
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="analytics">
                        <i data-lucide="bar-chart-3" class="w-4 h-4 mr-2"></i>
                        Advanced Analytics
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="ai">
                        <i data-lucide="brain" class="w-4 h-4 mr-2"></i>
                        AI Insights
                    </button>
                    <button class="tab-btn py-4 px-2 border-b-2 border-transparent text-gray-500 hover:text-gray-700" data-tab="system">
                        <i data-lucide="settings" class="w-4 h-4 mr-2"></i>
                        System Health
                    </button>
                </nav>
            </div>
        </div>

        <!-- Tab Content -->
        <div id="tabContent">
            <!-- Overview Tab -->
            <div id="overview" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Recent Activity -->
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Recent Activity</h3>
                        <div id="recentActivity" class="space-y-4">
                            <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                                <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                <span class="text-sm text-gray-600">Loading recent activity...</span>
                            </div>
                        </div>
                    </div>

                    <!-- Performance Chart -->
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Performance Trends</h3>
                        <canvas id="performanceChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- Tenant Management Tab -->
            <div id="tenants" class="tab-content hidden">
                <div class="bg-white rounded-xl shadow-sm p-6">
                    <div class="flex justify-between items-center mb-6">
                        <h3 class="text-lg font-semibold">Enhanced Tenant Management</h3>
                        <button id="openEnhancedTenants" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                            <i data-lucide="external-link" class="w-4 h-4 mr-2"></i>
                            Open Enhanced Interface
                        </button>
                    </div>
                    <div id="tenantsList" class="space-y-4">
                        <div class="text-center py-8 text-gray-500">
                            Loading tenant data...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Advanced Analytics Tab -->
            <div id="analytics" class="tab-content hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Performance Metrics</h3>
                        <div id="performanceMetrics" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                Loading performance metrics...
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Growth Trends</h3>
                        <canvas id="growthChart" width="400" height="200"></canvas>
                    </div>
                </div>
            </div>

            <!-- AI Insights Tab -->
            <div id="ai" class="tab-content hidden">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">High Performers</h3>
                        <div id="highPerformers" class="space-y-3">
                            <div class="text-center py-8 text-gray-500">
                                Loading AI insights...
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Upgrade Candidates</h3>
                        <div id="upgradeCandidates" class="space-y-3">
                            <div class="text-center py-8 text-gray-500">
                                Loading upgrade recommendations...
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Attention Needed</h3>
                        <div id="attentionNeeded" class="space-y-3">
                            <div class="text-center py-8 text-gray-500">
                                Loading attention alerts...
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- System Health Tab -->
            <div id="system" class="tab-content hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">Database Performance</h3>
                        <div id="databaseStats" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                Loading database statistics...
                            </div>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl shadow-sm p-6">
                        <h3 class="text-lg font-semibold mb-4">System Metrics</h3>
                        <div id="systemMetrics" class="space-y-4">
                            <div class="text-center py-8 text-gray-500">
                                Loading system metrics...
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <script>
        // Initialize Lucide icons
        lucide.createIcons();

        // Global variables
        let authToken = localStorage.getItem('authToken');
        let currentTheme = localStorage.getItem('theme') || 'light';

        // Apply theme
        function applyTheme(theme) {
            if (theme === 'dark') {
                document.body.classList.add('dark');
                document.querySelector('#themeToggle i').setAttribute('data-lucide', 'sun');
            } else {
                document.body.classList.remove('dark');
                document.querySelector('#themeToggle i').setAttribute('data-lucide', 'moon');
            }
            lucide.createIcons();
        }

        // Initialize theme
        applyTheme(currentTheme);

        // Theme toggle
        document.getElementById('themeToggle').addEventListener('click', () => {
            currentTheme = currentTheme === 'light' ? 'dark' : 'light';
            localStorage.setItem('theme', currentTheme);
            applyTheme(currentTheme);
        });

        // Tab switching
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const tabName = btn.dataset.tab;

                // Update active tab
                document.querySelectorAll('.tab-btn').forEach(b => {
                    b.classList.remove('active', 'border-blue-500', 'text-blue-600');
                    b.classList.add('border-transparent', 'text-gray-500');
                });
                btn.classList.add('active', 'border-blue-500', 'text-blue-600');
                btn.classList.remove('border-transparent', 'text-gray-500');

                // Show/hide content
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.add('hidden');
                });
                document.getElementById(tabName).classList.remove('hidden');

                // Load tab-specific data
                loadTabData(tabName);

            });
        });

        // API helper function
        async function apiCall(endpoint, options = {}) {
            try {
                const response = await fetch(`http://localhost:4000${endpoint}`, {
                    headers: {
                        'Authorization': `Bearer ${authToken}`,
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}`);
                }

                return await response.json();
            } catch (error) {
                console.error('API call failed:', error);
                throw error;
            }
        }

        // Load dashboard data
        async function loadDashboardData() {
            try {
                // Load basic statistics
                const stats = await apiCall('/api/admin/statistics');

                document.getElementById('totalTenants').textContent = stats.totalTenants || '0';
                document.getElementById('activeUsers').textContent = stats.activeUsers || '0';
                document.getElementById('monthlyRevenue').textContent = `$${(stats.monthlyRevenue || 0).toFixed(2)}`;
                document.getElementById('systemHealth').textContent = '99.9%';

            } catch (error) {
                console.error('Failed to load dashboard data:', error);
            }
        }

        // Load tab-specific data
        async function loadTabData(tabName) {
            switch (tabName) {
                case 'overview':
                    await loadOverviewData();
                    break;
                case 'tenants':
                    await loadTenantsData();
                    break;
                case 'analytics':
                    await loadAnalyticsData();
                    break;
                case 'ai':
                    await loadAIData();
                    break;
                case 'system':
                    await loadSystemData();
                    break;
            }
        }

        // Load overview data
        async function loadOverviewData() {
            try {
                const tenants = await apiCall('/api/admin/tenants');

                const recentActivity = document.getElementById('recentActivity');
                recentActivity.innerHTML = tenants.slice(0, 5).map(tenant => `
                    <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                        <span class="text-sm text-gray-600">${tenant.name} - ${tenant.status}</span>
                        <span class="text-xs text-gray-400 ml-auto">${new Date(tenant.created_at).toLocaleDateString()}</span>
                    </div>
                `).join('');

            } catch (error) {
                console.error('Failed to load overview data:', error);
            }
        }

        // Load tenants data
        async function loadTenantsData() {
            try {
                const tenants = await apiCall('/api/admin/tenants');

                const tenantsList = document.getElementById('tenantsList');
                tenantsList.innerHTML = tenants.map(tenant => `
                    <div class="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                        <div>
                            <h4 class="font-medium">${tenant.name}</h4>
                            <p class="text-sm text-gray-500">${tenant.email}</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <span class="px-2 py-1 text-xs rounded-full ${tenant.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}">
                                ${tenant.status}
                            </span>
                        </div>
                    </div>
        opacity: 0.7;
        margin-top: 16px;
        font-style: italic;
      }

      @keyframes featureSlide {
        to {
          transform: translateX(0);
          opacity: 1;
        }
      }

      .loading-progress {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 2px;
        overflow: hidden;
        margin: 0 auto;
      }

      .loading-progress-bar {
        height: 100%;
        background: linear-gradient(90deg, #ffffff, #fecaca);
        border-radius: 2px;
        animation: progress 2s ease-in-out infinite;
      }

      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }

      @keyframes progress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
      }



      /* Hide loading when app loads */
      .app-loaded .loading-container {
        display: none;
      }

      /* Responsive design */
      @media (max-width: 768px) {
        .loading-title {
          font-size: 20px;
        }

        .loading-subtitle {
          font-size: 14px;
        }


      }
    </style>
  </head>
  <body>
    <!-- Enterprise Security Indicator -->
    <div class="security-indicator low">Security: SECURE</div>

    <!-- Enhanced Security Loading Screen -->
    <div class="loading-container" id="securityLoadingScreen">
      <div class="loading-content">
        <div class="loading-spinner"></div>
        <div class="loading-title">� RestroFlow Security Center</div>
        <div class="loading-subtitle">Performing Security Verification...</div>
        <div class="loading-features">
          <div class="loading-feature">🔐 Verifying Access Credentials</div>
          <div class="loading-feature">🛡️ Checking Security Protocols</div>
          <div class="loading-feature">🔍 Scanning for Threats</div>
          <div class="loading-feature">✅ Establishing Secure Connection</div>
        </div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
        <div class="loading-status" id="securityStatus">Initializing security systems...</div>
      </div>
    </div>

    <!-- Security Warning Screen (Hidden by default) -->
    <div id="securityWarning" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(135deg, #dc2626, #991b1b); z-index: 10000; color: white; font-family: 'Inter', sans-serif;">
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; padding: 20px;">
        <div style="text-align: center; max-width: 600px;">
          <div style="font-size: 4rem; margin-bottom: 20px;">🚨</div>
          <h1 style="font-size: 2.5rem; font-weight: bold; margin-bottom: 20px;">SECURITY ALERT</h1>
          <p style="font-size: 1.2rem; margin-bottom: 30px; opacity: 0.9;">
            Unauthorized access attempt detected. This system is restricted to authorized Super Administrators only.
          </p>
          <div style="background: rgba(0,0,0,0.3); padding: 20px; border-radius: 10px; margin-bottom: 30px;">
            <p style="font-size: 0.9rem; margin-bottom: 10px;">🔒 Access Requirements:</p>
            <ul style="text-align: left; font-size: 0.9rem; opacity: 0.8;">
              <li>• Valid Super Administrator credentials</li>
              <li>• Authorized network access</li>
              <li>• Security clearance verification</li>
            </ul>
          </div>
          <button onclick="window.location.reload()" style="background: #ffffff; color: #dc2626; padding: 12px 30px; border: none; border-radius: 8px; font-weight: bold; cursor: pointer; font-size: 1rem;">
            🔄 Retry Access
          </button>
        </div>
      </div>
    </div>

    <!-- Main app container -->
    <div id="root"></div>

    <!-- Main application script -->
    <script type="module" src="/src/main-super-admin.tsx"></script>

    <!-- Enhanced Security Center Integration -->
    <script>
      // Global security monitoring
      window.securityCenter = {
        threatLevel: 'high',
        activeThreats: 0,
        blockedAttempts: 0,
        complianceScore: 98.5,
        lastScan: new Date().toISOString(),
        accessAttempts: 0,
        maxAccessAttempts: 3
      };

      // Enhanced Security Check - Immediate Protection
      function performSecurityCheck() {
        console.log('🔒 Performing Super Admin Security Check...');

        // Check for suspicious access patterns
        const accessCount = parseInt(sessionStorage.getItem('adminAccessAttempts') || '0');
        sessionStorage.setItem('adminAccessAttempts', (accessCount + 1).toString());

        if (accessCount > 5) {
          console.warn('⚠️ Multiple access attempts detected');
          window.securityCenter.threatLevel = 'critical';
          updateSecurityIndicators();
        }

        // Log access attempt
        const accessLog = {
          timestamp: new Date().toISOString(),
          userAgent: navigator.userAgent,
          ip: 'client-side', // Would be filled by backend
          referrer: document.referrer || 'direct'
        };

        console.log('📊 Access Log:', accessLog);

        // Store access attempt for audit
        const attempts = JSON.parse(localStorage.getItem('adminAccessLog') || '[]');
        attempts.push(accessLog);
        // Keep only last 10 attempts
        if (attempts.length > 10) attempts.shift();
        localStorage.setItem('adminAccessLog', JSON.stringify(attempts));
      }

      // Real-time security updates
      function updateSecurityStatus() {
        const token = localStorage.getItem('authToken');
        if (!token) {
          console.log('🔒 No auth token found - maintaining high security level');
          window.securityCenter.threatLevel = 'high';
          updateSecurityIndicators();
          return;
        }

        fetch('/api/admin/security/status', {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
        .then(response => response.json())
        .then(data => {
          window.securityCenter = { ...window.securityCenter, ...data };
          updateSecurityIndicators();
        })
        .catch(error => {
          console.error('Security status update failed:', error);
          window.securityCenter.threatLevel = 'high';
          updateSecurityIndicators();
        });
      }

      function updateSecurityIndicators() {
        const indicator = document.querySelector('.security-indicator');
        if (indicator) {
          const level = window.securityCenter.threatLevel;
          indicator.className = `security-indicator ${level}`;

          const statusText = {
            'low': 'SECURE',
            'medium': 'ELEVATED',
            'high': 'HIGH ALERT',
            'critical': 'CRITICAL'
          };

          indicator.textContent = `Security: ${statusText[level] || 'UNKNOWN'}`;
        }
      }

      // Enhanced access control
      function checkAuthorizedAccess() {
        // Check if accessing from authorized domain/port
        const allowedHosts = ['localhost:5173', 'localhost:5174', '127.0.0.1:5173', '127.0.0.1:5174'];
        const currentHost = window.location.host;

        if (!allowedHosts.includes(currentHost)) {
          console.error('🚨 Unauthorized host access detected:', currentHost);
          document.body.innerHTML = `
            <div style="background: #dc2626; color: white; padding: 20px; text-align: center; font-family: Arial;">
              <h1>🚨 UNAUTHORIZED ACCESS DETECTED</h1>
              <p>This system is restricted to authorized personnel only.</p>
              <p>Access attempt has been logged.</p>
            </div>
          `;
          return false;
        }

        return true;
      }

      // Initialize security on page load
      document.addEventListener('DOMContentLoaded', function() {
        if (!checkAuthorizedAccess()) return;

        performSecurityCheck();
        updateSecurityStatus();

        // Start security monitoring
        setInterval(updateSecurityStatus, 30000); // Update every 30 seconds
      });

      // Perform immediate security check
      if (!checkAuthorizedAccess()) {
        // Stop execution if unauthorized
      } else {
        performSecurityCheck();
      }
    </script>

    <!-- Remove loading screen when app loads -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          document.body.classList.add('app-loaded');
        }, 1000);
      });
    </script>
  </body>
</html>
