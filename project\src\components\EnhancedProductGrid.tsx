import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Coffee, Utensils, Wine, GlassWater } from 'lucide-react';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
}

const EnhancedProductGrid: React.FC = () => {
  const { state, dispatch, apiCall } = useEnhancedAppContext();
  const [selectedCategory, setSelectedCategory] = useState<string>('Beverages');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<string[]>([]);

  useEffect(() => {
    const loadData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🔄 Loading products and categories...');
        
        // Load products
        const productsResponse = await apiCall('/api/products');
        if (productsResponse.ok) {
          const productsData = await productsResponse.json();
          setProducts(productsData);
          console.log('✅ Products loaded:', productsData.length);
        }
        
        // Load categories
        const categoriesResponse = await apiCall('/api/categories');
        if (categoriesResponse.ok) {
          const categoriesData = await categoriesResponse.json();
          const categoryNames = categoriesData.map((cat: any) => cat.name);
          setCategories(categoryNames);
          console.log('✅ Categories loaded:', categoryNames);
          
          // Set first category as default
          if (categoryNames.length > 0) {
            setSelectedCategory(categoryNames[0]);
          }
        }
        
      } catch (error) {
        console.error('❌ Error loading data:', error);
        setError('Failed to load products. Using mock data.');
        
        // Fallback to mock data
        const mockProducts = [
          { id: '1', name: 'Coffee', price: 3.50, category: 'Beverages' },
          { id: '2', name: 'Espresso', price: 2.75, category: 'Beverages' },
          { id: '3', name: 'Sandwich', price: 8.99, category: 'Food' },
          { id: '4', name: 'Salad', price: 7.50, category: 'Food' },
          { id: '5', name: 'Burger', price: 12.99, category: 'Food' },
          { id: '6', name: 'Tea', price: 2.50, category: 'Beverages' }
        ];
        setProducts(mockProducts);
        setCategories(['Beverages', 'Food']);
        setSelectedCategory('Beverages');
      } finally {
        setIsLoading(false);
      }
    };
    
    loadData();
  }, [apiCall]);

  const getCategoryIcon = (category: string) => {
    switch(category.toLowerCase()) {
      case 'beverages':
        return <Coffee className="h-5 w-5" />;
      case 'food':
        return <Utensils className="h-5 w-5" />;
      case 'wine':
        return <Wine className="h-5 w-5" />;
      case 'drinks':
        return <GlassWater className="h-5 w-5" />;
      default:
        return <Coffee className="h-5 w-5" />;
    }
  };

  const handleAddToOrder = (product: Product) => {
    console.log('➕ Adding product to order:', product.name);

    // Add to current order using the correct action type
    dispatch({
      type: 'ADD_PRODUCT_TO_ORDER',
      payload: { ...product, inStock: true }
    });
  };

  const filteredProducts = products.filter(
    product => product.category === selectedCategory
  );

  const getCategoryCount = (category: string) => {
    return products.filter(p => p.category === category).length;
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center space-y-4">
          <svg className="animate-spin h-8 w-8 text-blue-500" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p className="text-gray-600">Loading products...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <h2 className="text-xl font-semibold text-gray-900">Products</h2>
        <p className="text-sm text-gray-500">Select items to add to your order</p>
      </div>

      {/* Category tabs */}
      <div className="flex overflow-x-auto bg-gray-50 p-3 space-x-2 border-b border-gray-200">
        {categories.map(category => (
          <button
            key={category}
            className={`flex items-center space-x-2 py-2 px-4 rounded-lg transition-colors whitespace-nowrap ${
              selectedCategory === category
              ? 'bg-blue-600 text-white shadow-md'
              : 'bg-white text-gray-700 hover:bg-gray-100 border border-gray-200'
            }`}
            onClick={() => setSelectedCategory(category)}
          >
            {getCategoryIcon(category)}
            <span className="font-medium">{category}</span>
            <span className={`text-xs px-2 py-1 rounded-full ${
              selectedCategory === category 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-600'
            }`}>
              {getCategoryCount(category)}
            </span>
          </button>
        ))}
      </div>

      {/* Products grid */}
      <div className="flex-1 overflow-y-auto p-4">
        {error && (
          <div className="mb-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
            <p className="text-yellow-800 text-sm">⚠️ {error}</p>
          </div>
        )}
        
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4">
          {filteredProducts.length === 0 ? (
            <div className="col-span-full flex items-center justify-center py-12 text-gray-500">
              <div className="text-center">
                <div className="text-4xl mb-2">📦</div>
                <p className="text-lg font-medium mb-1">No products available</p>
                <p className="text-sm">in {selectedCategory} category</p>
              </div>
            </div>
          ) : (
            filteredProducts.map(product => (
              <button
                key={product.id}
                className="bg-white hover:bg-gray-50 rounded-lg p-4 text-left transition-all duration-200 active:scale-95 hover:shadow-lg border border-gray-200 hover:border-blue-300 min-h-[140px] flex flex-col"
                onClick={() => handleAddToOrder(product)}
              >
                <div className="flex flex-col h-full">
                  {/* Product image placeholder */}
                  <div className="w-full h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-md mb-3 flex items-center justify-center">
                    {getCategoryIcon(product.category)}
                  </div>
                  
                  <h3 className="font-semibold text-gray-900 mb-2 text-sm line-clamp-2">
                    {product.name}
                  </h3>
                  
                  {product.description && (
                    <p className="text-gray-600 text-xs mb-2 flex-grow line-clamp-2">
                      {product.description}
                    </p>
                  )}
                  
                  <div className="mt-auto">
                    <p className="text-blue-600 font-bold text-lg">
                      ${product.price.toFixed(2)}
                    </p>
                  </div>
                </div>
              </button>
            ))
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="bg-gray-50 border-t border-gray-200 p-3">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <span>{filteredProducts.length} products in {selectedCategory}</span>
          <span>Tap any item to add to order</span>
        </div>
      </div>
    </div>
  );
};

export default EnhancedProductGrid;
