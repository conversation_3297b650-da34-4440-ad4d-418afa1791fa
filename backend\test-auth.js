const fetch = require('node-fetch');

async function testAuthentication() {
    const baseUrl = 'http://localhost:4000';
    
    console.log('🔐 Testing JWT Authentication System');
    console.log('=====================================\n');
    
    // Test cases
    const testCases = [
        {
            name: 'Super Admin Login',
            data: { pin: '123456', tenant_slug: 'barpos-system' },
            expectedRole: 'super_admin'
        },
        {
            name: 'Manager Login',
            data: { pin: '567890', tenant_slug: 'barpos-system' },
            expectedRole: 'manager'
        },
        {
            name: '<PERSON><PERSON><PERSON><PERSON> (Head Cashier)',
            data: { pin: '111111', tenant_slug: 'barpos-system' },
            expectedRole: 'employee'
        },
        {
            name: 'Kitchen Manager Login',
            data: { pin: '222222', tenant_slug: 'barpos-system' },
            expectedRole: 'kitchen'
        }
    ];
    
    let passedTests = 0;
    let totalTests = testCases.length;
    
    for (const testCase of testCases) {
        try {
            console.log(`Testing: ${testCase.name}`);
            console.log(`PIN: ${testCase.data.pin}, Tenant: ${testCase.data.tenant_slug}`);

            const response = await fetch(`${baseUrl}/api/auth/login`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(testCase.data)
            });

            const result = await response.json();

            if (response.ok && result.token && result.employee) {
                console.log(`✅ PASS: ${testCase.name}`);
                console.log(`   Token: ${result.token.substring(0, 20)}...`);
                console.log(`   User: ${result.employee.name} (${result.employee.role})`);
                console.log(`   Tenant: ${result.tenant.name} (${result.tenant.slug})`);

                if (result.employee.role === testCase.expectedRole) {
                    console.log(`   ✅ Role matches expected: ${testCase.expectedRole}`);
                } else {
                    console.log(`   ⚠️ Role mismatch: expected ${testCase.expectedRole}, got ${result.employee.role}`);
                }
                passedTests++;
            } else {
                console.log(`❌ FAIL: ${testCase.name}`);
                console.log(`   Status: ${response.status}`);
                console.log(`   Error: ${result.error || result.message || 'Unknown error'}`);
            }
            
        } catch (error) {
            console.log(`❌ FAIL: ${testCase.name}`);
            console.log(`   Error: ${error.message}`);
        }
        
        console.log(''); // Empty line for readability
    }
    
    // Summary
    console.log('=====================================');
    console.log(`📊 Test Results: ${passedTests}/${totalTests} passed`);
    console.log(`📈 Success Rate: ${((passedTests/totalTests) * 100).toFixed(1)}%`);
    
    if (passedTests === totalTests) {
        console.log('🎉 All authentication tests passed!');
    } else {
        console.log('⚠️ Some authentication tests failed. Check the logs above.');
    }
}

// Run the tests
testAuthentication().catch(console.error);
