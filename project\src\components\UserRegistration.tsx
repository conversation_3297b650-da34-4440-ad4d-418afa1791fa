import React, { useState } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { User, Mail, Phone, Building, MapPin, CreditCard, Eye, EyeOff, ArrowLeft } from 'lucide-react';

interface RegistrationData {
  // Business Information
  business_name: string;
  business_type: string;
  address: string;
  city: string;
  state: string;
  zip_code: string;
  phone: string;
  email: string;
  
  // Admin User
  admin_name: string;
  admin_email: string;
  admin_phone: string;
  admin_pin: string;
  confirm_pin: string;
  
  // Subscription
  plan_type: 'basic' | 'pro' | 'enterprise';
  payment_method: 'stripe' | 'moneris';
}

interface UserRegistrationProps {
  onBack: () => void;
  onSuccess: (data: any) => void;
}

const UserRegistration: React.FC<UserRegistrationProps> = ({ onBack, onSuccess }) => {
  const { apiCall } = useEnhancedAppContext();
  const [step, setStep] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showPin, setShowPin] = useState(false);
  const [formData, setFormData] = useState<RegistrationData>({
    business_name: '',
    business_type: 'restaurant',
    address: '',
    city: '',
    state: '',
    zip_code: '',
    phone: '',
    email: '',
    admin_name: '',
    admin_email: '',
    admin_phone: '',
    admin_pin: '',
    confirm_pin: '',
    plan_type: 'basic',
    payment_method: 'stripe'
  });

  const handleInputChange = (field: keyof RegistrationData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    setError(null);
  };

  const validateStep = (stepNumber: number): boolean => {
    switch (stepNumber) {
      case 1:
        return !!(formData.business_name && formData.business_type && formData.email && formData.phone);
      case 2:
        return !!(formData.address && formData.city && formData.state && formData.zip_code);
      case 3:
        return !!(formData.admin_name && formData.admin_email && formData.admin_pin && 
                 formData.admin_pin === formData.confirm_pin && formData.admin_pin.length >= 4);
      case 4:
        return !!(formData.plan_type && formData.payment_method);
      default:
        return false;
    }
  };

  const handleNext = () => {
    if (validateStep(step)) {
      setStep(step + 1);
    } else {
      setError('Please fill in all required fields correctly.');
    }
  };

  const handleSubmit = async () => {
    if (!validateStep(4)) {
      setError('Please complete all required fields.');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('🚀 Registering new business:', formData.business_name);
      
      const response = await apiCall('/api/auth/register', {
        method: 'POST',
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        const result = await response.json();
        console.log('✅ Registration successful:', result);
        onSuccess(result);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Registration failed. Please try again.');
      }
    } catch (error) {
      console.error('❌ Registration error:', error);
      setError('Registration failed. Please check your connection and try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const renderStep1 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Information</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Business Name *</label>
        <div className="relative">
          <Building className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            value={formData.business_name}
            onChange={(e) => handleInputChange('business_name', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter your business name"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Business Type *</label>
        <select
          value={formData.business_type}
          onChange={(e) => handleInputChange('business_type', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="restaurant">Restaurant</option>
          <option value="cafe">Cafe</option>
          <option value="bar">Bar</option>
          <option value="food_truck">Food Truck</option>
          <option value="bakery">Bakery</option>
          <option value="fast_food">Fast Food</option>
          <option value="other">Other</option>
        </select>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Business Email *</label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Business Phone *</label>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="(*************"
          />
        </div>
      </div>
    </div>
  );

  const renderStep2 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Business Address</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Street Address *</label>
        <div className="relative">
          <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="123 Main Street"
          />
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">City *</label>
          <input
            type="text"
            value={formData.city}
            onChange={(e) => handleInputChange('city', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="City"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">State/Province *</label>
          <input
            type="text"
            value={formData.state}
            onChange={(e) => handleInputChange('state', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="State"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">ZIP/Postal Code *</label>
        <input
          type="text"
          value={formData.zip_code}
          onChange={(e) => handleInputChange('zip_code', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="12345"
        />
      </div>
    </div>
  );

  const renderStep3 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Admin User Setup</h3>
      
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Admin Name *</label>
        <div className="relative">
          <User className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            value={formData.admin_name}
            onChange={(e) => handleInputChange('admin_name', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Admin full name"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Admin Email *</label>
        <div className="relative">
          <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="email"
            value={formData.admin_email}
            onChange={(e) => handleInputChange('admin_email', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="<EMAIL>"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Admin Phone</label>
        <div className="relative">
          <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="tel"
            value={formData.admin_phone}
            onChange={(e) => handleInputChange('admin_phone', e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="(*************"
          />
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Admin PIN (4+ digits) *</label>
        <div className="relative">
          <input
            type={showPin ? 'text' : 'password'}
            value={formData.admin_pin}
            onChange={(e) => handleInputChange('admin_pin', e.target.value)}
            className="w-full pr-10 pl-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Enter 4+ digit PIN"
          />
          <button
            type="button"
            onClick={() => setShowPin(!showPin)}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600"
          >
            {showPin ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">Confirm PIN *</label>
        <input
          type={showPin ? 'text' : 'password'}
          value={formData.confirm_pin}
          onChange={(e) => handleInputChange('confirm_pin', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Confirm PIN"
        />
      </div>

      {formData.admin_pin && formData.confirm_pin && formData.admin_pin !== formData.confirm_pin && (
        <p className="text-red-500 text-sm">PINs do not match</p>
      )}
    </div>
  );

  const renderStep4 = () => (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold text-gray-900 mb-4">Choose Your Plan</h3>
      
      <div className="space-y-3">
        {[
          { id: 'basic', name: 'Basic Plan', price: '$29/month', features: ['Core POS', 'Basic Inventory', 'Up to 2 users'] },
          { id: 'pro', name: 'Pro Plan', price: '$79/month', features: ['All Basic features', 'Advanced Analytics', 'Up to 10 users', 'Kitchen Display'] },
          { id: 'enterprise', name: 'Enterprise', price: '$199/month', features: ['All Pro features', 'Multi-location', 'Unlimited users', 'Priority support'] }
        ].map((plan) => (
          <div
            key={plan.id}
            className={`border rounded-lg p-4 cursor-pointer transition-colors ${
              formData.plan_type === plan.id
                ? 'border-blue-500 bg-blue-50'
                : 'border-gray-200 hover:border-gray-300'
            }`}
            onClick={() => handleInputChange('plan_type', plan.id as any)}
          >
            <div className="flex justify-between items-start">
              <div>
                <h4 className="font-semibold text-gray-900">{plan.name}</h4>
                <p className="text-2xl font-bold text-blue-600">{plan.price}</p>
                <ul className="text-sm text-gray-600 mt-2">
                  {plan.features.map((feature, index) => (
                    <li key={index}>• {feature}</li>
                  ))}
                </ul>
              </div>
              <div className={`w-4 h-4 rounded-full border-2 ${
                formData.plan_type === plan.id
                  ? 'border-blue-500 bg-blue-500'
                  : 'border-gray-300'
              }`} />
            </div>
          </div>
        ))}
      </div>

      <div className="mt-6">
        <label className="block text-sm font-medium text-gray-700 mb-3">Payment Method</label>
        <div className="space-y-2">
          {[
            { id: 'stripe', name: 'Stripe', description: 'Credit/Debit Cards, Digital Wallets' },
            { id: 'moneris', name: 'Moneris', description: 'Canadian Payment Processing' }
          ].map((method) => (
            <div
              key={method.id}
              className={`border rounded-lg p-3 cursor-pointer transition-colors ${
                formData.payment_method === method.id
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-200 hover:border-gray-300'
              }`}
              onClick={() => handleInputChange('payment_method', method.id as any)}
            >
              <div className="flex justify-between items-center">
                <div>
                  <h5 className="font-medium text-gray-900">{method.name}</h5>
                  <p className="text-sm text-gray-600">{method.description}</p>
                </div>
                <div className={`w-4 h-4 rounded-full border-2 ${
                  formData.payment_method === method.id
                    ? 'border-blue-500 bg-blue-500'
                    : 'border-gray-300'
                }`} />
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-indigo-900 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-xl p-8 w-full max-w-md">
        {/* Header */}
        <div className="flex items-center mb-6">
          <button
            onClick={step === 1 ? onBack : () => setStep(step - 1)}
            className="mr-3 p-1 text-gray-400 hover:text-gray-600 transition-colors"
          >
            <ArrowLeft className="h-5 w-5" />
          </button>
          <div className="flex-1">
            <h2 className="text-2xl font-bold text-gray-900">Create Account</h2>
            <p className="text-sm text-gray-500">Step {step} of 4</p>
          </div>
        </div>

        {/* Progress Bar */}
        <div className="mb-6">
          <div className="flex space-x-2">
            {[1, 2, 3, 4].map((stepNumber) => (
              <div
                key={stepNumber}
                className={`flex-1 h-2 rounded-full ${
                  stepNumber <= step ? 'bg-blue-500' : 'bg-gray-200'
                }`}
              />
            ))}
          </div>
        </div>

        {/* Error Message */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-red-800 text-sm">{error}</p>
          </div>
        )}

        {/* Step Content */}
        <div className="mb-6">
          {step === 1 && renderStep1()}
          {step === 2 && renderStep2()}
          {step === 3 && renderStep3()}
          {step === 4 && renderStep4()}
        </div>

        {/* Navigation Buttons */}
        <div className="flex space-x-3">
          {step < 4 ? (
            <button
              onClick={handleNext}
              disabled={!validateStep(step)}
              className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md font-semibold transition-colors"
            >
              Next
            </button>
          ) : (
            <button
              onClick={handleSubmit}
              disabled={isLoading || !validateStep(4)}
              className="flex-1 bg-green-600 hover:bg-green-700 disabled:bg-gray-300 disabled:cursor-not-allowed text-white py-2 px-4 rounded-md font-semibold transition-colors flex items-center justify-center"
            >
              {isLoading ? (
                <>
                  <svg className="animate-spin h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                  </svg>
                  Creating Account...
                </>
              ) : (
                'Create Account'
              )}
            </button>
          )}
        </div>

        {/* Footer */}
        <div className="mt-6 text-center">
          <p className="text-xs text-gray-500">
            By creating an account, you agree to our Terms of Service and Privacy Policy.
          </p>
        </div>
      </div>
    </div>
  );
};

export default UserRegistration;
