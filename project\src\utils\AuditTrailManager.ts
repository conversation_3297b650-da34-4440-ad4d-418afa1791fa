// Comprehensive Audit Trail Manager for Authentication System
// Tracks all user actions, security events, and system changes with detailed logging

interface AuditEvent {
  id: string;
  timestamp: string;
  eventType: 'authentication' | 'authorization' | 'data_access' | 'system_change' | 'security_incident';
  action: string;
  userId?: number;
  userName?: string;
  tenantId?: number;
  tenantName?: string;
  ipAddress: string;
  userAgent: string;
  sessionId?: string;
  resource?: string;
  resourceId?: string;
  oldValue?: any;
  newValue?: any;
  success: boolean;
  errorMessage?: string;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  metadata: Record<string, any>;
}

interface AuditFilter {
  startDate?: string;
  endDate?: string;
  eventType?: string;
  userId?: number;
  tenantId?: number;
  riskLevel?: string;
  success?: boolean;
  action?: string;
  limit?: number;
  offset?: number;
}

interface AuditReport {
  totalEvents: number;
  timeRange: { start: string; end: string };
  eventsByType: Record<string, number>;
  eventsByRisk: Record<string, number>;
  topUsers: Array<{ userId: number; userName: string; eventCount: number }>;
  topActions: Array<{ action: string; count: number }>;
  securityIncidents: number;
  failedLogins: number;
  suspiciousActivity: number;
}

class AuditTrailManager {
  private static instance: AuditTrailManager;
  private localEvents: AuditEvent[] = [];
  private maxLocalEvents = 1000;
  private batchSize = 50;
  private flushInterval = 30000; // 30 seconds
  private pendingEvents: AuditEvent[] = [];

  private constructor() {
    this.startPeriodicFlush();
    this.setupBeforeUnloadHandler();
  }

  public static getInstance(): AuditTrailManager {
    if (!AuditTrailManager.instance) {
      AuditTrailManager.instance = new AuditTrailManager();
    }
    return AuditTrailManager.instance;
  }

  // Log authentication events
  public logAuthenticationEvent(data: {
    action: 'login_attempt' | 'login_success' | 'login_failure' | 'logout' | 'session_expired' | 'password_change';
    userId?: number;
    userName?: string;
    tenantId?: number;
    tenantName?: string;
    method?: string;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): void {
    const riskLevel = this.calculateRiskLevel('authentication', data.action, data.success);
    
    this.logEvent({
      eventType: 'authentication',
      action: data.action,
      userId: data.userId,
      userName: data.userName,
      tenantId: data.tenantId,
      tenantName: data.tenantName,
      success: data.success,
      errorMessage: data.errorMessage,
      riskLevel,
      metadata: {
        method: data.method,
        ...data.metadata
      }
    });
  }

  // Log authorization events
  public logAuthorizationEvent(data: {
    action: 'access_granted' | 'access_denied' | 'permission_check' | 'role_change';
    userId: number;
    userName: string;
    tenantId?: number;
    resource: string;
    resourceId?: string;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): void {
    const riskLevel = this.calculateRiskLevel('authorization', data.action, data.success);
    
    this.logEvent({
      eventType: 'authorization',
      action: data.action,
      userId: data.userId,
      userName: data.userName,
      tenantId: data.tenantId,
      resource: data.resource,
      resourceId: data.resourceId,
      success: data.success,
      errorMessage: data.errorMessage,
      riskLevel,
      metadata: data.metadata
    });
  }

  // Log data access events
  public logDataAccessEvent(data: {
    action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import';
    userId: number;
    userName: string;
    tenantId: number;
    resource: string;
    resourceId: string;
    oldValue?: any;
    newValue?: any;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): void {
    const riskLevel = this.calculateRiskLevel('data_access', data.action, data.success);
    
    this.logEvent({
      eventType: 'data_access',
      action: data.action,
      userId: data.userId,
      userName: data.userName,
      tenantId: data.tenantId,
      resource: data.resource,
      resourceId: data.resourceId,
      oldValue: data.oldValue,
      newValue: data.newValue,
      success: data.success,
      errorMessage: data.errorMessage,
      riskLevel,
      metadata: data.metadata
    });
  }

  // Log system changes
  public logSystemChangeEvent(data: {
    action: 'config_change' | 'user_created' | 'user_deleted' | 'tenant_created' | 'tenant_deleted' | 'system_update';
    userId: number;
    userName: string;
    tenantId?: number;
    resource: string;
    resourceId?: string;
    oldValue?: any;
    newValue?: any;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): void {
    const riskLevel = this.calculateRiskLevel('system_change', data.action, data.success);
    
    this.logEvent({
      eventType: 'system_change',
      action: data.action,
      userId: data.userId,
      userName: data.userName,
      tenantId: data.tenantId,
      resource: data.resource,
      resourceId: data.resourceId,
      oldValue: data.oldValue,
      newValue: data.newValue,
      success: data.success,
      errorMessage: data.errorMessage,
      riskLevel,
      metadata: data.metadata
    });
  }

  // Log security incidents
  public logSecurityIncident(data: {
    action: 'brute_force_attempt' | 'suspicious_activity' | 'data_breach' | 'unauthorized_access' | 'malware_detected';
    userId?: number;
    userName?: string;
    tenantId?: number;
    resource?: string;
    success: boolean;
    errorMessage?: string;
    metadata?: Record<string, any>;
  }): void {
    this.logEvent({
      eventType: 'security_incident',
      action: data.action,
      userId: data.userId,
      userName: data.userName,
      tenantId: data.tenantId,
      resource: data.resource,
      success: data.success,
      errorMessage: data.errorMessage,
      riskLevel: 'critical',
      metadata: data.metadata
    });

    // Immediately send security incidents to backend
    this.flushPendingEvents();
  }

  // Core event logging method
  private logEvent(eventData: Omit<AuditEvent, 'id' | 'timestamp' | 'ipAddress' | 'userAgent' | 'sessionId'>): void {
    const event: AuditEvent = {
      id: `audit_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date().toISOString(),
      ipAddress: this.getClientIP(),
      userAgent: navigator.userAgent,
      sessionId: this.getSessionId(),
      ...eventData
    };

    // Add to local storage
    this.localEvents.push(event);
    if (this.localEvents.length > this.maxLocalEvents) {
      this.localEvents = this.localEvents.slice(-this.maxLocalEvents);
    }

    // Add to pending batch
    this.pendingEvents.push(event);

    // Log to console for development
    console.log(`📋 Audit Event: ${event.eventType}/${event.action}`, {
      user: event.userName,
      tenant: event.tenantName,
      success: event.success,
      risk: event.riskLevel
    });

    // Auto-flush if batch is full or high risk
    if (this.pendingEvents.length >= this.batchSize || event.riskLevel === 'critical') {
      this.flushPendingEvents();
    }
  }

  // Calculate risk level based on event type and action
  private calculateRiskLevel(eventType: string, action: string, success: boolean): 'low' | 'medium' | 'high' | 'critical' {
    // Failed events are generally higher risk
    if (!success) {
      if (eventType === 'authentication' && action.includes('login')) {
        return 'high';
      }
      if (eventType === 'authorization') {
        return 'medium';
      }
    }

    // Specific high-risk actions
    const highRiskActions = [
      'user_deleted', 'tenant_deleted', 'config_change', 'password_change',
      'role_change', 'delete', 'export'
    ];

    const criticalActions = [
      'brute_force_attempt', 'suspicious_activity', 'data_breach',
      'unauthorized_access', 'malware_detected'
    ];

    if (criticalActions.includes(action)) {
      return 'critical';
    }

    if (highRiskActions.includes(action)) {
      return 'high';
    }

    if (eventType === 'system_change' || action.includes('admin')) {
      return 'medium';
    }

    return 'low';
  }

  // Get client IP (simplified for demo)
  private getClientIP(): string {
    // In production, this would be determined by the backend
    return 'client_ip';
  }

  // Get current session ID
  private getSessionId(): string {
    return localStorage.getItem('sessionId') || 'no_session';
  }

  // Flush pending events to backend
  private async flushPendingEvents(): Promise<void> {
    if (this.pendingEvents.length === 0) return;

    const eventsToSend = [...this.pendingEvents];
    this.pendingEvents = [];

    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('http://localhost:4000/api/audit/events', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify({ events: eventsToSend })
      });

      if (response.ok) {
        console.log(`✅ Flushed ${eventsToSend.length} audit events to backend`);
      } else {
        console.error('❌ Failed to flush audit events');
        // Re-add failed events to pending queue
        this.pendingEvents.unshift(...eventsToSend);
      }
    } catch (error) {
      console.error('❌ Error flushing audit events:', error);
      // Re-add failed events to pending queue
      this.pendingEvents.unshift(...eventsToSend);
    }
  }

  // Start periodic flush
  private startPeriodicFlush(): void {
    setInterval(() => {
      this.flushPendingEvents();
    }, this.flushInterval);
  }

  // Setup beforeunload handler to flush events
  private setupBeforeUnloadHandler(): void {
    window.addEventListener('beforeunload', () => {
      // Use sendBeacon for reliable event sending on page unload
      if (this.pendingEvents.length > 0 && navigator.sendBeacon) {
        const token = localStorage.getItem('authToken');
        const data = JSON.stringify({ events: this.pendingEvents });
        
        navigator.sendBeacon('http://localhost:4000/api/audit/events', data);
      }
    });
  }

  // Get audit events with filtering
  public async getAuditEvents(filter: AuditFilter = {}): Promise<AuditEvent[]> {
    try {
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams();
      
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`http://localhost:4000/api/audit/events?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to fetch audit events');
        return [];
      }
    } catch (error) {
      console.error('Error fetching audit events:', error);
      return [];
    }
  }

  // Generate audit report
  public async generateAuditReport(filter: AuditFilter = {}): Promise<AuditReport | null> {
    try {
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams();
      
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });

      const response = await fetch(`http://localhost:4000/api/audit/report?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        return await response.json();
      } else {
        console.error('Failed to generate audit report');
        return null;
      }
    } catch (error) {
      console.error('Error generating audit report:', error);
      return null;
    }
  }

  // Export audit events
  public async exportAuditEvents(filter: AuditFilter = {}, format: 'csv' | 'json' | 'pdf' = 'csv'): Promise<Blob | null> {
    try {
      const token = localStorage.getItem('authToken');
      const queryParams = new URLSearchParams();
      
      Object.entries(filter).forEach(([key, value]) => {
        if (value !== undefined) {
          queryParams.append(key, value.toString());
        }
      });
      
      queryParams.append('format', format);

      const response = await fetch(`http://localhost:4000/api/audit/export?${queryParams}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        return await response.blob();
      } else {
        console.error('Failed to export audit events');
        return null;
      }
    } catch (error) {
      console.error('Error exporting audit events:', error);
      return null;
    }
  }

  // Get local events (for offline viewing)
  public getLocalEvents(filter: Partial<AuditFilter> = {}): AuditEvent[] {
    let events = [...this.localEvents];

    if (filter.eventType) {
      events = events.filter(e => e.eventType === filter.eventType);
    }

    if (filter.userId) {
      events = events.filter(e => e.userId === filter.userId);
    }

    if (filter.riskLevel) {
      events = events.filter(e => e.riskLevel === filter.riskLevel);
    }

    if (filter.success !== undefined) {
      events = events.filter(e => e.success === filter.success);
    }

    return events.slice(-(filter.limit || 100));
  }

  // Clear local events
  public clearLocalEvents(): void {
    this.localEvents = [];
    console.log('🗑️ Local audit events cleared');
  }

  // Get audit statistics
  public getAuditStatistics(): {
    totalEvents: number;
    eventsByType: Record<string, number>;
    eventsByRisk: Record<string, number>;
    recentEvents: number;
  } {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    const eventsByType: Record<string, number> = {};
    const eventsByRisk: Record<string, number> = {};
    let recentEvents = 0;

    this.localEvents.forEach(event => {
      // Count by type
      eventsByType[event.eventType] = (eventsByType[event.eventType] || 0) + 1;
      
      // Count by risk
      eventsByRisk[event.riskLevel] = (eventsByRisk[event.riskLevel] || 0) + 1;
      
      // Count recent events
      if (new Date(event.timestamp).getTime() > oneHourAgo) {
        recentEvents++;
      }
    });

    return {
      totalEvents: this.localEvents.length,
      eventsByType,
      eventsByRisk,
      recentEvents
    };
  }
}

// Export singleton instance
export const auditTrailManager = AuditTrailManager.getInstance();

// Export types
export type { AuditEvent, AuditFilter, AuditReport };

// Utility functions for common audit logging
export const logLogin = (userId: number, userName: string, tenantId: number, tenantName: string, success: boolean, method?: string, error?: string) => {
  auditTrailManager.logAuthenticationEvent({
    action: success ? 'login_success' : 'login_failure',
    userId,
    userName,
    tenantId,
    tenantName,
    success,
    errorMessage: error,
    metadata: { method }
  });
};

export const logLogout = (userId: number, userName: string, tenantId?: number) => {
  auditTrailManager.logAuthenticationEvent({
    action: 'logout',
    userId,
    userName,
    tenantId,
    success: true
  });
};

export const logDataAccess = (action: 'create' | 'read' | 'update' | 'delete', userId: number, userName: string, tenantId: number, resource: string, resourceId: string, success: boolean) => {
  auditTrailManager.logDataAccessEvent({
    action,
    userId,
    userName,
    tenantId,
    resource,
    resourceId,
    success
  });
};

export const logSecurityIncident = (action: string, details: Record<string, any>) => {
  auditTrailManager.logSecurityIncident({
    action: action as any,
    success: false,
    metadata: details
  });
};
