import React, { useState, useEffect } from 'react';
import { 
  Brain, 
  Shield, 
  TrendingUp, 
  Zap, 
  AlertTriangle,
  CheckCircle,
  Activity,
  BarChart3,
  Settings,
  RefreshCw,
  Play,
  Pause,
  Eye,
  Target,
  Cpu,
  Database
} from 'lucide-react';

interface AIModel {
  id: string;
  name: string;
  type: string;
  status: 'active' | 'training' | 'inactive';
  accuracy: number;
  lastTrained: string;
  predictions: number;
}

interface FraudAlert {
  id: string;
  transactionId: string;
  riskScore: number;
  reason: string;
  timestamp: string;
  status: 'pending' | 'reviewed' | 'approved' | 'blocked';
}

interface AutomationWorkflow {
  id: string;
  name: string;
  trigger: string;
  status: 'active' | 'paused' | 'error';
  executions: number;
  successRate: number;
}

const Phase5AICenter: React.FC = () => {
  const [aiModels, setAiModels] = useState<AIModel[]>([]);
  const [fraudAlerts, setFraudAlerts] = useState<FraudAlert[]>([]);
  const [workflows, setWorkflows] = useState<AutomationWorkflow[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [aiStats, setAiStats] = useState({
    totalPredictions: 0,
    fraudDetected: 0,
    automationSavings: 0,
    modelAccuracy: 0
  });

  useEffect(() => {
    loadAIData();
  }, []);

  const loadAIData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      
      // Try to load real AI data
      const response = await fetch('/api/ai/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setAiModels(data.models || []);
        setFraudAlerts(data.alerts || []);
        setWorkflows(data.workflows || []);
        setAiStats(data.stats || {});
      } else {
        // Fallback to mock data
        setAiModels([
          { id: '1', name: 'Fraud Detection Model', type: 'fraud_detection', status: 'active', accuracy: 96.5, lastTrained: '2024-01-15T08:00:00Z', predictions: 1247 },
          { id: '2', name: 'Sales Forecasting', type: 'sales_forecast', status: 'active', accuracy: 87.3, lastTrained: '2024-01-14T20:00:00Z', predictions: 89 },
          { id: '3', name: 'Demand Prediction', type: 'demand_prediction', status: 'training', accuracy: 82.1, lastTrained: '2024-01-13T12:00:00Z', predictions: 156 },
          { id: '4', name: 'Price Optimization', type: 'pricing', status: 'inactive', accuracy: 78.9, lastTrained: '2024-01-10T16:00:00Z', predictions: 45 }
        ]);

        setFraudAlerts([
          { id: '1', transactionId: 'TXN-001', riskScore: 85, reason: 'Unusual spending pattern', timestamp: '2024-01-15T10:30:00Z', status: 'pending' },
          { id: '2', transactionId: 'TXN-002', riskScore: 72, reason: 'New payment method', timestamp: '2024-01-15T10:15:00Z', status: 'reviewed' },
          { id: '3', transactionId: 'TXN-003', riskScore: 91, reason: 'High-risk location', timestamp: '2024-01-15T09:45:00Z', status: 'blocked' }
        ]);

        setWorkflows([
          { id: '1', name: 'Inventory Reorder', trigger: 'Low stock alert', status: 'active', executions: 234, successRate: 98.7 },
          { id: '2', name: 'Customer Follow-up', trigger: 'Order completion', status: 'active', executions: 1456, successRate: 95.2 },
          { id: '3', name: 'Price Adjustment', trigger: 'Demand spike', status: 'paused', executions: 67, successRate: 89.6 },
          { id: '4', name: 'Staff Scheduling', trigger: 'Forecast update', status: 'error', executions: 12, successRate: 75.0 }
        ]);

        setAiStats({
          totalPredictions: 15420,
          fraudDetected: 23,
          automationSavings: 12500,
          modelAccuracy: 88.7
        });
      }
    } catch (error) {
      console.error('Failed to load AI data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600 bg-green-100';
      case 'training': return 'text-blue-600 bg-blue-100';
      case 'inactive': return 'text-gray-600 bg-gray-100';
      case 'paused': return 'text-yellow-600 bg-yellow-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-orange-600 bg-orange-100';
      case 'reviewed': return 'text-blue-600 bg-blue-100';
      case 'approved': return 'text-green-600 bg-green-100';
      case 'blocked': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getRiskColor = (score: number) => {
    if (score >= 80) return 'text-red-600 bg-red-100';
    if (score >= 60) return 'text-yellow-600 bg-yellow-100';
    return 'text-green-600 bg-green-100';
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* AI Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Brain className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Predictions</p>
              <p className="text-2xl font-semibold text-gray-900">{aiStats.totalPredictions.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-8 w-8 text-red-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Fraud Detected</p>
              <p className="text-2xl font-semibold text-gray-900">{aiStats.fraudDetected}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Zap className="h-8 w-8 text-yellow-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Automation Savings</p>
              <p className="text-2xl font-semibold text-gray-900">${aiStats.automationSavings.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Target className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Model Accuracy</p>
              <p className="text-2xl font-semibold text-gray-900">{aiStats.modelAccuracy}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Fraud Alerts */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Fraud Alerts</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {fraudAlerts.slice(0, 3).map((alert) => (
              <div key={alert.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-red-500 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">Transaction {alert.transactionId}</p>
                    <p className="text-sm text-gray-500">{alert.reason}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRiskColor(alert.riskScore)}`}>
                    Risk: {alert.riskScore}%
                  </span>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                    {alert.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* AI Model Status */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">AI Model Status</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {aiModels.map((model) => (
              <div key={model.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{model.name}</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(model.status)}`}>
                    {model.status}
                  </span>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Accuracy:</span>
                    <span className="font-medium">{model.accuracy}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Predictions:</span>
                    <span className="font-medium">{model.predictions.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Last Trained:</span>
                    <span className="font-medium">{new Date(model.lastTrained).toLocaleDateString()}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderFraudDetection = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Fraud Detection Center</h3>
        <button className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium">
          Run Manual Scan
        </button>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Transaction</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Risk Score</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Reason</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {fraudAlerts.map((alert) => (
              <tr key={alert.id}>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {alert.transactionId}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getRiskColor(alert.riskScore)}`}>
                    {alert.riskScore}%
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {alert.reason}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(alert.status)}`}>
                    {alert.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex space-x-2">
                    <button className="text-blue-600 hover:text-blue-900">
                      <Eye className="h-4 w-4" />
                    </button>
                    <button className="text-green-600 hover:text-green-900">
                      <CheckCircle className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderAutomation = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Automation Workflows</h3>
        <button className="bg-purple-600 hover:bg-purple-700 text-white px-4 py-2 rounded-md text-sm font-medium">
          Create Workflow
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {workflows.map((workflow) => (
          <div key={workflow.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-medium text-gray-900">{workflow.name}</h4>
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(workflow.status)}`}>
                {workflow.status}
              </span>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Trigger:</span>
                <span className="text-gray-900">{workflow.trigger}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Executions:</span>
                <span className="text-gray-900">{workflow.executions.toLocaleString()}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Success Rate:</span>
                <span className="text-gray-900">{workflow.successRate}%</span>
              </div>
            </div>

            <div className="flex space-x-2">
              <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium flex items-center justify-center">
                {workflow.status === 'active' ? <Pause className="h-4 w-4 mr-1" /> : <Play className="h-4 w-4 mr-1" />}
                {workflow.status === 'active' ? 'Pause' : 'Start'}
              </button>
              <button className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Configure
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-purple-600" />
          <span className="text-gray-600">Loading AI systems...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Phase 5: AI & Automation Center</h2>
        <p className="text-gray-600">Artificial intelligence and automation management</p>
      </div>

      {/* Sub Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: Brain },
            { id: 'fraud', name: 'Fraud Detection', icon: Shield },
            { id: 'automation', name: 'Automation', icon: Zap }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-purple-500 text-purple-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'fraud' && renderFraudDetection()}
      {activeTab === 'automation' && renderAutomation()}
    </div>
  );
};

export default Phase5AICenter;
