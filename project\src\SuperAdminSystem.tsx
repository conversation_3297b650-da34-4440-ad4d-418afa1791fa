import React, { useState, useEffect, Component, ErrorInfo, ReactNode } from 'react';

// Lazy load heavy components to avoid blocking the initial render
const TenantProvider = React.lazy(() => import('./context/TenantContext').then(m => ({ default: m.TenantProvider })));
const EnhancedAppProvider = React.lazy(() => import('./context/EnhancedAppContext').then(m => ({ default: m.EnhancedAppProvider })));
const ComprehensiveAdminDashboard = React.lazy(() => import('./pages/ComprehensiveAdminDashboard').then(m => ({ default: m.ComprehensiveAdminDashboard })));

// Error Boundary Component
interface ErrorBoundaryState {
  hasError: boolean;
  error?: Error;
}

class ErrorBoundary extends Component<{ children: ReactNode }, ErrorBoundaryState> {
  constructor(props: { children: ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): ErrorBoundaryState {
    console.error('💥 ErrorBoundary: Caught error:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('💥 ErrorBoundary: Error details:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-red-500 text-white p-8">
          <h1 className="text-4xl font-bold mb-4">💥 APPLICATION ERROR</h1>
          <div className="bg-black bg-opacity-50 p-4 rounded-lg">
            <h2 className="text-xl font-bold mb-2">Error Details:</h2>
            <p className="mb-2">{this.state.error?.message}</p>
            <pre className="text-sm overflow-auto">{this.state.error?.stack}</pre>
            <button
              onClick={() => window.location.reload()}
              className="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              Reload Page
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Super Admin Interface Component with Suspense
const SuperAdminInterface: React.FC = () => {
  return (
    <React.Suspense fallback={
      <div className="min-h-screen bg-gray-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900">Loading Super Admin Dashboard...</h2>
          <p className="text-gray-600 mt-2">Please wait while we load the comprehensive interface</p>
        </div>
      </div>
    }>
      <ComprehensiveAdminDashboard />
    </React.Suspense>
  );
};

// Enhanced Super Admin Login Component
const SuperAdminLogin: React.FC<{ onLogin: (success: boolean) => void }> = ({ onLogin }) => {
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showPin, setShowPin] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [securityLevel, setSecurityLevel] = useState<'maximum' | 'high' | 'checking'>('checking');
  const [systemStatus, setSystemStatus] = useState<'operational' | 'maintenance' | 'checking'>('checking');

  // Check for dark mode preference and initialize security status
  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);

    // Simulate security check
    setTimeout(() => {
      setSecurityLevel('maximum');
      setSystemStatus('operational');
    }, 1500);
  }, []);



  const toggleTheme = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);

    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  // Verify token with backend to ensure session is valid
  const verifyTokenWithBackend = async (token: string) => {
    try {
      const response = await fetch('/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ Token verified with backend:', data);
      } else {
        console.log('❌ Token verification failed');
        // Clear invalid token
        localStorage.removeItem('authToken');
        localStorage.removeItem('currentEmployee');
        localStorage.removeItem('currentTenant');
      }
    } catch (error) {
      console.error('❌ Token verification error:', error);
    }
  };

  // Handle PIN input
  const handlePinInput = (digit: string) => {
    if (pin.length < 6) {
      setPin(prev => prev + digit);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    if (!pin.trim()) {
      setError('Please enter your Super Admin PIN');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      // Authenticate with main backend API (via proxy)
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: pin
          // Let backend auto-detect tenant
        }),
      });

      if (response.ok) {
        const data = await response.json();

        // Check if user has super admin role
        if (data.employee && data.employee.role === 'super_admin') {
          console.log('✅ Super Admin authenticated successfully');

          // Store the JWT token and user data in localStorage using consistent keys
          localStorage.setItem('authToken', data.token);
          localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
          localStorage.setItem('currentTenant', JSON.stringify(data.tenant));

          // Verify token with backend immediately after login
          verifyTokenWithBackend(data.token);

          setError('');
          onLogin(true);
        } else {
          setError(`Access Denied: Super Administrator privileges required. Current role: ${data.employee?.role || 'unknown'}`);
          setPin('');
        }
      } else {
        setError('Invalid Super Admin PIN. Access denied.');
        setPin('');
      }
    } catch (error) {
      console.error('Super Admin login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Connection failed. Please ensure the backend server is running.');
      } else {
        setError('Authentication failed. Please verify your Super Admin PIN.');
      }
      setPin('');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className={`min-h-screen w-full flex items-center justify-center p-4 relative overflow-hidden transition-colors duration-300 ${
      isDarkMode
        ? 'bg-gradient-to-br from-gray-900 via-red-900 to-purple-900'
        : 'bg-gradient-to-br from-red-50 via-pink-50 to-purple-50'
    }`}>

      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className={`fixed top-4 right-4 p-3 rounded-full transition-all duration-300 z-50 ${
          isDarkMode
            ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700'
            : 'bg-white text-gray-600 hover:bg-gray-100'
        } shadow-lg hover:shadow-xl`}
      >
        {isDarkMode ? '☀️' : '🌙'}
      </button>

      {/* Security Status Indicators */}
      <div className="fixed top-4 left-4 space-y-2 z-50">
        {/* Restricted Access Badge */}
        <div className={`px-4 py-2 rounded-lg font-bold text-sm shadow-lg ${
          isDarkMode
            ? 'bg-red-600 text-white'
            : 'bg-red-500 text-white'
        } animate-pulse`}>
          🔒 RESTRICTED ACCESS
        </div>

        {/* Security Level Indicator */}
        <div className={`px-3 py-1 rounded-lg text-xs font-medium shadow-lg ${
          securityLevel === 'maximum'
            ? isDarkMode
              ? 'bg-green-600 text-white'
              : 'bg-green-500 text-white'
            : isDarkMode
              ? 'bg-yellow-600 text-white'
              : 'bg-yellow-500 text-white'
        }`}>
          🛡️ Security: {securityLevel === 'checking' ? 'Checking...' : securityLevel.toUpperCase()}
        </div>

        {/* System Status Indicator */}
        <div className={`px-3 py-1 rounded-lg text-xs font-medium shadow-lg ${
          systemStatus === 'operational'
            ? isDarkMode
              ? 'bg-blue-600 text-white'
              : 'bg-blue-500 text-white'
            : isDarkMode
              ? 'bg-orange-600 text-white'
              : 'bg-orange-500 text-white'
        }`}>
          ⚡ System: {systemStatus === 'checking' ? 'Checking...' : systemStatus.toUpperCase()}
        </div>
      </div>

      {/* Main Login Card */}
      <div className={`w-full max-w-md relative z-10 transition-all duration-300 ${
        isDarkMode
          ? 'bg-gray-800/95 border-red-700'
          : 'bg-white/95 border-red-200'
      } backdrop-blur-xl rounded-2xl shadow-2xl border-2 p-8`}>

        {/* Header */}
        <div className="text-center mb-8">
          <div className="w-24 h-24 mx-auto mb-6 rounded-2xl bg-gradient-to-r from-red-500 to-pink-600 flex items-center justify-center shadow-lg relative">
            <svg className="w-12 h-12 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
            </svg>
            <div className="absolute -top-1 -right-1 w-6 h-6 bg-yellow-400 rounded-full flex items-center justify-center">
              <svg className="w-3 h-3 text-yellow-800" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
          </div>

          <h1 className={`text-3xl font-bold mb-2 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Super Admin
          </h1>

          <p className={`text-sm font-medium ${
            isDarkMode ? 'text-red-300' : 'text-red-600'
          }`}>
            System Administration Portal
          </p>

          <div className={`mt-4 p-3 rounded-lg ${
            isDarkMode
              ? 'bg-red-900/30 border border-red-700'
              : 'bg-red-50 border border-red-200'
          }`}>
            <p className={`text-xs ${
              isDarkMode ? 'text-red-300' : 'text-red-700'
            }`}>
              ⚠️ Authorized Personnel Only
            </p>
          </div>
        </div>

        {/* System Monitoring Grid */}
        <div className="grid grid-cols-4 gap-3 mb-6">
          <div className={`p-3 rounded-lg text-center ${
            isDarkMode
              ? 'bg-gray-700/50 border border-gray-600'
              : 'bg-gray-50 border border-gray-200'
          }`}>
            <svg className={`w-6 h-6 mx-auto mb-1 ${
              isDarkMode ? 'text-blue-400' : 'text-blue-600'
            }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 7v10c0 2.21 3.582 4 8 4s8-1.79 8-4V7M4 7c0 2.21 3.582 4 8 4s8-1.79 8-4M4 7c0-2.21 3.582-4 8-4s8 1.79 8 4" />
            </svg>
            <p className={`text-xs font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Database
            </p>
          </div>

          <div className={`p-3 rounded-lg text-center ${
            isDarkMode
              ? 'bg-gray-700/50 border border-gray-600'
              : 'bg-gray-50 border border-gray-200'
          }`}>
            <svg className={`w-6 h-6 mx-auto mb-1 ${
              isDarkMode ? 'text-green-400' : 'text-green-600'
            }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
            </svg>
            <p className={`text-xs font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Users
            </p>
          </div>

          <div className={`p-3 rounded-lg text-center ${
            isDarkMode
              ? 'bg-gray-700/50 border border-gray-600'
              : 'bg-gray-50 border border-gray-200'
          }`}>
            <svg className={`w-6 h-6 mx-auto mb-1 ${
              isDarkMode ? 'text-purple-400' : 'text-purple-600'
            }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            <p className={`text-xs font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              Analytics
            </p>
          </div>

          <div className={`p-3 rounded-lg text-center ${
            isDarkMode
              ? 'bg-gray-700/50 border border-gray-600'
              : 'bg-gray-50 border border-gray-200'
          }`}>
            <svg className={`w-6 h-6 mx-auto mb-1 ${
              isDarkMode ? 'text-orange-400' : 'text-orange-600'
            }`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5.636 18.364a9 9 0 010-12.728m12.728 0a9 9 0 010 12.728m-9.9-2.829a5 5 0 010-7.07m7.072 0a5 5 0 010 7.07M13 12a1 1 0 11-2 0 1 1 0 012 0z" />
            </svg>
            <p className={`text-xs font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-700'
            }`}>
              System Control
            </p>
          </div>
        </div>

        {/* PIN Input Section */}
        <form onSubmit={handleSubmit} className="space-y-6">

          {/* PIN Display */}
          <div>
            <label className={`block text-sm font-medium mb-3 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-700'
            }`}>
              <svg className="w-4 h-4 inline mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              Super Admin PIN
            </label>

            <div className="relative">
              <input
                type={showPin ? 'text' : 'password'}
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                placeholder="Enter 6-digit PIN"
                maxLength={6}
                className={`w-full px-4 py-3 pr-12 rounded-lg border-2 transition-all duration-200 ${
                  error
                    ? isDarkMode
                      ? 'border-red-500 bg-red-900/20 text-red-300'
                      : 'border-red-500 bg-red-50 text-red-700'
                    : isDarkMode
                      ? 'border-gray-600 bg-gray-700/50 text-white focus:border-red-500'
                      : 'border-gray-300 bg-white text-gray-900 focus:border-red-500'
                } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
                onKeyPress={(e) => e.key === 'Enter' && handleSubmit(e)}
              />

              <button
                type="button"
                onClick={() => setShowPin(!showPin)}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 p-1 rounded ${
                  isDarkMode
                    ? 'text-gray-400 hover:text-gray-300'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                {showPin ? (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21" />
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                )}
              </button>
            </div>

            {/* PIN Hint */}
            <p className={`mt-2 text-xs ${
              isDarkMode ? 'text-gray-400' : 'text-gray-500'
            }`}>
              💡 Default PIN: 999999 (for testing)
            </p>
          </div>

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 ${
              isDarkMode
                ? 'bg-red-900/30 border border-red-700 text-red-300'
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <svg className="w-5 h-5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span className="text-sm font-medium">{error}</span>
            </div>
          )}

          {/* Login Button */}
          <button
            type="submit"
            disabled={pin.length === 0 || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              pin.length === 0 || isLoading
                ? isDarkMode
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed'
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gradient-to-r from-red-500 to-pink-600 hover:from-red-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl'
            } transform hover:scale-105 active:scale-95`}
          >
            {isLoading ? (
              <>
                <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span>Authenticating...</span>
              </>
            ) : (
              <>
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                </svg>
                <span>Access Admin Dashboard</span>
                <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 8l4 4m0 0l-4 4m4-4H3" />
                </svg>
              </>
            )}
          </button>
        </form>

        {/* Footer */}
        <div className={`mt-6 pt-6 border-t text-center ${
          isDarkMode
            ? 'border-gray-700 text-gray-400'
            : 'border-gray-200 text-gray-500'
        }`}>
          <p className="text-xs">
            🔐 Secure Authentication • 🛡️ Protected System
          </p>
          <p className="text-xs mt-1">
            RESTROFLOW Super Admin Portal v2.0
          </p>

        </div>
      </div>

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute top-1/4 left-1/4 w-64 h-64 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob ${
          isDarkMode ? 'bg-red-400' : 'bg-red-300'
        }`}></div>
        <div className={`absolute top-1/3 right-1/4 w-64 h-64 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000 ${
          isDarkMode ? 'bg-pink-400' : 'bg-pink-300'
        }`}></div>
        <div className={`absolute bottom-1/4 left-1/3 w-64 h-64 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000 ${
          isDarkMode ? 'bg-purple-400' : 'bg-purple-300'
        }`}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob { animation: blob 7s infinite; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-4000 { animation-delay: 4s; }
      `}</style>
    </div>
  );
};

// Main Super Admin Content Component
const SuperAdminContent: React.FC<{
  isLoggedIn: boolean;
  setIsLoggedIn: (value: boolean) => void;
}> = ({ isLoggedIn, setIsLoggedIn }) => {
  console.log('🔐 SuperAdminContent: isLoggedIn =', isLoggedIn);

  if (!isLoggedIn) {
    console.log('🔐 SuperAdminContent: Showing login screen');
    return <SuperAdminLogin onLogin={setIsLoggedIn} />;
  }

  console.log('🔐 SuperAdminContent: Showing admin interface');
  return <SuperAdminInterface />;
};

// Main Super Admin System Component
const SuperAdminSystem: React.FC = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Debug logging
  console.log('🔐 SuperAdminSystem: isLoggedIn =', isLoggedIn);

  // Check for existing session on mount
  React.useEffect(() => {
    const checkSession = async () => {
      const token = localStorage.getItem('authToken');
      const employee = localStorage.getItem('currentEmployee');

      if (token && employee) {
        try {
          const employeeData = JSON.parse(employee);
          if (employeeData.role === 'super_admin') {
            console.log('🔍 Checking existing Super Admin session...');

            // Verify token is still valid
            const response = await fetch('/api/auth/verify', {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            });

            if (response.ok) {
              console.log('✅ Existing Super Admin session is valid, auto-logging in');
              setIsLoggedIn(true);
              return;
            }
          }
        } catch (error) {
          console.log('❌ Session check failed:', error);
        }
      }

      // No valid session found, ensure we show login screen
      setIsLoggedIn(false);
      console.log('🔐 SuperAdminSystem: No valid session, showing login screen');
    };

    checkSession();
  }, []);

  return (
    <ErrorBoundary>
      <React.Suspense fallback={
        <div className="min-h-screen bg-gradient-to-br from-red-600 to-purple-700 flex items-center justify-center">
          <div className="bg-white rounded-lg shadow-2xl p-8 text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-red-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900">Initializing Super Admin System...</h2>
            <p className="text-gray-600 mt-2">Loading security protocols and authentication</p>
          </div>
        </div>
      }>
        <EnhancedAppProvider>
          <TenantProvider>
            <SuperAdminContent isLoggedIn={isLoggedIn} setIsLoggedIn={setIsLoggedIn} />
          </TenantProvider>
        </EnhancedAppProvider>
      </React.Suspense>
    </ErrorBoundary>
  );
};

export default SuperAdminSystem;
