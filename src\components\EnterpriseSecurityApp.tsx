import React, { useState, useEffect } from 'react';
import SimpleSuperAdminDashboard from './SimpleSuperAdminDashboard';
import { apiService } from '../services/api';

interface SecurityStatus {
  threatLevel: 'low' | 'medium' | 'high' | 'critical';
  lastCheck: string;
  activeThreats: string[];
  systemStatus: string;
  securityEvents: Array<{
    timestamp: string;
    event: string;
    level: string;
  }>;
}

const EnterpriseSecurityApp: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [securityStatus, setSecurityStatus] = useState<SecurityStatus>({
    threatLevel: 'medium',
    lastCheck: new Date().toISOString(),
    activeThreats: [],
    systemStatus: 'initializing',
    securityEvents: []
  });

  useEffect(() => {
    // Initialize security monitoring
    initializeSecuritySystem();
    
    // Check for existing authentication
    checkExistingAuth();
    
    // Start security monitoring interval
    const securityInterval = setInterval(updateSecurityStatus, 30000);
    
    return () => clearInterval(securityInterval);
  }, []);

  const initializeSecuritySystem = () => {
    console.log('🔒 Initializing Enterprise Security System...');
    
    // Update global security center
    if (typeof window !== 'undefined') {
      (window as any).securityCenter = {
        ...(window as any).securityCenter,
        systemStatus: 'active',
        threatLevel: 'low',
        lastCheck: new Date().toISOString()
      };
    }

    setSecurityStatus(prev => ({
      ...prev,
      systemStatus: 'active',
      threatLevel: 'low',
      lastCheck: new Date().toISOString()
    }));

    console.log('✅ Enterprise Security System initialized');
  };

  const checkExistingAuth = async () => {
    const token = localStorage.getItem('authToken');
    const storedUser = localStorage.getItem('user');
    
    if (token && storedUser) {
      try {
        const userData = JSON.parse(storedUser);
        if (userData.role === 'super_admin') {
          setUser(userData);
          setIsAuthenticated(true);
          logSecurityEvent('Existing super admin session restored', 'info');
        } else {
          // Clear non-super-admin sessions
          localStorage.removeItem('authToken');
          localStorage.removeItem('user');
          logSecurityEvent('Non-super-admin session cleared', 'warning');
        }
      } catch (error) {
        console.error('Error parsing stored user:', error);
        localStorage.removeItem('authToken');
        localStorage.removeItem('user');
      }
    }
  };

  const updateSecurityStatus = () => {
    const currentStatus = (window as any).securityCenter || {};
    setSecurityStatus(prev => ({
      ...prev,
      ...currentStatus,
      lastCheck: new Date().toISOString()
    }));

    // Update security indicator in DOM
    const indicator = document.querySelector('.security-indicator');
    if (indicator) {
      const level = currentStatus.threatLevel || 'medium';
      indicator.className = `security-indicator ${level}`;
      
      const statusText = {
        'low': 'SECURE',
        'medium': 'ELEVATED',
        'high': 'HIGH ALERT',
        'critical': 'CRITICAL'
      };
      
      indicator.textContent = `Security: ${statusText[level] || 'UNKNOWN'}`;
    }
  };

  const logSecurityEvent = (event: string, level: 'info' | 'warning' | 'critical' = 'info') => {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      event,
      level
    };

    setSecurityStatus(prev => ({
      ...prev,
      securityEvents: [...prev.securityEvents.slice(-99), securityEvent]
    }));

    if (typeof window !== 'undefined' && (window as any).securityCenter) {
      (window as any).securityCenter.securityEvents = [
        ...((window as any).securityCenter.securityEvents || []).slice(-99),
        securityEvent
      ];
    }

    console.log(`🔒 SECURITY EVENT [${level.toUpperCase()}]:`, event);
  };

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      logSecurityEvent(`Super admin login attempt with PIN: ${pin.substring(0, 2)}***`, 'info');

      const response = await apiService.login(pin);
      
      if (response.user?.role !== 'super_admin') {
        throw new Error('Access denied: Super Administrator privileges required');
      }

      setUser(response.user);
      setIsAuthenticated(true);
      
      // Store authentication
      localStorage.setItem('authToken', response.token);
      localStorage.setItem('user', JSON.stringify(response.user));

      logSecurityEvent('Super admin authentication successful', 'info');
      
      // Update security status to secure
      setSecurityStatus(prev => ({
        ...prev,
        threatLevel: 'low',
        systemStatus: 'authenticated'
      }));

    } catch (error: any) {
      console.error('Authentication error:', error);
      setError(error.message || 'Authentication failed');
      logSecurityEvent(`Authentication failed: ${error.message}`, 'warning');
      
      // Increase threat level on failed login
      setSecurityStatus(prev => ({
        ...prev,
        threatLevel: 'medium'
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    setIsAuthenticated(false);
    setUser(null);
    setPin('');
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    
    logSecurityEvent('Super admin logout', 'info');
    
    setSecurityStatus(prev => ({
      ...prev,
      systemStatus: 'logged_out',
      threatLevel: 'medium'
    }));
  };

  // Render authenticated interface
  if (isAuthenticated && user) {
    return <SimpleSuperAdminDashboard onLogout={handleLogout} />;
  }

  // Render security login interface
  return (
    <div className="min-h-screen bg-gradient-to-br from-red-600 via-red-700 to-red-900 flex items-center justify-center">
      <div className="max-w-md w-full mx-4">
        {/* Security Header */}
        <div className="text-center mb-8">
          <div className="bg-white/10 backdrop-blur-sm rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center">
            <span className="text-4xl">🔒</span>
          </div>
          <h1 className="text-4xl font-bold text-white mb-2">
            Enterprise Security Center
          </h1>
          <p className="text-red-100">
            Super Administrator Access Required
          </p>
        </div>

        {/* Security Status */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-6 mb-6 border border-white/20">
          <div className="flex items-center justify-between mb-4">
            <span className="text-white font-medium">Security Status</span>
            <span className={`px-3 py-1 rounded-full text-xs font-bold ${
              securityStatus.threatLevel === 'low' ? 'bg-green-500 text-white' :
              securityStatus.threatLevel === 'medium' ? 'bg-yellow-500 text-black' :
              securityStatus.threatLevel === 'high' ? 'bg-red-500 text-white' :
              'bg-red-800 text-white animate-pulse'
            }`}>
              {securityStatus.threatLevel.toUpperCase()}
            </span>
          </div>
          <div className="text-red-100 text-sm space-y-1">
            <div>System: {securityStatus.systemStatus}</div>
            <div>Last Check: {new Date(securityStatus.lastCheck).toLocaleTimeString()}</div>
            <div>Events: {securityStatus.securityEvents.length}</div>
          </div>
        </div>

        {/* Login Form */}
        <div className="bg-white/10 backdrop-blur-sm rounded-xl p-8 border border-white/20">
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label className="block text-white font-medium mb-2">
                🔐 Super Administrator PIN
              </label>
              <input
                type="password"
                value={pin}
                onChange={(e) => setPin(e.target.value)}
                className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-red-200 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                placeholder="Enter your secure PIN"
                required
                disabled={loading}
              />
            </div>

            {error && (
              <div className="bg-red-800/50 border border-red-600 rounded-lg p-4">
                <div className="flex items-center">
                  <span className="text-red-300 mr-2">⚠️</span>
                  <span className="text-red-100 text-sm">{error}</span>
                </div>
              </div>
            )}

            <button
              type="submit"
              disabled={loading || !pin}
              className="w-full bg-white text-red-600 py-3 rounded-lg font-bold hover:bg-red-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
            >
              {loading ? (
                <span className="flex items-center justify-center">
                  <span className="animate-spin mr-2">⏳</span>
                  Authenticating...
                </span>
              ) : (
                '🚀 Access Security Center'
              )}
            </button>
          </form>

          <div className="mt-6 pt-6 border-t border-white/20">
            <div className="text-red-100 text-xs space-y-1">
              <div>🔒 Secure Connection Active</div>
              <div>🛡️ Multi-Factor Authentication</div>
              <div>📊 Real-Time Threat Monitoring</div>
            </div>
          </div>
        </div>

        {/* Security Notice */}
        <div className="mt-6 text-center">
          <p className="text-red-200 text-xs">
            This system is restricted to authorized Super Administrators only.
            All access attempts are logged and monitored.
          </p>
        </div>
      </div>
    </div>
  );
};

export default EnterpriseSecurityApp;
