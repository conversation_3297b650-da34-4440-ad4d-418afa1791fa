<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Super Admin Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #dc2626, #991b1b);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(0,0,0,0.3);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        button {
            background: #ffffff;
            color: #dc2626;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 10px;
        }
        button:hover {
            background: #f3f4f6;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
            background: rgba(0,0,0,0.5);
            border: 1px solid rgba(255,255,255,0.2);
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Debug Super Admin Authentication</h1>
        <p>This page helps debug the exact response format from the backend authentication.</p>
        
        <button onclick="testAuth('123456')">🔐 Test PIN 123456 (Super Admin)</button>
        <button onclick="testAuth('888888')">🔐 Test PIN 888888 (Invalid)</button>
        <button onclick="clearResults()">🗑️ Clear Results</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function testAuth(pin) {
            const resultsDiv = document.getElementById('results');
            
            try {
                console.log(`🔐 Testing authentication with PIN: ${pin}`);
                
                const startTime = Date.now();
                
                const response = await fetch('http://localhost:4000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pin: pin,
                        tenant_slug: 'auto-detect',
                        admin_access: true
                    })
                });
                
                const endTime = Date.now();
                const responseTime = endTime - startTime;
                
                console.log(`📡 Response received in ${responseTime}ms`);
                console.log(`📊 Status: ${response.status} ${response.statusText}`);
                console.log(`✅ OK: ${response.ok}`);
                
                let data;
                try {
                    data = await response.json();
                    console.log('📦 Response data:', data);
                } catch (jsonError) {
                    console.error('❌ JSON parsing error:', jsonError);
                    data = { error: 'Failed to parse JSON response' };
                }
                
                const result = {
                    timestamp: new Date().toISOString(),
                    pin: pin,
                    request: {
                        url: 'http://localhost:4000/api/auth/login',
                        method: 'POST',
                        body: {
                            pin: pin,
                            tenant_slug: 'auto-detect',
                            admin_access: true
                        }
                    },
                    response: {
                        status: response.status,
                        statusText: response.statusText,
                        ok: response.ok,
                        headers: Object.fromEntries(response.headers.entries()),
                        data: data
                    },
                    responseTime: responseTime,
                    analysis: analyzeResponse(response, data)
                };
                
                displayResult(result);
                
            } catch (error) {
                console.error('❌ Authentication error:', error);
                
                const errorResult = {
                    timestamp: new Date().toISOString(),
                    pin: pin,
                    error: {
                        name: error.name,
                        message: error.message,
                        stack: error.stack
                    },
                    analysis: {
                        issue: 'Network or connection error',
                        possibleCauses: [
                            'Backend server not running on port 4000',
                            'CORS policy blocking the request',
                            'Network connectivity issues',
                            'Firewall blocking the connection'
                        ]
                    }
                };
                
                displayResult(errorResult);
            }
        }
        
        function analyzeResponse(response, data) {
            const analysis = {
                success: false,
                issues: [],
                recommendations: []
            };
            
            if (!response.ok) {
                analysis.issues.push(`HTTP ${response.status}: ${response.statusText}`);
                analysis.recommendations.push('Check backend server logs for errors');
            }
            
            if (data && data.token) {
                analysis.success = true;
                analysis.issues.push('✅ Token received successfully');
                
                if (data.user || data.employee) {
                    const user = data.user || data.employee;
                    analysis.issues.push(`✅ User data: ${user.name} (${user.role})`);
                    
                    if (user.role === 'super_admin') {
                        analysis.issues.push('✅ Super admin role confirmed');
                    } else {
                        analysis.issues.push(`❌ Expected super_admin role, got: ${user.role}`);
                        analysis.recommendations.push('Check PIN and role assignment in backend');
                    }
                } else {
                    analysis.issues.push('❌ No user data in response');
                    analysis.recommendations.push('Check backend response format');
                }
            } else if (data && data.error) {
                analysis.issues.push(`❌ Authentication error: ${data.error}`);
                analysis.recommendations.push('Check PIN validity and backend configuration');
            } else {
                analysis.issues.push('❌ No token or error in response');
                analysis.recommendations.push('Check backend response format and authentication logic');
            }
            
            return analysis;
        }
        
        function displayResult(result) {
            const resultsDiv = document.getElementById('results');
            
            const resultElement = document.createElement('div');
            resultElement.className = 'result';
            resultElement.innerHTML = `
<strong>🔍 Authentication Test Result</strong>
<strong>Timestamp:</strong> ${result.timestamp}
<strong>PIN:</strong> ${result.pin}

<strong>📡 REQUEST:</strong>
URL: ${result.request?.url || 'N/A'}
Method: ${result.request?.method || 'N/A'}
Body: ${JSON.stringify(result.request?.body || {}, null, 2)}

<strong>📦 RESPONSE:</strong>
Status: ${result.response?.status || 'N/A'} ${result.response?.statusText || ''}
OK: ${result.response?.ok || false}
Response Time: ${result.responseTime || 'N/A'}ms

<strong>📄 Response Data:</strong>
${JSON.stringify(result.response?.data || result.error || {}, null, 2)}

<strong>🔍 ANALYSIS:</strong>
Success: ${result.analysis?.success || false}
Issues:
${(result.analysis?.issues || []).map(issue => `  • ${issue}`).join('\n')}

${result.analysis?.recommendations ? `Recommendations:
${result.analysis.recommendations.map(rec => `  • ${rec}`).join('\n')}` : ''}

${'='.repeat(80)}
            `;
            
            resultsDiv.appendChild(resultElement);
            resultElement.scrollIntoView({ behavior: 'smooth' });
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }
        
        // Auto-test on page load
        window.addEventListener('load', () => {
            console.log('🚀 Debug page loaded, ready for testing');
        });
    </script>
</body>
</html>
