import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Settings,
  Zap,
  Cloud,
  CreditCard,
  Mail,
  MessageSquare,
  Truck,
  BarChart3,
  Users,
  Calendar,
  FileText,
  Globe,
  Smartphone,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw
} from 'lucide-react';

interface Integration {
  id: string;
  name: string;
  category: string;
  description: string;
  status: 'connected' | 'disconnected' | 'pending' | 'error';
  icon: React.ReactNode;
  features: string[];
  lastSync?: Date;
  config?: any;
}

export function Phase2DEnterpriseIntegrations() {
  const [integrations, setIntegrations] = useState<Integration[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const mockIntegrations: Integration[] = [
    {
      id: 'stripe',
      name: 'Stripe',
      category: 'payment',
      description: 'Advanced payment processing with global support',
      status: 'connected',
      icon: <CreditCard className="h-6 w-6" />,
      features: ['Credit Cards', 'Digital Wallets', 'Subscriptions', 'Refunds'],
      lastSync: new Date(Date.now() - 300000), // 5 minutes ago
      config: { apiKey: 'sk_test_***', webhooks: true }
    },
    {
      id: 'moneris',
      name: 'Moneris',
      category: 'payment',
      description: 'Canadian payment processing solution',
      status: 'connected',
      icon: <CreditCard className="h-6 w-6" />,
      features: ['Interac', 'Credit Cards', 'Contactless', 'EMV'],
      lastSync: new Date(Date.now() - 600000), // 10 minutes ago
    },
    {
      id: 'quickbooks',
      name: 'QuickBooks',
      category: 'accounting',
      description: 'Automated accounting and financial reporting',
      status: 'pending',
      icon: <FileText className="h-6 w-6" />,
      features: ['Invoicing', 'Expense Tracking', 'Tax Reports', 'Payroll'],
    },
    {
      id: 'mailchimp',
      name: 'Mailchimp',
      category: 'marketing',
      description: 'Email marketing and customer engagement',
      status: 'disconnected',
      icon: <Mail className="h-6 w-6" />,
      features: ['Email Campaigns', 'Customer Segmentation', 'Analytics', 'Automation'],
    },
    {
      id: 'twilio',
      name: 'Twilio',
      category: 'communication',
      description: 'SMS notifications and customer communication',
      status: 'connected',
      icon: <MessageSquare className="h-6 w-6" />,
      features: ['SMS Alerts', 'Order Updates', 'Marketing Messages', 'Two-Factor Auth'],
      lastSync: new Date(Date.now() - 120000), // 2 minutes ago
    },
    {
      id: 'ubereats',
      name: 'Uber Eats',
      category: 'delivery',
      description: 'Food delivery platform integration',
      status: 'connected',
      icon: <Truck className="h-6 w-6" />,
      features: ['Order Sync', 'Menu Management', 'Delivery Tracking', 'Analytics'],
      lastSync: new Date(Date.now() - 180000), // 3 minutes ago
    },
    {
      id: 'doordash',
      name: 'DoorDash',
      category: 'delivery',
      description: 'On-demand delivery service integration',
      status: 'error',
      icon: <Truck className="h-6 w-6" />,
      features: ['Order Management', 'Real-time Tracking', 'Commission Tracking'],
    },
    {
      id: 'google-analytics',
      name: 'Google Analytics',
      category: 'analytics',
      description: 'Advanced web and customer analytics',
      status: 'connected',
      icon: <BarChart3 className="h-6 w-6" />,
      features: ['Customer Insights', 'Conversion Tracking', 'Behavior Analysis', 'Reports'],
      lastSync: new Date(Date.now() - 900000), // 15 minutes ago
    },
    {
      id: 'salesforce',
      name: 'Salesforce',
      category: 'crm',
      description: 'Customer relationship management platform',
      status: 'pending',
      icon: <Users className="h-6 w-6" />,
      features: ['Lead Management', 'Customer Profiles', 'Sales Pipeline', 'Automation'],
    },
    {
      id: 'calendly',
      name: 'Calendly',
      category: 'scheduling',
      description: 'Appointment and reservation scheduling',
      status: 'disconnected',
      icon: <Calendar className="h-6 w-6" />,
      features: ['Table Reservations', 'Staff Scheduling', 'Event Management', 'Reminders'],
    }
  ];

  useEffect(() => {
    // Simulate loading integrations
    setTimeout(() => {
      setIntegrations(mockIntegrations);
      setLoading(false);
    }, 1000);
  }, []);

  const categories = [
    { id: 'all', name: 'All Integrations', count: mockIntegrations.length },
    { id: 'payment', name: 'Payment', count: mockIntegrations.filter(i => i.category === 'payment').length },
    { id: 'accounting', name: 'Accounting', count: mockIntegrations.filter(i => i.category === 'accounting').length },
    { id: 'marketing', name: 'Marketing', count: mockIntegrations.filter(i => i.category === 'marketing').length },
    { id: 'delivery', name: 'Delivery', count: mockIntegrations.filter(i => i.category === 'delivery').length },
    { id: 'analytics', name: 'Analytics', count: mockIntegrations.filter(i => i.category === 'analytics').length },
    { id: 'communication', name: 'Communication', count: mockIntegrations.filter(i => i.category === 'communication').length },
    { id: 'crm', name: 'CRM', count: mockIntegrations.filter(i => i.category === 'crm').length },
    { id: 'scheduling', name: 'Scheduling', count: mockIntegrations.filter(i => i.category === 'scheduling').length },
  ];

  const filteredIntegrations = selectedCategory === 'all' 
    ? integrations 
    : integrations.filter(integration => integration.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'disconnected': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'pending': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error': return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'disconnected': return <AlertCircle className="h-4 w-4 text-gray-400" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-400" />;
    }
  };

  const handleConnect = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, status: 'pending' as const }
        : integration
    ));

    // Simulate connection process
    setTimeout(() => {
      setIntegrations(prev => prev.map(integration => 
        integration.id === integrationId 
          ? { ...integration, status: 'connected' as const, lastSync: new Date() }
          : integration
      ));
    }, 2000);
  };

  const handleDisconnect = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, status: 'disconnected' as const, lastSync: undefined }
        : integration
    ));
  };

  const handleSync = (integrationId: string) => {
    setIntegrations(prev => prev.map(integration => 
      integration.id === integrationId 
        ? { ...integration, lastSync: new Date() }
        : integration
    ));
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Enterprise Integrations</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2D Enterprise Integrations</h2>
          <p className="text-gray-600">Connect your POS system with third-party services and platforms</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Integration Settings
          </Button>
          <Button variant="outline" size="sm">
            <Zap className="h-4 w-4 mr-2" />
            Automation Rules
          </Button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center space-x-2"
          >
            <span>{category.name}</span>
            <Badge variant="secondary" className="ml-2">
              {category.count}
            </Badge>
          </Button>
        ))}
      </div>

      {/* Integration Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Connected</p>
                <p className="text-2xl font-bold text-green-600">
                  {integrations.filter(i => i.status === 'connected').length}
                </p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending</p>
                <p className="text-2xl font-bold text-yellow-600">
                  {integrations.filter(i => i.status === 'pending').length}
                </p>
              </div>
              <Clock className="h-8 w-8 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Errors</p>
                <p className="text-2xl font-bold text-red-600">
                  {integrations.filter(i => i.status === 'error').length}
                </p>
              </div>
              <AlertCircle className="h-8 w-8 text-red-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Available</p>
                <p className="text-2xl font-bold text-gray-600">
                  {integrations.filter(i => i.status === 'disconnected').length}
                </p>
              </div>
              <Cloud className="h-8 w-8 text-gray-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Integrations Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredIntegrations.map((integration) => (
          <Card key={integration.id} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {integration.icon}
                  </div>
                  <div>
                    <CardTitle className="text-lg">{integration.name}</CardTitle>
                    <p className="text-sm text-gray-600 capitalize">{integration.category}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(integration.status)}
                  <Badge className={getStatusColor(integration.status)}>
                    {integration.status}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 text-sm mb-4">{integration.description}</p>
              
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium text-sm text-gray-900 mb-2">Features</h4>
                  <div className="flex flex-wrap gap-1">
                    {integration.features.map((feature, index) => (
                      <Badge key={index} variant="outline" className="text-xs">
                        {feature}
                      </Badge>
                    ))}
                  </div>
                </div>

                {integration.lastSync && (
                  <div className="text-xs text-gray-500">
                    Last sync: {integration.lastSync.toLocaleString()}
                  </div>
                )}

                <div className="flex space-x-2 pt-2">
                  {integration.status === 'connected' ? (
                    <>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleSync(integration.id)}
                        className="flex-1"
                      >
                        <RefreshCw className="h-3 w-3 mr-1" />
                        Sync
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => handleDisconnect(integration.id)}
                        className="flex-1"
                      >
                        Disconnect
                      </Button>
                    </>
                  ) : integration.status === 'pending' ? (
                    <Button size="sm" disabled className="flex-1">
                      <Clock className="h-3 w-3 mr-1" />
                      Connecting...
                    </Button>
                  ) : (
                    <Button
                      size="sm"
                      onClick={() => handleConnect(integration.id)}
                      className="flex-1"
                    >
                      Connect
                    </Button>
                  )}
                  <Button size="sm" variant="outline">
                    <Settings className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
}
