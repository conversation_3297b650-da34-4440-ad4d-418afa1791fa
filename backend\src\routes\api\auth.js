// Authentication Routes for RESTROFLOW
const express = require('express');
const bcrypt = require('bcryptjs');
const jwt = require('jsonwebtoken');
const { pool } = require('../../database/config/connection');
const { authenticateToken, generateToken } = require('../../middleware/auth');

const router = express.Router();
const JWT_SECRET = process.env.JWT_SECRET || 'dev-secret-key-2024';

// Pre-computed bcrypt hashes for development
const DEMO_PINS = {
  '123456': '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO',
  '567890': '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO',
  '1234': '$2a$10$CwTycUXWue0Thq9StjUM0uJ8Z8W5.XVLw9E9P3nfzseOjHyp0gUjO'
};

// Enhanced input validation function
const validateInput = (input, type) => {
  if (!input) return false;

  switch (type) {
    case 'pin':
      return /^\d{4,6}$/.test(input);
    case 'tenant':
      return /^[a-zA-Z0-9-_]{1,50}$/.test(input);
    case 'alphanumeric':
      return /^[a-zA-Z0-9\s-_]{1,100}$/.test(input);
    default:
      return false;
  }
};

// Sanitize input to prevent injection attacks
const sanitizeInput = (input) => {
  if (typeof input !== 'string') return '';
  return input.replace(/[<>'";&\\]/g, '').trim();
};

// Employee login endpoint
router.post('/login', async (req, res) => {
  try {
    const { pin, tenantId } = req.body;

    console.log('🔐 Login attempt:', { pin: pin ? '***' : 'missing', tenantId });

    // Input validation
    if (!pin || !tenantId) {
      return res.status(400).json({
        error: 'PIN and tenant ID are required',
        code: 'MISSING_CREDENTIALS'
      });
    }

    if (!validateInput(pin, 'pin')) {
      return res.status(400).json({
        error: 'Invalid PIN format (4-6 digits required)',
        code: 'INVALID_PIN_FORMAT'
      });
    }

    if (!validateInput(tenantId, 'tenant')) {
      return res.status(400).json({
        error: 'Invalid tenant ID format',
        code: 'INVALID_TENANT_FORMAT'
      });
    }

    // Sanitize inputs
    const sanitizedPin = sanitizeInput(pin);
    const sanitizedTenantId = sanitizeInput(tenantId);

    let employee = null;
    let isValidPin = false;

    try {
      // Try database first
      const employeeQuery = `
        SELECT e.*, t.name as tenant_name, t.status as tenant_status
        FROM employees e
        JOIN tenants t ON e.tenant_id = t.id::text
        WHERE e.pin = $1 AND e.tenant_id = $2 AND e.is_active = true AND t.status = 'active'
      `;
      
      const result = await pool.query(employeeQuery, [sanitizedPin, sanitizedTenantId]);
      
      if (result.rows.length > 0) {
        employee = result.rows[0];
        isValidPin = await bcrypt.compare(sanitizedPin, employee.pin_hash || DEMO_PINS[sanitizedPin]);
      }
    } catch (dbError) {
      console.log('⚠️ Database query failed, using demo mode:', dbError.message);
      
      // Fallback to demo mode
      if (DEMO_PINS[sanitizedPin] && sanitizedTenantId === '1') {
        isValidPin = await bcrypt.compare(sanitizedPin, DEMO_PINS[sanitizedPin]);
        if (isValidPin) {
          employee = {
            id: 1,
            name: 'Demo Employee',
            role: sanitizedPin === '123456' ? 'super_admin' : 'employee',
            tenant_id: '1',
            tenant_name: 'Demo Restaurant',
            tenant_status: 'active',
            location_id: '1',
            is_active: true
          };
        }
      }
    }

    if (!employee || !isValidPin) {
      console.log('❌ Invalid login attempt');
      return res.status(401).json({
        error: 'Invalid PIN or tenant ID',
        code: 'INVALID_CREDENTIALS'
      });
    }

    // Generate JWT token
    const tokenPayload = {
      employeeId: employee.id,
      name: employee.name,
      role: employee.role,
      tenantId: employee.tenant_id,
      locationId: employee.location_id,
      permissions: employee.permissions || [],
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const token = jwt.sign(tokenPayload, JWT_SECRET);

    console.log('✅ Login successful:', {
      employeeId: employee.id,
      role: employee.role,
      tenantId: employee.tenant_id
    });

    res.json({
      success: true,
      message: 'Login successful',
      token,
      user: {
        id: employee.id,
        name: employee.name,
        role: employee.role,
        tenantId: employee.tenant_id,
        tenantName: employee.tenant_name,
        locationId: employee.location_id,
        permissions: employee.permissions || []
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Login error:', error);
    res.status(500).json({
      error: 'Login failed',
      code: 'LOGIN_ERROR',
      message: error.message
    });
  }
});

// Token verification endpoint
router.get('/verify', authenticateToken, (req, res) => {
  res.json({
    valid: true,
    user: {
      employeeId: req.user.employeeId,
      name: req.user.name,
      role: req.user.role,
      tenantId: req.user.tenantId,
      locationId: req.user.locationId,
      permissions: req.user.permissions
    },
    timestamp: new Date().toISOString()
  });
});

// Logout endpoint
router.post('/logout', authenticateToken, (req, res) => {
  // In a production environment, you might want to blacklist the token
  console.log(`👋 User ${req.user.employeeId} logged out`);
  
  res.json({
    success: true,
    message: 'Logged out successfully',
    timestamp: new Date().toISOString()
  });
});

// Refresh token endpoint
router.post('/refresh', authenticateToken, (req, res) => {
  try {
    // Generate new token with extended expiry
    const tokenPayload = {
      employeeId: req.user.employeeId,
      name: req.user.name,
      role: req.user.role,
      tenantId: req.user.tenantId,
      locationId: req.user.locationId,
      permissions: req.user.permissions,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    };

    const newToken = jwt.sign(tokenPayload, JWT_SECRET);

    res.json({
      success: true,
      token: newToken,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('💥 Token refresh error:', error);
    res.status(500).json({
      error: 'Token refresh failed',
      code: 'REFRESH_ERROR'
    });
  }
});

module.exports = router;
