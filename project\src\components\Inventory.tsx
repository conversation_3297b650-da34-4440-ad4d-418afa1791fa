import React, { useState, useEffect } from 'react';
import { Package, AlertTriangle, XCircle, DollarSign, Plus, Edit, Trash2, Download, Search } from 'lucide-react';
import { InventoryItem, InventoryStats } from '../types/inventory';

const API_URL = 'http://localhost:4000';

const Inventory: React.FC = () => {
  const [inventoryData, setInventoryData] = useState<InventoryItem[]>([]);
  const [filteredData, setFilteredData] = useState<InventoryItem[]>([]);
  const [stats, setStats] = useState<InventoryStats>({
    totalItems: 0,
    lowStockCount: 0,
    outOfStockCount: 0,
    totalValue: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState('all');
  const [showAddModal, setShowAddModal] = useState(false);
  const [newItem, setNewItem] = useState({
    name: '',
    category: 'beer',
    currentStock: 0,
    minThreshold: 0,
    unitCost: 0,
    supplier: ''
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch inventory data from API
  useEffect(() => {
    fetchInventoryData();
  }, []);

  const fetchInventoryData = async () => {
    try {
      setLoading(true);
      const response = await fetch(`${API_URL}/products`);
      if (!response.ok) throw new Error('Failed to fetch inventory data');
      
      const data = await response.json();
      const inventoryItems: InventoryItem[] = data.map((item: any) => ({
        id: item.id,
        name: item.name,
        category: item.category,
        currentStock: item.in_stock ? 1 : 0, // Convert boolean to number
        minThreshold: 5, // Default value
        unitCost: parseFloat(item.price),
        status: item.in_stock ? 'good' : 'out',
        supplier: '',
        lastUpdated: item.created_at
      }));

      setInventoryData(inventoryItems);
      setFilteredData(inventoryItems);
      updateStats(inventoryItems);
      setError(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Error fetching inventory:', err);
    } finally {
      setLoading(false);
    }
  };

  const updateStats = (data: InventoryItem[]) => {
    const totalItems = data.length;
    const lowStockCount = data.filter(item => item.status === 'low').length;
    const outOfStockCount = data.filter(item => item.status === 'out').length;
    const totalValue = data.reduce((sum, item) => sum + (item.currentStock * item.unitCost), 0);

    setStats({
      totalItems,
      lowStockCount,
      outOfStockCount,
      totalValue
    });
  };

  const handleSearch = (term: string) => {
    setSearchTerm(term);
    filterData(term, filterCategory);
  };

  const handleFilter = (category: string) => {
    setFilterCategory(category);
    filterData(searchTerm, category);
  };

  const filterData = (search: string, category: string) => {
    let filtered = inventoryData;

    if (search) {
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(search.toLowerCase()) ||
        item.category.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (category !== 'all') {
      if (category === 'low') {
        filtered = filtered.filter(item => item.status === 'low');
      } else if (category === 'out') {
        filtered = filtered.filter(item => item.status === 'out');
      } else {
        filtered = filtered.filter(item =>
          item.category.toLowerCase() === category.toLowerCase()
        );
      }
    }

    setFilteredData(filtered);
  };

  const addNewItem = async () => {
    try {
      const response = await fetch(`${API_URL}/products`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: newItem.name,
          price: newItem.unitCost,
          category: newItem.category,
          description: '',
          inStock: newItem.currentStock > 0
        })
      });

      if (!response.ok) throw new Error('Failed to add item');

      await fetchInventoryData(); // Refresh the data
      setShowAddModal(false);
      setNewItem({
        name: '',
        category: 'beer',
        currentStock: 0,
        minThreshold: 0,
        unitCost: 0,
        supplier: ''
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add item');
      console.error('Error adding item:', err);
    }
  };

  const deleteItem = async (itemId: string) => {
    if (!confirm('Are you sure you want to delete this item?')) return;

    try {
      const response = await fetch(`${API_URL}/products/${itemId}`, {
        method: 'DELETE'
      });

      if (!response.ok) throw new Error('Failed to delete item');

      await fetchInventoryData(); // Refresh the data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete item');
      console.error('Error deleting item:', err);
    }
  };

  const adjustStock = async (itemId: string) => {
    const item = inventoryData.find(i => i.id === itemId);
    if (!item) return;

    const newStatus = !item.currentStock; // Toggle in_stock status
    
    try {
      const response = await fetch(`${API_URL}/products/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          name: item.name,
          price: item.unitCost,
          category: item.category,
          inStock: newStatus
        })
      });

      if (!response.ok) throw new Error('Failed to update stock');

      await fetchInventoryData(); // Refresh the data
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update stock');
      console.error('Error updating stock:', err);
    }
  };

  const exportReport = () => {
    const headers = ['Item', 'Category', 'Current Stock', 'Min Threshold', 'Unit Cost', 'Total Value', 'Status', 'Supplier'];
    const csvContent = [
      headers.join(','),
      ...inventoryData.map(item => [
        item.name,
        item.category,
        item.currentStock,
        item.minThreshold,
        item.unitCost,
        (item.currentStock * item.unitCost).toFixed(2),
        item.status === 'good' ? 'In Stock' : item.status === 'low' ? 'Low Stock' : 'Out of Stock',
        item.supplier || ''
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `inventory-report-${new Date().toISOString().split('T')[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center space-y-4">
          <svg className="animate-spin h-8 w-8 text-purple-500" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
          </svg>
          <p className="text-gray-400">Loading inventory...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 bg-gray-900 min-h-full">
      {error && (
        <div className="mb-4 p-4 bg-red-900 text-white rounded-lg">
          {error}
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold text-white">Inventory Dashboard</h2>
        <div className="flex space-x-3">
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <Plus className="h-4 w-4" />
            <span>Add Item</span>
          </button>
          <button
            onClick={exportReport}
            className="bg-green-600 hover:bg-green-500 text-white px-4 py-2 rounded-md flex items-center space-x-2"
          >
            <Download className="h-4 w-4" />
            <span>Export Report</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-4 gap-6 mb-6">
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Items</p>
              <p className="text-2xl font-bold text-white">{stats.totalItems}</p>
            </div>
            <Package className="h-8 w-8 text-blue-400" />
          </div>
        </div>
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Low Stock Alerts</p>
              <p className="text-2xl font-bold text-red-400">{stats.lowStockCount}</p>
            </div>
            <AlertTriangle className="h-8 w-8 text-red-400" />
          </div>
        </div>
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Out of Stock</p>
              <p className="text-2xl font-bold text-orange-400">{stats.outOfStockCount}</p>
            </div>
            <XCircle className="h-8 w-8 text-orange-400" />
          </div>
        </div>
        <div className="bg-gray-800 p-4 rounded-lg">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-gray-400 text-sm">Total Value</p>
              <p className="text-2xl font-bold text-green-400">${stats.totalValue.toFixed(2)}</p>
            </div>
            <DollarSign className="h-8 w-8 text-green-400" />
          </div>
        </div>
      </div>

      {/* Inventory Table */}
      <div className="bg-gray-800 rounded-lg overflow-hidden">
        <div className="p-4 border-b border-gray-700">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-semibold text-white">Inventory Items</h3>
            <div className="flex space-x-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search items..."
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                  className="bg-gray-700 text-white pl-10 pr-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <select
                value={filterCategory}
                onChange={(e) => handleFilter(e.target.value)}
                className="bg-gray-700 text-white px-3 py-2 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
              >
                <option value="all">All Items</option>
                <option value="low">Low Stock</option>
                <option value="out">Out of Stock</option>
                <option value="beer">Beer</option>
                <option value="wine">Wine</option>
                <option value="spirits">Spirits</option>
                <option value="food">Food</option>
              </select>
            </div>
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="w-full">
            <thead className="bg-gray-700">
              <tr>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Item</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Category</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Status</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Unit Cost</th>
                <th className="px-4 py-3 text-left text-sm font-medium text-gray-300">Actions</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {filteredData.map((item) => (
                <tr key={item.id} className="hover:bg-gray-700">
                  <td className="px-4 py-3 text-white">{item.name}</td>
                  <td className="px-4 py-3 text-gray-300">{item.category}</td>
                  <td className="px-4 py-3 text-white">
                    {item.currentStock > 0 ? 'In Stock' : 'Out of Stock'}
                  </td>
                  <td className="px-4 py-3 text-white">${item.unitCost.toFixed(2)}</td>
                  <td className="px-4 py-3">
                    <div className="flex space-x-2">
                      <button
                        onClick={() => adjustStock(item.id)}
                        className="text-yellow-400 hover:text-yellow-300"
                        title="Toggle Stock Status"
                      >
                        <Package className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => deleteItem(item.id)}
                        className="text-red-400 hover:text-red-300"
                        title="Delete Item"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Add Item Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-white mb-4">Add New Item</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Name
                </label>
                <input
                  type="text"
                  value={newItem.name}
                  onChange={(e) => setNewItem({...newItem, name: e.target.value})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Category
                </label>
                <select
                  value={newItem.category}
                  onChange={(e) => setNewItem({...newItem, category: e.target.value})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md"
                >
                  <option value="beer">Beer</option>
                  <option value="wine">Wine</option>
                  <option value="spirits">Spirits</option>
                  <option value="food">Food</option>
                </select>
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Unit Cost
                </label>
                <input
                  type="number"
                  value={newItem.unitCost}
                  onChange={(e) => setNewItem({...newItem, unitCost: parseFloat(e.target.value)})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-300 mb-1">
                  Initial Stock
                </label>
                <input
                  type="number"
                  value={newItem.currentStock}
                  onChange={(e) => setNewItem({...newItem, currentStock: parseInt(e.target.value)})}
                  className="w-full bg-gray-700 text-white px-3 py-2 rounded-md"
                />
              </div>
            </div>
            <div className="flex justify-end space-x-2 mt-6">
              <button
                onClick={() => setShowAddModal(false)}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded-md"
              >
                Cancel
              </button>
              <button
                onClick={addNewItem}
                className="bg-blue-600 hover:bg-blue-500 text-white px-4 py-2 rounded-md"
              >
                Add Item
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default Inventory;
