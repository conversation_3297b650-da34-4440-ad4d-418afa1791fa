const puppeteer = require('puppeteer');

async function testFrontendInterfaces() {
    console.log('🚀 Starting Frontend Interface Testing...\n');
    
    const browser = await puppeteer.launch({ 
        headless: false, 
        defaultViewport: null,
        args: ['--start-maximized']
    });
    
    let testResults = {
        loginInterface: false,
        posInterface: false,
        superAdminInterface: false,
        authentication: false
    };
    
    try {
        // Test 1: Login Interface
        console.log('📱 Testing Login Interface...');
        const loginPage = await browser.newPage();
        await loginPage.goto('http://localhost:3000/../login.html');
        await loginPage.waitForTimeout(2000);
        
        // Check if login form exists
        const loginForm = await loginPage.$('input[type="password"], input[placeholder*="PIN"]');
        if (loginForm) {
            console.log('✅ Login interface loaded successfully');
            testResults.loginInterface = true;
            
            // Test PIN authentication
            await loginPage.type('input[type="password"], input[placeholder*="PIN"]', '1234');
            await loginPage.click('button[type="submit"], button:contains("Login")');
            await loginPage.waitForTimeout(2000);
            
            // Check if authentication worked
            const currentUrl = loginPage.url();
            if (currentUrl.includes('pos') || currentUrl !== 'http://localhost:3000/../login.html') {
                console.log('✅ Authentication working');
                testResults.authentication = true;
            }
        } else {
            console.log('❌ Login form not found');
        }
        
        // Test 2: POS Interface
        console.log('\n🏪 Testing POS Interface...');
        const posPage = await browser.newPage();
        await posPage.goto('http://localhost:3000/../standalone-pos.html');
        await posPage.waitForTimeout(3000);
        
        // Check for POS elements
        const posElements = await posPage.evaluate(() => {
            const cart = document.querySelector('[class*="cart"], [id*="cart"], .shopping-cart');
            const products = document.querySelector('[class*="product"], [class*="menu"], .product-grid');
            const total = document.querySelector('[class*="total"], [id*="total"]');
            const checkout = document.querySelector('[class*="checkout"], [class*="payment"], button:contains("Pay")');
            
            return {
                hasCart: !!cart,
                hasProducts: !!products,
                hasTotal: !!total,
                hasCheckout: !!checkout
            };
        });
        
        if (posElements.hasCart && posElements.hasProducts) {
            console.log('✅ POS interface loaded successfully');
            console.log(`   📦 Cart: ${posElements.hasCart ? '✅' : '❌'}`);
            console.log(`   🛍️  Products: ${posElements.hasProducts ? '✅' : '❌'}`);
            console.log(`   💰 Total: ${posElements.hasTotal ? '✅' : '❌'}`);
            console.log(`   💳 Checkout: ${posElements.hasCheckout ? '✅' : '❌'}`);
            testResults.posInterface = true;
        } else {
            console.log('❌ POS interface elements not found');
        }
        
        // Test 3: Super Admin Interface
        console.log('\n👑 Testing Super Admin Interface...');
        const adminPage = await browser.newPage();
        await adminPage.goto('http://localhost:3000/super-admin.html');
        await adminPage.waitForTimeout(3000);
        
        // Check for Super Admin elements
        const adminElements = await adminPage.evaluate(() => {
            const loginForm = document.querySelector('input[type="password"], input[placeholder*="PIN"]');
            const dashboard = document.querySelector('[class*="dashboard"], [class*="admin"], .admin-panel');
            const navigation = document.querySelector('nav, [class*="nav"], .navigation');
            const title = document.querySelector('h1, [class*="title"], .page-title');
            
            return {
                hasLoginForm: !!loginForm,
                hasDashboard: !!dashboard,
                hasNavigation: !!navigation,
                hasTitle: !!title,
                titleText: title ? title.textContent : ''
            };
        });
        
        if (adminElements.hasLoginForm || adminElements.hasDashboard) {
            console.log('✅ Super Admin interface loaded successfully');
            console.log(`   🔐 Login Form: ${adminElements.hasLoginForm ? '✅' : '❌'}`);
            console.log(`   📊 Dashboard: ${adminElements.hasDashboard ? '✅' : '❌'}`);
            console.log(`   🧭 Navigation: ${adminElements.hasNavigation ? '✅' : '❌'}`);
            console.log(`   📝 Title: ${adminElements.titleText || 'Not found'}`);
            testResults.superAdminInterface = true;
            
            // Test Super Admin authentication if login form exists
            if (adminElements.hasLoginForm) {
                console.log('\n🔐 Testing Super Admin Authentication...');
                await adminPage.type('input[type="password"], input[placeholder*="PIN"]', '999999');
                await adminPage.click('button[type="submit"], button:contains("Login")');
                await adminPage.waitForTimeout(3000);
                
                // Check if dashboard appeared after login
                const postLoginDashboard = await adminPage.$('[class*="dashboard"], [class*="admin"], .admin-panel');
                if (postLoginDashboard) {
                    console.log('✅ Super Admin authentication working');
                }
            }
        } else {
            console.log('❌ Super Admin interface elements not found');
        }
        
        // Summary
        console.log('\n' + '='.repeat(60));
        console.log('📊 FRONTEND INTERFACE TEST RESULTS');
        console.log('='.repeat(60));
        
        const totalTests = Object.keys(testResults).length;
        const passedTests = Object.values(testResults).filter(result => result).length;
        const successRate = ((passedTests / totalTests) * 100).toFixed(1);
        
        console.log(`Tests Passed: ${passedTests}/${totalTests}`);
        console.log(`Success Rate: ${successRate}%`);
        console.log('');
        
        Object.entries(testResults).forEach(([test, result]) => {
            const status = result ? '✅ PASS' : '❌ FAIL';
            const testName = test.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
            console.log(`${status} - ${testName}`);
        });
        
        if (successRate >= 75) {
            console.log('\n🎉 Frontend interfaces are working well!');
        } else {
            console.log('\n⚠️  Some frontend interfaces need attention.');
        }
        
        console.log('\n✅ Frontend interface testing completed!');
        
    } catch (error) {
        console.error('❌ Error during testing:', error.message);
    } finally {
        await browser.close();
    }
}

// Run the test
testFrontendInterfaces().catch(console.error);
