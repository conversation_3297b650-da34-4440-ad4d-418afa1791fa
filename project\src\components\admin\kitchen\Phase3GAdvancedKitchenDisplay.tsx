// Phase 3G: Advanced Kitchen Display System (KDS)
// Real-time Order Management with AI-Powered Optimization

import React, { useState, useEffect } from 'react';
import {
  Clock,
  ChefHat,
  AlertTriangle,
  CheckCircle,
  Timer,
  Users,
  TrendingUp,
  Zap,
  Target,
  BarChart3,
  Settings,
  Bell,
  Play,
  Pause,
  RotateCcw,
  Star,
  Flame,
  Eye
} from 'lucide-react';

// Enhanced Kitchen Order Interface
interface KitchenOrder {
  id: string;
  orderNumber: number;
  tableNumber?: number;
  customerName?: string;
  items: KitchenOrderItem[];
  status: 'new' | 'preparing' | 'ready' | 'served' | 'delayed';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  timestamp: number;
  estimatedTime: number;
  actualTime?: number;
  assignedChef?: string;
  specialInstructions?: string;
  allergens: string[];
  complexity: number; // 1-10 scale
  aiOptimization: {
    suggestedPrepTime: number;
    confidence: number;
    recommendations: string[];
  };
}

interface KitchenOrderItem {
  id: string;
  name: string;
  quantity: number;
  modifications: string[];
  cookingTime: number;
  station: 'grill' | 'fryer' | 'salad' | 'dessert' | 'beverage';
  status: 'pending' | 'cooking' | 'ready';
  allergens: string[];
}

// Kitchen Performance Metrics
interface KitchenMetrics {
  totalOrders: number;
  completedOrders: number;
  averagePrepTime: number;
  onTimeDelivery: number;
  efficiency: number;
  activeStations: number;
  peakHours: { hour: number; orders: number }[];
  aiOptimizationSavings: number;
}

// Kitchen Staff Interface
interface KitchenStaff {
  id: string;
  name: string;
  role: 'head_chef' | 'line_cook' | 'prep_cook' | 'expediter';
  station: string;
  status: 'active' | 'break' | 'offline';
  ordersCompleted: number;
  averageTime: number;
  efficiency: number;
}

const Phase3GAdvancedKitchenDisplay: React.FC = () => {
  const [orders, setOrders] = useState<KitchenOrder[]>([]);
  const [metrics, setMetrics] = useState<KitchenMetrics | null>(null);
  const [staff, setStaff] = useState<KitchenStaff[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeView, setActiveView] = useState<'orders' | 'metrics' | 'staff' | 'ai'>('orders');
  const [selectedOrder, setSelectedOrder] = useState<KitchenOrder | null>(null);
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Initialize mock data
  useEffect(() => {
    loadKitchenData();
    const interval = setInterval(() => {
      setCurrentTime(new Date());
      if (autoRefresh) {
        loadKitchenData();
      }
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [autoRefresh]);

  const loadKitchenData = () => {
    // Mock kitchen orders with AI optimization
    const mockOrders: KitchenOrder[] = [
      {
        id: 'ord-001',
        orderNumber: 1247,
        tableNumber: 12,
        customerName: 'Johnson Party',
        items: [
          {
            id: 'item-1',
            name: 'Grilled Salmon',
            quantity: 2,
            modifications: ['No sauce', 'Extra lemon'],
            cookingTime: 12,
            station: 'grill',
            status: 'cooking',
            allergens: ['fish']
          },
          {
            id: 'item-2',
            name: 'Caesar Salad',
            quantity: 1,
            modifications: ['Dressing on side'],
            cookingTime: 3,
            station: 'salad',
            status: 'ready',
            allergens: ['dairy', 'gluten']
          }
        ],
        status: 'preparing',
        priority: 'high',
        timestamp: Date.now() - 480000, // 8 minutes ago
        estimatedTime: 15,
        actualTime: 8,
        assignedChef: 'Chef Martinez',
        specialInstructions: 'Customer has fish allergy - please use separate prep area',
        allergens: ['fish', 'dairy', 'gluten'],
        complexity: 7,
        aiOptimization: {
          suggestedPrepTime: 13,
          confidence: 94.2,
          recommendations: [
            'Start salad preparation first',
            'Use grill station 2 for optimal timing',
            'Prepare allergen-free workspace'
          ]
        }
      },
      {
        id: 'ord-002',
        orderNumber: 1248,
        tableNumber: 8,
        customerName: 'Smith Family',
        items: [
          {
            id: 'item-3',
            name: 'Margherita Pizza',
            quantity: 1,
            modifications: ['Extra cheese'],
            cookingTime: 10,
            station: 'grill',
            status: 'pending',
            allergens: ['dairy', 'gluten']
          },
          {
            id: 'item-4',
            name: 'Chicken Wings',
            quantity: 12,
            modifications: ['Buffalo sauce'],
            cookingTime: 8,
            station: 'fryer',
            status: 'pending',
            allergens: []
          }
        ],
        status: 'new',
        priority: 'normal',
        timestamp: Date.now() - 120000, // 2 minutes ago
        estimatedTime: 18,
        assignedChef: 'Chef Rodriguez',
        specialInstructions: '',
        allergens: ['dairy', 'gluten'],
        complexity: 5,
        aiOptimization: {
          suggestedPrepTime: 16,
          confidence: 89.7,
          recommendations: [
            'Start wings first due to fryer availability',
            'Pizza can be prepared simultaneously',
            'Coordinate timing for table delivery'
          ]
        }
      },
      {
        id: 'ord-003',
        orderNumber: 1249,
        tableNumber: 15,
        customerName: 'Davis Couple',
        items: [
          {
            id: 'item-5',
            name: 'Chocolate Lava Cake',
            quantity: 2,
            modifications: ['Vanilla ice cream'],
            cookingTime: 6,
            station: 'dessert',
            status: 'ready',
            allergens: ['dairy', 'eggs', 'gluten']
          }
        ],
        status: 'ready',
        priority: 'low',
        timestamp: Date.now() - 900000, // 15 minutes ago
        estimatedTime: 6,
        actualTime: 6,
        assignedChef: 'Chef Thompson',
        specialInstructions: 'Serve immediately while warm',
        allergens: ['dairy', 'eggs', 'gluten'],
        complexity: 3,
        aiOptimization: {
          suggestedPrepTime: 6,
          confidence: 98.1,
          recommendations: [
            'Perfect timing achieved',
            'Ready for immediate service',
            'Temperature optimal for serving'
          ]
        }
      }
    ];

    setOrders(mockOrders);

    // Mock kitchen metrics
    const mockMetrics: KitchenMetrics = {
      totalOrders: 47,
      completedOrders: 44,
      averagePrepTime: 12.4,
      onTimeDelivery: 94.7,
      efficiency: 89.2,
      activeStations: 5,
      peakHours: [
        { hour: 12, orders: 15 },
        { hour: 13, orders: 18 },
        { hour: 18, orders: 22 },
        { hour: 19, orders: 25 },
        { hour: 20, orders: 19 }
      ],
      aiOptimizationSavings: 18.3
    };

    setMetrics(mockMetrics);

    // Mock kitchen staff
    const mockStaff: KitchenStaff[] = [
      {
        id: 'chef-1',
        name: 'Chef Martinez',
        role: 'head_chef',
        station: 'Grill Station',
        status: 'active',
        ordersCompleted: 12,
        averageTime: 11.2,
        efficiency: 94.5
      },
      {
        id: 'chef-2',
        name: 'Chef Rodriguez',
        role: 'line_cook',
        station: 'Fryer Station',
        status: 'active',
        ordersCompleted: 8,
        averageTime: 9.8,
        efficiency: 87.3
      },
      {
        id: 'chef-3',
        name: 'Chef Thompson',
        role: 'prep_cook',
        station: 'Dessert Station',
        status: 'break',
        ordersCompleted: 6,
        averageTime: 7.5,
        efficiency: 91.2
      }
    ];

    setStaff(mockStaff);
  };

  const getOrderStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-500';
      case 'preparing': return 'bg-yellow-500';
      case 'ready': return 'bg-green-500';
      case 'served': return 'bg-gray-500';
      case 'delayed': return 'bg-red-500';
      default: return 'bg-gray-400';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'border-red-500 bg-red-50';
      case 'high': return 'border-orange-500 bg-orange-50';
      case 'normal': return 'border-blue-500 bg-blue-50';
      case 'low': return 'border-gray-500 bg-gray-50';
      default: return 'border-gray-300 bg-white';
    }
  };

  const getTimeSinceOrder = (timestamp: number) => {
    const minutes = Math.floor((Date.now() - timestamp) / 60000);
    return minutes;
  };

  const updateOrderStatus = (orderId: string, newStatus: KitchenOrder['status']) => {
    setOrders(prev => prev.map(order =>
      order.id === orderId
        ? { ...order, status: newStatus, actualTime: newStatus === 'ready' ? getTimeSinceOrder(order.timestamp) : order.actualTime }
        : order
    ));
  };

  const startOrderPreparation = (orderId: string, chefId: string) => {
    setOrders(prev => prev.map(order =>
      order.id === orderId
        ? { ...order, status: 'preparing', assignedChef: chefId }
        : order
    ));
  };

  return (
    <div className="h-screen flex flex-col bg-gray-900 text-white">
      {/* Header */}
      <div className="bg-gray-800 border-b border-gray-700 p-4">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <ChefHat className="h-8 w-8 text-orange-500" />
            <div>
              <h1 className="text-2xl font-bold">Advanced Kitchen Display System</h1>
              <p className="text-gray-400">
                {currentTime.toLocaleTimeString()} • Phase 3G: AI-Powered Kitchen Management
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex bg-gray-700 rounded-lg p-1">
              {[
                { key: 'orders', label: 'Orders', icon: Clock },
                { key: 'metrics', label: 'Metrics', icon: BarChart3 },
                { key: 'staff', label: 'Staff', icon: Users },
                { key: 'ai', label: 'AI Insights', icon: Zap }
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setActiveView(key as any)}
                  className={`flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeView === key
                      ? 'bg-orange-500 text-white'
                      : 'text-gray-300 hover:text-white hover:bg-gray-600'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span>{label}</span>
                </button>
              ))}
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setSoundEnabled(!soundEnabled)}
                className={`p-2 rounded-lg ${soundEnabled ? 'bg-green-600' : 'bg-gray-600'}`}
              >
                <Bell className="h-4 w-4" />
              </button>
              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`p-2 rounded-lg ${autoRefresh ? 'bg-blue-600' : 'bg-gray-600'}`}
              >
                {autoRefresh ? <Pause className="h-4 w-4" /> : <Play className="h-4 w-4" />}
              </button>
              <button
                onClick={loadKitchenData}
                className="p-2 rounded-lg bg-gray-600 hover:bg-gray-500"
              >
                <RotateCcw className="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {activeView === 'orders' && (
          <div className="h-full p-4">
            {/* Quick Stats */}
            <div className="grid grid-cols-4 gap-4 mb-6">
              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Active Orders</p>
                    <p className="text-2xl font-bold text-blue-400">
                      {orders.filter(o => ['new', 'preparing'].includes(o.status)).length}
                    </p>
                  </div>
                  <Clock className="h-8 w-8 text-blue-400" />
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Ready to Serve</p>
                    <p className="text-2xl font-bold text-green-400">
                      {orders.filter(o => o.status === 'ready').length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-green-400" />
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">Avg Prep Time</p>
                    <p className="text-2xl font-bold text-yellow-400">
                      {metrics?.averagePrepTime.toFixed(1)}m
                    </p>
                  </div>
                  <Timer className="h-8 w-8 text-yellow-400" />
                </div>
              </div>

              <div className="bg-gray-800 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-400 text-sm">AI Optimization</p>
                    <p className="text-2xl font-bold text-purple-400">
                      +{metrics?.aiOptimizationSavings.toFixed(1)}%
                    </p>
                  </div>
                  <Zap className="h-8 w-8 text-purple-400" />
                </div>
              </div>
            </div>

            {/* Orders Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4 h-[calc(100vh-280px)] overflow-y-auto">
              {orders.map((order) => (
                <div
                  key={order.id}
                  className={`bg-gray-800 rounded-lg border-l-4 ${getPriorityColor(order.priority)} p-4 cursor-pointer hover:bg-gray-750 transition-colors`}
                  onClick={() => setSelectedOrder(order)}
                >
                  {/* Order Header */}
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <div className="flex items-center space-x-2">
                        <h3 className="text-lg font-bold">#{order.orderNumber}</h3>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getOrderStatusColor(order.status)} text-white`}>
                          {order.status.toUpperCase()}
                        </span>
                        {order.priority === 'urgent' && <Flame className="h-4 w-4 text-red-500" />}
                        {order.priority === 'high' && <AlertTriangle className="h-4 w-4 text-orange-500" />}
                      </div>
                      <p className="text-gray-400 text-sm">
                        Table {order.tableNumber} • {order.customerName}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-gray-400">
                        {getTimeSinceOrder(order.timestamp)}m ago
                      </p>
                      <p className="text-xs text-gray-500">
                        Est: {order.estimatedTime}m
                      </p>
                    </div>
                  </div>

                  {/* Order Items */}
                  <div className="space-y-2 mb-3">
                    {order.items.map((item) => (
                      <div key={item.id} className="flex justify-between items-center">
                        <div className="flex-1">
                          <p className="text-sm font-medium">{item.quantity}x {item.name}</p>
                          {item.modifications.length > 0 && (
                            <p className="text-xs text-gray-400">
                              {item.modifications.join(', ')}
                            </p>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-xs text-gray-400">{item.station}</span>
                          <span className={`w-2 h-2 rounded-full ${
                            item.status === 'ready' ? 'bg-green-500' :
                            item.status === 'cooking' ? 'bg-yellow-500' : 'bg-gray-500'
                          }`}></span>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* AI Optimization */}
                  <div className="bg-gray-700 rounded-lg p-2 mb-3">
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs text-purple-400 font-medium">AI Optimization</span>
                      <span className="text-xs text-gray-400">
                        {order.aiOptimization.confidence.toFixed(1)}% confidence
                      </span>
                    </div>
                    <p className="text-xs text-gray-300">
                      Suggested: {order.aiOptimization.suggestedPrepTime}m
                    </p>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex space-x-2">
                    {order.status === 'new' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateOrderStatus(order.id, 'preparing');
                        }}
                        className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Start Prep
                      </button>
                    )}
                    {order.status === 'preparing' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateOrderStatus(order.id, 'ready');
                        }}
                        className="flex-1 bg-green-600 hover:bg-green-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Mark Ready
                      </button>
                    )}
                    {order.status === 'ready' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          updateOrderStatus(order.id, 'served');
                        }}
                        className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Mark Served
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedOrder(order);
                      }}
                      className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors"
                    >
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>

                  {/* Allergen Warnings */}
                  {order.allergens.length > 0 && (
                    <div className="mt-2 flex flex-wrap gap-1">
                      {order.allergens.map((allergen) => (
                        <span
                          key={allergen}
                          className="px-2 py-1 bg-red-600 text-white text-xs rounded-full"
                        >
                          {allergen}
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {activeView === 'metrics' && metrics && (
          <div className="h-full p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Performance Overview */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-400" />
                  Kitchen Performance
                </h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Orders Completed</span>
                    <span className="text-xl font-bold text-green-400">
                      {metrics.completedOrders}/{metrics.totalOrders}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Average Prep Time</span>
                    <span className="text-xl font-bold text-blue-400">
                      {metrics.averagePrepTime.toFixed(1)}m
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">On-Time Delivery</span>
                    <span className="text-xl font-bold text-yellow-400">
                      {metrics.onTimeDelivery.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">Kitchen Efficiency</span>
                    <span className="text-xl font-bold text-purple-400">
                      {metrics.efficiency.toFixed(1)}%
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-gray-400">AI Optimization Savings</span>
                    <span className="text-xl font-bold text-orange-400">
                      +{metrics.aiOptimizationSavings.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>

              {/* Peak Hours Chart */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
                  Peak Hours Analysis
                </h3>
                <div className="space-y-3">
                  {metrics.peakHours.map((hour) => (
                    <div key={hour.hour} className="flex items-center">
                      <span className="w-12 text-gray-400 text-sm">
                        {hour.hour}:00
                      </span>
                      <div className="flex-1 mx-3">
                        <div className="bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                            style={{ width: `${(hour.orders / 25) * 100}%` }}
                          ></div>
                        </div>
                      </div>
                      <span className="text-sm font-medium text-white">
                        {hour.orders} orders
                      </span>
                    </div>
                  ))}
                </div>
              </div>

              {/* Station Status */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <Target className="h-5 w-5 mr-2 text-red-400" />
                  Station Status
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {['Grill', 'Fryer', 'Salad', 'Dessert', 'Beverage'].map((station) => (
                    <div key={station} className="bg-gray-700 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{station}</span>
                        <span className="w-2 h-2 bg-green-500 rounded-full"></span>
                      </div>
                      <p className="text-xs text-gray-400">
                        {Math.floor(Math.random() * 5) + 1} active orders
                      </p>
                    </div>
                  ))}
                </div>
              </div>

              {/* Real-time Alerts */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-yellow-400" />
                  Real-time Alerts
                </h3>
                <div className="space-y-3">
                  <div className="bg-yellow-900 border border-yellow-600 rounded-lg p-3">
                    <p className="text-sm font-medium text-yellow-200">
                      Order #1247 approaching time limit
                    </p>
                    <p className="text-xs text-yellow-300">
                      8 minutes elapsed, estimated 15 minutes
                    </p>
                  </div>
                  <div className="bg-blue-900 border border-blue-600 rounded-lg p-3">
                    <p className="text-sm font-medium text-blue-200">
                      Grill station optimal for next order
                    </p>
                    <p className="text-xs text-blue-300">
                      AI recommendation: Start order #1248
                    </p>
                  </div>
                  <div className="bg-green-900 border border-green-600 rounded-lg p-3">
                    <p className="text-sm font-medium text-green-200">
                      Kitchen efficiency above target
                    </p>
                    <p className="text-xs text-green-300">
                      Current: 89.2% (Target: 85%)
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'staff' && (
          <div className="h-full p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {staff.map((member) => (
                <div key={member.id} className="bg-gray-800 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                        <span className="text-white font-bold">
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <h4 className="font-bold">{member.name}</h4>
                        <p className="text-sm text-gray-400 capitalize">
                          {member.role.replace('_', ' ')}
                        </p>
                      </div>
                    </div>
                    <span className={`w-3 h-3 rounded-full ${
                      member.status === 'active' ? 'bg-green-500' :
                      member.status === 'break' ? 'bg-yellow-500' : 'bg-gray-500'
                    }`}></span>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-400">Station</span>
                      <span className="font-medium">{member.station}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Orders Completed</span>
                      <span className="font-medium">{member.ordersCompleted}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Average Time</span>
                      <span className="font-medium">{member.averageTime.toFixed(1)}m</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-400">Efficiency</span>
                      <span className="font-medium text-green-400">
                        {member.efficiency.toFixed(1)}%
                      </span>
                    </div>
                  </div>

                  <div className="mt-4">
                    <div className="flex justify-between text-sm mb-1">
                      <span className="text-gray-400">Performance</span>
                      <span>{member.efficiency.toFixed(0)}%</span>
                    </div>
                    <div className="bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-green-500 to-blue-500 h-2 rounded-full"
                        style={{ width: `${member.efficiency}%` }}
                      ></div>
                    </div>
                  </div>

                  <div className="mt-4 flex space-x-2">
                    <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-lg text-sm transition-colors">
                      Assign Order
                    </button>
                    <button className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg text-sm transition-colors">
                      <Settings className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {activeView === 'ai' && (
          <div className="h-full p-4">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* AI Recommendations */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <Zap className="h-5 w-5 mr-2 text-purple-400" />
                  AI Recommendations
                </h3>
                <div className="space-y-4">
                  <div className="bg-purple-900 border border-purple-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-purple-200">Optimize Grill Station</span>
                      <span className="text-xs text-purple-300">94.2% confidence</span>
                    </div>
                    <p className="text-sm text-purple-300">
                      Start order #1248 now to optimize grill station timing and reduce overall prep time by 2.3 minutes.
                    </p>
                  </div>

                  <div className="bg-blue-900 border border-blue-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-blue-200">Staff Reallocation</span>
                      <span className="text-xs text-blue-300">87.8% confidence</span>
                    </div>
                    <p className="text-sm text-blue-300">
                      Move Chef Rodriguez to salad station for next 30 minutes to handle incoming vegetarian orders.
                    </p>
                  </div>

                  <div className="bg-green-900 border border-green-600 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-green-200">Prep Time Optimization</span>
                      <span className="text-xs text-green-300">91.5% confidence</span>
                    </div>
                    <p className="text-sm text-green-300">
                      Batch prepare salads for next 3 orders to save 4.7 minutes total prep time.
                    </p>
                  </div>
                </div>
              </div>

              {/* Predictive Analytics */}
              <div className="bg-gray-800 rounded-lg p-6">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-400" />
                  Predictive Analytics
                </h3>
                <div className="space-y-4">
                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">Next Hour Orders</span>
                      <span className="font-bold text-blue-400">12-15 orders</span>
                    </div>
                    <div className="bg-gray-700 rounded-full h-2">
                      <div className="bg-blue-500 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">Peak Time Preparation</span>
                      <span className="font-bold text-yellow-400">18:30 - 20:00</span>
                    </div>
                    <p className="text-sm text-gray-300">
                      Recommend pre-preparing 8 salads and 6 desserts before peak hours.
                    </p>
                  </div>

                  <div>
                    <div className="flex justify-between mb-2">
                      <span className="text-gray-400">Efficiency Forecast</span>
                      <span className="font-bold text-green-400">+12.3%</span>
                    </div>
                    <p className="text-sm text-gray-300">
                      Following AI recommendations will improve efficiency by 12.3% over next 2 hours.
                    </p>
                  </div>
                </div>
              </div>

              {/* AI Model Performance */}
              <div className="bg-gray-800 rounded-lg p-6 lg:col-span-2">
                <h3 className="text-lg font-bold mb-4 flex items-center">
                  <Star className="h-5 w-5 mr-2 text-yellow-400" />
                  AI Model Performance
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Prep Time Prediction</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-green-400">94.2%</span>
                      <span className="text-sm text-gray-400">Accuracy</span>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      Based on 1,247 predictions this week
                    </p>
                  </div>

                  <div className="bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Order Prioritization</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-blue-400">89.7%</span>
                      <span className="text-sm text-gray-400">Accuracy</span>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      Optimal order sequencing predictions
                    </p>
                  </div>

                  <div className="bg-gray-700 rounded-lg p-4">
                    <h4 className="font-medium mb-2">Staff Optimization</h4>
                    <div className="flex items-center justify-between">
                      <span className="text-2xl font-bold text-purple-400">91.8%</span>
                      <span className="text-sm text-gray-400">Accuracy</span>
                    </div>
                    <p className="text-xs text-gray-400 mt-1">
                      Staff allocation recommendations
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3GAdvancedKitchenDisplay;