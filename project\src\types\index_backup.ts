// Type definitions for the POS system

export interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  description?: string;
  image?: string;
  inStock: boolean;
}

export interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  notes?: string;
  modifiers?: string[];
}

export interface Order {
  id: string;
  items: OrderItem[];
  tabName?: string;
  tableId?: string;
  timestamp: number;
  status: 'open' | 'paid' | 'canceled' | 'kitchen' | 'ready' | 'served';
  paymentMethod?: 'cash' | 'card' | 'mobile';
  total: number;
  subtotal: number;
  tax: number;
  tip?: number;
  employee_id?: string;
  kitchenNotes?: string;
  orderNumber?: number;
}

export interface Employee {
  id?: string; // Optional for creation, required after saved
  name: string;
  pin?: string; // Optional when returning from API
  role: 'admin' | 'manager' | 'bartender' | 'server' | 'kitchen';
  created_at?: string;
  phone?: string;
  email?: string;
  hourlyRate?: number;
}

export interface SystemConfig {
  tax_rate: number;
  receipt_header?: string;
  receipt_footer?: string;
  business_name?: string;
  business_address?: string;
  business_phone?: string;
  theme_primary_color?: string;
  theme_secondary_color?: string;
}

// Floor Layout Types
export interface Table {
  id: string;
  number: number;
  seats: number;
  x: number;
  y: number;
  width: number;
  height: number;
  shape: 'rectangle' | 'circle';
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning';
  currentOrderId?: string;
  reservationTime?: number;
}

export interface FloorLayout {
  id: string;
  name: string;
  tables: Table[];
  width: number;
  height: number;
}

// Staff Scheduling Types
export interface Shift {
  id: string;
  employeeId: string;
  employeeName: string;
  date: string;
  startTime: string;
  endTime: string;
  position: string;
  status: 'scheduled' | 'confirmed' | 'completed' | 'no-show';
  notes?: string;
}

export interface Schedule {
  id: string;
  weekStartDate: string;
  shifts: Shift[];
  published: boolean;
  createdBy: string;
}

// Loyalty Program Types
export interface Customer {
  id: string;
  name: string;
  phone: string;
  email?: string;
  points: number;
  totalSpent: number;
  visits: number;
  joinDate: string;
  lastVisit?: string;
  tier: 'bronze' | 'silver' | 'gold' | 'platinum';
}

export interface LoyaltyReward {
  id: string;
  name: string;
  description: string;
  pointsCost: number;
  category: 'discount' | 'free-item' | 'upgrade';
  value: number;
  active: boolean;
}

export interface LoyaltyTransaction {
  id: string;
  customerId: string;
  orderId?: string;
  type: 'earned' | 'redeemed';
  points: number;
  description: string;
  timestamp: number;
}

// Kitchen Display Types
export interface KitchenOrder {
  id: string;
  orderNumber: number;
  tableNumber?: number;
  items: OrderItem[];
  status: 'new' | 'preparing' | 'ready' | 'served';
  timestamp: number;
  estimatedTime?: number;
  priority: 'low' | 'normal' | 'high';
  notes?: string;
  assignedTo?: string;
  startedAt?: number;
  readyAt?: number;
  servedAt?: number;
  preparationTime?: number;
  totalTime?: number;
}

// Kitchen Performance Metrics
export interface KitchenMetrics {
  totalOrders: number;
  completedOrders: number;
  averagePreparationTime: number;
  averageTotalTime: number;
  ordersInProgress: number;
  ordersReady: number;
  ordersOverdue: number;
  efficiency: number;
  peakHours: { hour: number; count: number }[];
}

// Kitchen Filter Options
export interface KitchenFilters {
  status?: KitchenOrder['status'][];
  priority?: KitchenOrder['priority'][];
  assignedTo?: string[];
  timeRange?: {
    start: number;
    end: number;
  };
  searchTerm?: string;
  tableNumber?: number;
  showCompleted?: boolean;
}

// Kitchen Settings
export interface KitchenSettings {
  audioEnabled: boolean;
  audioVolume: number;
  autoRefresh: boolean;
  refreshInterval: number;
  showMetrics: boolean;
  compactView: boolean;
  maxOrdersPerColumn: number;
  overdueThreshold: number; // minutes
  priorityColors: {
    low: string;
    normal: string;
    high: string;
  };
}

// Kitchen Staff Assignment
export interface KitchenStaff {
  id: string;
  name: string;
  role: 'chef' | 'cook' | 'prep';
  isActive: boolean;
  currentOrders: string[];
  maxOrders: number;
  efficiency: number;
}

export interface AppState {
  products: Product[];
  orders: Order[];
  currentOrder: Order | null;
  employees: Employee[];
  currentEmployee: Employee | null;
  isAuthenticated: boolean;
  categories: Category[];
  systemConfig: SystemConfig;
  floorLayout: FloorLayout | null;
  tables: Table[];
  schedules: Schedule[];
  customers: Customer[];
  loyaltyRewards: LoyaltyReward[];
  kitchenOrders: KitchenOrder[];
  selectedTable: Table | null;
  kitchenMetrics: KitchenMetrics | null;
  kitchenSettings: KitchenSettings;
  kitchenStaff: KitchenStaff[];
}

// Allow dynamic categories
export type Category = string;

export type AppAction = 
  | { type: 'ADD_PRODUCT_TO_ORDER'; payload: Product }
  | { type: 'REMOVE_ITEM_FROM_ORDER'; payload: string }
  | { type: 'UPDATE_ITEM_QUANTITY'; payload: { id: string; quantity: number } }
  | { type: 'CLEAR_CURRENT_ORDER' }
  | { type: 'SET_CURRENT_ORDER'; payload: Order }
  | { type: 'COMPLETE_ORDER'; payload: { paymentMethod: 'cash' | 'card' | 'mobile'; tip?: number } }
  | { type: 'SET_TAB_NAME'; payload: string }
  | { type: 'LOGIN'; payload: Employee }
  | { type: 'LOGOUT' }
  | { type: 'SET_PRODUCTS'; payload: Product[] }
  | { type: 'ADD_PRODUCT'; payload: Product }
  | { type: 'UPDATE_PRODUCT'; payload: Product }
  | { type: 'DELETE_PRODUCT'; payload: string }
  | { type: 'SET_CATEGORIES'; payload: Category[] }
  | { type: 'ADD_CATEGORY'; payload: Category }
  | { type: 'SET_EMPLOYEES'; payload: Employee[] }
  | { type: 'ADD_EMPLOYEE'; payload: Employee }
  | { type: 'UPDATE_EMPLOYEE'; payload: Employee }
  | { type: 'DELETE_EMPLOYEE'; payload: string }
  | { type: 'SET_ORDERS'; payload: Order[] }
  | { type: 'UPDATE_SYSTEM_CONFIG'; payload: Partial<SystemConfig> }
  | { type: 'SET_FLOOR_LAYOUT'; payload: FloorLayout }
  | { type: 'SET_TABLES'; payload: Table[] }
  | { type: 'UPDATE_TABLE_STATUS'; payload: { tableId: string; status: Table['status']; orderId?: string } }
  | { type: 'SELECT_TABLE'; payload: Table | null }
  | { type: 'SET_SCHEDULES'; payload: Schedule[] }
  | { type: 'ADD_SCHEDULE'; payload: Schedule }
  | { type: 'UPDATE_SCHEDULE'; payload: Schedule }
  | { type: 'SET_CUSTOMERS'; payload: Customer[] }
  | { type: 'ADD_CUSTOMER'; payload: Customer }
  | { type: 'UPDATE_CUSTOMER'; payload: Customer }
  | { type: 'SET_LOYALTY_REWARDS'; payload: LoyaltyReward[] }
  | { type: 'SET_KITCHEN_ORDERS'; payload: KitchenOrder[] }
  | { type: 'ADD_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'UPDATE_KITCHEN_ORDER'; payload: KitchenOrder }
  | { type: 'SEND_ORDER_TO_KITCHEN'; payload: string };

export type AppContextType = {
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  addProduct: (product: Product) => Promise<void>;
  updateProduct: (product: Product) => Promise<void>;
  deleteProduct: (productId: string) => Promise<void>;
  addCategory: (category: Category) => Promise<void>;
  addEmployee: (employee: Employee) => Promise<void>;
  updateEmployee: (employee: Employee) => Promise<void>;
  deleteEmployee: (employeeId: string) => Promise<void>;
  updateSettings: (settings: Partial<SystemConfig>) => Promise<void>;
  fetchEmployees: () => Promise<Employee[]>;
  fetchSettings: () => Promise<void>;
  validateEmployeePin: (pin: string) => Promise<Employee | null>;
  fetchProducts: () => Promise<Product[]>;
  fetchCategories: () => Promise<Category[]>;
  importProducts: (file: File) => Promise<{ success: any[]; errors: any[] }>;
  exportProducts: () => Promise<void>;
  downloadTemplate: () => Promise<void>;
  // Floor Layout functions
  fetchFloorLayout: () => Promise<FloorLayout | null>;
  updateTableStatus: (tableId: string, status: Table['status'], orderId?: string) => Promise<void>;
  // Scheduling functions
  fetchSchedules: () => Promise<Schedule[]>;
  addSchedule: (schedule: Schedule) => Promise<void>;
  updateSchedule: (schedule: Schedule) => Promise<void>;
  // Loyalty functions
  fetchCustomers: () => Promise<Customer[]>;
  addCustomer: (customer: Customer) => Promise<void>;
  updateCustomer: (customer: Customer) => Promise<void>;
  fetchLoyaltyRewards: () => Promise<LoyaltyReward[]>;
  // Kitchen functions
  fetchKitchenOrders: () => Promise<KitchenOrder[]>;
  sendOrderToKitchen: (orderId: string) => Promise<void>;
  updateKitchenOrderStatus: (orderId: string, status: KitchenOrder['status']) => Promise<void>;
  // Enhanced Kitchen functions
  fetchKitchenMetrics: () => Promise<KitchenMetrics>;
  updateKitchenSettings: (settings: Partial<KitchenSettings>) => Promise<void>;
  fetchKitchenStaff: () => Promise<KitchenStaff[]>;
  assignOrderToStaff: (orderId: string, staffId: string) => Promise<void>;
  updateKitchenOrder: (orderId: string, updates: Partial<KitchenOrder>) => Promise<void>;
  bulkUpdateKitchenOrders: (orderIds: string[], status: KitchenOrder['status']) => Promise<void>;
};
