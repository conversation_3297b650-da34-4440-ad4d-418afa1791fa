-- RESTROFLOW DATABASE OPTIMIZATION SCRIPT
-- This script fixes database performance issues and schema problems

-- Fix the global_performance_metrics table constraint issue
ALTER TABLE global_performance_metrics 
ALTER COLUMN country_code SET DEFAULT 'GLOBAL';

UPDATE global_performance_metrics 
SET country_code = 'GLOBAL' 
WHERE country_code IS NULL;

ALTER TABLE global_performance_metrics 
ALTER COLUMN country_code SET NOT NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_global_performance_metrics_date 
ON global_performance_metrics(date);

CREATE INDEX IF NOT EXISTS idx_global_performance_metrics_currency 
ON global_performance_metrics(base_currency);

CREATE INDEX IF NOT EXISTS idx_global_performance_metrics_tenant 
ON global_performance_metrics(tenant_id);

-- Optimize exchange rate tables
CREATE INDEX IF NOT EXISTS idx_live_exchange_rates_pair 
ON live_exchange_rates(from_currency, to_currency);

CREATE INDEX IF NOT EXISTS idx_live_exchange_rates_timestamp 
ON live_exchange_rates(timestamp);

-- Add connection pooling optimization
-- Increase connection limits if needed
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- Optimize authentication tables
CREATE INDEX IF NOT EXISTS idx_employees_pin 
ON employees(pin) WHERE pin IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_employees_tenant 
ON employees(tenant_id);

CREATE INDEX IF NOT EXISTS idx_employees_role 
ON employees(role);

-- Clean up old data to reduce memory usage
DELETE FROM live_exchange_rates 
WHERE timestamp < NOW() - INTERVAL '7 days';

DELETE FROM global_performance_metrics 
WHERE date < CURRENT_DATE - INTERVAL '30 days';

-- Vacuum and analyze tables for better performance
VACUUM ANALYZE global_performance_metrics;
VACUUM ANALYZE live_exchange_rates;
VACUUM ANALYZE employees;
VACUUM ANALYZE tenants;
VACUUM ANALYZE products;
VACUUM ANALYZE orders;

-- Create materialized view for frequently accessed data
CREATE MATERIALIZED VIEW IF NOT EXISTS current_exchange_rates AS
SELECT DISTINCT ON (from_currency, to_currency)
    from_currency,
    to_currency,
    rate,
    timestamp
FROM live_exchange_rates
ORDER BY from_currency, to_currency, timestamp DESC;

CREATE INDEX IF NOT EXISTS idx_current_exchange_rates_pair
ON current_exchange_rates(from_currency, to_currency);

-- Refresh the materialized view
REFRESH MATERIALIZED VIEW current_exchange_rates;

-- Add table partitioning for large tables (if needed)
-- This is commented out as it requires careful planning
/*
-- Partition global_performance_metrics by date
CREATE TABLE global_performance_metrics_2024 PARTITION OF global_performance_metrics
FOR VALUES FROM ('2024-01-01') TO ('2025-01-01');

CREATE TABLE global_performance_metrics_2025 PARTITION OF global_performance_metrics
FOR VALUES FROM ('2025-01-01') TO ('2026-01-01');
*/

-- Add database monitoring views
CREATE OR REPLACE VIEW database_performance AS
SELECT 
    schemaname,
    tablename,
    attname,
    n_distinct,
    correlation
FROM pg_stats 
WHERE schemaname = 'public'
ORDER BY tablename, attname;

CREATE OR REPLACE VIEW active_connections AS
SELECT 
    pid,
    usename,
    application_name,
    client_addr,
    state,
    query_start,
    state_change
FROM pg_stat_activity 
WHERE state = 'active';

-- Add function to monitor table sizes
CREATE OR REPLACE FUNCTION get_table_sizes()
RETURNS TABLE(
    table_name text,
    size_pretty text,
    size_bytes bigint
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        t.table_name::text,
        pg_size_pretty(pg_total_relation_size(quote_ident(t.table_name)))::text,
        pg_total_relation_size(quote_ident(t.table_name))
    FROM information_schema.tables t
    WHERE t.table_schema = 'public'
    AND t.table_type = 'BASE TABLE'
    ORDER BY pg_total_relation_size(quote_ident(t.table_name)) DESC;
END;
$$ LANGUAGE plpgsql;

-- Create function to clean up old data automatically
CREATE OR REPLACE FUNCTION cleanup_old_data()
RETURNS void AS $$
BEGIN
    -- Clean up old exchange rate data (keep last 7 days)
    DELETE FROM live_exchange_rates 
    WHERE timestamp < NOW() - INTERVAL '7 days';
    
    -- Clean up old performance metrics (keep last 30 days)
    DELETE FROM global_performance_metrics 
    WHERE date < CURRENT_DATE - INTERVAL '30 days';
    
    -- Refresh materialized view
    REFRESH MATERIALIZED VIEW current_exchange_rates;
    
    -- Log cleanup
    INSERT INTO system_logs (level, message, timestamp)
    VALUES ('INFO', 'Automated data cleanup completed', NOW());
    
EXCEPTION WHEN OTHERS THEN
    -- Log error
    INSERT INTO system_logs (level, message, timestamp)
    VALUES ('ERROR', 'Data cleanup failed: ' || SQLERRM, NOW());
END;
$$ LANGUAGE plpgsql;

-- Create system logs table if it doesn't exist
CREATE TABLE IF NOT EXISTS system_logs (
    id SERIAL PRIMARY KEY,
    level VARCHAR(10) NOT NULL,
    message TEXT NOT NULL,
    timestamp TIMESTAMP DEFAULT NOW()
);

-- Schedule automatic cleanup (requires pg_cron extension)
-- SELECT cron.schedule('cleanup-old-data', '0 2 * * *', 'SELECT cleanup_old_data();');

-- Add constraints to prevent data integrity issues
ALTER TABLE live_exchange_rates 
ADD CONSTRAINT check_positive_rate 
CHECK (rate > 0);

ALTER TABLE global_performance_metrics 
ADD CONSTRAINT check_valid_date 
CHECK (date <= CURRENT_DATE);

-- Optimize configuration for better performance
-- These settings should be adjusted based on available system resources
ALTER SYSTEM SET checkpoint_completion_target = 0.9;
ALTER SYSTEM SET wal_buffers = '16MB';
ALTER SYSTEM SET default_statistics_target = 100;
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Reload configuration
SELECT pg_reload_conf();

-- Final optimization: update table statistics
ANALYZE;

-- Create performance monitoring function
CREATE OR REPLACE FUNCTION get_performance_stats()
RETURNS TABLE(
    metric text,
    value text
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        'Active Connections'::text,
        COUNT(*)::text
    FROM pg_stat_activity 
    WHERE state = 'active'
    
    UNION ALL
    
    SELECT 
        'Database Size'::text,
        pg_size_pretty(pg_database_size(current_database()))::text
    
    UNION ALL
    
    SELECT 
        'Cache Hit Ratio'::text,
        ROUND(
            (sum(blks_hit) * 100.0 / sum(blks_hit + blks_read))::numeric, 2
        )::text || '%'
    FROM pg_stat_database
    WHERE datname = current_database()
    
    UNION ALL
    
    SELECT 
        'Transactions Per Second'::text,
        ROUND(
            (sum(xact_commit + xact_rollback) / 
             EXTRACT(EPOCH FROM (now() - stats_reset)))::numeric, 2
        )::text
    FROM pg_stat_database
    WHERE datname = current_database();
END;
$$ LANGUAGE plpgsql;
