# Production Environment Configuration for RESTROFLOW
NODE_ENV=production
PORT=4000

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=RESTROFLOW
DB_USER=BARPOS
DB_PASSWORD=Chaand@0319
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=2000

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production
JWT_EXPIRES_IN=24h
BCRYPT_ROUNDS=12

# CORS Configuration
CORS_ORIGIN=https://your-frontend-domain.com
CORS_CREDENTIALS=true

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Cache Configuration
CACHE_TTL_DEFAULT=300
CACHE_TTL_PRODUCTS=600
CACHE_TTL_CATEGORIES=900
CACHE_TTL_ORDERS=60
CACHE_TTL_ANALYTICS=120

# Performance Monitoring
SLOW_QUERY_THRESHOLD=1000
SLOW_REQUEST_THRESHOLD=2000
ENABLE_QUERY_LOGGING=false
ENABLE_PERFORMANCE_MONITORING=true

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/restroflow.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5

# SSL Configuration (if using HTTPS)
SSL_ENABLED=false
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Payment Gateway Configuration (Production)
STRIPE_SECRET_KEY=sk_live_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret
MONERIS_STORE_ID=your_moneris_store_id
MONERIS_API_TOKEN=your_moneris_api_token

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# File Upload Configuration
UPLOAD_MAX_SIZE=10485760
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf

# Session Configuration
SESSION_SECRET=your-super-secure-session-secret-change-this
SESSION_MAX_AGE=86400000

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30000
HEALTH_CHECK_TIMEOUT=5000

# Backup Configuration
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=./backups

# Monitoring and Alerts
MONITORING_ENABLED=true
ALERT_EMAIL=<EMAIL>
ALERT_WEBHOOK_URL=https://your-monitoring-service.com/webhook

# Feature Flags
ENABLE_AI_FEATURES=true
ENABLE_GLOBAL_FEATURES=true
ENABLE_ADVANCED_ANALYTICS=true
ENABLE_MULTI_LOCATION=true

# Third-party Integrations
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
SENTRY_DSN=https://<EMAIL>/project-id

# API Rate Limiting by Endpoint
API_RATE_LIMIT_AUTH=10
API_RATE_LIMIT_ORDERS=50
API_RATE_LIMIT_PRODUCTS=100
API_RATE_LIMIT_ANALYTICS=20
