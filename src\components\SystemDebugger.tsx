import React, { useState, useEffect } from 'react';

interface SystemDebuggerProps {
  onClose: () => void;
  currentUser: any;
  currentPin: string;
}

const SystemDebugger: React.FC<SystemDebuggerProps> = ({ onClose, currentUser, currentPin }) => {
  const [debugInfo, setDebugInfo] = useState<any>({});

  useEffect(() => {
    collectDebugInfo();
  }, []);

  const collectDebugInfo = () => {
    const info = {
      currentUser: currentUser,
      currentPin: currentPin,
      userRole: currentUser?.role,
      localStorage: {
        authToken: localStorage.getItem('authToken'),
        user: localStorage.getItem('user')
      },
      components: {
        SimpleSuperAdminDashboard: 'Checking...',
        SimplePOSSystem: 'Checking...',
        OriginalInterfaceSwitcher: 'Checking...'
      },
      routing: {
        expectedInterface: currentUser?.role === 'super_admin' ? 'SimpleSuperAdminDashboard' : 'SimplePOSSystem',
        specialPinCheck: currentPin === '999999' ? 'OriginalInterfaceSwitcher' : 'Standard routing'
      },
      cssIssues: [],
      timestamp: new Date().toISOString()
    };

    // Check for common CSS issues
    const cssIssues = [];
    
    // Check if Tailwind CSS is loaded
    const tailwindTest = document.createElement('div');
    tailwindTest.className = 'bg-blue-500';
    document.body.appendChild(tailwindTest);
    const computedStyle = window.getComputedStyle(tailwindTest);
    if (computedStyle.backgroundColor !== 'rgb(59, 130, 246)') {
      cssIssues.push('Tailwind CSS not properly loaded');
    }
    document.body.removeChild(tailwindTest);

    // Check for viewport issues
    const viewport = document.querySelector('meta[name="viewport"]');
    if (!viewport) {
      cssIssues.push('Missing viewport meta tag');
    }

    info.cssIssues = cssIssues;
    setDebugInfo(info);
  };

  const testComponentRendering = () => {
    console.log('Testing component rendering...');
    console.log('Current User:', currentUser);
    console.log('Current PIN:', currentPin);
    console.log('Expected Route:', currentUser?.role === 'super_admin' ? 'Super Admin' : 'POS System');
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-96 overflow-y-auto">
        <div className="p-6 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-xl font-bold text-gray-900">🔍 System Debugger</h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 text-2xl"
            >
              ✕
            </button>
          </div>
        </div>
        
        <div className="p-6 space-y-6">
          {/* Current State */}
          <div className="bg-blue-50 p-4 rounded-lg">
            <h4 className="font-bold text-blue-900 mb-2">📊 Current System State</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <strong>User:</strong> {debugInfo.currentUser?.name || 'Not logged in'}
              </div>
              <div>
                <strong>Role:</strong> {debugInfo.userRole || 'Unknown'}
              </div>
              <div>
                <strong>PIN:</strong> {debugInfo.currentPin || 'None'}
              </div>
              <div>
                <strong>Expected Interface:</strong> {debugInfo.routing?.expectedInterface}
              </div>
            </div>
          </div>

          {/* Authentication Debug */}
          <div className="bg-green-50 p-4 rounded-lg">
            <h4 className="font-bold text-green-900 mb-2">🔐 Authentication Debug</h4>
            <div className="text-sm space-y-1">
              <div>
                <strong>Auth Token:</strong> {debugInfo.localStorage?.authToken ? '✅ Present' : '❌ Missing'}
              </div>
              <div>
                <strong>Stored User:</strong> {debugInfo.localStorage?.user ? '✅ Present' : '❌ Missing'}
              </div>
              <div>
                <strong>Special PIN (999999):</strong> {debugInfo.currentPin === '999999' ? '✅ Active' : '❌ Not used'}
              </div>
            </div>
          </div>

          {/* CSS Issues */}
          <div className="bg-yellow-50 p-4 rounded-lg">
            <h4 className="font-bold text-yellow-900 mb-2">🎨 CSS Issues Detected</h4>
            {debugInfo.cssIssues?.length > 0 ? (
              <ul className="text-sm space-y-1">
                {debugInfo.cssIssues.map((issue: string, index: number) => (
                  <li key={index} className="text-red-600">❌ {issue}</li>
                ))}
              </ul>
            ) : (
              <p className="text-green-600 text-sm">✅ No CSS issues detected</p>
            )}
          </div>

          {/* Component Status */}
          <div className="bg-purple-50 p-4 rounded-lg">
            <h4 className="font-bold text-purple-900 mb-2">🧩 Component Status</h4>
            <div className="text-sm space-y-1">
              <div>SimpleSuperAdminDashboard: {debugInfo.components?.SimpleSuperAdminDashboard}</div>
              <div>SimplePOSSystem: {debugInfo.components?.SimplePOSSystem}</div>
              <div>OriginalInterfaceSwitcher: {debugInfo.components?.OriginalInterfaceSwitcher}</div>
            </div>
          </div>

          {/* Quick Actions */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-bold text-gray-900 mb-2">⚡ Quick Actions</h4>
            <div className="flex flex-wrap gap-2">
              <button
                onClick={testComponentRendering}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm"
              >
                Test Component Rendering
              </button>
              <button
                onClick={() => console.log('Debug Info:', debugInfo)}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm"
              >
                Log Debug Info
              </button>
              <button
                onClick={() => window.location.reload()}
                className="bg-orange-600 hover:bg-orange-700 text-white px-3 py-1 rounded text-sm"
              >
                Reload Page
              </button>
            </div>
          </div>

          {/* Raw Debug Data */}
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="font-bold text-gray-900 mb-2">📋 Raw Debug Data</h4>
            <pre className="text-xs bg-white p-2 rounded border overflow-x-auto">
              {JSON.stringify(debugInfo, null, 2)}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SystemDebugger;
