// Unified POS System Component - Main POS Interface
import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { apiService } from '../../services/api';

interface Product {
  id: number;
  name: string;
  price: number;
  category_id: number;
  category_name: string;
  category_color: string;
  description?: string;
  is_active: boolean;
}

interface OrderItem {
  product: Product;
  quantity: number;
  subtotal: number;
}

interface Order {
  id: string;
  items: OrderItem[];
  total: number;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  timestamp: string;
  table_id?: number;
  customer_name?: string;
}

const UnifiedPOSSystem: React.FC = () => {
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<any[]>([]);
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [error, setError] = useState<string | null>(null);

  // Load data from API
  useEffect(() => {
    loadInitialData();
  }, []);

  const loadInitialData = async () => {
    setLoading(true);
    setError(null);

    try {
      // Load products and categories from API
      const [productsData, categoriesData] = await Promise.all([
        apiService.getProducts(),
        apiService.getCategories()
      ]);

      setProducts(productsData);
      setCategories([{ id: 0, name: 'All Categories' }, ...categoriesData]);

      // Load existing orders
      try {
        const ordersData = await apiService.getOrders();
        setOrders(ordersData);
      } catch (orderError) {
        console.warn('Failed to load orders:', orderError);
        // Continue without orders if API fails
      }

    } catch (error) {
      console.error('Failed to load initial data:', error);
      setError('Failed to load data. Please check your connection.');
    } finally {
      setLoading(false);
    }
  };

  const addToOrder = (product: Product) => {
    setCurrentOrder(prev => {
      const existingItem = prev.find(item => item.product.id === product.id);
      if (existingItem) {
        return prev.map(item =>
          item.product.id === product.id
            ? { ...item, quantity: item.quantity + 1, subtotal: (item.quantity + 1) * product.price }
            : item
        );
      } else {
        return [...prev, { product, quantity: 1, subtotal: product.price }];
      }
    });
  };

  const removeFromOrder = (productId: number) => {
    setCurrentOrder(prev => prev.filter(item => item.product.id !== productId));
  };

  const updateQuantity = (productId: number, quantity: number) => {
    if (quantity <= 0) {
      removeFromOrder(productId);
      return;
    }

    setCurrentOrder(prev =>
      prev.map(item =>
        item.product.id === productId
          ? { ...item, quantity, subtotal: quantity * item.product.price }
          : item
      )
    );
  };

  const calculateTotal = () => {
    return currentOrder.reduce((total, item) => total + item.subtotal, 0);
  };

  const processOrder = async () => {
    if (currentOrder.length === 0) return;

    setLoading(true);
    setError(null);

    try {
      const orderData = {
        items: currentOrder.map(item => ({
          product_id: item.product.id,
          quantity: item.quantity,
          price: item.product.price,
          subtotal: item.subtotal
        })),
        total: calculateTotal(),
        status: 'pending'
      };

      // Create order via API
      const createdOrder = await apiService.createOrder(orderData);

      // Add to local orders list
      setOrders(prev => [createdOrder, ...prev]);
      setCurrentOrder([]);

      console.log('Order processed successfully:', createdOrder);

    } catch (error) {
      console.error('Error processing order:', error);
      setError('Failed to process order. Please try again.');

      // Fallback: add order locally even if API fails
      const fallbackOrder: Order = {
        id: `local_${Date.now()}`,
        items: [...currentOrder],
        total: calculateTotal(),
        status: 'pending',
        timestamp: new Date().toISOString()
      };

      setOrders(prev => [fallbackOrder, ...prev]);
      setCurrentOrder([]);
    } finally {
      setLoading(false);
    }
  };

  const filteredProducts = selectedCategory === 'all'
    ? products
    : products.filter(product => product.category_name === selectedCategory);

  if (loading && products.length === 0) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4 flex items-center justify-center">
        <Card className="p-8">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-lg">Loading RESTROFLOW POS System...</p>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">RESTROFLOW POS System</h1>
          <p className="text-gray-600">Multi-tenant restaurant point of sale system</p>
          {error && (
            <div className="mt-2 p-3 bg-red-100 border border-red-400 text-red-700 rounded">
              {error}
              <Button
                variant="outline"
                size="sm"
                className="ml-2"
                onClick={loadInitialData}
              >
                Retry
              </Button>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Product Grid */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Products ({filteredProducts.length})</CardTitle>
                <div className="flex gap-2 mt-4 flex-wrap">
                  {categories.map(category => (
                    <Button
                      key={category.id || category.name}
                      variant={selectedCategory === (category.name === 'All Categories' ? 'all' : category.name) ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedCategory(category.name === 'All Categories' ? 'all' : category.name)}
                      className="capitalize"
                      style={category.color ? { borderColor: category.color } : {}}
                    >
                      {category.name}
                    </Button>
                  ))}
                </div>
              </CardHeader>
              <CardContent>
                {filteredProducts.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <p>No products available in this category</p>
                  </div>
                ) : (
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                    {filteredProducts.map(product => (
                      <Card
                        key={product.id}
                        className="cursor-pointer hover:shadow-lg transition-shadow"
                        onClick={() => addToOrder(product)}
                      >
                        <CardContent className="p-4">
                          <h3 className="font-semibold text-lg">{product.name}</h3>
                          <p className="text-sm text-gray-600 mb-2">{product.description}</p>
                          <div className="flex justify-between items-center">
                            <Badge
                              variant="secondary"
                              style={{ backgroundColor: product.category_color + '20', color: product.category_color }}
                            >
                              {product.category_name}
                            </Badge>
                            <span className="font-bold text-lg">${product.price.toFixed(2)}</span>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Order Panel */}
          <div>
            <Card>
              <CardHeader>
                <CardTitle>Current Order</CardTitle>
              </CardHeader>
              <CardContent>
                {currentOrder.length === 0 ? (
                  <p className="text-gray-500 text-center py-8">No items in order</p>
                ) : (
                  <div className="space-y-4">
                    {currentOrder.map(item => (
                      <div key={item.product.id} className="flex justify-between items-center p-3 bg-gray-50 rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium">{item.product.name}</h4>
                          <p className="text-sm text-gray-600">${item.product.price.toFixed(2)} each</p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.product.id, item.quantity - 1)}
                          >
                            -
                          </Button>
                          <span className="w-8 text-center">{item.quantity}</span>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => updateQuantity(item.product.id, item.quantity + 1)}
                          >
                            +
                          </Button>
                          <Button
                            size="sm"
                            variant="destructive"
                            onClick={() => removeFromOrder(item.product.id)}
                          >
                            ×
                          </Button>
                        </div>
                        <div className="ml-4 font-semibold">
                          ${item.subtotal.toFixed(2)}
                        </div>
                      </div>
                    ))}
                    
                    <div className="border-t pt-4">
                      <div className="flex justify-between items-center text-xl font-bold">
                        <span>Total:</span>
                        <span>${calculateTotal().toFixed(2)}</span>
                      </div>
                      <Button
                        className="w-full mt-4"
                        onClick={processOrder}
                        disabled={loading}
                      >
                        {loading ? 'Processing...' : 'Process Order'}
                      </Button>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Recent Orders */}
            {orders.length > 0 && (
              <Card className="mt-6">
                <CardHeader>
                  <CardTitle>Recent Orders</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {orders.slice(0, 5).map(order => (
                      <div key={order.id} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                        <div>
                          <span className="font-medium">{order.id}</span>
                          <Badge variant="outline" className="ml-2">{order.status}</Badge>
                        </div>
                        <span className="font-semibold">${order.total.toFixed(2)}</span>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedPOSSystem;
