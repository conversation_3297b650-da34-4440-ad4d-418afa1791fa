# 🚀 ENHANCED LOGIN SYSTEM - IMPLEMENTATION GUIDE

## 📧 **EMAIL INTEGRATION**

### **Email Service Configuration**
```typescript
// Import the email service
import { emailService, sendLoginNotification } from './src/utils/EmailService';

// Send login notification
const loginEvent = {
  employeeId: 123,
  employeeName: '<PERSON>',
  employeeEmail: '<EMAIL>',
  tenantName: 'Demo Restaurant',
  loginTime: new Date().toISOString(),
  ipAddress: '*************',
  userAgent: navigator.userAgent,
  loginType: 'employee' as const,
  success: true
};

await sendLoginNotification(loginEvent);
```

### **Backend Email Endpoints**
```javascript
// Add to backend/working-server.js
app.post('/api/email/send', authenticateToken, async (req, res) => {
  try {
    const { to, template, data, priority } = req.body;
    
    // Email sending logic here
    const emailResult = await sendEmailTemplate(to, template, data);
    
    res.json({ success: true, messageId: emailResult.messageId });
  } catch (error) {
    console.error('Email sending failed:', error);
    res.status(500).json({ error: 'Failed to send email' });
  }
});
```

## 💻 **CORE CODE COMPONENTS**

### **1. Tenant Employee Login**
```typescript
// Usage Example
import TenantEmployeeLogin from './src/components/TenantEmployeeLogin';

function App() {
  const handleEmployeeLogin = (success: boolean) => {
    if (success) {
      // Redirect to POS system
      window.location.href = '/pos.html';
    }
  };

  return (
    <TenantEmployeeLogin
      onLogin={handleEmployeeLogin}
      tenantSlug="demo-restaurant"
      onTenantChange={(slug) => console.log('Tenant changed:', slug)}
    />
  );
}
```

### **2. Super Admin Login**
```typescript
// Usage Example
import EnhancedSuperAdminLogin from './src/components/EnhancedSuperAdminLogin';

function AdminApp() {
  const handleAdminLogin = (success: boolean) => {
    if (success) {
      // Redirect to admin dashboard
      window.location.href = '/super-admin.html';
    }
  };

  return (
    <EnhancedSuperAdminLogin onLogin={handleAdminLogin} />
  );
}
```

### **3. Session Management**
```typescript
// Usage Example
import { sessionManager, isAuthenticated, hasRole } from './src/utils/SessionManager';

// Check authentication
if (isAuthenticated()) {
  console.log('User is authenticated');
}

// Check role
if (hasRole('super_admin')) {
  console.log('User is super admin');
}

// Get current session
const session = sessionManager.getSession();
console.log('Current user:', session?.employee.name);

// Logout
sessionManager.logout();
```

### **4. Unified Authentication System**
```typescript
// Complete Implementation
import UnifiedAuthenticationSystem from './src/components/UnifiedAuthenticationSystem';

function AuthApp() {
  const handleAuthSuccess = (userType: 'employee' | 'admin') => {
    console.log(`${userType} authenticated successfully`);
    
    if (userType === 'admin') {
      // Load admin dashboard
      loadAdminDashboard();
    } else {
      // Load POS system
      loadPOSSystem();
    }
  };

  return (
    <UnifiedAuthenticationSystem
      onAuthenticationSuccess={handleAuthSuccess}
      defaultMode="employee"
    />
  );
}
```

## 🔗 **API INTEGRATION CODE**

### **Authentication API Calls**
```typescript
// Employee Login
const employeeLogin = async (pin: string, tenantSlug: string) => {
  const response = await fetch('http://localhost:4000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ pin, tenant_slug: tenantSlug })
  });
  
  const data = await response.json();
  
  if (response.ok && data.token) {
    localStorage.setItem('authToken', data.token);
    localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
    localStorage.setItem('currentTenant', JSON.stringify(data.tenant));
    return true;
  }
  
  return false;
};

// Super Admin Login
const adminLogin = async (pin: string) => {
  const response = await fetch('http://localhost:4000/api/auth/login', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ pin, admin_access: true })
  });
  
  const data = await response.json();
  
  if (response.ok && data.token && data.employee?.role === 'super_admin') {
    localStorage.setItem('authToken', data.token);
    localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
    localStorage.setItem('adminAccess', 'true');
    return true;
  }
  
  return false;
};
```

### **Tenant Management API**
```typescript
// Get Public Tenants
const getPublicTenants = async () => {
  const response = await fetch('http://localhost:4000/api/tenants/public');
  return response.ok ? await response.json() : [];
};

// Get Tenant Profile
const getTenantProfile = async (slug: string) => {
  const token = localStorage.getItem('authToken');
  const response = await fetch(`http://localhost:4000/api/tenants/profile/${slug}`, {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  return response.ok ? await response.json() : null;
};

// Update Tenant Profile
const updateTenantProfile = async (slug: string, updates: any) => {
  const token = localStorage.getItem('authToken');
  const response = await fetch(`http://localhost:4000/api/tenants/profile/${slug}`, {
    method: 'PUT',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(updates)
  });
  return response.ok ? await response.json() : null;
};
```

## 🗄️ **DATABASE INTEGRATION**

### **PostgreSQL Connection**
```javascript
// Database Configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

// Employee Authentication Query
const authenticateEmployee = async (pin, tenantSlug) => {
  const tenantResult = await pool.query(
    'SELECT * FROM tenants WHERE slug = $1 AND status = $2',
    [tenantSlug, 'active']
  );
  
  if (tenantResult.rows.length === 0) {
    throw new Error('Tenant not found');
  }
  
  const employeeResult = await pool.query(
    'SELECT * FROM employees WHERE tenant_id = $1 AND is_active = true',
    [tenantResult.rows[0].id]
  );
  
  for (const employee of employeeResult.rows) {
    if (employee.pin_hash && await bcrypt.compare(pin, employee.pin_hash)) {
      return { employee, tenant: tenantResult.rows[0] };
    }
  }
  
  throw new Error('Invalid PIN');
};
```

### **Database Schema**
```sql
-- Tenants Table
CREATE TABLE tenants (
  id SERIAL PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  business_name VARCHAR(255),
  email VARCHAR(255),
  phone VARCHAR(50),
  address TEXT,
  city VARCHAR(100),
  state VARCHAR(50),
  zip_code VARCHAR(20),
  logo_url VARCHAR(500),
  primary_color VARCHAR(7) DEFAULT '#3b82f6',
  secondary_color VARCHAR(7) DEFAULT '#8b5cf6',
  accent_color VARCHAR(7) DEFAULT '#10b981',
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Employees Table
CREATE TABLE employees (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES tenants(id),
  name VARCHAR(255) NOT NULL,
  email VARCHAR(255),
  role VARCHAR(50) NOT NULL,
  pin VARCHAR(10),
  pin_hash VARCHAR(255),
  is_active BOOLEAN DEFAULT true,
  permissions TEXT[],
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

## 🔧 **SETUP INSTRUCTIONS**

### **1. Install Dependencies**
```bash
# Frontend dependencies
npm install lucide-react

# Backend dependencies (if not already installed)
npm install bcryptjs jsonwebtoken pg cors express
```

### **2. Environment Configuration**
```bash
# Create .env file
JWT_SECRET=your-super-secret-jwt-key-here
DB_USER=BARPOS
DB_HOST=localhost
DB_NAME=RESTROFLOW
DB_PASSWORD=Chaand@0319
DB_PORT=5432
```

### **3. Database Setup**
```sql
-- Create sample tenant
INSERT INTO tenants (name, slug, business_name, email, status) 
VALUES ('Demo Restaurant', 'demo-restaurant', 'Demo Restaurant LLC', '<EMAIL>', 'active');

-- Create sample employees
INSERT INTO employees (tenant_id, name, email, role, pin_hash, is_active)
VALUES 
  (1, 'Super Admin', '<EMAIL>', 'super_admin', '$2b$10$hashedpin', true),
  (1, 'Restaurant Manager', '<EMAIL>', 'manager', '$2b$10$hashedpin', true);
```

### **4. Component Integration**
```typescript
// In your main App.tsx
import { useState, useEffect } from 'react';
import ProductionAuthSystem from './src/components/ProductionAuthSystem';
import { sessionManager } from './src/utils/SessionManager';

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userType, setUserType] = useState<'employee' | 'admin'>('employee');

  useEffect(() => {
    // Check for existing session
    const checkSession = async () => {
      const hasSession = await sessionManager.autoLogin();
      if (hasSession) {
        setIsAuthenticated(true);
        const session = sessionManager.getSession();
        setUserType(session?.adminAccess ? 'admin' : 'employee');
      }
    };
    
    checkSession();
  }, []);

  const handleAuthComplete = (type: 'employee' | 'admin') => {
    setIsAuthenticated(true);
    setUserType(type);
  };

  if (!isAuthenticated) {
    return <ProductionAuthSystem onAuthenticationComplete={handleAuthComplete} />;
  }

  // Render authenticated app based on user type
  return userType === 'admin' ? <AdminDashboard /> : <POSSystem />;
}
```

## 🧪 **TESTING CODE**

### **Authentication Testing**
```typescript
// Test authentication functions
const testAuth = async () => {
  // Test employee login
  const employeeSuccess = await employeeLogin('123456', 'demo-restaurant');
  console.log('Employee login:', employeeSuccess ? '✅ PASS' : '❌ FAIL');

  // Test admin login
  const adminSuccess = await adminLogin('123456');
  console.log('Admin login:', adminSuccess ? '✅ PASS' : '❌ FAIL');

  // Test session management
  const session = sessionManager.getSession();
  console.log('Session active:', session ? '✅ PASS' : '❌ FAIL');
};
```

## 📱 **MOBILE/TABLET OPTIMIZATION**

### **Touch-Friendly Styles**
```css
/* Add to your CSS */
.touch-friendly-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 16px;
  font-size: 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.touch-friendly-button:active {
  transform: scale(0.95);
}

.pin-input {
  font-size: 18px;
  padding: 16px;
  text-align: center;
  letter-spacing: 0.5em;
}

@media (max-width: 768px) {
  .login-card {
    margin: 16px;
    padding: 24px;
  }
}
```

## 🔒 **SECURITY BEST PRACTICES**

### **Implementation Checklist**
- ✅ Use HTTPS in production
- ✅ Implement rate limiting on login endpoints
- ✅ Log all authentication attempts
- ✅ Use secure JWT secrets
- ✅ Implement session timeout
- ✅ Validate all inputs
- ✅ Use parameterized database queries
- ✅ Implement proper error handling
- ✅ Add CSRF protection
- ✅ Use secure headers

This implementation guide provides all the essential code and configuration needed to deploy the enhanced login system with email integration and complete database connectivity.
