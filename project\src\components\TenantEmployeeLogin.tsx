import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Lock, 
  User, 
  Eye, 
  EyeOff, 
  ArrowRight,
  Loader2,
  AlertCircle,
  CheckCircle,
  Store,
  Users,
  Shield
} from 'lucide-react';

interface TenantEmployeeLoginProps {
  onLogin: (success: boolean) => void;
  tenantSlug?: string;
  onTenantChange?: (slug: string) => void;
}

interface TenantInfo {
  id: number;
  name: string;
  slug: string;
  business_name: string;
  logo_url?: string;
  primary_color?: string;
  secondary_color?: string;
  status: string;
}

const TenantEmployeeLogin: React.FC<TenantEmployeeLoginProps> = ({ 
  onLogin, 
  tenantSlug,
  onTenantChange
}) => {
  const [pin, setPin] = useState('');
  const [selectedTenantSlug, setSelectedTenantSlug] = useState(tenantSlug || '');
  const [tenantInfo, setTenantInfo] = useState<TenantInfo | null>(null);
  const [availableTenants, setAvailableTenants] = useState<TenantInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPin, setShowPin] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [loadingTenants, setLoadingTenants] = useState(true);

  // Check for dark mode preference
  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  // Load available tenants
  useEffect(() => {
    loadAvailableTenants();
  }, []);

  // Load tenant info when slug changes
  useEffect(() => {
    if (selectedTenantSlug) {
      loadTenantInfo(selectedTenantSlug);
    }
  }, [selectedTenantSlug]);

  const loadAvailableTenants = async () => {
    try {
      setLoadingTenants(true);
      const response = await fetch('http://localhost:4000/api/tenants/public', {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const tenants = await response.json();
        setAvailableTenants(Array.isArray(tenants) ? tenants : []);
        
        // Auto-select first tenant if none selected
        if (!selectedTenantSlug && tenants.length > 0) {
          const firstTenant = tenants[0];
          setSelectedTenantSlug(firstTenant.slug);
          if (onTenantChange) {
            onTenantChange(firstTenant.slug);
          }
        }
      } else {
        console.error('Failed to load tenants');
        setError('Unable to load restaurant locations');
      }
    } catch (error) {
      console.error('Error loading tenants:', error);
      setError('Connection failed. Please check your network.');
    } finally {
      setLoadingTenants(false);
    }
  };

  const loadTenantInfo = async (slug: string) => {
    try {
      const response = await fetch(`http://localhost:4000/api/tenants/public/${slug}`, {
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const tenant = await response.json();
        setTenantInfo(tenant);
        setError('');
      } else {
        setError('Restaurant not found');
        setTenantInfo(null);
      }
    } catch (error) {
      console.error('Error loading tenant info:', error);
      setError('Unable to load restaurant information');
      setTenantInfo(null);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!pin.trim()) {
      setError('Please enter your PIN');
      return;
    }

    if (!selectedTenantSlug) {
      setError('Please select a restaurant location');
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          pin: pin.trim(),
          tenant_slug: selectedTenantSlug
        })
      });

      const data = await response.json();

      if (response.ok && data.token) {
        // Store authentication data
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
        localStorage.setItem('currentTenant', JSON.stringify(data.tenant));
        localStorage.setItem('tenantSlug', selectedTenantSlug);

        console.log(`✅ Employee login successful: ${data.employee.name} (${data.employee.role})`);
        onLogin(true);
      } else {
        setError(data.error || 'Invalid PIN or restaurant selection. Please try again.');
      }
    } catch (error) {
      console.error('Login error:', error);
      if (error instanceof TypeError && error.message.includes('fetch')) {
        setError('Connection failed. Please check your network connection.');
      } else {
        setError('Login failed. Please check your PIN and try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleTenantChange = (slug: string) => {
    setSelectedTenantSlug(slug);
    setPin(''); // Clear PIN when changing tenants
    setError('');
    if (onTenantChange) {
      onTenantChange(slug);
    }
  };

  const toggleTheme = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  const getTenantColors = () => {
    if (tenantInfo?.primary_color && tenantInfo?.secondary_color) {
      return {
        primary: tenantInfo.primary_color,
        secondary: tenantInfo.secondary_color
      };
    }
    return {
      primary: '#3b82f6', // Default blue
      secondary: '#8b5cf6' // Default purple
    };
  };

  const colors = getTenantColors();

  if (loadingTenants) {
    return (
      <div className={`min-h-screen flex items-center justify-center transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
          <p className={`mt-4 transition-colors duration-300 ${
            isDarkMode ? 'text-gray-300' : 'text-gray-600'
          }`}>
            Loading restaurant locations...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen w-full flex items-center justify-center p-4 relative overflow-hidden transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gradient-to-br from-gray-900 via-blue-900 to-indigo-900' 
        : 'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50'
    }`}>
      
      {/* Theme Toggle */}
      <button
        onClick={toggleTheme}
        className={`fixed top-4 right-4 p-3 rounded-full transition-all duration-300 z-50 ${
          isDarkMode 
            ? 'bg-gray-800 text-yellow-400 hover:bg-gray-700' 
            : 'bg-white text-gray-600 hover:bg-gray-100'
        } shadow-lg hover:shadow-xl`}
      >
        {isDarkMode ? '☀️' : '🌙'}
      </button>

      {/* Main Login Card */}
      <div className={`w-full max-w-md relative z-10 transition-all duration-300 ${
        isDarkMode 
          ? 'bg-gray-800/90 border-gray-700' 
          : 'bg-white/90 border-gray-200'
      } backdrop-blur-xl rounded-2xl shadow-2xl border p-8`}>
        
        {/* Tenant Selection Header */}
        <div className="text-center mb-8">
          {tenantInfo ? (
            <>
              {/* Tenant Logo/Icon */}
              <div 
                className="w-20 h-20 mx-auto mb-4 rounded-2xl flex items-center justify-center shadow-lg"
                style={{
                  background: `linear-gradient(45deg, ${colors.primary}, ${colors.secondary})`
                }}
              >
                {tenantInfo.logo_url ? (
                  <img 
                    src={tenantInfo.logo_url} 
                    alt={tenantInfo.business_name}
                    className="w-12 h-12 rounded-lg object-cover"
                  />
                ) : (
                  <Store className="w-10 h-10 text-white" />
                )}
              </div>
              
              {/* Tenant Name */}
              <h1 className={`text-2xl font-bold mb-2 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                {tenantInfo.business_name || tenantInfo.name}
              </h1>
              
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Employee Login Portal
              </p>
            </>
          ) : (
            <>
              <div className="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                <Building2 className="w-10 h-10 text-white" />
              </div>
              
              <h1 className={`text-2xl font-bold mb-2 ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Restaurant POS
              </h1>
              
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-300' : 'text-gray-600'
              }`}>
                Employee Login Portal
              </p>
            </>
          )}
        </div>

        {/* Tenant Selection Dropdown */}
        <div className="mb-6">
          <label className={`block text-sm font-medium mb-3 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            <Store className="w-4 h-4 inline mr-2" />
            Select Restaurant Location
          </label>
          
          <select
            value={selectedTenantSlug}
            onChange={(e) => handleTenantChange(e.target.value)}
            className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
            } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
          >
            <option value="">Choose your restaurant...</option>
            {availableTenants.map((tenant) => (
              <option key={tenant.slug} value={tenant.slug}>
                {tenant.business_name || tenant.name}
                {tenant.status !== 'active' && ' (Inactive)'}
              </option>
            ))}
          </select>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* PIN Input Section */}
          <div>
            <label className={`block text-sm font-medium mb-3 ${
              isDarkMode ? 'text-gray-200' : 'text-gray-700'
            }`}>
              <Lock className="w-4 h-4 inline mr-2" />
              Enter Your PIN
            </label>
            
            <div className="relative">
              <input
                type={showPin ? 'text' : 'password'}
                value={pin}
                onChange={(e) => setPin(e.target.value.slice(0, 6))}
                className={`w-full px-4 py-3 rounded-lg border-2 transition-all duration-200 text-center text-lg font-mono tracking-widest ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                    : 'bg-gray-50 border-gray-300 text-gray-900 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                placeholder="••••••"
                maxLength={6}
                autoComplete="off"
                disabled={!selectedTenantSlug}
              />
              
              <button
                type="button"
                onClick={() => setShowPin(!showPin)}
                className={`absolute right-3 top-1/2 transform -translate-y-1/2 ${
                  isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-500 hover:text-gray-700'
                } transition-colors`}
                disabled={!selectedTenantSlug}
              >
                {showPin ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
              </button>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 ${
              isDarkMode 
                ? 'bg-red-900/50 border border-red-700 text-red-300' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}

          {/* Login Button */}
          <button
            type="submit"
            disabled={pin.length === 0 || !selectedTenantSlug || isLoading}
            className={`w-full py-3 px-4 rounded-lg font-semibold transition-all duration-200 flex items-center justify-center space-x-2 ${
              pin.length === 0 || !selectedTenantSlug || isLoading
                ? isDarkMode 
                  ? 'bg-gray-700 text-gray-400 cursor-not-allowed' 
                  : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : `text-white shadow-lg hover:shadow-xl transform hover:scale-105 active:scale-95`
            }`}
            style={{
              background: (pin.length > 0 && selectedTenantSlug && !isLoading) 
                ? `linear-gradient(45deg, ${colors.primary}, ${colors.secondary})`
                : undefined
            }}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-5 h-5 animate-spin" />
                <span>Signing In...</span>
              </>
            ) : (
              <>
                <User className="w-5 h-5" />
                <span>Sign In to {tenantInfo?.business_name || 'Restaurant'}</span>
                <ArrowRight className="w-5 h-5" />
              </>
            )}
          </button>
        </form>

        {/* Security Notice */}
        <div className={`mt-6 p-3 rounded-lg ${
          isDarkMode 
            ? 'bg-blue-900/30 border border-blue-700' 
            : 'bg-blue-50 border border-blue-200'
        }`}>
          <div className="flex items-center space-x-2">
            <Shield className={`w-4 h-4 ${
              isDarkMode ? 'text-blue-300' : 'text-blue-600'
            }`} />
            <p className={`text-xs ${
              isDarkMode ? 'text-blue-300' : 'text-blue-700'
            }`}>
              Secure employee authentication for {tenantInfo?.business_name || 'your restaurant'}
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center">
          <div className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
            <p>RestroFlow POS System</p>
            <p className="mt-1">© 2024 RestroFlow. All rights reserved.</p>
          </div>
        </div>
      </div>

      {/* Background Elements */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className={`absolute -top-40 -right-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob`}
             style={{ backgroundColor: `${colors.primary}40` }}></div>
        <div className={`absolute -bottom-40 -left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-2000`}
             style={{ backgroundColor: `${colors.secondary}40` }}></div>
        <div className={`absolute top-40 left-40 w-80 h-80 rounded-full mix-blend-multiply filter blur-xl opacity-70 animate-blob animation-delay-4000`}
             style={{ backgroundColor: `${colors.primary}30` }}></div>
      </div>

      <style>{`
        @keyframes blob {
          0% { transform: translate(0px, 0px) scale(1); }
          33% { transform: translate(30px, -50px) scale(1.1); }
          66% { transform: translate(-20px, 20px) scale(0.9); }
          100% { transform: translate(0px, 0px) scale(1); }
        }
        .animate-blob { animation: blob 7s infinite; }
        .animation-delay-2000 { animation-delay: 2s; }
        .animation-delay-4000 { animation-delay: 4s; }
      `}</style>
    </div>
  );
};

export default TenantEmployeeLogin;
