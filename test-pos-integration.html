<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTROFLOW POS - Backend Integration Test</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading-spinner {
            border: 4px solid #f3f4f6;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-50 to-indigo-100 min-h-screen">
    <div class="container mx-auto p-4">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">RESTROFLOW POS System</h1>
            <p class="text-gray-600">Backend Integration Test - Real API Connections</p>
            <div id="connection-status" class="mt-2 p-2 rounded text-sm">
                <span class="loading-spinner inline-block mr-2"></span>
                Connecting to backend...
            </div>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- Product Grid -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-lg shadow-md border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Products</h3>
                        <div id="category-buttons" class="flex gap-2 mt-4 flex-wrap">
                            <!-- Categories will be loaded here -->
                        </div>
                    </div>
                    <div class="px-6 py-4">
                        <div id="products-grid" class="grid grid-cols-2 md:grid-cols-3 gap-4">
                            <!-- Products will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Order Panel -->
            <div>
                <div class="bg-white rounded-lg shadow-md border border-gray-200">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Current Order</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div id="current-order">
                            <p class="text-gray-500 text-center py-8">No items in order</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Orders -->
                <div id="recent-orders" class="bg-white rounded-lg shadow-md border border-gray-200 mt-6" style="display: none;">
                    <div class="px-6 py-4 border-b border-gray-200">
                        <h3 class="text-lg font-semibold text-gray-900">Recent Orders</h3>
                    </div>
                    <div class="px-6 py-4">
                        <div id="orders-list">
                            <!-- Recent orders will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // API Configuration
        const API_BASE = 'http://localhost:4000/api';
        let currentOrder = [];
        let products = [];
        let categories = [];
        let selectedCategory = 'all';

        // API Service
        class APIService {
            async makeRequest(endpoint, options = {}) {
                const url = `${API_BASE}${endpoint}`;
                const config = {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                };

                try {
                    const response = await fetch(url, config);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return await response.json();
                } catch (error) {
                    console.error('API request failed:', error);
                    throw error;
                }
            }

            async getProducts() {
                try {
                    return await this.makeRequest('/products');
                } catch (error) {
                    console.warn('Products API failed, using fallback data:', error);
                    return this.getMockProducts();
                }
            }

            async getCategories() {
                try {
                    return await this.makeRequest('/categories');
                } catch (error) {
                    console.warn('Categories API failed, using fallback data:', error);
                    return this.getMockCategories();
                }
            }

            async createOrder(orderData) {
                try {
                    return await this.makeRequest('/orders', {
                        method: 'POST',
                        body: JSON.stringify(orderData)
                    });
                } catch (error) {
                    console.warn('Create order API failed, using fallback:', error);
                    return {
                        id: `local_${Date.now()}`,
                        ...orderData,
                        status: 'pending',
                        timestamp: new Date().toISOString()
                    };
                }
            }

            getMockProducts() {
                return [
                    { id: 1, name: 'Coffee', price: 4.99, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', description: 'Fresh brewed coffee', is_active: true },
                    { id: 2, name: 'Sandwich', price: 8.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', description: 'Delicious sandwich', is_active: true },
                    { id: 3, name: 'Tea', price: 3.99, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', description: 'Hot tea', is_active: true },
                    { id: 4, name: 'Salad', price: 12.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', description: 'Fresh garden salad', is_active: true }
                ];
            }

            getMockCategories() {
                return [
                    { id: 1, name: 'Beverages', description: 'Hot and cold drinks', color: '#3B82F6', sort_order: 1, is_active: true },
                    { id: 2, name: 'Main Courses', description: 'Full meals and entrees', color: '#10B981', sort_order: 2, is_active: true },
                    { id: 3, name: 'Appetizers', description: 'Starters and small plates', color: '#F59E0B', sort_order: 3, is_active: true },
                    { id: 4, name: 'Desserts', description: 'Sweet treats and desserts', color: '#EF4444', sort_order: 4, is_active: true }
                ];
            }
        }

        const apiService = new APIService();

        // Initialize the application
        async function initializeApp() {
            try {
                updateConnectionStatus('loading', 'Loading data from backend...');
                
                // Load products and categories
                const [productsData, categoriesData] = await Promise.all([
                    apiService.getProducts(),
                    apiService.getCategories()
                ]);
                
                products = productsData;
                categories = [{ id: 0, name: 'All Categories', color: '#6B7280' }, ...categoriesData];
                
                updateConnectionStatus('success', 'Connected to backend successfully!');
                renderCategories();
                renderProducts();
                
            } catch (error) {
                console.error('Failed to initialize app:', error);
                updateConnectionStatus('error', 'Failed to connect to backend. Using offline mode.');
            }
        }

        function updateConnectionStatus(status, message) {
            const statusEl = document.getElementById('connection-status');
            statusEl.className = `mt-2 p-2 rounded text-sm ${
                status === 'loading' ? 'bg-blue-100 text-blue-800' :
                status === 'success' ? 'bg-green-100 text-green-800' :
                'bg-red-100 text-red-800'
            }`;
            statusEl.innerHTML = status === 'loading' ? 
                `<span class="loading-spinner inline-block mr-2"></span>${message}` : 
                message;
        }

        function renderCategories() {
            const container = document.getElementById('category-buttons');
            container.innerHTML = categories.map(category => `
                <button 
                    onclick="selectCategory('${category.name === 'All Categories' ? 'all' : category.name}')"
                    class="px-3 py-1.5 text-sm rounded-md font-medium transition-colors ${
                        selectedCategory === (category.name === 'All Categories' ? 'all' : category.name) 
                            ? 'bg-blue-600 text-white' 
                            : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                    }"
                    style="${category.color && category.name !== 'All Categories' ? `border-color: ${category.color}` : ''}"
                >
                    ${category.name}
                </button>
            `).join('');
        }

        function selectCategory(categoryName) {
            selectedCategory = categoryName;
            renderCategories();
            renderProducts();
        }

        function renderProducts() {
            const filteredProducts = selectedCategory === 'all' 
                ? products 
                : products.filter(product => product.category_name === selectedCategory);
            
            const container = document.getElementById('products-grid');
            
            if (filteredProducts.length === 0) {
                container.innerHTML = '<div class="col-span-full text-center py-8 text-gray-500">No products available in this category</div>';
                return;
            }
            
            container.innerHTML = filteredProducts.map(product => `
                <div 
                    onclick="addToOrder(${product.id})"
                    class="bg-white rounded-lg shadow-md border border-gray-200 cursor-pointer hover:shadow-lg transition-shadow p-4"
                >
                    <h3 class="font-semibold text-lg">${product.name}</h3>
                    <p class="text-sm text-gray-600 mb-2">${product.description || ''}</p>
                    <div class="flex justify-between items-center">
                        <span 
                            class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                            style="background-color: ${product.category_color}20; color: ${product.category_color}"
                        >
                            ${product.category_name}
                        </span>
                        <span class="font-bold text-lg">$${product.price.toFixed(2)}</span>
                    </div>
                </div>
            `).join('');
        }

        function addToOrder(productId) {
            const product = products.find(p => p.id === productId);
            if (!product) return;
            
            const existingItem = currentOrder.find(item => item.product.id === productId);
            if (existingItem) {
                existingItem.quantity += 1;
                existingItem.subtotal = existingItem.quantity * product.price;
            } else {
                currentOrder.push({
                    product,
                    quantity: 1,
                    subtotal: product.price
                });
            }
            
            renderCurrentOrder();
        }

        function updateQuantity(productId, newQuantity) {
            if (newQuantity <= 0) {
                removeFromOrder(productId);
                return;
            }
            
            const item = currentOrder.find(item => item.product.id === productId);
            if (item) {
                item.quantity = newQuantity;
                item.subtotal = newQuantity * item.product.price;
                renderCurrentOrder();
            }
        }

        function removeFromOrder(productId) {
            currentOrder = currentOrder.filter(item => item.product.id !== productId);
            renderCurrentOrder();
        }

        function calculateTotal() {
            return currentOrder.reduce((total, item) => total + item.subtotal, 0);
        }

        function renderCurrentOrder() {
            const container = document.getElementById('current-order');
            
            if (currentOrder.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center py-8">No items in order</p>';
                return;
            }
            
            const orderItems = currentOrder.map(item => `
                <div class="flex justify-between items-center p-3 bg-gray-50 rounded-lg mb-4">
                    <div class="flex-1">
                        <h4 class="font-medium">${item.product.name}</h4>
                        <p class="text-sm text-gray-600">$${item.product.price.toFixed(2)} each</p>
                    </div>
                    <div class="flex items-center gap-2">
                        <button 
                            onclick="updateQuantity(${item.product.id}, ${item.quantity - 1})"
                            class="px-2 py-1 bg-white border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >-</button>
                        <span class="w-8 text-center">${item.quantity}</span>
                        <button 
                            onclick="updateQuantity(${item.product.id}, ${item.quantity + 1})"
                            class="px-2 py-1 bg-white border border-gray-300 rounded text-sm hover:bg-gray-50"
                        >+</button>
                        <button 
                            onclick="removeFromOrder(${item.product.id})"
                            class="px-2 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700"
                        >×</button>
                    </div>
                    <div class="ml-4 font-semibold">$${item.subtotal.toFixed(2)}</div>
                </div>
            `).join('');
            
            const total = calculateTotal();
            
            container.innerHTML = `
                <div class="space-y-4">
                    ${orderItems}
                    <div class="border-t pt-4">
                        <div class="flex justify-between items-center text-xl font-bold mb-4">
                            <span>Total:</span>
                            <span>$${total.toFixed(2)}</span>
                        </div>
                        <button 
                            onclick="processOrder()"
                            class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors"
                        >
                            Process Order
                        </button>
                    </div>
                </div>
            `;
        }

        async function processOrder() {
            if (currentOrder.length === 0) return;
            
            try {
                const orderData = {
                    items: currentOrder.map(item => ({
                        product_id: item.product.id,
                        quantity: item.quantity,
                        price: item.product.price,
                        subtotal: item.subtotal
                    })),
                    total: calculateTotal(),
                    status: 'pending'
                };
                
                const createdOrder = await apiService.createOrder(orderData);
                console.log('Order processed successfully:', createdOrder);
                
                // Clear current order
                currentOrder = [];
                renderCurrentOrder();
                
                // Show success message
                updateConnectionStatus('success', 'Order processed successfully!');
                setTimeout(() => {
                    updateConnectionStatus('success', 'Connected to backend successfully!');
                }, 3000);
                
            } catch (error) {
                console.error('Error processing order:', error);
                updateConnectionStatus('error', 'Failed to process order. Please try again.');
            }
        }

        // Initialize the app when the page loads
        document.addEventListener('DOMContentLoaded', initializeApp);
    </script>
</body>
</html>
