// Performance Monitoring Middleware for RESTROFLOW
const { cache } = require('../utils/cache');
const { dbOptimizer } = require('../utils/database-optimizer');

class PerformanceMonitor {
  constructor() {
    this.requestStats = {
      totalRequests: 0,
      slowRequests: 0,
      errorRequests: 0,
      requestTimes: [],
      endpointStats: new Map()
    };

    this.slowRequestThreshold = 2000; // 2 seconds
    console.log('📊 Performance Monitor initialized');
  }

  // Request timing middleware
  requestTimer() {
    return (req, res, next) => {
      const startTime = Date.now();
      const requestId = Math.random().toString(36).substr(2, 9);
      
      req.requestId = requestId;
      req.startTime = startTime;

      // Log request start
      console.log(`🌐 ${requestId} ${req.method} ${req.path} - Started`);

      // Override res.end to capture response time
      const originalEnd = res.end;
      res.end = function(...args) {
        const endTime = Date.now();
        const responseTime = endTime - startTime;
        
        // Track request performance
        this.trackRequestPerformance(req, res, responseTime);
        
        // Log request completion
        const statusEmoji = res.statusCode >= 400 ? '❌' : res.statusCode >= 300 ? '⚠️' : '✅';
        console.log(`${statusEmoji} ${requestId} ${req.method} ${req.path} - ${res.statusCode} (${responseTime}ms)`);
        
        originalEnd.apply(res, args);
      }.bind(this);

      next();
    };
  }

  // Track request performance statistics
  trackRequestPerformance(req, res, responseTime) {
    this.requestStats.totalRequests++;
    this.requestStats.requestTimes.push(responseTime);
    
    // Keep only last 1000 request times for memory efficiency
    if (this.requestStats.requestTimes.length > 1000) {
      this.requestStats.requestTimes = this.requestStats.requestTimes.slice(-1000);
    }
    
    // Track slow requests
    if (responseTime > this.slowRequestThreshold) {
      this.requestStats.slowRequests++;
      console.warn(`🐌 SLOW REQUEST: ${req.method} ${req.path} - ${responseTime}ms`);
    }
    
    // Track error requests
    if (res.statusCode >= 400) {
      this.requestStats.errorRequests++;
    }
    
    // Track per-endpoint statistics
    const endpoint = `${req.method} ${req.route?.path || req.path}`;
    if (!this.requestStats.endpointStats.has(endpoint)) {
      this.requestStats.endpointStats.set(endpoint, {
        count: 0,
        totalTime: 0,
        minTime: Infinity,
        maxTime: 0,
        errors: 0
      });
    }
    
    const endpointStat = this.requestStats.endpointStats.get(endpoint);
    endpointStat.count++;
    endpointStat.totalTime += responseTime;
    endpointStat.minTime = Math.min(endpointStat.minTime, responseTime);
    endpointStat.maxTime = Math.max(endpointStat.maxTime, responseTime);
    
    if (res.statusCode >= 400) {
      endpointStat.errors++;
    }
  }

  // Memory usage monitoring
  memoryMonitor() {
    return (req, res, next) => {
      const memUsage = process.memoryUsage();
      
      // Log memory usage for slow requests or high memory usage
      if (memUsage.heapUsed > 100 * 1024 * 1024) { // 100MB
        console.warn(`🧠 HIGH MEMORY USAGE: ${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      }
      
      // Add memory info to response headers in development
      if (process.env.NODE_ENV === 'development') {
        res.set('X-Memory-Usage', `${(memUsage.heapUsed / 1024 / 1024).toFixed(2)}MB`);
      }
      
      next();
    };
  }

  // Rate limiting middleware
  rateLimiter(maxRequests = 100, windowMs = 60000) {
    const requests = new Map();
    
    return (req, res, next) => {
      const clientId = req.ip || req.connection.remoteAddress;
      const now = Date.now();
      const windowStart = now - windowMs;
      
      // Clean old requests
      if (requests.has(clientId)) {
        const clientRequests = requests.get(clientId);
        const validRequests = clientRequests.filter(time => time > windowStart);
        requests.set(clientId, validRequests);
      }
      
      // Check rate limit
      const clientRequests = requests.get(clientId) || [];
      if (clientRequests.length >= maxRequests) {
        console.warn(`🚫 RATE LIMIT EXCEEDED: ${clientId} - ${clientRequests.length} requests`);
        return res.status(429).json({
          error: 'Too many requests',
          message: `Rate limit exceeded. Max ${maxRequests} requests per ${windowMs/1000} seconds.`,
          retry_after: Math.ceil(windowMs / 1000)
        });
      }
      
      // Add current request
      clientRequests.push(now);
      requests.set(clientId, clientRequests);
      
      next();
    };
  }

  // Response compression middleware
  compressionMiddleware() {
    return (req, res, next) => {
      // Only compress JSON responses
      const originalJson = res.json;
      
      res.json = function(data) {
        // Add compression headers for large responses
        const dataString = JSON.stringify(data);
        if (dataString.length > 1024) { // 1KB
          res.set('X-Response-Size', `${(dataString.length / 1024).toFixed(2)}KB`);
          
          // Enable gzip compression hint
          if (req.headers['accept-encoding']?.includes('gzip')) {
            res.set('Content-Encoding', 'gzip');
          }
        }
        
        return originalJson.call(this, data);
      };
      
      next();
    };
  }

  // Database connection monitoring
  dbConnectionMonitor() {
    return async (req, res, next) => {
      try {
        const poolStats = await dbOptimizer.getConnectionPoolStats();
        
        // Warn if connection pool is getting full
        const usagePercent = (poolStats.total_connections / poolStats.max_connections) * 100;
        if (usagePercent > 80) {
          console.warn(`🔌 HIGH DB CONNECTION USAGE: ${usagePercent.toFixed(1)}% (${poolStats.total_connections}/${poolStats.max_connections})`);
        }
        
        // Add connection info to response headers in development
        if (process.env.NODE_ENV === 'development') {
          res.set('X-DB-Connections', `${poolStats.total_connections}/${poolStats.max_connections}`);
          res.set('X-DB-Idle', poolStats.idle_connections.toString());
        }
        
        next();
      } catch (error) {
        console.error('❌ DB Connection Monitor Error:', error);
        next();
      }
    };
  }

  // Get performance statistics
  getStats() {
    const avgResponseTime = this.requestStats.requestTimes.length > 0
      ? this.requestStats.requestTimes.reduce((a, b) => a + b, 0) / this.requestStats.requestTimes.length
      : 0;

    const slowRequestPercent = this.requestStats.totalRequests > 0
      ? (this.requestStats.slowRequests / this.requestStats.totalRequests * 100)
      : 0;

    const errorRate = this.requestStats.totalRequests > 0
      ? (this.requestStats.errorRequests / this.requestStats.totalRequests * 100)
      : 0;

    // Convert endpoint stats to array
    const endpointStats = Array.from(this.requestStats.endpointStats.entries()).map(([endpoint, stats]) => ({
      endpoint,
      count: stats.count,
      avg_time: stats.totalTime / stats.count,
      min_time: stats.minTime === Infinity ? 0 : stats.minTime,
      max_time: stats.maxTime,
      error_rate: (stats.errors / stats.count * 100).toFixed(2) + '%'
    })).sort((a, b) => b.avg_time - a.avg_time);

    return {
      total_requests: this.requestStats.totalRequests,
      avg_response_time: Math.round(avgResponseTime),
      slow_requests: this.requestStats.slowRequests,
      slow_request_percent: slowRequestPercent.toFixed(2) + '%',
      error_requests: this.requestStats.errorRequests,
      error_rate: errorRate.toFixed(2) + '%',
      requests_per_second: this.requestStats.totalRequests / (process.uptime() || 1),
      uptime_seconds: Math.floor(process.uptime()),
      memory_usage: process.memoryUsage(),
      endpoint_stats: endpointStats.slice(0, 10), // Top 10 slowest endpoints
      cache_stats: cache.getStats()
    };
  }

  // Health check endpoint data
  async getHealthCheck() {
    try {
      const [dbHealth, cacheHealth] = await Promise.all([
        dbOptimizer.healthCheck(),
        Promise.resolve(cache.healthCheck())
      ]);

      const performanceStats = this.getStats();
      
      return {
        status: 'healthy',
        timestamp: new Date().toISOString(),
        uptime: Math.floor(process.uptime()),
        database: dbHealth,
        cache: cacheHealth,
        performance: {
          avg_response_time: performanceStats.avg_response_time,
          error_rate: performanceStats.error_rate,
          requests_per_second: performanceStats.requests_per_second.toFixed(2)
        },
        memory: {
          used_mb: (performanceStats.memory_usage.heapUsed / 1024 / 1024).toFixed(2),
          total_mb: (performanceStats.memory_usage.heapTotal / 1024 / 1024).toFixed(2)
        }
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

// Create singleton instance
const performanceMonitor = new PerformanceMonitor();

module.exports = {
  PerformanceMonitor,
  performanceMonitor
};
