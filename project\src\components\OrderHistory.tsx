import React, { useState } from 'react';
import { useAppContext } from '../context/AppContext';
import { Order } from '../types';
import { Clock, Calendar, CreditCard, DollarSign, Search, Tag, History } from 'lucide-react';

const OrderHistory: React.FC = () => {
  const { state, dispatch } = useAppContext();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDate, setSelectedDate] = useState<string>('all');
  
  // Format timestamp to readable date
  const formatDate = (timestamp: number): string => {
    const date = new Date(timestamp);
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
  };
  
  // Get payment method icon
  const getPaymentIcon = (method?: string) => {
    switch (method) {
      case 'cash':
        return <DollarSign className="h-4 w-4 text-green-400" />;
      case 'card':
        return <CreditCard className="h-4 w-4 text-blue-400" />;
      case 'mobile':
        return (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-purple-400" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
            <path d="M12 18h.01" />
          </svg>
        );
      default:
        return <CreditCard className="h-4 w-4 text-gray-400" />;
    }
  };
  
  // Filter orders by search term and date
  const filteredOrders = state.orders.filter(order => {
    const matchesSearch = 
      searchTerm === '' || 
      order.tabName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      order.items.some(item => item.name.toLowerCase().includes(searchTerm.toLowerCase()));
      
    const orderDate = new Date(order.timestamp);
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    
    const lastWeekStart = new Date(today);
    lastWeekStart.setDate(lastWeekStart.getDate() - 7);
    
    if (selectedDate === 'all') {
      return matchesSearch;
    } else if (selectedDate === 'today') {
      return matchesSearch && orderDate >= today;
    } else if (selectedDate === 'yesterday') {
      return matchesSearch && orderDate >= yesterday && orderDate < today;
    } else if (selectedDate === 'lastWeek') {
      return matchesSearch && orderDate >= lastWeekStart;
    }
    
    return matchesSearch;
  }).sort((a, b) => b.timestamp - a.timestamp);
  
  // Render an individual order
  const renderOrder = (order: Order) => (
    <div key={order.id} className="bg-gray-800 rounded-lg p-4 mb-3">
      <div className="flex justify-between items-start mb-3">
        <div>
          <div className="flex items-center">
            <Clock className="h-4 w-4 text-gray-400 mr-2" />
            <span className="text-gray-300 text-sm">{formatDate(order.timestamp)}</span>
          </div>
          
          {order.tabName && (
            <div className="flex items-center mt-1">
              <Tag className="h-4 w-4 text-purple-400 mr-2" />
              <span className="text-white">{order.tabName}</span>
            </div>
          )}
        </div>
        
        <div className="flex items-center">
          {getPaymentIcon(order.paymentMethod)}
          <span className="ml-1 text-gray-300">
            {order.paymentMethod ? order.paymentMethod.charAt(0).toUpperCase() + order.paymentMethod.slice(1) : 'Unknown'}
          </span>
        </div>
      </div>
      
      <div className="border-t border-gray-700 py-2 mb-2">
        {order.items.map(item => (
          <div key={item.id} className="flex justify-between py-1">
            <div className="text-white">
              {item.quantity}x {item.name}
            </div>
            <div className="text-gray-300">${(item.price * item.quantity).toFixed(2)}</div>
          </div>
        ))}
      </div>
      
      <div className="border-t border-gray-700 pt-2">
        <div className="flex justify-between text-sm">
          <span className="text-gray-400">Subtotal</span>
          <span className="text-gray-300">${order.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between text-sm">
          <span className="text-gray-400">Tax</span>
          <span className="text-gray-300">${order.tax.toFixed(2)}</span>
        </div>
        {order.tip !== undefined && order.tip > 0 && (
          <div className="flex justify-between text-sm">
            <span className="text-gray-400">Tip</span>
            <span className="text-gray-300">${order.tip.toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between font-semibold mt-1">
          <span className="text-white">Total</span>
          <span className="text-amber-400">${order.total.toFixed(2)}</span>
        </div>
      </div>
    </div>
  );
  
  // If no orders, show empty state
  if (state.orders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <History className="h-12 w-12 text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Order History</h3>
        <p className="text-gray-400">
          Completed orders will appear here
        </p>
      </div>
    );
  }
  
  return (
    <div className="p-4 h-full flex flex-col">
      <h2 className="text-xl font-semibold text-white mb-4">Order History</h2>
      
      {/* Search and filter */}
      <div className="flex flex-col md:flex-row space-y-2 md:space-y-0 md:space-x-2 mb-4">
        <div className="relative flex-grow">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="Search orders..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full bg-gray-800 text-white pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
          />
        </div>
        
        <select
          value={selectedDate}
          onChange={(e) => setSelectedDate(e.target.value)}
          className="bg-gray-800 text-white px-4 py-2 rounded-md focus:outline-none focus:ring-1 focus:ring-purple-500"
        >
          <option value="all">All Time</option>
          <option value="today">Today</option>
          <option value="yesterday">Yesterday</option>
          <option value="lastWeek">Last 7 Days</option>
        </select>
      </div>
      
      {/* Order count */}
      <div className="text-gray-400 text-sm mb-3">
        Showing {filteredOrders.length} of {state.orders.length} orders
      </div>
      
      {/* Orders list */}
      <div className="overflow-y-auto flex-grow scrollbar-thin scrollbar-thumb-gray-700">
        {filteredOrders.length > 0 ? (
          filteredOrders.map(renderOrder)
        ) : (
          <div className="text-center text-gray-400 py-8">
            No orders match your search criteria
          </div>
        )}
      </div>
    </div>
  );
};

export default OrderHistory;