import React, { useState, useEffect } from 'react';
import { 
  Check, 
  ChevronRight, 
  Star, 
  Users, 
  Clock, 
  MapPin, 
  Truck, 
  Calendar, 
  Building,
  Zap,
  Coffee,
  Wine,
  Music
} from 'lucide-react';
import { BusinessType, INDUSTRY_THEMES } from '../contexts/IndustryThemeContext';

interface BusinessTypeSelectorProps {
  onSelect: (businessType: BusinessType) => void;
  selectedType?: BusinessType | null;
  showDetails?: boolean;
}

// Mock business types data (in real app, this would come from API)
const BUSINESS_TYPES: BusinessType[] = [
  {
    id: '1',
    name: 'Fine Dining Restaurant',
    code: 'FINE_DINING',
    description: 'Upscale restaurants with table service, wine pairings, and multi-course meals',
    icon: '🍽️',
    colorScheme: 'burgundy',
    defaultTheme: INDUSTRY_THEMES.FINE_DINING,
    featureSet: {
      wineManagement: true,
      courseTiming: true,
      guestProfiles: true,
      sommelierNotes: true,
      tableSideService: true,
      reservationSystem: true,
      privateEvents: true
    },
    workflowConfig: {
      serviceType: 'table',
      orderFlow: 'multi_course',
      paymentTiming: 'end_of_meal',
      reservationRequired: true
    }
  },
  {
    id: '2',
    name: 'Quick Service Restaurant',
    code: 'QUICK_SERVICE',
    description: 'Fast food restaurants with counter service and quick turnover',
    icon: '⚡',
    colorScheme: 'orange',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE,
    featureSet: {
      orderQueue: true,
      kitchenDisplay: true,
      mobileOrdering: true,
      loyaltyProgram: true,
      driveThrough: true,
      speedOfService: true,
      upselling: true
    },
    workflowConfig: {
      serviceType: 'counter',
      orderFlow: 'single_step',
      paymentTiming: 'immediate',
      reservationRequired: false
    }
  },
  {
    id: '3',
    name: 'Cafe & Coffee Shop',
    code: 'CAFE',
    description: 'Coffee shops and cafes with beverage focus and light food options',
    icon: '☕',
    colorScheme: 'brown',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE, // Would have its own theme
    featureSet: {
      beverageCustomization: true,
      customerNames: true,
      preOrdering: true,
      subscriptions: true,
      seasonalMenus: true,
      baristaWorkflow: true,
      loyaltyCards: true
    },
    workflowConfig: {
      serviceType: 'counter',
      orderFlow: 'beverage_focused',
      paymentTiming: 'immediate',
      reservationRequired: false
    }
  },
  {
    id: '4',
    name: 'Bar & Pub',
    code: 'BAR',
    description: 'Bars and pubs with alcohol service and entertainment focus',
    icon: '🍺',
    colorScheme: 'blue',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE, // Would have its own theme
    featureSet: {
      alcoholTracking: true,
      ageVerification: true,
      tabManagement: true,
      happyHour: true,
      eventManagement: true,
      pourTracking: true,
      entertainment: true
    },
    workflowConfig: {
      serviceType: 'bar',
      orderFlow: 'tab_based',
      paymentTiming: 'tab_close',
      reservationRequired: false
    }
  },
  {
    id: '5',
    name: 'Food Truck',
    code: 'FOOD_TRUCK',
    description: 'Mobile food vendors with outdoor operations and limited space',
    icon: '🚚',
    colorScheme: 'vibrant',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE, // Would have its own theme
    featureSet: {
      offlineCapability: true,
      locationServices: true,
      weatherIntegration: true,
      socialMedia: true,
      mobileOptimized: true,
      routePlanning: true,
      eventBooking: true
    },
    workflowConfig: {
      serviceType: 'mobile',
      orderFlow: 'simplified',
      paymentTiming: 'immediate',
      reservationRequired: false
    }
  },
  {
    id: '6',
    name: 'Catering Service',
    code: 'CATERING',
    description: 'Event catering services with advance planning and custom menus',
    icon: '🎉',
    colorScheme: 'professional',
    defaultTheme: INDUSTRY_THEMES.QUICK_SERVICE, // Would have its own theme
    featureSet: {
      eventPlanning: true,
      customMenus: true,
      deliveryManagement: true,
      equipmentTracking: true,
      clientPortal: true,
      proposalGeneration: true,
      staffScheduling: true
    },
    workflowConfig: {
      serviceType: 'catering',
      orderFlow: 'event_based',
      paymentTiming: 'advance_deposit',
      reservationRequired: true
    }
  },
  {
    id: '7',
    name: 'Hotel Restaurant',
    code: 'HOTEL',
    description: 'Hotel restaurants with room service and multiple service types',
    icon: '🏨',
    colorScheme: 'luxury',
    defaultTheme: INDUSTRY_THEMES.FINE_DINING, // Would have its own theme
    featureSet: {
      pmsIntegration: true,
      roomService: true,
      multiLanguage: true,
      currencySupport: true,
      banquetManagement: true,
      guestBilling: true,
      conciergeServices: true
    },
    workflowConfig: {
      serviceType: 'multi',
      orderFlow: 'hotel_integrated',
      paymentTiming: 'room_charge',
      reservationRequired: true
    }
  }
];

const getBusinessTypeIcon = (code: string) => {
  switch (code) {
    case 'FINE_DINING': return Wine;
    case 'QUICK_SERVICE': return Zap;
    case 'CAFE': return Coffee;
    case 'BAR': return Music;
    case 'FOOD_TRUCK': return Truck;
    case 'CATERING': return Calendar;
    case 'HOTEL': return Building;
    default: return Users;
  }
};

const getFeatureIcon = (feature: string) => {
  switch (feature) {
    case 'wineManagement': return Wine;
    case 'orderQueue': return Clock;
    case 'beverageCustomization': return Coffee;
    case 'alcoholTracking': return Music;
    case 'offlineCapability': return Truck;
    case 'eventPlanning': return Calendar;
    case 'pmsIntegration': return Building;
    default: return Star;
  }
};

const BusinessTypeSelector: React.FC<BusinessTypeSelectorProps> = ({
  onSelect,
  selectedType,
  showDetails = true
}) => {
  const [hoveredType, setHoveredType] = useState<string | null>(null);

  const handleSelect = (businessType: BusinessType) => {
    onSelect(businessType);
  };

  const getColorClasses = (colorScheme: string, isSelected: boolean, isHovered: boolean) => {
    const baseClasses = 'transition-all duration-300 transform';
    
    if (isSelected) {
      switch (colorScheme) {
        case 'burgundy':
          return `${baseClasses} bg-gradient-to-br from-red-900 to-red-800 text-white border-red-700 shadow-xl scale-105`;
        case 'orange':
          return `${baseClasses} bg-gradient-to-br from-orange-600 to-red-500 text-white border-orange-500 shadow-xl scale-105`;
        case 'brown':
          return `${baseClasses} bg-gradient-to-br from-amber-700 to-amber-600 text-white border-amber-600 shadow-xl scale-105`;
        case 'blue':
          return `${baseClasses} bg-gradient-to-br from-blue-800 to-blue-700 text-white border-blue-600 shadow-xl scale-105`;
        case 'vibrant':
          return `${baseClasses} bg-gradient-to-br from-pink-600 to-purple-600 text-white border-pink-500 shadow-xl scale-105`;
        case 'professional':
          return `${baseClasses} bg-gradient-to-br from-indigo-700 to-indigo-600 text-white border-indigo-600 shadow-xl scale-105`;
        case 'luxury':
          return `${baseClasses} bg-gradient-to-br from-gray-800 to-gray-700 text-white border-gray-600 shadow-xl scale-105`;
        default:
          return `${baseClasses} bg-gradient-to-br from-gray-700 to-gray-600 text-white border-gray-500 shadow-xl scale-105`;
      }
    }
    
    if (isHovered) {
      switch (colorScheme) {
        case 'burgundy':
          return `${baseClasses} bg-red-50 border-red-300 text-red-900 shadow-lg hover:scale-102`;
        case 'orange':
          return `${baseClasses} bg-orange-50 border-orange-300 text-orange-900 shadow-lg hover:scale-102`;
        case 'brown':
          return `${baseClasses} bg-amber-50 border-amber-300 text-amber-900 shadow-lg hover:scale-102`;
        case 'blue':
          return `${baseClasses} bg-blue-50 border-blue-300 text-blue-900 shadow-lg hover:scale-102`;
        case 'vibrant':
          return `${baseClasses} bg-pink-50 border-pink-300 text-pink-900 shadow-lg hover:scale-102`;
        case 'professional':
          return `${baseClasses} bg-indigo-50 border-indigo-300 text-indigo-900 shadow-lg hover:scale-102`;
        case 'luxury':
          return `${baseClasses} bg-gray-50 border-gray-300 text-gray-900 shadow-lg hover:scale-102`;
        default:
          return `${baseClasses} bg-gray-50 border-gray-300 text-gray-900 shadow-lg hover:scale-102`;
      }
    }
    
    return `${baseClasses} bg-white border-gray-200 text-gray-700 hover:border-gray-300 hover:shadow-md`;
  };

  return (
    <div className="space-y-6">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-2">Choose Your Business Type</h2>
        <p className="text-gray-600 text-lg">Select the type that best describes your restaurant or hospitality business</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {BUSINESS_TYPES.map((businessType) => {
          const IconComponent = getBusinessTypeIcon(businessType.code);
          const isSelected = selectedType?.id === businessType.id;
          const isHovered = hoveredType === businessType.id;
          
          return (
            <div
              key={businessType.id}
              className={`relative p-6 rounded-2xl border-2 cursor-pointer ${getColorClasses(
                businessType.colorScheme,
                isSelected,
                isHovered
              )}`}
              onClick={() => handleSelect(businessType)}
              onMouseEnter={() => setHoveredType(businessType.id)}
              onMouseLeave={() => setHoveredType(null)}
            >
              {isSelected && (
                <div className="absolute -top-2 -right-2 bg-green-500 text-white rounded-full p-2 shadow-lg">
                  <Check className="w-4 h-4" />
                </div>
              )}
              
              <div className="flex items-center space-x-4 mb-4">
                <div className={`p-3 rounded-xl ${isSelected ? 'bg-white/20' : 'bg-gray-100'}`}>
                  <IconComponent className={`w-8 h-8 ${isSelected ? 'text-white' : 'text-gray-600'}`} />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold mb-1">{businessType.name}</h3>
                  <p className={`text-sm ${isSelected ? 'text-white/80' : 'text-gray-600'}`}>
                    {businessType.description}
                  </p>
                </div>
              </div>

              {showDetails && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className={`text-sm font-medium ${isSelected ? 'text-white/90' : 'text-gray-700'}`}>
                      Key Features:
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 gap-2">
                    {Object.entries(businessType.featureSet)
                      .slice(0, 4)
                      .map(([feature, enabled]) => {
                        if (!enabled) return null;
                        const FeatureIcon = getFeatureIcon(feature);
                        
                        return (
                          <div
                            key={feature}
                            className={`flex items-center space-x-2 text-xs ${
                              isSelected ? 'text-white/80' : 'text-gray-600'
                            }`}
                          >
                            <FeatureIcon className="w-3 h-3" />
                            <span className="capitalize">
                              {feature.replace(/([A-Z])/g, ' $1').trim()}
                            </span>
                          </div>
                        );
                      })}
                  </div>

                  <div className={`flex items-center justify-between pt-3 border-t ${
                    isSelected ? 'border-white/20' : 'border-gray-200'
                  }`}>
                    <span className={`text-sm ${isSelected ? 'text-white/90' : 'text-gray-700'}`}>
                      Service Type: {businessType.workflowConfig.serviceType}
                    </span>
                    <ChevronRight className={`w-4 h-4 ${isSelected ? 'text-white' : 'text-gray-400'}`} />
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {selectedType && (
        <div className="mt-8 p-6 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-2xl">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-green-500 text-white rounded-lg">
              <Check className="w-5 h-5" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-green-900">
                {selectedType.name} Selected
              </h3>
              <p className="text-green-700">
                Your POS system will be optimized for {selectedType.name.toLowerCase()} operations
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-800">
                {Object.values(selectedType.featureSet).filter(Boolean).length}
              </div>
              <div className="text-sm text-green-600">Specialized Features</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-800">
                {selectedType.workflowConfig.serviceType}
              </div>
              <div className="text-sm text-green-600">Service Model</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-800">
                {selectedType.workflowConfig.reservationRequired ? 'Yes' : 'No'}
              </div>
              <div className="text-sm text-green-600">Reservations</div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BusinessTypeSelector;
