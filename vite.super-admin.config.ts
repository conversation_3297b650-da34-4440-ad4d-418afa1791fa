import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    rollupOptions: {
      input: {
        'super-admin': resolve(__dirname, 'project/super-admin.html'),
      },
      output: {
        entryFileNames: 'assets/super-admin-[hash].js',
        chunkFileNames: 'assets/super-admin-[hash].js',
        assetFileNames: 'assets/super-admin-[hash].[ext]'
      }
    },
    outDir: 'dist-super-admin',
    emptyOutDir: true,
  },
  server: {
    port: 5174, // Different port for super admin
    host: true,
    cors: true,
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
      'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' https:; connect-src 'self' http://localhost:4000 ws://localhost:*;"
    }
  },
  preview: {
    port: 5174,
    host: true,
    cors: true,
    headers: {
      'X-Frame-Options': 'DENY',
      'X-Content-Type-Options': 'nosniff',
      'X-XSS-Protection': '1; mode=block',
      'Strict-Transport-Security': 'max-age=31536000; includeSubDomains'
    }
  },
  define: {
    __SUPER_ADMIN_MODE__: true,
    __SECURITY_LEVEL__: '"MAXIMUM"',
    __BUILD_TIMESTAMP__: JSON.stringify(new Date().toISOString())
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
    },
  },
  css: {
    postcss: './postcss.config.js',
  },
  optimizeDeps: {
    include: ['react', 'react-dom']
  }
})
