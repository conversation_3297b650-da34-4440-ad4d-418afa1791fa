// Enhanced Authentication System Test
// Tests all login interfaces, session management, and database integration

import fetch from 'node-fetch';

const BASE_URL = 'http://localhost:4000';
let authTokens = {
  employee: '',
  admin: ''
};

// Test data
const TEST_DATA = {
  employee: {
    pin: '123456',
    tenant_slug: 'demo-restaurant'
  },
  admin: {
    pin: '123456',
    admin_access: true
  }
};

async function testSystemHealth() {
  console.log('🏥 Testing System Health...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/health`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Backend server is healthy');
      console.log(`   Status: ${data.status}`);
      console.log(`   Timestamp: ${data.timestamp}`);
      return true;
    } else {
      console.log('❌ Backend server health check failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Backend server is not accessible:', error.message);
    return false;
  }
}

async function testTenantPublicEndpoints() {
  console.log('🏢 Testing Public Tenant Endpoints...');
  
  try {
    // Test public tenants list
    const tenantsResponse = await fetch(`${BASE_URL}/api/tenants/public`);
    
    if (tenantsResponse.ok) {
      const tenants = await tenantsResponse.json();
      console.log('✅ Public tenants endpoint working');
      console.log(`   Found ${tenants.length} active tenants`);
      
      if (tenants.length > 0) {
        const firstTenant = tenants[0];
        console.log(`   Sample tenant: ${firstTenant.business_name || firstTenant.name} (${firstTenant.slug})`);
        
        // Test specific tenant info
        const tenantResponse = await fetch(`${BASE_URL}/api/tenants/public/${firstTenant.slug}`);
        
        if (tenantResponse.ok) {
          const tenantInfo = await tenantResponse.json();
          console.log('✅ Public tenant info endpoint working');
          console.log(`   Business type: ${tenantInfo.business_type}`);
          console.log(`   Primary color: ${tenantInfo.primary_color}`);
          return true;
        } else {
          console.log('❌ Public tenant info endpoint failed');
          return false;
        }
      }
      
      return true;
    } else {
      console.log('❌ Public tenants endpoint failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Public tenant endpoints error:', error.message);
    return false;
  }
}

async function testEmployeeLogin() {
  console.log('👥 Testing Employee Login...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: TEST_DATA.employee.pin,
        tenant_slug: TEST_DATA.employee.tenant_slug
      })
    });

    const data = await response.json();
    
    if (response.ok && data.token) {
      authTokens.employee = data.token;
      console.log('✅ Employee login successful');
      console.log(`   Employee: ${data.employee.name} (${data.employee.role})`);
      console.log(`   Tenant: ${data.tenant.business_name || data.tenant.name}`);
      console.log(`   Token: ${data.token.substring(0, 20)}...`);
      
      // Verify token
      const verifyResponse = await fetch(`${BASE_URL}/api/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${data.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (verifyResponse.ok) {
        console.log('✅ Employee token verification successful');
        return true;
      } else {
        console.log('❌ Employee token verification failed');
        return false;
      }
    } else {
      console.log('❌ Employee login failed:', data.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.log('❌ Employee login error:', error.message);
    return false;
  }
}

async function testSuperAdminLogin() {
  console.log('🔒 Testing Super Admin Login...');
  
  try {
    const response = await fetch(`${BASE_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: TEST_DATA.admin.pin,
        admin_access: TEST_DATA.admin.admin_access
      })
    });

    const data = await response.json();
    
    if (response.ok && data.token && data.employee?.role === 'super_admin') {
      authTokens.admin = data.token;
      console.log('✅ Super Admin login successful');
      console.log(`   Admin: ${data.employee.name} (${data.employee.role})`);
      console.log(`   Token: ${data.token.substring(0, 20)}...`);
      
      // Verify admin token
      const verifyResponse = await fetch(`${BASE_URL}/api/auth/verify`, {
        headers: {
          'Authorization': `Bearer ${data.token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (verifyResponse.ok) {
        console.log('✅ Super Admin token verification successful');
        return true;
      } else {
        console.log('❌ Super Admin token verification failed');
        return false;
      }
    } else {
      console.log('❌ Super Admin login failed:', data.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.log('❌ Super Admin login error:', error.message);
    return false;
  }
}

async function testTenantProfileAccess() {
  console.log('🏢 Testing Tenant Profile Access...');
  
  if (!authTokens.employee) {
    console.log('❌ No employee token available for testing');
    return false;
  }
  
  try {
    const response = await fetch(`${BASE_URL}/api/tenants/profile/${TEST_DATA.employee.tenant_slug}`, {
      headers: {
        'Authorization': `Bearer ${authTokens.employee}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const profile = await response.json();
      console.log('✅ Tenant profile access successful');
      console.log(`   Business: ${profile.business_name}`);
      console.log(`   Employees: ${profile.employee_count}`);
      console.log(`   Status: ${profile.status}`);
      return true;
    } else {
      const error = await response.json();
      console.log('❌ Tenant profile access failed:', error.error || 'Unknown error');
      return false;
    }
  } catch (error) {
    console.log('❌ Tenant profile access error:', error.message);
    return false;
  }
}

async function testAdminDashboardAccess() {
  console.log('📊 Testing Admin Dashboard Access...');
  
  if (!authTokens.admin) {
    console.log('❌ No admin token available for testing');
    return false;
  }
  
  try {
    // Test admin dashboard stats
    const statsResponse = await fetch(`${BASE_URL}/api/admin/dashboard/stats`, {
      headers: {
        'Authorization': `Bearer ${authTokens.admin}`,
        'Content-Type': 'application/json'
      }
    });

    if (statsResponse.ok) {
      const stats = await statsResponse.json();
      console.log('✅ Admin dashboard stats access successful');
      console.log(`   Total tenants: ${stats.totalTenants}`);
      console.log(`   Active users: ${stats.activeUsers}`);
      
      // Test admin tenants access
      const tenantsResponse = await fetch(`${BASE_URL}/api/admin/tenants`, {
        headers: {
          'Authorization': `Bearer ${authTokens.admin}`,
          'Content-Type': 'application/json'
        }
      });

      if (tenantsResponse.ok) {
        const tenants = await tenantsResponse.json();
        console.log('✅ Admin tenants access successful');
        console.log(`   Managed tenants: ${tenants.length}`);
        return true;
      } else {
        console.log('❌ Admin tenants access failed');
        return false;
      }
    } else {
      console.log('❌ Admin dashboard stats access failed');
      return false;
    }
  } catch (error) {
    console.log('❌ Admin dashboard access error:', error.message);
    return false;
  }
}

async function testTenantDataIsolation() {
  console.log('🔒 Testing Tenant Data Isolation...');
  
  if (!authTokens.employee) {
    console.log('❌ No employee token available for testing');
    return false;
  }
  
  try {
    // Try to access another tenant's profile (should fail)
    const response = await fetch(`${BASE_URL}/api/tenants/profile/other-tenant`, {
      headers: {
        'Authorization': `Bearer ${authTokens.employee}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.status === 404 || response.status === 403) {
      console.log('✅ Tenant data isolation working correctly');
      console.log('   Employee cannot access other tenant data');
      return true;
    } else if (response.ok) {
      console.log('❌ Tenant data isolation failed - employee can access other tenant data');
      return false;
    } else {
      console.log('✅ Tenant data isolation working (access denied)');
      return true;
    }
  } catch (error) {
    console.log('❌ Tenant data isolation test error:', error.message);
    return false;
  }
}

async function runAllTests() {
  console.log('🚀 Starting Enhanced Authentication System Tests');
  console.log('=================================================');
  
  const results = {
    systemHealth: false,
    publicEndpoints: false,
    employeeLogin: false,
    adminLogin: false,
    tenantProfile: false,
    adminDashboard: false,
    dataIsolation: false
  };

  // Test system health
  results.systemHealth = await testSystemHealth();
  
  if (!results.systemHealth) {
    console.log('❌ Cannot proceed without backend server');
    return results;
  }

  // Test public endpoints
  results.publicEndpoints = await testTenantPublicEndpoints();
  
  // Test employee login
  results.employeeLogin = await testEmployeeLogin();
  
  // Test super admin login
  results.adminLogin = await testSuperAdminLogin();
  
  // Test tenant profile access
  results.tenantProfile = await testTenantProfileAccess();
  
  // Test admin dashboard access
  results.adminDashboard = await testAdminDashboardAccess();
  
  // Test tenant data isolation
  results.dataIsolation = await testTenantDataIsolation();

  console.log('\n📋 Enhanced Authentication Test Results');
  console.log('========================================');
  console.log(`System Health:        ${results.systemHealth ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Public Endpoints:     ${results.publicEndpoints ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Employee Login:       ${results.employeeLogin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Super Admin Login:    ${results.adminLogin ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Tenant Profile:       ${results.tenantProfile ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Admin Dashboard:      ${results.adminDashboard ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Data Isolation:       ${results.dataIsolation ? '✅ PASS' : '❌ FAIL'}`);

  const allPassed = Object.values(results).every(result => result);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('🎉 Enhanced authentication system is working correctly!');
    console.log('🔒 All security features are properly implemented:');
    console.log('   • Tenant profile separation');
    console.log('   • Role-based access control');
    console.log('   • Session management');
    console.log('   • Data isolation');
    console.log('   • PostgreSQL database integration');
    console.log('   • No mock data dependencies');
  } else {
    console.log('⚠️ Some issues detected. Check the logs above for details.');
  }

  return results;
}

// Run the tests
runAllTests().catch(console.error);
