import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Zap,
  TrendingUp,
  Database,
  Server,
  Cpu,
  HardDrive,
  Network,
  Clock,
  CheckCircle,
  AlertTriangle,
  Settings,
  BarChart3,
  RefreshCw,
  Gauge
} from 'lucide-react';

interface OptimizationMetrics {
  overallScore: number;
  categories: {
    database: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    api: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    frontend: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
    infrastructure: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
  };
  optimizations: Array<{
    id: string;
    title: string;
    description: string;
    impact: 'high' | 'medium' | 'low';
    effort: 'high' | 'medium' | 'low';
    category: string;
    status: 'pending' | 'in-progress' | 'completed';
    estimatedImprovement: string;
  }>;
  performanceTrends: {
    responseTime: number[];
    throughput: number[];
    errorRate: number[];
    labels: string[];
  };
}

export function Phase2COptimizationDashboard() {
  const [metrics, setMetrics] = useState<OptimizationMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const fetchOptimizationData = async () => {
    try {
      setLoading(true);
      
      // Mock optimization data - in production, this would come from real analysis
      const mockData: OptimizationMetrics = {
        overallScore: 87,
        categories: {
          database: {
            score: 92,
            issues: [
              'Connection pool could be optimized',
              'Some queries lack proper indexing'
            ],
            recommendations: [
              'Increase connection pool size to 25',
              'Add composite indexes on frequently queried columns',
              'Implement query result caching'
            ]
          },
          api: {
            score: 85,
            issues: [
              'Some endpoints have high response times',
              'Rate limiting could be more granular'
            ],
            recommendations: [
              'Implement response caching for read-heavy endpoints',
              'Add request compression',
              'Optimize serialization logic'
            ]
          },
          frontend: {
            score: 89,
            issues: [
              'Bundle size could be reduced',
              'Some components re-render unnecessarily'
            ],
            recommendations: [
              'Implement code splitting',
              'Add React.memo to expensive components',
              'Optimize image loading with lazy loading'
            ]
          },
          infrastructure: {
            score: 83,
            issues: [
              'CPU usage spikes during peak hours',
              'Memory usage could be more efficient'
            ],
            recommendations: [
              'Scale horizontally during peak times',
              'Implement memory pooling',
              'Add CDN for static assets'
            ]
          }
        },
        optimizations: [
          {
            id: 'opt-1',
            title: 'Database Query Optimization',
            description: 'Optimize slow-running queries and add missing indexes',
            impact: 'high',
            effort: 'medium',
            category: 'database',
            status: 'pending',
            estimatedImprovement: '25% faster query response'
          },
          {
            id: 'opt-2',
            title: 'API Response Caching',
            description: 'Implement Redis caching for frequently accessed endpoints',
            impact: 'high',
            effort: 'medium',
            category: 'api',
            status: 'in-progress',
            estimatedImprovement: '40% reduction in response time'
          },
          {
            id: 'opt-3',
            title: 'Frontend Bundle Optimization',
            description: 'Implement code splitting and tree shaking',
            impact: 'medium',
            effort: 'low',
            category: 'frontend',
            status: 'completed',
            estimatedImprovement: '30% smaller bundle size'
          },
          {
            id: 'opt-4',
            title: 'Auto-scaling Implementation',
            description: 'Set up automatic scaling based on CPU and memory usage',
            impact: 'high',
            effort: 'high',
            category: 'infrastructure',
            status: 'pending',
            estimatedImprovement: '50% better peak performance'
          }
        ],
        performanceTrends: {
          responseTime: [45, 42, 38, 35, 32, 30, 28],
          throughput: [120, 125, 130, 135, 140, 145, 150],
          errorRate: [2.1, 1.8, 1.5, 1.2, 0.9, 0.7, 0.5],
          labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7']
        }
      };

      setMetrics(mockData);
    } catch (error) {
      console.error('Failed to fetch optimization data:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchOptimizationData();
  }, []);

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in-progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredOptimizations = selectedCategory === 'all' 
    ? metrics?.optimizations 
    : metrics?.optimizations.filter(opt => opt.category === selectedCategory);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">System Optimization</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 8 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2C System Optimization</h2>
          <p className="text-gray-600">Advanced performance analysis and optimization recommendations</p>
        </div>
        <Button
          variant="outline"
          onClick={fetchOptimizationData}
          disabled={loading}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
          Refresh Analysis
        </Button>
      </div>

      {/* Overall Score */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Gauge className="h-5 w-5 mr-2" />
            Overall Optimization Score
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className={`text-4xl font-bold ${getScoreColor(metrics?.overallScore || 0)}`}>
                {metrics?.overallScore}%
              </div>
              <div className="text-sm text-gray-600">Overall Score</div>
            </div>
            <div className="flex-1">
              <Progress value={metrics?.overallScore || 0} className="h-3" />
              <div className="mt-2 text-sm text-gray-600">
                System is performing well with room for targeted improvements
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Category Scores */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {Object.entries(metrics?.categories || {}).map(([category, data]) => (
          <Card key={category}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium capitalize">{category}</CardTitle>
              {category === 'database' && <Database className="h-4 w-4 text-muted-foreground" />}
              {category === 'api' && <Network className="h-4 w-4 text-muted-foreground" />}
              {category === 'frontend' && <BarChart3 className="h-4 w-4 text-muted-foreground" />}
              {category === 'infrastructure' && <Server className="h-4 w-4 text-muted-foreground" />}
            </CardHeader>
            <CardContent>
              <div className={`text-2xl font-bold ${getScoreColor(data.score)}`}>
                {data.score}%
              </div>
              <Progress value={data.score} className="mt-2 h-2" />
              <div className="mt-2 text-xs text-gray-600">
                {data.issues.length} issues, {data.recommendations.length} recommendations
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Optimization Recommendations */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Optimization Recommendations
            </CardTitle>
            <div className="flex space-x-2">
              <Button
                variant={selectedCategory === 'all' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory('all')}
              >
                All
              </Button>
              {Object.keys(metrics?.categories || {}).map(category => (
                <Button
                  key={category}
                  variant={selectedCategory === category ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setSelectedCategory(category)}
                  className="capitalize"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredOptimizations?.map((optimization) => (
              <div key={optimization.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-semibold text-gray-900">{optimization.title}</h4>
                      <Badge className={getImpactColor(optimization.impact)}>
                        {optimization.impact} impact
                      </Badge>
                      <Badge className={getStatusColor(optimization.status)}>
                        {optimization.status}
                      </Badge>
                    </div>
                    <p className="text-gray-600 text-sm mb-2">{optimization.description}</p>
                    <div className="flex items-center space-x-4 text-sm text-gray-500">
                      <span>Effort: {optimization.effort}</span>
                      <span>•</span>
                      <span>Category: {optimization.category}</span>
                      <span>•</span>
                      <span className="text-green-600">{optimization.estimatedImprovement}</span>
                    </div>
                  </div>
                  <div className="ml-4">
                    {optimization.status === 'completed' && (
                      <CheckCircle className="h-5 w-5 text-green-500" />
                    )}
                    {optimization.status === 'in-progress' && (
                      <Clock className="h-5 w-5 text-blue-500" />
                    )}
                    {optimization.status === 'pending' && (
                      <AlertTriangle className="h-5 w-5 text-gray-400" />
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Performance Trends */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="h-5 w-5 mr-2" />
            Performance Trends (7 Weeks)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Response Time (ms)</h4>
              <div className="space-y-2">
                {metrics?.performanceTrends.responseTime.map((value, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{metrics.performanceTrends.labels[index]}</span>
                    <span className="font-medium">{value}ms</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Throughput (req/s)</h4>
              <div className="space-y-2">
                {metrics?.performanceTrends.throughput.map((value, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{metrics.performanceTrends.labels[index]}</span>
                    <span className="font-medium text-green-600">{value}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Error Rate (%)</h4>
              <div className="space-y-2">
                {metrics?.performanceTrends.errorRate.map((value, index) => (
                  <div key={index} className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">{metrics.performanceTrends.labels[index]}</span>
                    <span className="font-medium text-red-600">{value}%</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
