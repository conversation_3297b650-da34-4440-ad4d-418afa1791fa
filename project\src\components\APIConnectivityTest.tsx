import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>riangle, CheckCircle, X, Wifi, WifiOff, Server } from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

const APIConnectivityTest: React.FC = () => {
  const { apiCall, state } = useEnhancedAppContext();
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [backendStatus, setBackendStatus] = useState<'unknown' | 'connected' | 'disconnected'>('unknown');
  const [authStatus, setAuthStatus] = useState<'unknown' | 'authenticated' | 'unauthenticated'>('unknown');

  const addTestResult = (result: string) => {
    setTestResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${result}`]);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  // Test backend connectivity
  const testBackendConnectivity = async () => {
    try {
      addTestResult('🔍 Testing backend connectivity...');
      
      // Test direct fetch to health endpoint
      const response = await fetch('http://localhost:4000/api/health');
      
      if (response.ok) {
        const data = await response.json();
        addTestResult(`✅ Backend is running: ${JSON.stringify(data)}`);
        setBackendStatus('connected');
        return true;
      } else {
        addTestResult(`❌ Backend health check failed: ${response.status} ${response.statusText}`);
        setBackendStatus('disconnected');
        return false;
      }
    } catch (error) {
      addTestResult(`💥 Backend connection error: ${error}`);
      setBackendStatus('disconnected');
      return false;
    }
  };

  // Test authentication status
  const testAuthenticationStatus = async () => {
    try {
      addTestResult('🔐 Testing authentication status...');
      addTestResult(`📋 Current auth token: ${state.authToken ? 'Present' : 'Missing'}`);
      addTestResult(`👤 Current user: ${state.currentEmployee?.name || 'None'}`);
      addTestResult(`🏢 Current tenant: ${state.currentTenant?.name || 'None'}`);
      
      if (state.authToken) {
        setAuthStatus('authenticated');
        addTestResult('✅ User is authenticated');
      } else {
        setAuthStatus('unauthenticated');
        addTestResult('❌ User is not authenticated');
      }
    } catch (error) {
      addTestResult(`💥 Auth status check error: ${error}`);
      setAuthStatus('unauthenticated');
    }
  };

  // Test category endpoints specifically
  const testCategoryEndpoints = async () => {
    try {
      addTestResult('📂 Testing category endpoints...');
      
      // Test GET categories
      addTestResult('🔍 Testing GET /api/tenant/categories...');
      const getResponse = await apiCall('/api/tenant/categories');
      addTestResult(`📡 GET Response: ${getResponse.status} ${getResponse.statusText}`);
      
      if (getResponse.ok) {
        const categories = await getResponse.json();
        addTestResult(`✅ GET categories successful: ${categories.length} categories found`);
      } else {
        const errorText = await getResponse.text();
        addTestResult(`❌ GET categories failed: ${errorText}`);
      }

      // Test POST categories
      addTestResult('🔍 Testing POST /api/tenant/categories...');
      const testCategory = {
        name: `Test Category ${Date.now()}`,
        description: 'API connectivity test category',
        color: '#FF5733',
        is_active: true
      };
      
      const postResponse = await apiCall('/api/tenant/categories', {
        method: 'POST',
        body: JSON.stringify(testCategory)
      });
      
      addTestResult(`📡 POST Response: ${postResponse.status} ${postResponse.statusText}`);
      
      if (postResponse.ok) {
        const newCategory = await postResponse.json();
        addTestResult(`✅ POST category successful: ${JSON.stringify(newCategory)}`);
      } else {
        const errorText = await postResponse.text();
        addTestResult(`❌ POST category failed: ${errorText}`);
      }
      
    } catch (error) {
      addTestResult(`💥 Category endpoint test error: ${error}`);
    }
  };

  // Test API call function behavior
  const testAPICallFunction = async () => {
    try {
      addTestResult('🔧 Testing apiCall function behavior...');
      
      // Test with a non-existent endpoint to see fallback behavior
      const testResponse = await apiCall('/api/test/nonexistent');
      addTestResult(`📡 Test endpoint response: ${testResponse.status} ${testResponse.statusText}`);
      
      if (testResponse.status === 404) {
        const responseText = await testResponse.text();
        addTestResult(`📄 Response body: ${responseText}`);
        
        if (responseText.includes('Mock mode')) {
          addTestResult('🔧 API is in mock mode (backend not connected)');
        } else {
          addTestResult('🌐 API is connected to real backend');
        }
      }
    } catch (error) {
      addTestResult(`💥 API call function test error: ${error}`);
    }
  };

  // Run comprehensive connectivity test
  const runComprehensiveTest = async () => {
    setIsLoading(true);
    clearResults();
    
    addTestResult('🚀 Starting comprehensive API connectivity test...');
    
    await testBackendConnectivity();
    await testAuthenticationStatus();
    await testAPICallFunction();
    await testCategoryEndpoints();
    
    addTestResult('🏁 Comprehensive test completed!');
    setIsLoading(false);
  };

  // Auto-run basic tests on component mount
  useEffect(() => {
    testBackendConnectivity();
    testAuthenticationStatus();
  }, []);

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">API Connectivity Diagnostics</h2>
        <p className="text-gray-600 mb-6">
          This component diagnoses API connectivity issues and tests the category creation endpoints.
        </p>

        {/* Status Indicators */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
          <div className="flex items-center space-x-3 p-4 border rounded-lg">
            {backendStatus === 'connected' ? (
              <Wifi className="h-6 w-6 text-green-500" />
            ) : backendStatus === 'disconnected' ? (
              <WifiOff className="h-6 w-6 text-red-500" />
            ) : (
              <Server className="h-6 w-6 text-gray-400" />
            )}
            <div>
              <h3 className="font-medium text-gray-900">Backend Status</h3>
              <p className={`text-sm ${
                backendStatus === 'connected' ? 'text-green-600' : 
                backendStatus === 'disconnected' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {backendStatus === 'connected' ? 'Connected' : 
                 backendStatus === 'disconnected' ? 'Disconnected' : 'Unknown'}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-3 p-4 border rounded-lg">
            {authStatus === 'authenticated' ? (
              <CheckCircle className="h-6 w-6 text-green-500" />
            ) : authStatus === 'unauthenticated' ? (
              <X className="h-6 w-6 text-red-500" />
            ) : (
              <AlertTriangle className="h-6 w-6 text-gray-400" />
            )}
            <div>
              <h3 className="font-medium text-gray-900">Authentication</h3>
              <p className={`text-sm ${
                authStatus === 'authenticated' ? 'text-green-600' : 
                authStatus === 'unauthenticated' ? 'text-red-600' : 'text-gray-500'
              }`}>
                {authStatus === 'authenticated' ? 'Authenticated' : 
                 authStatus === 'unauthenticated' ? 'Not Authenticated' : 'Unknown'}
              </p>
            </div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="flex flex-wrap gap-3 mb-6">
          <button
            onClick={testBackendConnectivity}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
          >
            Test Backend
          </button>
          
          <button
            onClick={testAuthenticationStatus}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50"
          >
            Check Auth
          </button>

          <button
            onClick={testCategoryEndpoints}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 disabled:opacity-50"
          >
            Test Categories
          </button>

          <button
            onClick={runComprehensiveTest}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 disabled:opacity-50"
          >
            Full Test
          </button>

          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {isLoading && (
          <div className="flex items-center justify-center py-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <span className="ml-2 text-gray-600">Testing...</span>
          </div>
        )}
      </div>

      {/* Current State Information */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Current State</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">API Configuration</h4>
            <div className="space-y-1 text-gray-600">
              <div>Base URL: http://localhost:4000</div>
              <div>Auth Token: {state.authToken ? 'Present' : 'Missing'}</div>
              <div>User Role: {state.currentEmployee?.role || 'None'}</div>
            </div>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Tenant Information</h4>
            <div className="space-y-1 text-gray-600">
              <div>Tenant: {state.currentTenant?.name || 'None'}</div>
              <div>Location: {state.currentLocation?.name || 'None'}</div>
              <div>Tenant ID: {state.currentTenant?.id || 'None'}</div>
            </div>
          </div>
        </div>
      </div>

      {/* Test Results */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Results</h3>
        <div className="bg-gray-50 rounded-md p-4 max-h-96 overflow-y-auto">
          {testResults.length === 0 ? (
            <p className="text-gray-500">No test results yet. Run a test to see results here.</p>
          ) : (
            <div className="space-y-1">
              {testResults.map((result, index) => (
                <div key={index} className="text-sm font-mono text-gray-700">
                  {result}
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Troubleshooting Guide */}
      <div className="bg-white rounded-lg shadow-sm border p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Troubleshooting Guide</h3>
        <div className="space-y-3 text-sm">
          <div>
            <strong>404 Not Found Error:</strong>
            <ul className="list-disc list-inside ml-4 text-gray-600">
              <li>Backend server may not be running (check http://localhost:4000/api/health)</li>
              <li>Endpoint path may be incorrect</li>
              <li>Authentication middleware may be blocking the request</li>
            </ul>
          </div>
          <div>
            <strong>Authentication Issues:</strong>
            <ul className="list-disc list-inside ml-4 text-gray-600">
              <li>Login with valid credentials (PIN: 123456 for super admin)</li>
              <li>Check if auth token is present and valid</li>
              <li>Verify tenant context is properly set</li>
            </ul>
          </div>
          <div>
            <strong>Backend Connection:</strong>
            <ul className="list-disc list-inside ml-4 text-gray-600">
              <li>Start backend: cd backend && npm run dev</li>
              <li>Check if port 4000 is available</li>
              <li>Verify CORS settings allow frontend requests</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default APIConnectivityTest;
