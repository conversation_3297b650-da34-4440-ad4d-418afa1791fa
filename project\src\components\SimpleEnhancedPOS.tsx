import React, { useState } from 'react';

const SimpleEnhancedPOS: React.FC = () => {
  const [currentOrder, setCurrentOrder] = useState<any[]>([]);
  const [total, setTotal] = useState(0);

  const products = [
    { name: 'Coffee', price: 4.99, emoji: '☕' },
    { name: 'Sandwich', price: 8.99, emoji: '🥪' },
    { name: 'Salad', price: 12.99, emoji: '🥗' },
    { name: 'Pizza', price: 15.99, emoji: '🍕' },
    { name: 'Burger', price: 11.99, emoji: '🍔' },
    { name: 'Pasta', price: 13.99, emoji: '🍝' }
  ];

  const addToOrder = (product: any) => {
    setCurrentOrder([...currentOrder, product]);
    setTotal(total + product.price);
  };

  const clearOrder = () => {
    setCurrentOrder([]);
    setTotal(0);
  };

  return (
    <div className="space-y-6 p-6 bg-gradient-to-br from-green-50 to-blue-50 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 flex items-center gap-3">
            🛒 Enhanced POS System
          </h1>
          <p className="text-gray-600 mt-2">
            AI-powered transaction processing with global payment support
          </p>
        </div>
        <div className="flex gap-2">
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
            ✅ System Online
          </span>
          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
            🌍 Global Ready
          </span>
        </div>
      </div>

      {/* Main POS Interface */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Product Grid */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Products</h3>
            </div>
            <div className="px-6 py-4">
              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                {products.map((product, index) => (
                  <button
                    key={index}
                    onClick={() => addToOrder(product)}
                    className="p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors text-center"
                  >
                    <div className="text-2xl mb-2">{product.emoji}</div>
                    <div className="font-medium text-gray-900">{product.name}</div>
                    <div className="text-sm text-gray-600">${product.price}</div>
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Order Summary */}
        <div className="space-y-6">
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Current Order</h3>
            </div>
            <div className="px-6 py-4">
              {currentOrder.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No items in order</p>
              ) : (
                <div className="space-y-2">
                  {currentOrder.map((item, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <span className="text-sm">{item.emoji} {item.name}</span>
                      <span className="text-sm font-medium">${item.price}</span>
                    </div>
                  ))}
                  <div className="border-t pt-2 mt-4">
                    <div className="flex justify-between items-center font-bold">
                      <span>Total:</span>
                      <span>${total.toFixed(2)}</span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Payment Options */}
          <div className="bg-white rounded-lg shadow-md border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">Payment</h3>
            </div>
            <div className="px-6 py-4 space-y-3">
              <button className="w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors">
                💳 Credit Card
              </button>
              <button className="w-full bg-green-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-700 transition-colors">
                💰 Cash
              </button>
              <button className="w-full bg-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-purple-700 transition-colors">
                📱 Digital Wallet
              </button>
              <button 
                onClick={clearOrder}
                className="w-full bg-gray-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-gray-700 transition-colors"
              >
                🗑️ Clear Order
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* AI Features */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">🤖 AI Fraud Detection</h4>
          <p className="text-sm text-gray-600">Real-time transaction analysis</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
              96.5% Accuracy
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">🌍 Global Payments</h4>
          <p className="text-sm text-gray-600">15+ currencies supported</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              23 Countries
            </span>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-md border border-gray-200 p-6">
          <h4 className="font-semibold text-gray-900 mb-2">⚡ Performance</h4>
          <p className="text-sm text-gray-600">Sub-second processing</p>
          <div className="mt-2">
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
              &lt;500ms
            </span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SimpleEnhancedPOS;
