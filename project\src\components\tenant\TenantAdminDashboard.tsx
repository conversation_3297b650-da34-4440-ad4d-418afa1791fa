// Tenant Admin Dashboard Component
import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardH<PERSON>er, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';

interface TenantProfile {
  id: string;
  name: string;
  business_name: string;
  employee_count: number;
  active_employees: number;
  location_count: number;
  created_at: string;
}

interface Employee {
  id: number;
  name: string;
  role: string;
  location_id: string;
  is_active: boolean;
  permissions: string[];
  created_at: string;
}

interface Analytics {
  totalOrders: number;
  totalRevenue: number;
  averageOrderValue: number;
  ordersByStatus: {
    pending: number;
    preparing: number;
    ready: number;
    completed: number;
    cancelled: number;
  };
}

const TenantAdminDashboard: React.FC = () => {
  const [profile, setProfile] = useState<TenantProfile | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);
  const [analytics, setAnalytics] = useState<Analytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'employees' | 'analytics' | 'settings'>('overview');

  useEffect(() => {
    fetchTenantData();
  }, []);

  const fetchTenantData = async () => {
    try {
      setLoading(true);
      
      // Mock data for demonstration
      const mockProfile: TenantProfile = {
        id: '1',
        name: 'Demo Restaurant',
        business_name: 'Demo Restaurant LLC',
        employee_count: 8,
        active_employees: 6,
        location_count: 2,
        created_at: '2024-01-15T10:00:00Z'
      };

      const mockEmployees: Employee[] = [
        {
          id: 1,
          name: 'John Manager',
          role: 'manager',
          location_id: '1',
          is_active: true,
          permissions: ['pos_access', 'reports', 'employee_management'],
          created_at: '2024-01-15T10:00:00Z'
        },
        {
          id: 2,
          name: 'Jane Server',
          role: 'server',
          location_id: '1',
          is_active: true,
          permissions: ['pos_access'],
          created_at: '2024-01-16T09:00:00Z'
        },
        {
          id: 3,
          name: 'Mike Cook',
          role: 'kitchen',
          location_id: '1',
          is_active: true,
          permissions: ['kitchen_display'],
          created_at: '2024-01-17T08:00:00Z'
        }
      ];

      const mockAnalytics: Analytics = {
        totalOrders: 245,
        totalRevenue: 12450.75,
        averageOrderValue: 50.82,
        ordersByStatus: {
          pending: 3,
          preparing: 5,
          ready: 2,
          completed: 230,
          cancelled: 5
        }
      };

      setProfile(mockProfile);
      setEmployees(mockEmployees);
      setAnalytics(mockAnalytics);
      
    } catch (error) {
      console.error('Error fetching tenant data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager': return 'bg-blue-500';
      case 'server': return 'bg-green-500';
      case 'kitchen': return 'bg-orange-500';
      case 'cashier': return 'bg-purple-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-4xl font-bold text-gray-900 mb-2">{profile?.business_name}</h1>
          <p className="text-gray-600">Tenant Administration Dashboard</p>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-6">
          <div className="flex space-x-1 bg-white rounded-lg p-1 shadow-sm">
            {[
              { id: 'overview', label: 'Overview' },
              { id: 'employees', label: 'Employees' },
              { id: 'analytics', label: 'Analytics' },
              { id: 'settings', label: 'Settings' }
            ].map(tab => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "default" : "ghost"}
                onClick={() => setActiveTab(tab.id as any)}
                className="flex-1"
              >
                {tab.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Overview Tab */}
        {activeTab === 'overview' && profile && analytics && (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Active Employees</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{profile.active_employees}</div>
                  <p className="text-xs text-gray-500">of {profile.employee_count} total</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Orders</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.totalOrders}</div>
                  <p className="text-xs text-green-600">+15 today</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Total Revenue</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(analytics.totalRevenue)}</div>
                  <p className="text-xs text-green-600">+8% this week</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium text-gray-600">Avg Order Value</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{formatCurrency(analytics.averageOrderValue)}</div>
                  <p className="text-xs text-green-600">+3% vs last week</p>
                </CardContent>
              </Card>
            </div>

            {/* Order Status Overview */}
            <Card>
              <CardHeader>
                <CardTitle>Current Order Status</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
                  {Object.entries(analytics.ordersByStatus).map(([status, count]) => (
                    <div key={status} className="text-center p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{count}</div>
                      <p className="text-sm text-gray-600 capitalize">{status}</p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Employees Tab */}
        {activeTab === 'employees' && (
          <div className="space-y-6">
            <div className="flex justify-between items-center">
              <h2 className="text-2xl font-bold">Employee Management</h2>
              <Button>Add Employee</Button>
            </div>
            
            <Card>
              <CardContent className="p-0">
                <div className="space-y-0">
                  {employees.map((employee, index) => (
                    <div key={employee.id} className={`p-6 ${index !== employees.length - 1 ? 'border-b' : ''}`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`w-10 h-10 rounded-full ${getRoleColor(employee.role)} flex items-center justify-center text-white font-semibold`}>
                            {employee.name.charAt(0)}
                          </div>
                          <div>
                            <h3 className="font-semibold text-lg">{employee.name}</h3>
                            <p className="text-gray-600 capitalize">{employee.role}</p>
                            <p className="text-sm text-gray-500">Joined: {formatDate(employee.created_at)}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right">
                            <div className="flex flex-wrap gap-1 mb-2">
                              {employee.permissions.map(permission => (
                                <Badge key={permission} variant="outline" className="text-xs">
                                  {permission.replace('_', ' ')}
                                </Badge>
                              ))}
                            </div>
                            <Badge variant={employee.is_active ? 'default' : 'secondary'}>
                              {employee.is_active ? 'Active' : 'Inactive'}
                            </Badge>
                          </div>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Analytics Tab */}
        {activeTab === 'analytics' && analytics && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">Business Analytics</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle>Revenue Metrics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span>Total Revenue:</span>
                      <span className="font-semibold">{formatCurrency(analytics.totalRevenue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Average Order Value:</span>
                      <span className="font-semibold">{formatCurrency(analytics.averageOrderValue)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Total Orders:</span>
                      <span className="font-semibold">{analytics.totalOrders}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Order Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.ordersByStatus).map(([status, count]) => {
                      const percentage = (count / analytics.totalOrders) * 100;
                      return (
                        <div key={status}>
                          <div className="flex justify-between text-sm mb-1">
                            <span className="capitalize">{status}</span>
                            <span>{count} ({percentage.toFixed(1)}%)</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${percentage}%` }}
                            ></div>
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        )}

        {/* Settings Tab */}
        {activeTab === 'settings' && profile && (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold">Restaurant Settings</h2>
            
            <Card>
              <CardHeader>
                <CardTitle>Business Information</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Business Name</label>
                    <input 
                      type="text" 
                      value={profile.business_name}
                      className="w-full p-3 border border-gray-300 rounded-lg"
                      readOnly
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Display Name</label>
                    <input 
                      type="text" 
                      value={profile.name}
                      className="w-full p-3 border border-gray-300 rounded-lg"
                      readOnly
                    />
                  </div>
                </div>
                <div className="mt-6">
                  <Button>Update Settings</Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  );
};

export default TenantAdminDashboard;
