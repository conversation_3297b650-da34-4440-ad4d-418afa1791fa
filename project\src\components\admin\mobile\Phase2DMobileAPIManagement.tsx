import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Smartphone,
  Globe,
  Key,
  BarChart3,
  Shield,
  Zap,
  Clock,
  Users,
  Download,
  Upload,
  Settings,
  RefreshCw,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Database,
  Monitor,
  Wifi
} from 'lucide-react';

interface APIEndpoint {
  id: string;
  path: string;
  method: string;
  description: string;
  usage: number;
  responseTime: number;
  errorRate: number;
  status: 'healthy' | 'warning' | 'error';
  lastUsed: Date;
}

interface MobileApp {
  id: string;
  name: string;
  platform: 'ios' | 'android' | 'web';
  version: string;
  downloads: number;
  activeUsers: number;
  rating: number;
  status: 'live' | 'beta' | 'development';
  lastUpdate: Date;
}

export function Phase2DMobileAPIManagement() {
  const [apiEndpoints, setApiEndpoints] = useState<APIEndpoint[]>([]);
  const [mobileApps, setMobileApps] = useState<MobileApp[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'api' | 'mobile'>('api');

  const mockAPIEndpoints: APIEndpoint[] = [
    {
      id: 'api-1',
      path: '/api/auth/login',
      method: 'POST',
      description: 'User authentication endpoint',
      usage: 15420,
      responseTime: 145,
      errorRate: 0.2,
      status: 'healthy',
      lastUsed: new Date(Date.now() - 60000)
    },
    {
      id: 'api-2',
      path: '/api/orders',
      method: 'GET',
      description: 'Retrieve order information',
      usage: 8930,
      responseTime: 89,
      errorRate: 0.1,
      status: 'healthy',
      lastUsed: new Date(Date.now() - 30000)
    },
    {
      id: 'api-3',
      path: '/api/payments/process',
      method: 'POST',
      description: 'Process payment transactions',
      usage: 5670,
      responseTime: 320,
      errorRate: 1.2,
      status: 'warning',
      lastUsed: new Date(Date.now() - 120000)
    },
    {
      id: 'api-4',
      path: '/api/inventory',
      method: 'GET',
      description: 'Inventory management data',
      usage: 3450,
      responseTime: 67,
      errorRate: 0.05,
      status: 'healthy',
      lastUsed: new Date(Date.now() - 180000)
    },
    {
      id: 'api-5',
      path: '/api/analytics/sales',
      method: 'GET',
      description: 'Sales analytics and reporting',
      usage: 2340,
      responseTime: 890,
      errorRate: 3.5,
      status: 'error',
      lastUsed: new Date(Date.now() - 300000)
    }
  ];

  const mockMobileApps: MobileApp[] = [
    {
      id: 'app-1',
      name: 'POS Manager',
      platform: 'ios',
      version: '2.1.4',
      downloads: 12500,
      activeUsers: 8900,
      rating: 4.7,
      status: 'live',
      lastUpdate: new Date(Date.now() - 604800000) // 1 week ago
    },
    {
      id: 'app-2',
      name: 'POS Manager',
      platform: 'android',
      version: '2.1.3',
      downloads: 18700,
      activeUsers: 13200,
      rating: 4.5,
      status: 'live',
      lastUpdate: new Date(Date.now() - **********) // 2 weeks ago
    },
    {
      id: 'app-3',
      name: 'Customer App',
      platform: 'ios',
      version: '1.8.2',
      downloads: 45600,
      activeUsers: 28900,
      rating: 4.3,
      status: 'live',
      lastUpdate: new Date(Date.now() - 432000000) // 5 days ago
    },
    {
      id: 'app-4',
      name: 'Customer App',
      platform: 'android',
      version: '1.8.1',
      downloads: 67800,
      activeUsers: 41200,
      rating: 4.1,
      status: 'live',
      lastUpdate: new Date(Date.now() - 864000000) // 10 days ago
    },
    {
      id: 'app-5',
      name: 'Kitchen Display',
      platform: 'web',
      version: '3.0.0-beta',
      downloads: 0,
      activeUsers: 156,
      rating: 0,
      status: 'beta',
      lastUpdate: new Date(Date.now() - 86400000) // 1 day ago
    }
  ];

  useEffect(() => {
    setTimeout(() => {
      setApiEndpoints(mockAPIEndpoints);
      setMobileApps(mockMobileApps);
      setLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'live': return 'bg-green-100 text-green-800';
      case 'warning':
      case 'beta': return 'bg-yellow-100 text-yellow-800';
      case 'error':
      case 'development': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
      case 'live': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning':
      case 'beta': return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error':
      case 'development': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getPlatformIcon = (platform: string) => {
    switch (platform) {
      case 'ios': return '🍎';
      case 'android': return '🤖';
      case 'web': return '🌐';
      default: return '📱';
    }
  };

  const getMethodColor = (method: string) => {
    switch (method) {
      case 'GET': return 'bg-blue-100 text-blue-800';
      case 'POST': return 'bg-green-100 text-green-800';
      case 'PUT': return 'bg-yellow-100 text-yellow-800';
      case 'DELETE': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Mobile & API Management</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 2D Mobile & API Management</h2>
          <p className="text-gray-600">Manage mobile applications and API endpoints</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            API Settings
          </Button>
          <Button variant="outline" size="sm">
            <Key className="h-4 w-4 mr-2" />
            API Keys
          </Button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg w-fit">
        <Button
          variant={selectedTab === 'api' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedTab('api')}
          className="flex items-center space-x-2"
        >
          <Globe className="h-4 w-4" />
          <span>API Management</span>
        </Button>
        <Button
          variant={selectedTab === 'mobile' ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setSelectedTab('mobile')}
          className="flex items-center space-x-2"
        >
          <Smartphone className="h-4 w-4" />
          <span>Mobile Apps</span>
        </Button>
      </div>

      {selectedTab === 'api' && (
        <>
          {/* API Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Requests</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {apiEndpoints.reduce((sum, endpoint) => sum + endpoint.usage, 0).toLocaleString()}
                    </p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                    <p className="text-2xl font-bold text-green-600">
                      {Math.round(apiEndpoints.reduce((sum, endpoint) => sum + endpoint.responseTime, 0) / apiEndpoints.length)}ms
                    </p>
                  </div>
                  <Zap className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Error Rate</p>
                    <p className="text-2xl font-bold text-red-600">
                      {(apiEndpoints.reduce((sum, endpoint) => sum + endpoint.errorRate, 0) / apiEndpoints.length).toFixed(2)}%
                    </p>
                  </div>
                  <AlertTriangle className="h-8 w-8 text-red-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Healthy Endpoints</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {apiEndpoints.filter(e => e.status === 'healthy').length}/{apiEndpoints.length}
                    </p>
                  </div>
                  <CheckCircle className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* API Endpoints */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                API Endpoints
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {apiEndpoints.map((endpoint) => (
                  <div key={endpoint.id} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <Badge className={getMethodColor(endpoint.method)}>
                          {endpoint.method}
                        </Badge>
                        <code className="text-sm bg-gray-100 px-2 py-1 rounded">{endpoint.path}</code>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(endpoint.status)}
                          <Badge className={getStatusColor(endpoint.status)}>
                            {endpoint.status}
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-600 mb-2">{endpoint.description}</p>
                      <div className="flex items-center space-x-4 text-xs text-gray-500">
                        <span>{endpoint.usage.toLocaleString()} requests</span>
                        <span>{endpoint.responseTime}ms avg</span>
                        <span>{endpoint.errorRate}% errors</span>
                        <span>Last used: {endpoint.lastUsed.toLocaleTimeString()}</span>
                      </div>
                    </div>
                    <div className="flex space-x-2">
                      <Button size="sm" variant="outline">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        Analytics
                      </Button>
                      <Button size="sm" variant="outline">
                        <Settings className="h-3 w-3" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </>
      )}

      {selectedTab === 'mobile' && (
        <>
          {/* Mobile Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Downloads</p>
                    <p className="text-2xl font-bold text-blue-600">
                      {mobileApps.reduce((sum, app) => sum + app.downloads, 0).toLocaleString()}
                    </p>
                  </div>
                  <Download className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Active Users</p>
                    <p className="text-2xl font-bold text-green-600">
                      {mobileApps.reduce((sum, app) => sum + app.activeUsers, 0).toLocaleString()}
                    </p>
                  </div>
                  <Users className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Rating</p>
                    <p className="text-2xl font-bold text-yellow-600">
                      {(mobileApps.filter(app => app.rating > 0).reduce((sum, app) => sum + app.rating, 0) / mobileApps.filter(app => app.rating > 0).length).toFixed(1)}⭐
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-yellow-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Live Apps</p>
                    <p className="text-2xl font-bold text-purple-600">
                      {mobileApps.filter(app => app.status === 'live').length}/{mobileApps.length}
                    </p>
                  </div>
                  <Smartphone className="h-8 w-8 text-purple-500" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Mobile Apps */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {mobileApps.map((app) => (
              <Card key={app.id} className="hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="text-2xl">{getPlatformIcon(app.platform)}</div>
                      <div>
                        <CardTitle className="text-lg">{app.name}</CardTitle>
                        <p className="text-sm text-gray-600 capitalize">{app.platform}</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-1">
                      {getStatusIcon(app.status)}
                      <Badge className={getStatusColor(app.status)}>
                        {app.status}
                      </Badge>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Version</span>
                      <span className="font-medium">{app.version}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Downloads</span>
                      <span className="font-medium">{app.downloads.toLocaleString()}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Active Users</span>
                      <span className="font-medium">{app.activeUsers.toLocaleString()}</span>
                    </div>
                    {app.rating > 0 && (
                      <div className="flex items-center justify-between">
                        <span className="text-sm text-gray-600">Rating</span>
                        <span className="font-medium">{app.rating}⭐</span>
                      </div>
                    )}
                    <div className="text-xs text-gray-500">
                      Last updated: {app.lastUpdate.toLocaleDateString()}
                    </div>
                    <div className="flex space-x-2 pt-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <BarChart3 className="h-3 w-3 mr-1" />
                        Analytics
                      </Button>
                      <Button size="sm" variant="outline" className="flex-1">
                        <Upload className="h-3 w-3 mr-1" />
                        Update
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </>
      )}
    </div>
  );
}
