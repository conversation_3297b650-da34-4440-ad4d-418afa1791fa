// Production Monitoring & Health Check Service
// Phase 7: Comprehensive system monitoring and alerting

const os = require('os');
const fs = require('fs').promises;
const { Pool } = require('pg');
const axios = require('axios');

// PostgreSQL connection pool
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 20,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000,
});

class ProductionMonitoringService {
  constructor() {
    this.metrics = {
      system: {
        uptime: 0,
        cpuUsage: 0,
        memoryUsage: 0,
        diskUsage: 0,
        networkLatency: 0
      },
      application: {
        responseTime: 0,
        errorRate: 0,
        throughput: 0,
        availability: 100
      },
      database: {
        connectionPool: 0,
        queryTime: 0,
        lockWaits: 0,
        replicationLag: 0
      },
      business: {
        transactionVolume: 0,
        fraudDetectionRate: 0,
        customerSatisfaction: 0,
        revenuePerHour: 0
      }
    };
    
    this.alerts = [];
    this.healthChecks = new Map();
    this.performanceHistory = [];
    
    // Thresholds for alerts
    this.thresholds = {
      critical: {
        cpuUsage: 90,
        memoryUsage: 90,
        diskUsage: 95,
        errorRate: 5,
        responseTime: 5000
      },
      warning: {
        cpuUsage: 70,
        memoryUsage: 80,
        diskUsage: 85,
        errorRate: 1,
        responseTime: 1000
      }
    };
    
    // Start monitoring
    this.startMonitoring();
  }

  // =====================================================
  // SYSTEM MONITORING
  // =====================================================

  async collectSystemMetrics() {
    try {
      // CPU Usage
      const cpuUsage = await this.getCPUUsage();
      
      // Memory Usage
      const totalMemory = os.totalmem();
      const freeMemory = os.freemem();
      const memoryUsage = ((totalMemory - freeMemory) / totalMemory) * 100;
      
      // Disk Usage
      const diskUsage = await this.getDiskUsage();
      
      // Network Latency
      const networkLatency = await this.getNetworkLatency();
      
      // System Uptime
      const uptime = os.uptime();
      
      this.metrics.system = {
        uptime: uptime,
        cpuUsage: cpuUsage,
        memoryUsage: memoryUsage,
        diskUsage: diskUsage,
        networkLatency: networkLatency,
        loadAverage: os.loadavg(),
        platform: os.platform(),
        arch: os.arch(),
        nodeVersion: process.version,
        timestamp: new Date()
      };
      
      // Check for alerts
      this.checkSystemAlerts();
      
      return this.metrics.system;

    } catch (error) {
      console.error('❌ Error collecting system metrics:', error);
      return null;
    }
  }

  async getCPUUsage() {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = Date.now();
      
      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = Date.now();
        
        const userUsage = endUsage.user / 1000; // Convert to milliseconds
        const systemUsage = endUsage.system / 1000;
        const totalUsage = userUsage + systemUsage;
        const totalTime = endTime - startTime;
        
        const cpuPercent = (totalUsage / totalTime) * 100;
        resolve(Math.min(cpuPercent, 100));
      }, 100);
    });
  }

  async getDiskUsage() {
    try {
      const stats = await fs.stat('.');
      // This is a simplified disk usage check
      // In production, you'd want to use a more comprehensive method
      return 45.2; // Mock value for now
    } catch (error) {
      return 0;
    }
  }

  async getNetworkLatency() {
    try {
      const start = Date.now();
      await axios.get('https://www.google.com', { timeout: 5000 });
      return Date.now() - start;
    } catch (error) {
      return 9999; // High latency indicates network issues
    }
  }

  // =====================================================
  // APPLICATION MONITORING
  // =====================================================

  async collectApplicationMetrics() {
    try {
      // Response Time
      const responseTime = await this.getAverageResponseTime();
      
      // Error Rate
      const errorRate = await this.getErrorRate();
      
      // Throughput
      const throughput = await this.getThroughput();
      
      // Availability
      const availability = await this.getAvailability();
      
      this.metrics.application = {
        responseTime: responseTime,
        errorRate: errorRate,
        throughput: throughput,
        availability: availability,
        activeConnections: this.getActiveConnections(),
        memoryHeapUsed: process.memoryUsage().heapUsed,
        memoryHeapTotal: process.memoryUsage().heapTotal,
        timestamp: new Date()
      };
      
      // Check for alerts
      this.checkApplicationAlerts();
      
      return this.metrics.application;

    } catch (error) {
      console.error('❌ Error collecting application metrics:', error);
      return null;
    }
  }

  async getAverageResponseTime() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT AVG(processing_time_ms) as avg_response_time
        FROM payment_transactions 
        WHERE created_at >= NOW() - INTERVAL '5 minutes'
      `);
      
      client.release();
      
      return result.rows[0]?.avg_response_time || 0;

    } catch (error) {
      return 0;
    }
  }

  async getErrorRate() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT 
          COUNT(*) as total_requests,
          COUNT(CASE WHEN status = 'failed' THEN 1 END) as failed_requests
        FROM payment_transactions 
        WHERE created_at >= NOW() - INTERVAL '5 minutes'
      `);
      
      client.release();
      
      const total = result.rows[0]?.total_requests || 0;
      const failed = result.rows[0]?.failed_requests || 0;
      
      return total > 0 ? (failed / total) * 100 : 0;

    } catch (error) {
      return 0;
    }
  }

  async getThroughput() {
    try {
      const client = await pool.connect();
      
      const result = await client.query(`
        SELECT COUNT(*) as requests_per_minute
        FROM payment_transactions 
        WHERE created_at >= NOW() - INTERVAL '1 minute'
      `);
      
      client.release();
      
      return result.rows[0]?.requests_per_minute || 0;

    } catch (error) {
      return 0;
    }
  }

  async getAvailability() {
    // Calculate uptime percentage based on successful health checks
    const totalChecks = this.healthChecks.size;
    const successfulChecks = Array.from(this.healthChecks.values())
      .filter(check => check.status === 'healthy').length;
    
    return totalChecks > 0 ? (successfulChecks / totalChecks) * 100 : 100;
  }

  getActiveConnections() {
    // This would typically come from your web server metrics
    return Math.floor(Math.random() * 100) + 50; // Mock value
  }

  // =====================================================
  // DATABASE MONITORING
  // =====================================================

  async collectDatabaseMetrics() {
    try {
      const client = await pool.connect();
      
      // Connection pool utilization
      const poolStats = pool.totalCount;
      const poolUtilization = (poolStats / pool.options.max) * 100;
      
      // Query performance
      const queryPerformance = await client.query(`
        SELECT 
          AVG(total_time) as avg_query_time,
          COUNT(*) as total_queries
        FROM pg_stat_statements 
        WHERE last_exec >= NOW() - INTERVAL '5 minutes'
        LIMIT 1
      `);
      
      // Lock waits
      const lockWaits = await client.query(`
        SELECT COUNT(*) as lock_waits
        FROM pg_locks 
        WHERE NOT granted
      `);
      
      // Database size
      const dbSize = await client.query(`
        SELECT pg_size_pretty(pg_database_size('BARPOS')) as database_size
      `);
      
      client.release();
      
      this.metrics.database = {
        connectionPool: poolUtilization,
        queryTime: queryPerformance.rows[0]?.avg_query_time || 0,
        lockWaits: lockWaits.rows[0]?.lock_waits || 0,
        replicationLag: 0, // Would need to implement for production
        databaseSize: dbSize.rows[0]?.database_size || 'Unknown',
        activeConnections: poolStats,
        maxConnections: pool.options.max,
        timestamp: new Date()
      };
      
      // Check for alerts
      this.checkDatabaseAlerts();
      
      return this.metrics.database;

    } catch (error) {
      console.error('❌ Error collecting database metrics:', error);
      return null;
    }
  }

  // =====================================================
  // BUSINESS METRICS
  // =====================================================

  async collectBusinessMetrics() {
    try {
      const client = await pool.connect();
      
      // Transaction volume
      const transactionVolume = await client.query(`
        SELECT COUNT(*) as volume
        FROM payment_transactions 
        WHERE created_at >= NOW() - INTERVAL '1 hour'
      `);
      
      // Revenue per hour
      const revenue = await client.query(`
        SELECT COALESCE(SUM(amount), 0) as revenue
        FROM payment_transactions 
        WHERE status = 'completed' 
          AND created_at >= NOW() - INTERVAL '1 hour'
      `);
      
      // Fraud detection rate
      const fraudDetection = await client.query(`
        SELECT 
          COUNT(*) as total_analyzed,
          COUNT(CASE WHEN risk_score > 0.7 THEN 1 END) as high_risk_detected
        FROM ai_fraud_analysis 
        WHERE created_at >= NOW() - INTERVAL '1 hour'
      `);
      
      client.release();
      
      const totalAnalyzed = fraudDetection.rows[0]?.total_analyzed || 0;
      const highRiskDetected = fraudDetection.rows[0]?.high_risk_detected || 0;
      const fraudDetectionRate = totalAnalyzed > 0 ? (highRiskDetected / totalAnalyzed) * 100 : 0;
      
      this.metrics.business = {
        transactionVolume: transactionVolume.rows[0]?.volume || 0,
        fraudDetectionRate: fraudDetectionRate,
        customerSatisfaction: 95.2, // Would come from customer feedback system
        revenuePerHour: revenue.rows[0]?.revenue || 0,
        timestamp: new Date()
      };
      
      return this.metrics.business;

    } catch (error) {
      console.error('❌ Error collecting business metrics:', error);
      return null;
    }
  }

  // =====================================================
  // HEALTH CHECKS
  // =====================================================

  async performHealthChecks() {
    const checks = {
      database: await this.checkDatabaseHealth(),
      redis: await this.checkRedisHealth(),
      externalAPIs: await this.checkExternalAPIsHealth(),
      diskSpace: await this.checkDiskSpaceHealth(),
      memory: await this.checkMemoryHealth()
    };
    
    // Store health check results
    for (const [service, result] of Object.entries(checks)) {
      this.healthChecks.set(service, {
        ...result,
        lastChecked: new Date()
      });
    }
    
    return checks;
  }

  async checkDatabaseHealth() {
    try {
      const start = Date.now();
      const client = await pool.connect();
      
      await client.query('SELECT 1');
      client.release();
      
      const responseTime = Date.now() - start;
      
      return {
        status: responseTime < 1000 ? 'healthy' : 'degraded',
        responseTime: responseTime,
        message: 'Database connection successful'
      };

    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        message: 'Database connection failed'
      };
    }
  }

  async checkRedisHealth() {
    // Mock Redis health check
    return {
      status: 'healthy',
      responseTime: 5,
      message: 'Redis connection successful'
    };
  }

  async checkExternalAPIsHealth() {
    const apis = [
      { name: 'Stripe', url: 'https://api.stripe.com/v1' },
      { name: 'ExchangeRate-API', url: 'https://api.exchangerate-api.com' }
    ];
    
    const results = [];
    
    for (const api of apis) {
      try {
        const start = Date.now();
        await axios.get(api.url, { timeout: 5000, validateStatus: () => true });
        const responseTime = Date.now() - start;
        
        results.push({
          name: api.name,
          status: 'healthy',
          responseTime: responseTime
        });
      } catch (error) {
        results.push({
          name: api.name,
          status: 'unhealthy',
          error: error.message
        });
      }
    }
    
    const healthyCount = results.filter(r => r.status === 'healthy').length;
    const overallStatus = healthyCount === results.length ? 'healthy' : 
                         healthyCount > 0 ? 'degraded' : 'unhealthy';
    
    return {
      status: overallStatus,
      apis: results,
      message: `${healthyCount}/${results.length} APIs healthy`
    };
  }

  async checkDiskSpaceHealth() {
    const diskUsage = await this.getDiskUsage();
    
    return {
      status: diskUsage < 85 ? 'healthy' : diskUsage < 95 ? 'warning' : 'critical',
      usage: diskUsage,
      message: `Disk usage: ${diskUsage.toFixed(1)}%`
    };
  }

  async checkMemoryHealth() {
    const memoryUsage = this.metrics.system.memoryUsage;
    
    return {
      status: memoryUsage < 80 ? 'healthy' : memoryUsage < 90 ? 'warning' : 'critical',
      usage: memoryUsage,
      message: `Memory usage: ${memoryUsage.toFixed(1)}%`
    };
  }

  // =====================================================
  // ALERTING SYSTEM
  // =====================================================

  checkSystemAlerts() {
    const { cpuUsage, memoryUsage, diskUsage } = this.metrics.system;
    
    if (cpuUsage > this.thresholds.critical.cpuUsage) {
      this.createAlert('critical', 'system', `High CPU usage: ${cpuUsage.toFixed(1)}%`);
    } else if (cpuUsage > this.thresholds.warning.cpuUsage) {
      this.createAlert('warning', 'system', `Elevated CPU usage: ${cpuUsage.toFixed(1)}%`);
    }
    
    if (memoryUsage > this.thresholds.critical.memoryUsage) {
      this.createAlert('critical', 'system', `High memory usage: ${memoryUsage.toFixed(1)}%`);
    } else if (memoryUsage > this.thresholds.warning.memoryUsage) {
      this.createAlert('warning', 'system', `Elevated memory usage: ${memoryUsage.toFixed(1)}%`);
    }
    
    if (diskUsage > this.thresholds.critical.diskUsage) {
      this.createAlert('critical', 'system', `High disk usage: ${diskUsage.toFixed(1)}%`);
    } else if (diskUsage > this.thresholds.warning.diskUsage) {
      this.createAlert('warning', 'system', `Elevated disk usage: ${diskUsage.toFixed(1)}%`);
    }
  }

  checkApplicationAlerts() {
    const { responseTime, errorRate } = this.metrics.application;
    
    if (responseTime > this.thresholds.critical.responseTime) {
      this.createAlert('critical', 'application', `High response time: ${responseTime}ms`);
    } else if (responseTime > this.thresholds.warning.responseTime) {
      this.createAlert('warning', 'application', `Elevated response time: ${responseTime}ms`);
    }
    
    if (errorRate > this.thresholds.critical.errorRate) {
      this.createAlert('critical', 'application', `High error rate: ${errorRate.toFixed(2)}%`);
    } else if (errorRate > this.thresholds.warning.errorRate) {
      this.createAlert('warning', 'application', `Elevated error rate: ${errorRate.toFixed(2)}%`);
    }
  }

  checkDatabaseAlerts() {
    const { connectionPool, lockWaits } = this.metrics.database;
    
    if (connectionPool > 90) {
      this.createAlert('critical', 'database', `High connection pool usage: ${connectionPool.toFixed(1)}%`);
    } else if (connectionPool > 80) {
      this.createAlert('warning', 'database', `Elevated connection pool usage: ${connectionPool.toFixed(1)}%`);
    }
    
    if (lockWaits > 10) {
      this.createAlert('warning', 'database', `Database lock waits detected: ${lockWaits}`);
    }
  }

  createAlert(severity, category, message) {
    const alert = {
      id: `alert_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      severity: severity,
      category: category,
      message: message,
      timestamp: new Date(),
      acknowledged: false
    };
    
    this.alerts.push(alert);
    
    // Keep only last 100 alerts
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
    
    console.log(`🚨 ${severity.toUpperCase()} ALERT [${category}]: ${message}`);
    
    // In production, you would send this to your alerting system
    // (Slack, PagerDuty, email, etc.)
    this.sendAlert(alert);
  }

  async sendAlert(alert) {
    // Mock alert sending - in production, integrate with your alerting system
    if (alert.severity === 'critical') {
      console.log(`📧 Sending critical alert to on-call team: ${alert.message}`);
    }
  }

  // =====================================================
  // MONITORING LIFECYCLE
  // =====================================================

  startMonitoring() {
    console.log('📊 Starting production monitoring service');
    
    // Collect metrics every 30 seconds
    this.metricsInterval = setInterval(async () => {
      await this.collectAllMetrics();
    }, 30000);
    
    // Perform health checks every 60 seconds
    this.healthInterval = setInterval(async () => {
      await this.performHealthChecks();
    }, 60000);
    
    // Store performance history every 5 minutes
    this.historyInterval = setInterval(() => {
      this.storePerformanceHistory();
    }, 300000);
    
    // Initial collection
    this.collectAllMetrics();
    this.performHealthChecks();
  }

  stopMonitoring() {
    console.log('🛑 Stopping production monitoring service');
    
    if (this.metricsInterval) clearInterval(this.metricsInterval);
    if (this.healthInterval) clearInterval(this.healthInterval);
    if (this.historyInterval) clearInterval(this.historyInterval);
  }

  async collectAllMetrics() {
    try {
      await Promise.all([
        this.collectSystemMetrics(),
        this.collectApplicationMetrics(),
        this.collectDatabaseMetrics(),
        this.collectBusinessMetrics()
      ]);
    } catch (error) {
      console.error('❌ Error collecting metrics:', error);
    }
  }

  storePerformanceHistory() {
    const snapshot = {
      timestamp: new Date(),
      metrics: JSON.parse(JSON.stringify(this.metrics))
    };
    
    this.performanceHistory.push(snapshot);
    
    // Keep only last 24 hours of history (288 snapshots at 5-minute intervals)
    if (this.performanceHistory.length > 288) {
      this.performanceHistory = this.performanceHistory.slice(-288);
    }
  }

  // =====================================================
  // API ENDPOINTS
  // =====================================================

  getOverallHealth() {
    const healthChecks = Array.from(this.healthChecks.values());
    const healthyCount = healthChecks.filter(check => check.status === 'healthy').length;
    const totalChecks = healthChecks.length;
    
    let overallStatus = 'healthy';
    if (healthyCount < totalChecks) {
      overallStatus = healthyCount > totalChecks * 0.5 ? 'degraded' : 'unhealthy';
    }
    
    return {
      status: overallStatus,
      uptime: process.uptime(),
      timestamp: new Date(),
      metrics: this.metrics,
      healthChecks: Object.fromEntries(this.healthChecks),
      alerts: this.alerts.filter(alert => !alert.acknowledged).length,
      version: process.env.APP_VERSION || '1.0.0'
    };
  }

  getDetailedMetrics() {
    return {
      current: this.metrics,
      history: this.performanceHistory.slice(-12), // Last hour
      alerts: this.alerts.slice(-20), // Last 20 alerts
      healthChecks: Object.fromEntries(this.healthChecks)
    };
  }

  acknowledgeAlert(alertId) {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedAt = new Date();
      return true;
    }
    return false;
  }
}

module.exports = ProductionMonitoringService;
