import puppeteer from 'puppeteer';

async function testSuperAdminLogin() {
  console.log('🔐 Testing Super Admin Login Page...\n');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1920, height: 1080 },
    args: ['--start-maximized']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to the Super Admin page
    console.log('📱 Loading Super Admin page...');
    await page.goto('http://localhost:5173/super-admin', { waitUntil: 'networkidle0' });
    
    // Wait for the app to load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Check if React root element exists
    const rootElement = await page.$('#root');
    console.log(rootElement ? '✅ Root element found' : '❌ Root element not found');

    // Check root element content
    const rootContent = await page.$eval('#root', el => el.innerHTML).catch(() => 'Error reading root content');
    console.log(`🔍 Root content length: ${rootContent.length} characters`);

    // Check if any React components are rendered
    const reactElements = await page.$$('div, button, h1');
    console.log(`🔍 React elements found: ${reactElements.length}`);
    
    // Check what's currently on the page
    const pageTitle = await page.title();
    console.log(`✅ Page title: ${pageTitle}`);
    
    // Check for login elements
    const loginTitle = await page.$eval('h1', el => el.textContent).catch(() => null);
    console.log(loginTitle ? `✅ Login title found: ${loginTitle}` : '❌ Login title not found');
    
    // Check for PIN input
    const pinButtons = await page.$$('[data-testid^="pin-button-"]');
    console.log(`${pinButtons.length > 0 ? '✅' : '❌'} PIN buttons found: ${pinButtons.length}`);
    
    // Check for dashboard elements (should not be present if login is working)
    const dashboardHeader = await page.$('h1:contains("Super Admin Dashboard")').catch(() => null);
    console.log(dashboardHeader ? '❌ Dashboard header found (login bypassed!)' : '✅ Dashboard header not found (login working)');
    
    // Check for navigation tabs (should not be present if login is working)
    const navTabs = await page.$$('nav button');
    console.log(`${navTabs.length === 0 ? '✅' : '❌'} Navigation tabs found: ${navTabs.length} (should be 0 if login is working)`);
    
    // Check console logs for debugging
    page.on('console', msg => {
      console.log(`🔍 Console ${msg.type()}: ${msg.text()}`);
    });

    // Check for JavaScript errors
    page.on('pageerror', error => {
      console.log(`❌ Page Error: ${error.message}`);
    });
    
    // Wait a bit more to see console logs
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // Check the current URL
    const currentUrl = page.url();
    console.log(`🔗 Current URL: ${currentUrl}`);
    
    // Take a screenshot for debugging
    await page.screenshot({ path: 'super-admin-debug.png', fullPage: true });
    console.log('📸 Screenshot saved as super-admin-debug.png');
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  } finally {
    await browser.close();
  }
}

// Run the test
testSuperAdminLogin().catch(console.error);
