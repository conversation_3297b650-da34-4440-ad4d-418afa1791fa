import React, { useState } from 'react';
import { X, BarChart3, Download, Calendar, Filter, FileText } from 'lucide-react';
import { Button } from '../ui/button';

interface GenerateReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (reportConfig: any) => void;
}

export const GenerateReportModal: React.FC<GenerateReportModalProps> = ({
  isOpen,
  onClose,
  onGenerate
}) => {
  const [reportConfig, setReportConfig] = useState({
    type: 'comprehensive',
    dateRange: '30d',
    customStartDate: '',
    customEndDate: '',
    includeMetrics: {
      tenants: true,
      revenue: true,
      users: true,
      performance: true,
      security: false,
      compliance: false
    },
    format: 'pdf',
    includeCharts: true,
    includeRawData: false,
    tenantFilter: 'all',
    specificTenants: []
  });

  const [isGenerating, setIsGenerating] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const reportTypes = [
    {
      id: 'comprehensive',
      name: 'Comprehensive Report',
      description: 'Complete system overview with all metrics',
      icon: BarChart3
    },
    {
      id: 'financial',
      name: 'Financial Report',
      description: 'Revenue, billing, and financial analytics',
      icon: FileText
    },
    {
      id: 'performance',
      name: 'Performance Report',
      description: 'System performance and uptime metrics',
      icon: BarChart3
    },
    {
      id: 'security',
      name: 'Security Report',
      description: 'Security events and compliance status',
      icon: FileText
    }
  ];

  const dateRanges = [
    { id: '7d', name: 'Last 7 days' },
    { id: '30d', name: 'Last 30 days' },
    { id: '90d', name: 'Last 90 days' },
    { id: '1y', name: 'Last year' },
    { id: 'custom', name: 'Custom range' }
  ];

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (reportConfig.dateRange === 'custom') {
      if (!reportConfig.customStartDate) {
        newErrors.customStartDate = 'Start date is required';
      }
      if (!reportConfig.customEndDate) {
        newErrors.customEndDate = 'End date is required';
      }
      if (reportConfig.customStartDate && reportConfig.customEndDate) {
        if (new Date(reportConfig.customStartDate) > new Date(reportConfig.customEndDate)) {
          newErrors.customEndDate = 'End date must be after start date';
        }
      }
    }

    const selectedMetrics = Object.values(reportConfig.includeMetrics).some(Boolean);
    if (!selectedMetrics) {
      newErrors.metrics = 'Please select at least one metric to include';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleGenerate = async () => {
    if (!validateForm()) {
      return;
    }

    setIsGenerating(true);
    try {
      const response = await fetch('/api/admin/reports/generate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        },
        body: JSON.stringify(reportConfig)
      });

      if (response.ok) {
        const result = await response.json();
        
        if (reportConfig.format === 'pdf' || reportConfig.format === 'excel') {
          // Download file
          const blob = await response.blob();
          const url = window.URL.createObjectURL(blob);
          const a = document.createElement('a');
          a.href = url;
          a.download = `barpos-report-${Date.now()}.${reportConfig.format}`;
          document.body.appendChild(a);
          a.click();
          window.URL.revokeObjectURL(url);
          document.body.removeChild(a);
        }

        onGenerate(result);
        onClose();
      } else {
        const error = await response.json();
        setErrors({ submit: error.message || 'Failed to generate report' });
      }
    } catch (error) {
      setErrors({ submit: 'Network error. Please try again.' });
    } finally {
      setIsGenerating(false);
    }
  };

  const handleConfigChange = (field: string, value: any) => {
    setReportConfig(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user makes changes
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const handleMetricChange = (metric: string, enabled: boolean) => {
    setReportConfig(prev => ({
      ...prev,
      includeMetrics: {
        ...prev.includeMetrics,
        [metric]: enabled
      }
    }));
    
    if (errors.metrics) {
      setErrors(prev => ({
        ...prev,
        metrics: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-3xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-purple-100 rounded-lg">
              <BarChart3 className="h-6 w-6 text-purple-600" />
            </div>
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Generate Report</h2>
              <p className="text-sm text-gray-500">Create comprehensive analytics report</p>
            </div>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-5 w-5" />
          </Button>
        </div>

        {/* Form */}
        <div className="p-6 space-y-6">
          {/* Report Type */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Report Type</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {reportTypes.map((type) => (
                <div
                  key={type.id}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                    reportConfig.type === type.id
                      ? 'border-purple-500 bg-purple-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => handleConfigChange('type', type.id)}
                >
                  <div className="flex items-start space-x-3">
                    <type.icon className={`h-5 w-5 mt-0.5 ${
                      reportConfig.type === type.id ? 'text-purple-600' : 'text-gray-400'
                    }`} />
                    <div>
                      <h4 className="font-medium text-gray-900">{type.name}</h4>
                      <p className="text-sm text-gray-600 mt-1">{type.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Date Range */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Date Range</h3>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {dateRanges.map((range) => (
                <button
                  key={range.id}
                  type="button"
                  onClick={() => handleConfigChange('dateRange', range.id)}
                  className={`p-3 text-sm border rounded-lg transition-all ${
                    reportConfig.dateRange === range.id
                      ? 'border-purple-500 bg-purple-50 text-purple-700'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  {range.name}
                </button>
              ))}
            </div>

            {reportConfig.dateRange === 'custom' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Start Date
                  </label>
                  <input
                    type="date"
                    value={reportConfig.customStartDate}
                    onChange={(e) => handleConfigChange('customStartDate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                      errors.customStartDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.customStartDate && (
                    <p className="text-sm text-red-600 mt-1">{errors.customStartDate}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    End Date
                  </label>
                  <input
                    type="date"
                    value={reportConfig.customEndDate}
                    onChange={(e) => handleConfigChange('customEndDate', e.target.value)}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500 ${
                      errors.customEndDate ? 'border-red-500' : 'border-gray-300'
                    }`}
                  />
                  {errors.customEndDate && (
                    <p className="text-sm text-red-600 mt-1">{errors.customEndDate}</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Metrics to Include */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Metrics to Include</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries({
                tenants: 'Tenant Analytics',
                revenue: 'Revenue & Billing',
                users: 'User Activity',
                performance: 'System Performance',
                security: 'Security Events',
                compliance: 'Compliance Status'
              }).map(([key, label]) => (
                <label key={key} className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeMetrics[key as keyof typeof reportConfig.includeMetrics]}
                    onChange={(e) => handleMetricChange(key, e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">{label}</span>
                </label>
              ))}
            </div>
            {errors.metrics && (
              <p className="text-sm text-red-600">{errors.metrics}</p>
            )}
          </div>

          {/* Format Options */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium text-gray-900">Format Options</h3>
            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Export Format
                </label>
                <div className="flex space-x-4">
                  {[
                    { id: 'pdf', name: 'PDF Document' },
                    { id: 'excel', name: 'Excel Spreadsheet' },
                    { id: 'json', name: 'JSON Data' }
                  ].map((format) => (
                    <label key={format.id} className="flex items-center space-x-2">
                      <input
                        type="radio"
                        name="format"
                        value={format.id}
                        checked={reportConfig.format === format.id}
                        onChange={(e) => handleConfigChange('format', e.target.value)}
                        className="h-4 w-4 text-purple-600 focus:ring-purple-500"
                      />
                      <span className="text-sm text-gray-700">{format.name}</span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="space-y-2">
                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeCharts}
                    onChange={(e) => handleConfigChange('includeCharts', e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Include Charts & Visualizations</span>
                </label>

                <label className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    checked={reportConfig.includeRawData}
                    onChange={(e) => handleConfigChange('includeRawData', e.target.checked)}
                    className="h-4 w-4 text-purple-600 focus:ring-purple-500 border-gray-300 rounded"
                  />
                  <span className="text-sm text-gray-700">Include Raw Data Tables</span>
                </label>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {errors.submit && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{errors.submit}</p>
            </div>
          )}

          {/* Actions */}
          <div className="flex items-center justify-end space-x-3 pt-6 border-t border-gray-200">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button onClick={handleGenerate} disabled={isGenerating}>
              {isGenerating ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Generating...
                </>
              ) : (
                <>
                  <Download className="h-4 w-4 mr-2" />
                  Generate Report
                </>
              )}
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};
