// Force Enable Restructured POS Mode
// Run this script in the browser console

console.log('🚀 FORCE ENABLING RESTRUCTURED POS MODE');
console.log('=====================================');

// Step 1: Clear any existing flags
console.log('1. Clearing existing localStorage...');
localStorage.removeItem('useRestructuredPOS');
localStorage.removeItem('useIndustryStandardPOS');

// Step 2: Set the required flags
console.log('2. Setting required localStorage flags...');
localStorage.setItem('useRestructuredPOS', 'true');
localStorage.setItem('useIndustryStandardPOS', 'true');

// Step 3: Verify flags are set
console.log('3. Verifying flags...');
console.log('   useRestructuredPOS:', localStorage.getItem('useRestructuredPOS'));
console.log('   useIndustryStandardPOS:', localStorage.getItem('useIndustryStandardPOS'));

// Step 4: Build the correct URL
console.log('4. Building target URL...');
const targetUrl = new URL(window.location.origin);
targetUrl.searchParams.set('industry', 'true');
targetUrl.searchParams.set('restructured', 'true');

console.log('   Target URL:', targetUrl.toString());

// Step 5: Show current state
console.log('5. Current state check...');
console.log('   Current URL:', window.location.href);
console.log('   Has industry param:', window.location.search.includes('industry=true'));
console.log('   Has restructured param:', window.location.search.includes('restructured=true'));

// Step 6: Redirect
console.log('6. Redirecting to restructured POS...');
console.log('🚀 REDIRECTING NOW!');

// Force redirect
window.location.href = targetUrl.toString();
