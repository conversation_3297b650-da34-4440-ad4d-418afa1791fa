const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function checkEmployeeSchema() {
  try {
    console.log('Checking employees table schema:');
    const schemaResult = await pool.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_name = 'employees'
      ORDER BY ordinal_position;
    `);
    
    console.table(schemaResult.rows);
    
    console.log('\nChecking is_active column values:');
    const activeResult = await pool.query(`
      SELECT id, name, is_active, tenant_id
      FROM employees 
      WHERE tenant_id = 3
      ORDER BY id;
    `);
    
    console.table(activeResult.rows);
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

checkEmployeeSchema();
