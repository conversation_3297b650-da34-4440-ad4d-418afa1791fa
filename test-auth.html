<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Super Admin Authentication</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #dc2626, #991b1b);
            color: white;
            min-height: 100vh;
        }
        .container {
            background: rgba(0,0,0,0.3);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        input, button {
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            border: none;
            border-radius: 8px;
            font-size: 16px;
        }
        button {
            background: #ffffff;
            color: #dc2626;
            font-weight: bold;
            cursor: pointer;
        }
        button:hover {
            background: #f3f4f6;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            white-space: pre-wrap;
        }
        .success {
            background: rgba(34, 197, 94, 0.2);
            border: 1px solid rgba(34, 197, 94, 0.5);
        }
        .error {
            background: rgba(239, 68, 68, 0.2);
            border: 1px solid rgba(239, 68, 68, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Super Admin Authentication Test</h1>
        <p>This page tests the super admin authentication system to help troubleshoot login issues.</p>
        
        <form id="testForm">
            <label for="pin">Enter Super Admin PIN:</label>
            <input type="password" id="pin" placeholder="Enter 6-digit PIN (try 123456)" maxlength="6" value="123456">
            
            <button type="submit">🚀 Test Authentication</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('testForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const pin = document.getElementById('pin').value;
            const resultDiv = document.getElementById('result');
            
            resultDiv.innerHTML = '<div class="result">🔄 Testing authentication...</div>';
            
            try {
                console.log('🔐 Testing authentication with PIN:', pin);
                
                const response = await fetch('http://localhost:4000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        pin: pin,
                        tenant: 'auto-detect',
                        isAdmin: true
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.token) {
                    resultDiv.innerHTML = `
                        <div class="result success">
                            ✅ Authentication Successful!
                            
                            User: ${data.user.name}
                            Role: ${data.user.role}
                            Tenant: ${data.user.tenant}
                            Token: ${data.token.substring(0, 50)}...
                            
                            🎉 Super admin authentication is working correctly!
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div class="result error">
                            ❌ Authentication Failed
                            
                            Status: ${response.status}
                            Error: ${data.error || 'Unknown error'}
                            
                            💡 Try PIN: 123456 for super admin access
                        </div>
                    `;
                }
                
            } catch (error) {
                console.error('❌ Authentication error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        ❌ Connection Error
                        
                        Error: ${error.message}
                        
                        🔧 Possible issues:
                        • Backend server not running on port 4000
                        • CORS policy blocking the request
                        • Network connectivity issues
                        
                        💡 Make sure the backend server is running!
                    </div>
                `;
            }
        });
        
        // Auto-focus on PIN input
        document.getElementById('pin').focus();
    </script>
</body>
</html>
