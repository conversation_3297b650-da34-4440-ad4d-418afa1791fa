import React, { useState } from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './styles/enhanced-pos.css';

// Simple Test Component
const SimpleTestComponent: React.FC = () => {
  const [pin, setPin] = useState('');
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  const handleLogin = () => {
    if (pin === '999999') {
      setIsLoggedIn(true);
    } else {
      alert('Invalid PIN. Use 999999');
      setPin('');
    }
  };

  if (isLoggedIn) {
    return (
      <div className="min-h-screen bg-gray-100 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              🔒 Super Admin Dashboard - TEST MODE
            </h1>
            <p className="text-lg text-gray-600 mb-6">
              Super Admin functionality is now ACTIVE and working!
            </p>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h3 className="font-semibold text-blue-900">System Status</h3>
                <p className="text-blue-700">✅ Operational</p>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <h3 className="font-semibold text-green-900">Authentication</h3>
                <p className="text-green-700">✅ Verified</p>
              </div>
              <div className="bg-purple-50 p-4 rounded-lg">
                <h3 className="font-semibold text-purple-900">Access Level</h3>
                <p className="text-purple-700">✅ Super Admin</p>
              </div>
            </div>
            <button
              onClick={() => setIsLoggedIn(false)}
              className="mt-6 px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700"
            >
              Logout
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-600 to-purple-700 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl p-8 w-full max-w-md">
        <div className="text-center mb-6">
          <div className="text-4xl mb-4">🔒</div>
          <h1 className="text-2xl font-bold text-gray-900">Super Admin Login</h1>
          <p className="text-gray-600 mt-2">Enter your Super Admin PIN</p>
        </div>

        <div className="space-y-4">
          <input
            type="password"
            value={pin}
            onChange={(e) => setPin(e.target.value)}
            placeholder="Enter PIN (999999)"
            className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            maxLength={6}
          />
          <button
            onClick={handleLogin}
            className="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700 font-semibold"
          >
            Login
          </button>
        </div>
      </div>
    </div>
  );
};

// Enhanced error handling for Super Admin
window.addEventListener('error', (event) => {
  console.error('🚨 Super Admin Global Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Super Admin Unhandled Promise Rejection:', event.reason);
});

// Log Super Admin initialization
console.log('🔒 Super Admin System initializing...');
console.log('📍 Current URL:', window.location.href);
console.log('🌐 Environment:', {
  port: window.location.port,
  protocol: window.location.protocol,
  host: window.location.host
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SimpleTestComponent />
  </React.StrictMode>
);

console.log('✅ Super Admin System initialized successfully');
