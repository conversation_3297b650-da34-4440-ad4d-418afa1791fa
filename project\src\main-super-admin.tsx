import React from 'react';
import <PERSON>actD<PERSON> from 'react-dom/client';
import SuperAdminSystem from './SuperAdminSystem';
import './index.css';
import './styles/enhanced-pos.css';

// Enhanced error handling for Super Admin
window.addEventListener('error', (event) => {
  console.error('🚨 Super Admin Global Error:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 Super Admin Unhandled Promise Rejection:', event.reason);
});

// Log Super Admin initialization
console.log('🔒 Super Admin System initializing...');
console.log('📍 Current URL:', window.location.href);
console.log('🌐 Environment:', {
  port: window.location.port,
  protocol: window.location.protocol,
  host: window.location.host
});

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <SuperAdminSystem />
  </React.StrictMode>
);

console.log('✅ Super Admin System initialized successfully');
