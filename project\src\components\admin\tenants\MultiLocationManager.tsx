import React, { useState, useEffect } from 'react';
import {
  MapPin,
  Plus,
  Edit,
  Trash2,
  Star,
  Phone,
  Mail,
  Clock,
  Settings,
  BarChart3,
  Users,
  DollarSign
} from 'lucide-react';
import { mockApiService, Location } from '../../../services/mockApiService';



interface LocationAnalytics {
  total_orders: number;
  total_revenue: number;
  active_users: number;
  avg_order_value: number;
  customer_count: number;
}

interface MultiLocationManagerProps {
  tenantId: number;
  tenantName: string;
}

export function MultiLocationManager({ tenantId, tenantName }: MultiLocationManagerProps) {
  const [locations, setLocations] = useState<Location[]>([]);
  const [analytics, setAnalytics] = useState<Record<number, LocationAnalytics>>({});
  const [loading, setLoading] = useState(true);
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);

  useEffect(() => {
    fetchLocations();
    fetchLocationAnalytics();
  }, [tenantId]);

  const fetchLocations = async () => {
    try {
      const data = await mockApiService.getLocations(tenantId);
      setLocations(data);
    } catch (error) {
      console.error('Error fetching locations:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchLocationAnalytics = async () => {
    try {
      const data = await mockApiService.getAnalytics({ tenantId, timeRange: '30d' });

      // Group analytics by location_id (simulate location-specific data)
      const analyticsMap: Record<number, LocationAnalytics> = {};
      locations.forEach((location, index) => {
        const locationData = data[index] || data[0]; // Use first data point as fallback
        analyticsMap[location.id] = {
          total_orders: locationData?.total_orders || Math.floor(Math.random() * 100) + 50,
          total_revenue: locationData?.total_revenue || Math.floor(Math.random() * 5000) + 2000,
          active_users: locationData?.active_users || Math.floor(Math.random() * 50) + 20,
          avg_order_value: locationData?.avg_order_value || Math.floor(Math.random() * 50) + 25,
          customer_count: locationData?.customer_count || Math.floor(Math.random() * 80) + 30
        };
      });

      setAnalytics(analyticsMap);
    } catch (error) {
      console.error('Error fetching location analytics:', error);
    }
  };

  const LocationCard = ({ location }: { location: Location }) => {
    const locationAnalytics = analytics[location.id] || {
      total_orders: 0,
      total_revenue: 0,
      active_users: 0,
      avg_order_value: 0,
      customer_count: 0
    };

    return (
      <div className="bg-white rounded-lg border border-gray-200 p-6 hover:shadow-lg transition-all duration-200">
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
              location.is_primary ? 'bg-yellow-100 text-yellow-600' : 'bg-blue-100 text-blue-600'
            }`}>
              <MapPin className="h-5 w-5" />
            </div>
            <div>
              <div className="flex items-center space-x-2">
                <h3 className="font-semibold text-gray-900">{location.name}</h3>
                {location.is_primary && (
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                )}
              </div>
              <p className="text-sm text-gray-500">{location.city}, {location.state}</p>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <span className={`px-2 py-1 text-xs rounded-full ${
              location.is_active 
                ? 'bg-green-100 text-green-700' 
                : 'bg-red-100 text-red-700'
            }`}>
              {location.is_active ? 'Active' : 'Inactive'}
            </span>
            <button 
              onClick={() => setSelectedLocation(location)}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100"
            >
              <Edit className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Location Details */}
        <div className="space-y-3 mb-4">
          <div className="text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <MapPin className="h-4 w-4" />
              <span>{location.address}</span>
            </div>
          </div>
          
          {location.phone && (
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Phone className="h-4 w-4" />
                <span>{location.phone}</span>
              </div>
            </div>
          )}
          
          {location.email && (
            <div className="text-sm text-gray-600">
              <div className="flex items-center space-x-2">
                <Mail className="h-4 w-4" />
                <span>{location.email}</span>
              </div>
            </div>
          )}
          
          <div className="text-sm text-gray-600">
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4" />
              <span>{location.timezone}</span>
            </div>
          </div>
        </div>

        {/* Analytics */}
        <div className="border-t border-gray-200 pt-4">
          <h4 className="text-sm font-medium text-gray-700 mb-3">30-Day Performance</h4>
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 text-blue-600 mb-1">
                <BarChart3 className="h-4 w-4" />
                <span className="text-xs font-medium">Orders</span>
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {locationAnalytics.total_orders.toLocaleString()}
              </div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 text-green-600 mb-1">
                <DollarSign className="h-4 w-4" />
                <span className="text-xs font-medium">Revenue</span>
              </div>
              <div className="text-lg font-semibold text-gray-900">
                ${locationAnalytics.total_revenue.toLocaleString()}
              </div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 text-purple-600 mb-1">
                <Users className="h-4 w-4" />
                <span className="text-xs font-medium">Customers</span>
              </div>
              <div className="text-lg font-semibold text-gray-900">
                {locationAnalytics.customer_count.toLocaleString()}
              </div>
            </div>
            
            <div className="text-center">
              <div className="flex items-center justify-center space-x-1 text-orange-600 mb-1">
                <BarChart3 className="h-4 w-4" />
                <span className="text-xs font-medium">AOV</span>
              </div>
              <div className="text-lg font-semibold text-gray-900">
                ${locationAnalytics.avg_order_value.toFixed(2)}
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="border-t border-gray-200 pt-4 mt-4">
          <div className="flex space-x-2">
            <button className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
              View Details
            </button>
            <button className="flex-1 px-3 py-2 text-sm bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
              <Settings className="h-4 w-4 inline mr-1" />
              Configure
            </button>
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-gray-200 h-80 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">
            {tenantName} - Locations
          </h2>
          <p className="text-gray-600 mt-1">
            Manage multiple locations for this tenant
          </p>
        </div>
        <button
          onClick={() => setShowCreateForm(true)}
          className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Location
        </button>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <MapPin className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Locations</p>
              <p className="text-2xl font-semibold text-gray-900">{locations.length}</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Star className="h-8 w-8 text-yellow-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Active Locations</p>
              <p className="text-2xl font-semibold text-gray-900">
                {locations.filter(l => l.is_active).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <DollarSign className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-semibold text-gray-900">
                ${Object.values(analytics).reduce((sum, a) => sum + a.total_revenue, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <BarChart3 className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Total Orders</p>
              <p className="text-2xl font-semibold text-gray-900">
                {Object.values(analytics).reduce((sum, a) => sum + a.total_orders, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Locations Grid */}
      {locations.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {locations.map(location => (
            <LocationCard key={location.id} location={location} />
          ))}
        </div>
      ) : (
        <div className="text-center py-12">
          <MapPin className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No locations found</h3>
          <p className="text-gray-500 mb-6">Get started by adding the first location for this tenant.</p>
          <button
            onClick={() => setShowCreateForm(true)}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add First Location
          </button>
        </div>
      )}
    </div>
  );
}
