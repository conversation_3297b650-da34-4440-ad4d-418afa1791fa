<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - POS System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        .hidden { display: none !important; }
        .show { display: block !important; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Theme Toggle -->
    <button onclick="toggleTheme()" class="theme-toggle" title="Toggle Theme">🌙</button>

    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">RestroFlow</h1>
                <p class="text-gray-600">Enter your PIN to access the system</p>
            </div>
            
            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">PIN Code</label>
                    <input
                        type="password"
                        id="pinInput"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your PIN"
                    />
                </div>
                
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    Invalid PIN. Please try again.
                </div>
                
                <button
                    onclick="login()"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Access System
                </button>
            </div>
            
            <div class="mt-6 text-center text-sm text-gray-500">
                <p>Demo PIN: 1234</p>
            </div>
        </div>
    </div>

    <!-- POS System -->
    <div id="posSystem" class="hidden min-h-screen bg-gray-50">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-blue-600">RestroFlow</h1>
                        <span class="ml-4 text-gray-600">Point of Sale System</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Welcome, Demo User</span>
                        <button
                            onclick="logout()"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex h-screen pt-16">
            <!-- Product Grid -->
            <div class="flex-1 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Products</h2>
                <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <div onclick="addToCart('Burger', 12.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Burger</h3>
                        <p class="text-sm text-gray-600">Main Course</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$12.99</p>
                    </div>
                    <div onclick="addToCart('Pizza', 18.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Pizza</h3>
                        <p class="text-sm text-gray-600">Main Course</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$18.99</p>
                    </div>
                    <div onclick="addToCart('Salad', 8.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Salad</h3>
                        <p class="text-sm text-gray-600">Appetizer</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$8.99</p>
                    </div>
                    <div onclick="addToCart('Soda', 2.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Soda</h3>
                        <p class="text-sm text-gray-600">Beverage</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$2.99</p>
                    </div>
                    <div onclick="addToCart('Coffee', 3.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Coffee</h3>
                        <p class="text-sm text-gray-600">Beverage</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$3.99</p>
                    </div>
                    <div onclick="addToCart('Dessert', 6.99)" class="bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer">
                        <h3 class="font-semibold text-gray-800">Dessert</h3>
                        <p class="text-sm text-gray-600">Sweet</p>
                        <p class="text-lg font-bold text-blue-600 mt-2">$6.99</p>
                    </div>
                </div>
            </div>

            <!-- Cart Panel -->
            <div class="w-80 bg-white border-l border-gray-200 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Current Order</h2>
                
                <div id="cartEmpty" class="text-gray-500 text-center py-8">
                    No items in cart
                </div>
                
                <div id="cartItems" class="hidden">
                    <div id="cartList" class="space-y-3 mb-6">
                        <!-- Cart items will be loaded here -->
                    </div>
                    
                    <div class="border-t pt-4">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-semibold text-gray-800">Total:</span>
                            <span id="totalAmount" class="text-2xl font-bold text-blue-600">$0.00</span>
                        </div>
                        
                        <button
                            onclick="processPayment()"
                            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold"
                        >
                            Process Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let cart = [];
        let darkMode = false;

        // Login function
        function login() {
            const pin = document.getElementById('pinInput').value;
            const errorMessage = document.getElementById('errorMessage');
            
            if (pin === '1234') {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('posSystem').classList.remove('hidden');
                errorMessage.classList.add('hidden');
            } else {
                errorMessage.classList.remove('hidden');
            }
        }

        // Logout function
        function logout() {
            document.getElementById('posSystem').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('pinInput').value = '';
            cart = [];
            updateCart();
        }

        // Add to cart function
        function addToCart(name, price) {
            const existingItem = cart.find(item => item.name === name);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ name: name, price: price, quantity: 1 });
            }
            updateCart();
        }

        // Update cart display
        function updateCart() {
            const cartEmpty = document.getElementById('cartEmpty');
            const cartItems = document.getElementById('cartItems');
            const cartList = document.getElementById('cartList');
            const totalAmount = document.getElementById('totalAmount');

            if (cart.length === 0) {
                cartEmpty.classList.remove('hidden');
                cartItems.classList.add('hidden');
            } else {
                cartEmpty.classList.add('hidden');
                cartItems.classList.remove('hidden');
                
                cartList.innerHTML = '';
                cart.forEach((item, index) => {
                    const cartItem = document.createElement('div');
                    cartItem.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
                    cartItem.innerHTML = `
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-800">${item.name}</h4>
                            <p class="text-sm text-gray-600">$${item.price.toFixed(2)} each</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="updateQuantity(${index}, ${item.quantity - 1})" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">-</button>
                            <span class="w-8 text-center">${item.quantity}</span>
                            <button onclick="updateQuantity(${index}, ${item.quantity + 1})" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">+</button>
                        </div>
                    `;
                    cartList.appendChild(cartItem);
                });
                
                const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                totalAmount.textContent = `$${total.toFixed(2)}`;
            }
        }

        // Update quantity
        function updateQuantity(index, quantity) {
            if (quantity <= 0) {
                cart.splice(index, 1);
            } else {
                cart[index].quantity = quantity;
            }
            updateCart();
        }

        // Process payment
        function processPayment() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`Payment processed successfully! Total: $${total.toFixed(2)}`);
            cart = [];
            updateCart();
        }

        // Toggle theme
        function toggleTheme() {
            darkMode = !darkMode;
            const themeToggle = document.querySelector('.theme-toggle');
            
            if (darkMode) {
                document.body.classList.add('dark');
                themeToggle.textContent = '☀️';
            } else {
                document.body.classList.remove('dark');
                themeToggle.textContent = '🌙';
            }
        }

        // Allow Enter key to login
        document.getElementById('pinInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });

        // Initialize
        console.log('RestroFlow POS System Loaded Successfully!');
    </script>
</body>
</html>
