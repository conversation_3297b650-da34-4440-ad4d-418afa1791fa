# Database Consolidation Plan - RESTROFLOW POS System

## 🎯 Overview

This document outlines the consolidation strategy for database configurations, schema files, and migration scripts to create a unified, maintainable database structure for the multi-tenant RESTROFLOW POS system.

## 📊 Current Database State Analysis

### Database Configuration Files

| File | Purpose | Status | Action |
|------|---------|--------|---------|
| `backend/db/pool.js` | Connection pool (BARPOS DB) | ❌ OUTDATED | Replace |
| `backend/src/database/config/connection.js` | Enhanced connection (RESTROFLOW DB) | ✅ KEEP | Primary Config |
| `backend/working-server.js` (DB config) | Inline connection | 🔄 EXTRACT | Move to config |
| `project/docker-compose.yml` | Docker DB setup | ✅ KEEP | Container config |

### Schema Files

| File | Purpose | Status | Action |
|------|---------|--------|---------|
| `db/schema.sql` | Basic schema | ❌ OUTDATED | Archive |
| `backend/setup-complete-database.sql` | Complete setup | ✅ KEEP | Primary schema |
| `project/database/industry_specific_schema.sql` | Industry features | ✅ KEEP | Merge into main |
| `project/database/init-comprehensive-admin.sql` | Admin features | ✅ KEEP | Merge into main |

### Migration Files

| Directory | Purpose | Status | Action |
|-----------|---------|--------|---------|
| `backend/migrations/` | Core migrations | ✅ KEEP | Primary location |
| `backend/database/migrations/` | Alternative location | 🔄 MERGE | Consolidate |
| `project/database/` | Project-specific | 🔄 MERGE | Consolidate |

## 🏗️ Target Database Structure

### Unified Configuration
```
database/
├── config/
│   ├── connection.js          # Single connection config
│   ├── environments.js        # Environment-specific settings
│   └── pool.js               # Connection pool management
├── migrations/
│   ├── 001_initial_schema.sql
│   ├── 002_multi_tenancy.sql
│   ├── 003_rbac_system.sql
│   ├── 004_industry_features.sql
│   ├── 005_advanced_analytics.sql
│   └── migrate.js            # Migration runner
├── seeds/
│   ├── 001_default_tenants.sql
│   ├── 002_sample_products.sql
│   └── 003_demo_data.sql
├── backups/
│   └── .gitkeep
└── schema.sql               # Complete unified schema
```

## 🔧 Database Consolidation Strategy

### 1. Connection Configuration Consolidation

#### Current Issues:
- Multiple database names (BARPOS vs RESTROFLOW)
- Inconsistent connection parameters
- Hardcoded credentials in multiple files

#### Solution:
**Single Configuration File**: `database/config/connection.js`

```javascript
// Unified database configuration
const { Pool } = require('pg');

const environments = {
  development: {
    user: process.env.DB_USER || 'BARPOS',
    host: process.env.DB_HOST || 'localhost',
    database: process.env.DB_NAME || 'RESTROFLOW',
    password: process.env.DB_PASSWORD || 'Chaand@0319',
    port: parseInt(process.env.DB_PORT) || 5432,
  },
  production: {
    // Production settings
  },
  test: {
    // Test settings
  }
};

const config = environments[process.env.NODE_ENV || 'development'];

const pool = new Pool({
  ...config,
  max: parseInt(process.env.DB_MAX_CONNECTIONS) || 20,
  idleTimeoutMillis: parseInt(process.env.DB_IDLE_TIMEOUT) || 30000,
  connectionTimeoutMillis: parseInt(process.env.DB_CONNECTION_TIMEOUT) || 10000,
  query_timeout: parseInt(process.env.DB_QUERY_TIMEOUT) || 30000,
  statement_timeout: parseInt(process.env.DB_STATEMENT_TIMEOUT) || 60000,
  keepAlive: true,
  keepAliveInitialDelayMillis: 10000,
});

module.exports = { pool, config };
```

### 2. Schema Consolidation

#### Migration Sequence:
1. **001_initial_schema.sql** - Core tables (tenants, locations, employees)
2. **002_multi_tenancy.sql** - Multi-tenant support
3. **003_rbac_system.sql** - Role-based access control
4. **004_industry_features.sql** - Industry-specific features
5. **005_advanced_analytics.sql** - Analytics and reporting

#### Core Tables Structure:

```sql
-- Tenants (Multi-tenancy foundation)
CREATE TABLE tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    address TEXT,
    business_type VARCHAR(50) DEFAULT 'restaurant',
    status VARCHAR(20) DEFAULT 'active',
    plan_type VARCHAR(20) DEFAULT 'basic',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Locations (Multi-location support)
CREATE TABLE locations (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    address TEXT,
    phone VARCHAR(50),
    is_primary BOOLEAN DEFAULT false,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Employees (User management)
CREATE TABLE employees (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    location_id INTEGER REFERENCES locations(id),
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255),
    phone VARCHAR(50),
    pin VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'employee',
    permissions JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. Migration Consolidation

#### Current Migration Files to Consolidate:

| Source | Target | Content |
|--------|--------|---------|
| `backend/migrations/001_add_multi_tenancy.sql` | `001_initial_schema.sql` | Core multi-tenant structure |
| `backend/migrations/002_rbac_and_multilocation.sql` | `003_rbac_system.sql` | RBAC and permissions |
| `project/database/industry_specific_schema.sql` | `004_industry_features.sql` | Industry customizations |
| `backend/migrations/002_phase3_advanced_features.sql` | `005_advanced_analytics.sql` | Analytics features |

#### Migration Runner Enhancement:

```javascript
// Enhanced migration runner
const fs = require('fs');
const path = require('path');
const { pool } = require('../config/connection');

class MigrationRunner {
  async run() {
    await this.createMigrationsTable();
    const pendingMigrations = await this.getPendingMigrations();
    
    for (const migration of pendingMigrations) {
      await this.executeMigration(migration);
    }
  }

  async createMigrationsTable() {
    await pool.query(`
      CREATE TABLE IF NOT EXISTS schema_migrations (
        id SERIAL PRIMARY KEY,
        version VARCHAR(255) NOT NULL UNIQUE,
        filename VARCHAR(255) NOT NULL,
        executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        checksum VARCHAR(255)
      )
    `);
  }

  async getPendingMigrations() {
    const files = fs.readdirSync(__dirname)
      .filter(file => file.endsWith('.sql'))
      .sort();
    
    const executed = await pool.query(
      'SELECT filename FROM schema_migrations ORDER BY version'
    );
    
    const executedFiles = executed.rows.map(row => row.filename);
    return files.filter(file => !executedFiles.includes(file));
  }

  async executeMigration(filename) {
    const filePath = path.join(__dirname, filename);
    const sql = fs.readFileSync(filePath, 'utf8');
    
    const client = await pool.connect();
    try {
      await client.query('BEGIN');
      await client.query(sql);
      await client.query(
        'INSERT INTO schema_migrations (version, filename) VALUES ($1, $2)',
        [filename.replace('.sql', ''), filename]
      );
      await client.query('COMMIT');
      console.log(`✅ Migration ${filename} executed successfully`);
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  }
}
```

## 🔄 Implementation Steps

### Phase 1: Backup and Preparation
1. **Create Database Backup**
   ```bash
   pg_dump -U BARPOS -h localhost RESTROFLOW > backup_$(date +%Y%m%d_%H%M%S).sql
   ```

2. **Document Current Schema**
   ```bash
   pg_dump -U BARPOS -h localhost RESTROFLOW --schema-only > current_schema.sql
   ```

### Phase 2: Configuration Consolidation
1. Create unified connection configuration
2. Update all server files to use new config
3. Test database connectivity
4. Update environment variables

### Phase 3: Schema Consolidation
1. Merge all schema files into unified structure
2. Create sequential migration files
3. Test migration sequence on copy of database
4. Validate data integrity

### Phase 4: Migration System Enhancement
1. Implement enhanced migration runner
2. Add rollback capabilities
3. Create migration validation
4. Test migration system

### Phase 5: Cleanup and Documentation
1. Remove duplicate configuration files
2. Archive old schema files
3. Update documentation
4. Create database setup guide

## 🧪 Testing Strategy

### Database Testing Checklist
- [ ] Connection configuration works in all environments
- [ ] All migrations run successfully
- [ ] Data integrity maintained
- [ ] Multi-tenant isolation preserved
- [ ] Performance benchmarks met
- [ ] Backup and restore procedures work

### Migration Testing
- [ ] Fresh database setup
- [ ] Incremental migrations
- [ ] Rollback procedures
- [ ] Data migration validation
- [ ] Schema consistency checks

## 📋 Environment Configuration

### Development Environment
```env
# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_NAME=RESTROFLOW
DB_USER=BARPOS
DB_PASSWORD=Chaand@0319

# Connection Pool Settings
DB_MAX_CONNECTIONS=20
DB_IDLE_TIMEOUT=30000
DB_CONNECTION_TIMEOUT=10000
DB_QUERY_TIMEOUT=30000
DB_STATEMENT_TIMEOUT=60000
```

### Production Environment
```env
# Database Configuration (Production)
DB_HOST=${PRODUCTION_DB_HOST}
DB_PORT=5432
DB_NAME=RESTROFLOW_PROD
DB_USER=${PRODUCTION_DB_USER}
DB_PASSWORD=${PRODUCTION_DB_PASSWORD}

# Enhanced Connection Pool for Production
DB_MAX_CONNECTIONS=50
DB_IDLE_TIMEOUT=60000
DB_CONNECTION_TIMEOUT=5000
DB_QUERY_TIMEOUT=60000
DB_STATEMENT_TIMEOUT=120000
```

## 🚨 Risk Mitigation

### Data Safety Measures
1. **Multiple Backups**: Before any changes
2. **Staged Rollout**: Test → Staging → Production
3. **Rollback Plan**: Quick restoration procedures
4. **Monitoring**: Database performance and errors

### Migration Safety
1. **Transaction Wrapping**: All migrations in transactions
2. **Validation Checks**: Data integrity verification
3. **Incremental Approach**: Small, testable changes
4. **Rollback Scripts**: Reverse migration capability

## 📈 Success Metrics

### Performance Targets
- Connection establishment: < 100ms
- Query response time: < 50ms average
- Migration execution: < 5 minutes total
- Database startup: < 30 seconds

### Reliability Targets
- 99.9% uptime
- Zero data loss during migrations
- Successful rollback capability
- Multi-tenant data isolation: 100%
