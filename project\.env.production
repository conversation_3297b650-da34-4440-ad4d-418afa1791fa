# RestroFlow POS - Production Environment Configuration
# =====================================================

# Application Information
APP_NAME=RestroFlow POS
APP_VERSION=2.0.0
APP_ENVIRONMENT=production
BUILD_DATE=2024-01-01T00:00:00Z

# Security Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-change-this-in-production
ENCRYPTION_KEY=your-32-character-encryption-key-here
SESSION_SECRET=your-session-secret-key-change-this

# Database Configuration
DB_HOST=postgres
DB_PORT=5432
DB_NAME=RESTROFLOW
DB_USER=BARPOS
DB_PASSWORD=Chaand@0319
DB_SSL=true
DB_POOL_MIN=5
DB_POOL_MAX=20
DB_CONNECTION_TIMEOUT=30000
DB_IDLE_TIMEOUT=600000

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=RestroFlow2024!
REDIS_DB=0
REDIS_TTL=3600
REDIS_MAX_CONNECTIONS=10

# Rate Limiting Configuration
RATE_LIMIT_WINDOW=900000          # 15 minutes in milliseconds
RATE_LIMIT_MAX_ATTEMPTS=5         # Maximum login attempts
RATE_LIMIT_ADMIN_WINDOW=600000    # 10 minutes for admin
RATE_LIMIT_ADMIN_MAX_ATTEMPTS=3   # Maximum admin attempts
BLOCK_DURATION=1800000            # 30 minutes block duration
SECURITY_RISK_THRESHOLD=70        # Risk score threshold for blocking

# Multi-Factor Authentication
OTP_EXPIRY=300000                 # 5 minutes in milliseconds
BACKUP_CODE_LENGTH=8              # Backup code character length
TOTP_WINDOW=30                    # TOTP time window in seconds
TOTP_ISSUER=RestroFlow            # TOTP issuer name
MFA_REQUIRED_ROLES=super_admin,tenant_admin

# Audit and Logging
AUDIT_RETENTION_DAYS=365          # 1 year retention
AUDIT_BATCH_SIZE=100              # Batch size for audit logs
AUDIT_FLUSH_INTERVAL=60000        # 1 minute flush interval
LOG_LEVEL=info                    # Logging level
LOG_FORMAT=json                   # Log format
LOG_MAX_SIZE=100MB                # Maximum log file size
LOG_MAX_FILES=10                  # Maximum log files to keep

# Performance Monitoring
METRICS_RETENTION=10000           # Maximum metrics to retain
PERFORMANCE_BATCH_SIZE=100        # Performance metrics batch size
MONITORING_INTERVAL=30000         # 30 seconds monitoring interval
HEALTH_CHECK_INTERVAL=60000       # 1 minute health check interval

# Email Configuration
EMAIL_SERVICE=smtp                # Email service type
EMAIL_HOST=smtp.gmail.com         # SMTP host
EMAIL_PORT=587                    # SMTP port
EMAIL_SECURE=false                # Use TLS
EMAIL_USER=<EMAIL>   # SMTP username
EMAIL_PASS=your-app-password      # SMTP password
EMAIL_FROM=RestroFlow POS <<EMAIL>>
EMAIL_REPLY_TO=<EMAIL>

# SMS Configuration
SMS_SERVICE=twilio                # SMS service provider
SMS_API_KEY=your-twilio-account-sid
SMS_API_SECRET=your-twilio-auth-token
SMS_FROM_NUMBER=+**********       # SMS sender number

# File Upload Configuration
UPLOAD_MAX_SIZE=********          # 10MB maximum file size
UPLOAD_ALLOWED_TYPES=image/jpeg,image/png,image/gif,application/pdf
UPLOAD_PATH=/app/uploads          # Upload directory
UPLOAD_PUBLIC_URL=/uploads        # Public URL for uploads

# SSL/TLS Configuration
SSL_ENABLED=true                  # Enable SSL
SSL_CERT_PATH=/app/certificates/cert.pem
SSL_KEY_PATH=/app/certificates/key.pem
SSL_CA_PATH=/app/certificates/ca.pem
FORCE_HTTPS=true                  # Force HTTPS redirects

# CORS Configuration
CORS_ORIGIN=https://yourdomain.com,https://www.yourdomain.com
CORS_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=Content-Type,Authorization,X-Requested-With
CORS_CREDENTIALS=true

# WebSocket Configuration
WS_ENABLED=true                   # Enable WebSocket
WS_PORT=4001                      # WebSocket port
WS_PATH=/socket.io                # WebSocket path
WS_CORS_ORIGIN=https://yourdomain.com

# Backup Configuration
BACKUP_ENABLED=true               # Enable automated backups
BACKUP_SCHEDULE=0 2 * * *         # Daily at 2 AM (cron format)
BACKUP_RETENTION_DAYS=30          # Keep backups for 30 days
BACKUP_COMPRESSION=gzip           # Compression type
BACKUP_ENCRYPTION=true            # Encrypt backups

# Cloud Storage (S3) Configuration
S3_ENABLED=false                  # Enable S3 storage
S3_BACKUP_BUCKET=restroflow-backups
S3_ACCESS_KEY=your-s3-access-key
S3_SECRET_KEY=your-s3-secret-key
S3_REGION=us-east-1
S3_ENDPOINT=https://s3.amazonaws.com

# Monitoring and Alerting
PROMETHEUS_ENABLED=true           # Enable Prometheus metrics
GRAFANA_PASSWORD=secure-grafana-password
ALERT_EMAIL=<EMAIL>  # Alert notification email
ALERT_WEBHOOK=https://hooks.slack.com/your-webhook-url
HEALTH_CHECK_URL=https://yourdomain.com/health

# Feature Flags
FEATURE_MFA_ENABLED=true          # Enable multi-factor authentication
FEATURE_BIOMETRIC_ENABLED=true   # Enable biometric authentication
FEATURE_QR_LOGIN_ENABLED=true    # Enable QR code login
FEATURE_OFFLINE_MODE_ENABLED=true # Enable offline mode
FEATURE_AUDIT_ENABLED=true       # Enable audit logging
FEATURE_PERFORMANCE_MONITORING=true # Enable performance monitoring
FEATURE_EMERGENCY_ACCESS=true    # Enable emergency access
FEATURE_MOBILE_APP=true          # Enable mobile app features

# API Configuration
API_VERSION=v1                    # API version
API_PREFIX=/api                   # API prefix
API_RATE_LIMIT=1000              # API rate limit per hour
API_TIMEOUT=30000                # API timeout in milliseconds
API_MAX_PAYLOAD_SIZE=50MB        # Maximum API payload size

# Cache Configuration
CACHE_ENABLED=true               # Enable caching
CACHE_TTL=3600                   # Cache TTL in seconds
CACHE_MAX_SIZE=1000              # Maximum cache entries
CACHE_STRATEGY=lru               # Cache eviction strategy

# Session Configuration
SESSION_TIMEOUT=86400000         # 24 hours in milliseconds
SESSION_REFRESH_THRESHOLD=3600000 # 1 hour refresh threshold
SESSION_SECURE=true              # Secure session cookies
SESSION_HTTP_ONLY=true           # HTTP-only session cookies
SESSION_SAME_SITE=strict         # SameSite cookie policy

# Security Headers
SECURITY_HSTS_ENABLED=true       # Enable HSTS
SECURITY_CSP_ENABLED=true        # Enable Content Security Policy
SECURITY_FRAME_OPTIONS=DENY      # X-Frame-Options header
SECURITY_CONTENT_TYPE_NOSNIFF=true # X-Content-Type-Options header
SECURITY_XSS_PROTECTION=true     # X-XSS-Protection header

# Development and Debug (set to false in production)
DEBUG_MODE=false                 # Debug mode
VERBOSE_LOGGING=false            # Verbose logging
STACK_TRACE_ENABLED=false       # Include stack traces in errors
PROFILING_ENABLED=false          # Enable performance profiling

# Timezone and Localization
TIMEZONE=UTC                     # Server timezone
DEFAULT_LOCALE=en-US             # Default locale
SUPPORTED_LOCALES=en-US,es-ES,fr-FR,de-DE
DATE_FORMAT=YYYY-MM-DD           # Date format
TIME_FORMAT=HH:mm:ss             # Time format

# Business Configuration
BUSINESS_HOURS_START=06:00       # Business hours start time
BUSINESS_HOURS_END=23:00         # Business hours end time
BUSINESS_TIMEZONE=America/New_York # Business timezone
CURRENCY_DEFAULT=USD             # Default currency
CURRENCY_SYMBOL=$                # Currency symbol
TAX_RATE_DEFAULT=0.08            # Default tax rate (8%)

# Notification Configuration
NOTIFICATION_ENABLED=true        # Enable notifications
NOTIFICATION_CHANNELS=email,sms,push,webhook
NOTIFICATION_RETRY_ATTEMPTS=3    # Retry attempts for failed notifications
NOTIFICATION_RETRY_DELAY=5000    # Retry delay in milliseconds

# Integration Configuration
INTEGRATION_STRIPE_ENABLED=false # Enable Stripe integration
INTEGRATION_SQUARE_ENABLED=false # Enable Square integration
INTEGRATION_PAYPAL_ENABLED=false # Enable PayPal integration
INTEGRATION_QUICKBOOKS_ENABLED=false # Enable QuickBooks integration

# Compliance Configuration
COMPLIANCE_GDPR_ENABLED=true     # Enable GDPR compliance
COMPLIANCE_CCPA_ENABLED=true     # Enable CCPA compliance
COMPLIANCE_PCI_DSS_ENABLED=true  # Enable PCI DSS compliance
COMPLIANCE_DATA_RETENTION_DAYS=2555 # 7 years data retention

# Load Balancing Configuration
LOAD_BALANCER_ENABLED=false      # Enable load balancing
LOAD_BALANCER_ALGORITHM=round_robin # Load balancing algorithm
LOAD_BALANCER_HEALTH_CHECK=true  # Enable health checks
LOAD_BALANCER_STICKY_SESSIONS=true # Enable sticky sessions

# Scaling Configuration
AUTO_SCALING_ENABLED=false       # Enable auto-scaling
AUTO_SCALING_MIN_INSTANCES=2     # Minimum instances
AUTO_SCALING_MAX_INSTANCES=10    # Maximum instances
AUTO_SCALING_CPU_THRESHOLD=80    # CPU threshold for scaling
AUTO_SCALING_MEMORY_THRESHOLD=80 # Memory threshold for scaling
