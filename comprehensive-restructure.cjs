#!/usr/bin/env node

/**
 * COMPREHENSIVE RESTROFLOW RESTRUCTURING SCRIPT
 * 
 * This script consolidates the messy structure into a clean, professional architecture:
 * - Consolidates multiple root directories
 * - Eliminates duplicate components
 * - Standardizes naming conventions
 * - Creates proper separation of concerns
 * - Organizes documentation
 */

const fs = require('fs');
const path = require('path');

console.log('🏗️  COMPREHENSIVE RESTROFLOW RESTRUCTURING');
console.log('==========================================');

// Phase 1: Create Archive Directory
console.log('\n📦 Phase 1: Creating Archive Directory...');

const archiveDir = 'archive';
if (!fs.existsSync(archiveDir)) {
  fs.mkdirSync(archiveDir, { recursive: true });
}

// Create archive subdirectories
const archiveSubdirs = [
  'archive/old-frontend',
  'archive/old-backend-files', 
  'archive/duplicate-components',
  'archive/old-documentation',
  'archive/test-files',
  'archive/legacy-configs'
];

archiveSubdirs.forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
    console.log(`✅ Created: ${dir}`);
  }
});

// Phase 2: Identify Primary Directories
console.log('\n🎯 Phase 2: Analyzing Directory Structure...');

const analysis = {
  backend: {
    primary: 'backend',
    alternatives: ['project/backend', 'production/backend'],
    status: 'Keep as primary'
  },
  frontend: {
    primary: 'frontend', 
    alternatives: ['project/src', 'project/frontend'],
    status: 'Keep current, archive project/'
  },
  documentation: {
    scattered: [
      'README-STARTUP.md',
      'project/*.md',
      'backend/README.md',
      'docs/*'
    ],
    target: 'docs/',
    status: 'Consolidate all docs'
  }
};

console.log('📊 Analysis Results:');
console.log(`   Backend Primary: ${analysis.backend.primary}`);
console.log(`   Frontend Primary: ${analysis.frontend.primary}`);
console.log(`   Documentation Target: ${analysis.documentation.target}`);

// Phase 3: Backend Consolidation
console.log('\n🔧 Phase 3: Backend Consolidation...');

// Identify the best server file
const serverFiles = [
  'backend/working-server.js',
  'backend/src/server.js', 
  'backend/server.js'
];

let primaryServer = null;
for (const serverFile of serverFiles) {
  if (fs.existsSync(serverFile)) {
    const stats = fs.statSync(serverFile);
    console.log(`   Found: ${serverFile} (${Math.round(stats.size / 1024)}KB)`);
    if (!primaryServer || stats.size > fs.statSync(primaryServer).size) {
      primaryServer = serverFile;
    }
  }
}

console.log(`✅ Primary Server: ${primaryServer}`);

// Phase 4: Frontend Consolidation  
console.log('\n🎨 Phase 4: Frontend Analysis...');

const frontendDirs = ['frontend', 'project'];
frontendDirs.forEach(dir => {
  if (fs.existsSync(dir)) {
    const packageJsonPath = path.join(dir, 'package.json');
    if (fs.existsSync(packageJsonPath)) {
      const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
      console.log(`   ${dir}: ${packageJson.name} v${packageJson.version}`);
      
      if (packageJson.dependencies) {
        const deps = Object.keys(packageJson.dependencies);
        console.log(`     Dependencies: ${deps.length} packages`);
        if (deps.includes('vite')) console.log(`     ✅ Modern Vite setup`);
        if (deps.includes('react')) console.log(`     ✅ React framework`);
        if (deps.includes('typescript')) console.log(`     ✅ TypeScript support`);
      }
    }
  }
});

// Phase 5: Component Analysis
console.log('\n🧩 Phase 5: Component Duplication Analysis...');

const componentPatterns = [
  'UnifiedPOSSystem',
  'EnhancedPaymentProcessor', 
  'ModernLogin',
  'SuperAdminInterface',
  'TenantAdminInterface'
];

componentPatterns.forEach(pattern => {
  console.log(`\n🔍 Analyzing: ${pattern}`);
  
  const locations = [];
  
  // Search in frontend
  if (fs.existsSync('frontend/src/components')) {
    const files = fs.readdirSync('frontend/src/components');
    files.forEach(file => {
      if (file.includes(pattern)) {
        const filePath = `frontend/src/components/${file}`;
        const stats = fs.statSync(filePath);
        locations.push({
          path: filePath,
          size: stats.size,
          modified: stats.mtime
        });
      }
    });
  }
  
  // Search in project
  if (fs.existsSync('project/src')) {
    const searchDir = (dir) => {
      if (!fs.existsSync(dir)) return;
      const files = fs.readdirSync(dir);
      files.forEach(file => {
        const filePath = path.join(dir, file);
        const stats = fs.statSync(filePath);
        if (stats.isDirectory()) {
          searchDir(filePath);
        } else if (file.includes(pattern)) {
          locations.push({
            path: filePath,
            size: stats.size,
            modified: stats.mtime
          });
        }
      });
    };
    searchDir('project/src');
  }
  
  if (locations.length > 1) {
    console.log(`   ⚠️  DUPLICATE FOUND: ${locations.length} versions`);
    locations.forEach((loc, index) => {
      console.log(`     ${index + 1}. ${loc.path} (${Math.round(loc.size / 1024)}KB, ${loc.modified.toISOString().split('T')[0]})`);
    });
    
    // Recommend primary version (largest, most recent)
    const primary = locations.reduce((best, current) => {
      if (current.size > best.size || current.modified > best.modified) {
        return current;
      }
      return best;
    });
    console.log(`   ✅ Recommended Primary: ${primary.path}`);
  } else if (locations.length === 1) {
    console.log(`   ✅ Single version: ${locations[0].path}`);
  } else {
    console.log(`   ❌ Not found`);
  }
});

// Phase 6: Documentation Consolidation Plan
console.log('\n📚 Phase 6: Documentation Consolidation Plan...');

const docFiles = [];

// Find all markdown files
const findMarkdownFiles = (dir, prefix = '') => {
  if (!fs.existsSync(dir)) return;
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    const stats = fs.statSync(filePath);
    if (stats.isDirectory() && !file.includes('node_modules') && !file.includes('.git')) {
      findMarkdownFiles(filePath, `${prefix}${file}/`);
    } else if (file.endsWith('.md')) {
      docFiles.push({
        path: filePath,
        name: file,
        location: prefix,
        size: stats.size
      });
    }
  });
};

findMarkdownFiles('.');

console.log(`📄 Found ${docFiles.length} documentation files:`);
docFiles.slice(0, 10).forEach(doc => {
  console.log(`   ${doc.path} (${Math.round(doc.size / 1024)}KB)`);
});
if (docFiles.length > 10) {
  console.log(`   ... and ${docFiles.length - 10} more files`);
}

// Phase 7: Recommendations
console.log('\n🎯 Phase 7: RESTRUCTURING RECOMMENDATIONS');
console.log('==========================================');

console.log('\n✅ IMMEDIATE ACTIONS RECOMMENDED:');
console.log('1. 🗂️  Archive project/ directory (contains older version)');
console.log('2. 🔧 Use backend/working-server.js as primary server');
console.log('3. 🎨 Keep current frontend/ as primary');
console.log('4. 📚 Consolidate all .md files to docs/ directory');
console.log('5. 🧹 Clean up root directory (move test files to tests/)');

console.log('\n📊 STRUCTURE ANALYSIS COMPLETE');
console.log('==============================');
console.log(`📁 Directories analyzed: ${frontendDirs.length + 1}`);
console.log(`🧩 Component patterns checked: ${componentPatterns.length}`);
console.log(`📄 Documentation files found: ${docFiles.length}`);
console.log(`🔧 Server files evaluated: ${serverFiles.length}`);

console.log('\n💡 NEXT STEPS:');
console.log('1. Review the analysis above');
console.log('2. Run the restructuring commands (will be provided)');
console.log('3. Test the consolidated system');
console.log('4. Update documentation');

console.log('\n🎉 Analysis complete! Ready for restructuring.');
