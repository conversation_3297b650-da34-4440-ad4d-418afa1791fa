<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - Working POS System</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <style>
        body { 
            font-family: 'Inter', sans-serif; 
            transition: all 0.3s ease;
            margin: 0;
            padding: 0;
        }
        .gradient-bg { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
        }
        .gradient-bg-dark { 
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%); 
        }
        .card-hover { 
            transition: all 0.3s ease; 
        }
        .card-hover:hover { 
            transform: translateY(-2px); 
            box-shadow: 0 10px 25px rgba(0,0,0,0.1); 
        }
        .slide-in { 
            animation: slideIn 0.5s ease-out; 
        }
        @keyframes slideIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Dark theme styles */
        .dark {
            background-color: #1a1a2e;
            color: #ffffff;
        }
        .dark .bg-white { background-color: #2d2d44 !important; }
        .dark .text-gray-800 { color: #ffffff !important; }
        .dark .text-gray-600 { color: #a0a0a0 !important; }
        .dark .text-gray-500 { color: #888888 !important; }
        .dark .border-gray-200 { border-color: #404040 !important; }
        .dark .border-gray-300 { border-color: #505050 !important; }
        .dark .bg-gray-50 { background-color: #1a1a2e !important; }
        .dark .bg-gray-100 { background-color: #2d2d44 !important; }
        .dark .card-hover:hover { box-shadow: 0 10px 25px rgba(255,255,255,0.1) !important; }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            font-size: 20px;
        }
        .theme-toggle:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
        }
        .dark .theme-toggle {
            background: rgba(45, 45, 68, 0.9);
            color: #ffffff;
        }
        
        .hidden { display: none !important; }
        .show { display: block !important; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Theme Toggle -->
    <button id="themeToggle" class="theme-toggle" title="Toggle Theme">🌙</button>

    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md slide-in">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">RestroFlow</h1>
                <p class="text-gray-600">Enter your PIN to access the system</p>
            </div>
            
            <form id="loginForm" class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">
                        PIN Code
                    </label>
                    <input
                        type="password"
                        id="pinInput"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                        placeholder="Enter your PIN"
                        required
                    />
                </div>
                
                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                </div>
                
                <button
                    type="submit"
                    id="loginButton"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                    Access System
                </button>
            </form>
            
            <div class="mt-6 text-center text-sm text-gray-500">
                <p>Demo PIN: 1234</p>
            </div>
        </div>
    </div>

    <!-- POS System -->
    <div id="posSystem" class="hidden min-h-screen bg-gray-50">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">
                    <div class="flex items-center">
                        <h1 class="text-2xl font-bold text-blue-600">RestroFlow</h1>
                        <span class="ml-4 text-gray-600">Point of Sale System</span>
                    </div>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Welcome, Demo User</span>
                        <button
                            id="logoutButton"
                            class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors"
                        >
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </header>

        <div class="flex h-screen pt-16">
            <!-- Product Grid -->
            <div class="flex-1 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Products</h2>
                <div id="productGrid" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                    <!-- Products will be loaded here -->
                </div>
            </div>

            <!-- Cart Panel -->
            <div class="w-80 bg-white border-l border-gray-200 p-6">
                <h2 class="text-xl font-semibold mb-4 text-gray-800">Current Order</h2>
                
                <div id="cartEmpty" class="text-gray-500 text-center py-8">
                    No items in cart
                </div>
                
                <div id="cartItems" class="hidden">
                    <div id="cartList" class="space-y-3 mb-6">
                        <!-- Cart items will be loaded here -->
                    </div>
                    
                    <div class="border-t pt-4">
                        <div class="flex justify-between items-center mb-4">
                            <span class="text-lg font-semibold text-gray-800">Total:</span>
                            <span id="totalAmount" class="text-2xl font-bold text-blue-600">$0.00</span>
                        </div>
                        
                        <button
                            id="paymentButton"
                            class="w-full bg-green-600 text-white py-3 px-4 rounded-lg hover:bg-green-700 transition-colors font-semibold"
                        >
                            Process Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Application State
        let isAuthenticated = false;
        let darkMode = localStorage.getItem('theme') === 'dark';
        let cart = [];
        
        const products = [
            { id: 1, name: 'Burger', price: 12.99, category: 'Main' },
            { id: 2, name: 'Pizza', price: 18.99, category: 'Main' },
            { id: 3, name: 'Salad', price: 8.99, category: 'Appetizer' },
            { id: 4, name: 'Soda', price: 2.99, category: 'Beverage' },
            { id: 5, name: 'Coffee', price: 3.99, category: 'Beverage' },
            { id: 6, name: 'Dessert', price: 6.99, category: 'Dessert' }
        ];

        // DOM Elements
        const themeToggle = document.getElementById('themeToggle');
        const loginScreen = document.getElementById('loginScreen');
        const posSystem = document.getElementById('posSystem');
        const loginForm = document.getElementById('loginForm');
        const pinInput = document.getElementById('pinInput');
        const loginButton = document.getElementById('loginButton');
        const errorMessage = document.getElementById('errorMessage');
        const logoutButton = document.getElementById('logoutButton');
        const productGrid = document.getElementById('productGrid');
        const cartEmpty = document.getElementById('cartEmpty');
        const cartItems = document.getElementById('cartItems');
        const cartList = document.getElementById('cartList');
        const totalAmount = document.getElementById('totalAmount');
        const paymentButton = document.getElementById('paymentButton');

        // Initialize theme
        function initializeTheme() {
            if (darkMode) {
                document.documentElement.classList.add('dark');
                themeToggle.textContent = '☀️';
            } else {
                document.documentElement.classList.remove('dark');
                themeToggle.textContent = '🌙';
            }
        }

        // Toggle theme
        function toggleTheme() {
            darkMode = !darkMode;
            
            if (darkMode) {
                document.documentElement.classList.add('dark');
                themeToggle.textContent = '☀️';
                localStorage.setItem('theme', 'dark');
            } else {
                document.documentElement.classList.remove('dark');
                themeToggle.textContent = '🌙';
                localStorage.setItem('theme', 'light');
            }
        }

        // Show error message
        function showError(message) {
            errorMessage.textContent = message;
            errorMessage.classList.remove('hidden');
        }

        // Hide error message
        function hideError() {
            errorMessage.classList.add('hidden');
        }

        // Login function
        async function login(pin) {
            loginButton.innerHTML = '<div class="loading-spinner"></div> Authenticating...';
            loginButton.disabled = true;
            hideError();
            
            // Simulate API call
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            if (pin === '1234') {
                isAuthenticated = true;
                localStorage.setItem('authToken', 'demo-token');
                localStorage.setItem('user', JSON.stringify({ id: 1, name: 'Demo User', role: 'admin' }));
                showPOSSystem();
            } else {
                showError('Invalid PIN. Please try again.');
            }
            
            loginButton.innerHTML = 'Access System';
            loginButton.disabled = false;
        }

        // Show POS system
        function showPOSSystem() {
            loginScreen.classList.add('hidden');
            posSystem.classList.remove('hidden');
            loadProducts();
            updateCart();
        }

        // Show login screen
        function showLoginScreen() {
            posSystem.classList.add('hidden');
            loginScreen.classList.remove('hidden');
            pinInput.value = '';
            hideError();
        }

        // Logout function
        function logout() {
            isAuthenticated = false;
            cart = [];
            localStorage.removeItem('authToken');
            localStorage.removeItem('user');
            showLoginScreen();
        }

        // Load products
        function loadProducts() {
            productGrid.innerHTML = '';
            products.forEach(product => {
                const productCard = document.createElement('div');
                productCard.className = 'bg-white rounded-lg shadow-md p-4 card-hover cursor-pointer';
                productCard.innerHTML = `
                    <h3 class="font-semibold text-gray-800">${product.name}</h3>
                    <p class="text-sm text-gray-600">${product.category}</p>
                    <p class="text-lg font-bold text-blue-600 mt-2">$${product.price}</p>
                `;
                productCard.addEventListener('click', () => addToCart(product));
                productGrid.appendChild(productCard);
            });
        }

        // Add to cart
        function addToCart(product) {
            const existingItem = cart.find(item => item.id === product.id);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ ...product, quantity: 1 });
            }
            updateCart();
        }

        // Update cart display
        function updateCart() {
            if (cart.length === 0) {
                cartEmpty.classList.remove('hidden');
                cartItems.classList.add('hidden');
            } else {
                cartEmpty.classList.add('hidden');
                cartItems.classList.remove('hidden');
                
                cartList.innerHTML = '';
                cart.forEach(item => {
                    const cartItem = document.createElement('div');
                    cartItem.className = 'flex items-center justify-between bg-gray-50 p-3 rounded-lg';
                    cartItem.innerHTML = `
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-800">${item.name}</h4>
                            <p class="text-sm text-gray-600">$${item.price} each</p>
                        </div>
                        <div class="flex items-center space-x-2">
                            <button onclick="updateQuantity(${item.id}, ${item.quantity - 1})" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">-</button>
                            <span class="w-8 text-center">${item.quantity}</span>
                            <button onclick="updateQuantity(${item.id}, ${item.quantity + 1})" class="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center hover:bg-gray-300">+</button>
                        </div>
                    `;
                    cartList.appendChild(cartItem);
                });
                
                const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                totalAmount.textContent = `$${total.toFixed(2)}`;
            }
        }

        // Update quantity
        function updateQuantity(productId, quantity) {
            if (quantity <= 0) {
                cart = cart.filter(item => item.id !== productId);
            } else {
                const item = cart.find(item => item.id === productId);
                if (item) {
                    item.quantity = quantity;
                }
            }
            updateCart();
        }

        // Process payment
        function processPayment() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`Payment processed successfully! Total: $${total.toFixed(2)}`);
            cart = [];
            updateCart();
        }

        // Event listeners
        themeToggle.addEventListener('click', toggleTheme);
        loginForm.addEventListener('submit', (e) => {
            e.preventDefault();
            login(pinInput.value);
        });
        logoutButton.addEventListener('click', logout);
        paymentButton.addEventListener('click', processPayment);

        // Check for existing authentication
        function checkAuth() {
            const token = localStorage.getItem('authToken');
            const userData = localStorage.getItem('user');
            if (token && userData) {
                isAuthenticated = true;
                showPOSSystem();
            }
        }

        // Initialize application
        initializeTheme();
        checkAuth();
    </script>
</body>
</html>
