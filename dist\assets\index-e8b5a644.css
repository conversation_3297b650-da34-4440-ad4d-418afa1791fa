@tailwind base;@tailwind components;@tailwind utilities;html,body,#root{height:100%}body{font-family:Inter,-apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;transition:background-color .2s ease-in-out,color .2s ease-in-out}.scrollbar-thin::-webkit-scrollbar{width:6px;height:6px}.scrollbar-thin::-webkit-scrollbar-track{background:rgba(156,163,175,.1);border-radius:3px}.scrollbar-thin::-webkit-scrollbar-thumb{background:rgba(156,163,175,.3);border-radius:3px}.scrollbar-thin::-webkit-scrollbar-thumb:hover{background:rgba(156,163,175,.5)}.dark .scrollbar-thin::-webkit-scrollbar-track{background:rgba(75,85,99,.3)}.dark .scrollbar-thin::-webkit-scrollbar-thumb{background:rgba(75,85,99,.6)}.dark .scrollbar-thin::-webkit-scrollbar-thumb:hover{background:rgba(75,85,99,.8)}.animate-fade-in{animation:fadeIn .3s ease-in}.animate-slide-up{animation:slideUp .3s ease-out}@keyframes fadeIn{0%{opacity:0}to{opacity:1}}@keyframes slideUp{0%{transform:translateY(20px);opacity:0}to{transform:translateY(0);opacity:1}}.bg-grid-pattern{background-image:linear-gradient(rgba(0,0,0,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(0,0,0,.1) 1px,transparent 1px);background-size:20px 20px}.dark .bg-grid-pattern{background-image:linear-gradient(rgba(255,255,255,.1) 1px,transparent 1px),linear-gradient(90deg,rgba(255,255,255,.1) 1px,transparent 1px)}
