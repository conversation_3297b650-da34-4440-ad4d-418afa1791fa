import React, { useState, useEffect } from 'react';
import { 
  FileText, 
  Download, 
  Calendar, 
  Filter, 
  TrendingUp, 
  DollarSign, 
  Users, 
  ShoppingCart,
  Clock,
  Target,
  BarChart3,
  PieChart,
  LineChart,
  Mail,
  Printer,
  Share2,
  Settings,
  Eye,
  Plus,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Star,
  Award
} from 'lucide-react';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  Line<PERSON>hart as RechartsLineChart,
  Line,
  <PERSON><PERSON>hart as RechartsPie<PERSON>hart,
  Pie,
  Cell,
  AreaChart,
  Area,
  ComposedChart
} from 'recharts';

interface Report {
  id: string;
  name: string;
  type: 'sales' | 'financial' | 'operational' | 'customer' | 'inventory' | 'staff';
  description: string;
  frequency: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom';
  format: 'pdf' | 'excel' | 'csv' | 'dashboard';
  lastGenerated: string;
  nextScheduled?: string;
  recipients: string[];
  isAutomated: boolean;
  parameters: {
    dateRange: string;
    locations: string[];
    departments: string[];
    metrics: string[];
  };
  status: 'active' | 'paused' | 'draft';
}

interface ReportData {
  sales_summary: {
    total_revenue: number;
    total_orders: number;
    avg_order_value: number;
    growth_rate: number;
    top_products: Array<{
      name: string;
      revenue: number;
      quantity: number;
      growth: number;
    }>;
    revenue_by_hour: Array<{
      hour: number;
      revenue: number;
      orders: number;
    }>;
    payment_methods: Array<{
      method: string;
      amount: number;
      percentage: number;
    }>;
  };
  financial_summary: {
    gross_revenue: number;
    net_revenue: number;
    total_costs: number;
    profit_margin: number;
    tax_collected: number;
    cost_breakdown: Array<{
      category: string;
      amount: number;
      percentage: number;
    }>;
    profit_trends: Array<{
      date: string;
      revenue: number;
      costs: number;
      profit: number;
    }>;
  };
  operational_metrics: {
    avg_service_time: number;
    table_turnover: number;
    kitchen_efficiency: number;
    staff_productivity: number;
    customer_satisfaction: number;
    waste_percentage: number;
    peak_hours: Array<{
      hour: number;
      efficiency: number;
      orders: number;
    }>;
  };
  customer_insights: {
    total_customers: number;
    new_customers: number;
    returning_customers: number;
    customer_lifetime_value: number;
    retention_rate: number;
    churn_rate: number;
    demographics: Array<{
      segment: string;
      count: number;
      revenue: number;
    }>;
    satisfaction_scores: Array<{
      category: string;
      score: number;
      trend: number;
    }>;
  };
}

const Phase3ReportingSystem: React.FC = () => {
  const [reports, setReports] = useState<Report[]>([]);
  const [reportData, setReportData] = useState<ReportData | null>(null);
  const [activeTab, setActiveTab] = useState<'dashboard' | 'reports' | 'scheduled' | 'custom'>('dashboard');
  const [selectedReport, setSelectedReport] = useState<Report | null>(null);
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'quarter' | 'year'>('month');
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  useEffect(() => {
    loadReportingData();
  }, [dateRange]);

  const loadReportingData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - in production this would come from API
      const mockReports: Report[] = [
        {
          id: '1',
          name: 'Daily Sales Report',
          type: 'sales',
          description: 'Comprehensive daily sales performance and trends',
          frequency: 'daily',
          format: 'pdf',
          lastGenerated: '2024-06-04T09:00:00Z',
          nextScheduled: '2024-06-05T09:00:00Z',
          recipients: ['<EMAIL>', '<EMAIL>'],
          isAutomated: true,
          parameters: {
            dateRange: 'yesterday',
            locations: ['main'],
            departments: ['all'],
            metrics: ['revenue', 'orders', 'avg_order_value']
          },
          status: 'active'
        },
        {
          id: '2',
          name: 'Monthly Financial Summary',
          type: 'financial',
          description: 'Monthly profit & loss, cost analysis, and financial KPIs',
          frequency: 'monthly',
          format: 'excel',
          lastGenerated: '2024-06-01T08:00:00Z',
          nextScheduled: '2024-07-01T08:00:00Z',
          recipients: ['<EMAIL>', '<EMAIL>'],
          isAutomated: true,
          parameters: {
            dateRange: 'last_month',
            locations: ['all'],
            departments: ['all'],
            metrics: ['revenue', 'costs', 'profit', 'taxes']
          },
          status: 'active'
        },
        {
          id: '3',
          name: 'Customer Analytics Report',
          type: 'customer',
          description: 'Customer behavior, satisfaction, and retention analysis',
          frequency: 'weekly',
          format: 'dashboard',
          lastGenerated: '2024-06-03T10:00:00Z',
          nextScheduled: '2024-06-10T10:00:00Z',
          recipients: ['<EMAIL>'],
          isAutomated: true,
          parameters: {
            dateRange: 'last_week',
            locations: ['all'],
            departments: ['front_of_house'],
            metrics: ['satisfaction', 'retention', 'lifetime_value']
          },
          status: 'active'
        }
      ];

      const mockReportData: ReportData = {
        sales_summary: {
          total_revenue: 45678.90,
          total_orders: 1234,
          avg_order_value: 37.02,
          growth_rate: 12.5,
          top_products: [
            { name: 'Margherita Pizza', revenue: 8945.50, quantity: 234, growth: 15.2 },
            { name: 'Caesar Salad', revenue: 6789.25, quantity: 189, growth: 8.7 },
            { name: 'Grilled Chicken', revenue: 5432.10, quantity: 156, growth: 22.1 },
            { name: 'Fish & Chips', revenue: 4321.75, quantity: 123, growth: -3.4 },
            { name: 'Chocolate Cake', revenue: 3210.40, quantity: 98, growth: 18.9 }
          ],
          revenue_by_hour: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            revenue: Math.random() * 2000 + 500,
            orders: Math.floor(Math.random() * 50 + 10)
          })),
          payment_methods: [
            { method: 'Credit Card', amount: 25678.90, percentage: 56.2 },
            { method: 'Cash', amount: 12345.60, percentage: 27.0 },
            { method: 'Digital Wallet', amount: 5432.10, percentage: 11.9 },
            { method: 'Gift Card', amount: 2222.30, percentage: 4.9 }
          ]
        },
        financial_summary: {
          gross_revenue: 45678.90,
          net_revenue: 41111.01,
          total_costs: 32456.78,
          profit_margin: 21.1,
          tax_collected: 4567.89,
          cost_breakdown: [
            { category: 'Food Costs', amount: 15678.90, percentage: 48.3 },
            { category: 'Labor', amount: 9876.54, percentage: 30.4 },
            { category: 'Rent', amount: 3456.78, percentage: 10.6 },
            { category: 'Utilities', amount: 1234.56, percentage: 3.8 },
            { category: 'Other', amount: 2210.00, percentage: 6.9 }
          ],
          profit_trends: Array.from({ length: 30 }, (_, i) => ({
            date: new Date(Date.now() - (29 - i) * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            revenue: Math.random() * 2000 + 1000,
            costs: Math.random() * 1500 + 700,
            profit: Math.random() * 500 + 200
          }))
        },
        operational_metrics: {
          avg_service_time: 14.5,
          table_turnover: 2.8,
          kitchen_efficiency: 87.3,
          staff_productivity: 92.1,
          customer_satisfaction: 4.6,
          waste_percentage: 3.2,
          peak_hours: Array.from({ length: 24 }, (_, i) => ({
            hour: i,
            efficiency: Math.random() * 30 + 70,
            orders: Math.floor(Math.random() * 50 + 10)
          }))
        },
        customer_insights: {
          total_customers: 2456,
          new_customers: 234,
          returning_customers: 2222,
          customer_lifetime_value: 287.50,
          retention_rate: 68.5,
          churn_rate: 12.3,
          demographics: [
            { segment: '18-25', count: 456, revenue: 12345.67 },
            { segment: '26-35', count: 789, revenue: 23456.78 },
            { segment: '36-45', count: 654, revenue: 18765.43 },
            { segment: '46-55', count: 321, revenue: 9876.54 },
            { segment: '55+', count: 236, revenue: 7654.32 }
          ],
          satisfaction_scores: [
            { category: 'Food Quality', score: 4.7, trend: 0.2 },
            { category: 'Service Speed', score: 4.3, trend: -0.1 },
            { category: 'Staff Friendliness', score: 4.8, trend: 0.3 },
            { category: 'Ambiance', score: 4.5, trend: 0.0 },
            { category: 'Value for Money', score: 4.2, trend: 0.1 }
          ]
        }
      };

      setReports(mockReports);
      setReportData(mockReportData);
    } catch (error) {
      console.error('Error loading reporting data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`;
  const formatPercentage = (value: number) => `${value.toFixed(1)}%`;

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4 text-green-500" />;
    if (growth < 0) return <TrendingUp className="h-4 w-4 text-red-500 transform rotate-180" />;
    return <div className="h-4 w-4" />;
  };

  const COLORS = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#8dd1e1'];

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading reporting system...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <FileText className="h-6 w-6 mr-2 text-blue-600" />
              Business Intelligence & Reporting
            </h1>
            <p className="text-gray-600 mt-1">
              Comprehensive business reports and analytics dashboard
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <select
              value={dateRange}
              onChange={(e) => setDateRange(e.target.value as any)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="today">Today</option>
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
            
            <button
              onClick={() => setShowCreateModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <Plus className="h-4 w-4" />
              <span>Create Report</span>
            </button>
            
            <button
              onClick={loadReportingData}
              className="bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700 flex items-center space-x-2"
            >
              <RefreshCw className="h-4 w-4" />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {[
            { id: 'dashboard', label: 'Executive Dashboard', icon: BarChart3 },
            { id: 'reports', label: 'Report Library', icon: FileText },
            { id: 'scheduled', label: 'Scheduled Reports', icon: Calendar },
            { id: 'custom', label: 'Custom Reports', icon: Settings }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeTab === 'dashboard' && reportData && (
          <div className="space-y-6">
            {/* Executive Summary Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(reportData.sales_summary.total_revenue)}
                    </p>
                    <div className="flex items-center mt-1">
                      {getGrowthIcon(reportData.sales_summary.growth_rate)}
                      <span className={`text-sm ml-1 ${getGrowthColor(reportData.sales_summary.growth_rate)}`}>
                        {formatPercentage(reportData.sales_summary.growth_rate)}
                      </span>
                    </div>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Orders</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {reportData.sales_summary.total_orders.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Avg: {formatCurrency(reportData.sales_summary.avg_order_value)}
                    </p>
                  </div>
                  <ShoppingCart className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Profit Margin</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatPercentage(reportData.financial_summary.profit_margin)}
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Net: {formatCurrency(reportData.financial_summary.net_revenue)}
                    </p>
                  </div>
                  <Target className="h-8 w-8 text-purple-500" />
                </div>
              </div>
              
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Customer Satisfaction</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {reportData.operational_metrics.customer_satisfaction}/5
                    </p>
                    <p className="text-sm text-gray-500 mt-1">
                      Retention: {formatPercentage(reportData.customer_insights.retention_rate)}
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </div>
            </div>

            {/* Charts Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Revenue Trends */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Revenue Trends</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <AreaChart data={reportData.financial_summary.profit_trends.slice(-14)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" tickFormatter={(value) => new Date(value).toLocaleDateString()} />
                    <YAxis />
                    <Tooltip formatter={(value: any) => [formatCurrency(value), '']} />
                    <Area type="monotone" dataKey="revenue" stackId="1" stroke="#8884d8" fill="#8884d8" />
                    <Area type="monotone" dataKey="costs" stackId="2" stroke="#82ca9d" fill="#82ca9d" />
                  </AreaChart>
                </ResponsiveContainer>
              </div>

              {/* Top Products */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Top Performing Products</h3>
                <div className="space-y-3">
                  {reportData.sales_summary.top_products.slice(0, 5).map((product, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="text-sm font-medium text-blue-600">{index + 1}</span>
                        </div>
                        <div>
                          <p className="font-medium text-gray-900">{product.name}</p>
                          <p className="text-sm text-gray-500">{product.quantity} sold</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">{formatCurrency(product.revenue)}</p>
                        <div className="flex items-center">
                          {getGrowthIcon(product.growth)}
                          <span className={`text-sm ml-1 ${getGrowthColor(product.growth)}`}>
                            {formatPercentage(product.growth)}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Payment Methods */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Methods</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <RechartsPieChart>
                    <Pie
                      data={reportData.sales_summary.payment_methods}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      label={({ method, percentage }) => `${method}: ${percentage}%`}
                      outerRadius={80}
                      fill="#8884d8"
                      dataKey="amount"
                    >
                      {reportData.sales_summary.payment_methods.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value: any) => [formatCurrency(value), 'Amount']} />
                  </RechartsPieChart>
                </ResponsiveContainer>
              </div>

              {/* Customer Demographics */}
              <div className="bg-white p-6 rounded-lg shadow-sm border">
                <h3 className="text-lg font-semibold text-gray-900 mb-4">Customer Demographics</h3>
                <ResponsiveContainer width="100%" height={300}>
                  <BarChart data={reportData.customer_insights.demographics}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="segment" />
                    <YAxis />
                    <Tooltip formatter={(value: any, name: any) => [
                      name === 'revenue' ? formatCurrency(value) : value,
                      name === 'revenue' ? 'Revenue' : 'Customers'
                    ]} />
                    <Legend />
                    <Bar dataKey="count" fill="#8884d8" name="Customers" />
                    <Bar dataKey="revenue" fill="#82ca9d" name="Revenue" />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </div>

            {/* Operational Metrics */}
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Operational Performance</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {reportData.operational_metrics.avg_service_time}m
                  </div>
                  <div className="text-sm text-gray-500">Avg Service Time</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {reportData.operational_metrics.table_turnover}x
                  </div>
                  <div className="text-sm text-gray-500">Table Turnover</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatPercentage(reportData.operational_metrics.kitchen_efficiency)}
                  </div>
                  <div className="text-sm text-gray-500">Kitchen Efficiency</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-orange-600">
                    {formatPercentage(reportData.operational_metrics.staff_productivity)}
                  </div>
                  <div className="text-sm text-gray-500">Staff Productivity</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {reportData.operational_metrics.customer_satisfaction}/5
                  </div>
                  <div className="text-sm text-gray-500">Satisfaction</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-red-600">
                    {formatPercentage(reportData.operational_metrics.waste_percentage)}
                  </div>
                  <div className="text-sm text-gray-500">Waste Rate</div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs content would be implemented here */}
        {activeTab !== 'dashboard' && (
          <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} System
              </h3>
              <p className="text-gray-600 mb-4">
                Advanced {activeTab} management system is being implemented with comprehensive automation and customization.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">
                  This section will include full {activeTab} capabilities with automated generation and distribution.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3ReportingSystem;
