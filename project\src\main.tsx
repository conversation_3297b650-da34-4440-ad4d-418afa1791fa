import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import UnifiedPOSSystem from './UnifiedPOSSystem';
import IndustryStandardPOSSystem from './IndustryStandardPOSSystem';
import TestRestructuredPOS from './components/TestRestructuredPOS';
import DirectRestructuredEntry from './DirectRestructuredEntry';
import SuperAdminApp from './SuperAdminApp';
import { EnhancedAppProvider } from './context/EnhancedAppContext';
import './index.css';
import './styles/industry-standard-pos.css';

// Check for direct routes
const isTestRoute = window.location.pathname.includes('test-restructured') ||
                   window.location.search.includes('test=true');

const isDirectRestructured = window.location.search.includes('direct=true') ||
                            window.location.pathname.includes('direct-restructured');

const isSuperAdmin = window.location.search.includes('admin=true') ||
                    window.location.pathname.includes('super-admin') ||
                    window.location.pathname.includes('admin');

// Check if we should load the industry-standard POS system
const useIndustryStandard = window.location.pathname.includes('industry-standard') ||
                           window.location.search.includes('industry=true') ||
                           window.location.search.includes('restructured=true') ||
                           localStorage.getItem('useIndustryStandardPOS') === 'true' ||
                           localStorage.getItem('useRestructuredPOS') === 'true';

// Log which system is being loaded
console.log(`🚀 Loading ${useIndustryStandard ? 'Industry Standard' : 'Legacy'} POS System`);
console.log('🔧 Debug Info:');
console.log('  - pathname:', window.location.pathname);
console.log('  - search:', window.location.search);
console.log('  - localStorage.useIndustryStandardPOS:', localStorage.getItem('useIndustryStandardPOS'));
console.log('  - useIndustryStandard:', useIndustryStandard);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    {isSuperAdmin ? (
      <SuperAdminApp
        initialTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
      />
    ) : isDirectRestructured ? (
      <DirectRestructuredEntry />
    ) : isTestRoute ? (
      <EnhancedAppProvider>
        <TestRestructuredPOS
          isDarkMode={document.documentElement.classList.contains('dark')}
          onThemeToggle={() => {
            document.documentElement.classList.toggle('dark');
          }}
        />
      </EnhancedAppProvider>
    ) : useIndustryStandard ? (
      <IndustryStandardPOSSystem
        initialTheme={document.documentElement.classList.contains('dark') ? 'dark' : 'light'}
        companyBranding={{
          companyName: 'RestroFlow',
          primaryColor: '#3B82F6',
          secondaryColor: '#10B981'
        }}
      />
    ) : (
      <UnifiedPOSSystem />
    )}
  </StrictMode>
);
