import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Printer, Wifi, Bluetooth, Usb, <PERSON><PERSON><PERSON>, CheckCircle, AlertTriangle, RefreshCw, Plus } from 'lucide-react';

interface PrinterDevice {
  id: string;
  name: string;
  brand: 'epson' | 'star' | 'sunmi' | 'citizen' | 'bixolon';
  model: string;
  connection_type: 'usb' | 'bluetooth' | 'wifi' | 'ethernet';
  status: 'connected' | 'disconnected' | 'error' | 'printing';
  ip_address?: string;
  mac_address?: string;
  paper_width: '58mm' | '80mm' | '112mm';
  capabilities: {
    auto_cut: boolean;
    partial_cut: boolean;
    logo_printing: boolean;
    barcode_printing: boolean;
    qr_printing: boolean;
    color_printing: boolean;
  };
  settings: {
    auto_print: boolean;
    print_logo: boolean;
    print_footer: boolean;
    copies: number;
    cut_type: 'full' | 'partial' | 'none';
  };
  last_print: string;
  print_queue: number;
  error_message?: string;
}

interface ReceiptTemplate {
  id: string;
  name: string;
  type: 'sale' | 'refund' | 'void' | 'reprint';
  header: {
    logo: boolean;
    business_name: string;
    address: string[];
    phone: string;
    email: string;
  };
  body: {
    show_customer_info: boolean;
    show_cashier: boolean;
    show_table_number: boolean;
    item_details: 'full' | 'summary';
    show_modifiers: boolean;
  };
  footer: {
    tax_breakdown: boolean;
    payment_details: boolean;
    qr_code: boolean;
    barcode: boolean;
    thank_you_message: string;
    return_policy: string;
  };
  formatting: {
    font_size: 'small' | 'medium' | 'large';
    alignment: 'left' | 'center' | 'right';
    line_spacing: number;
    margin: number;
  };
}

const ReceiptPrinterManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [printers, setPrinters] = useState<PrinterDevice[]>([]);
  const [templates, setTemplates] = useState<ReceiptTemplate[]>([]);
  const [selectedPrinter, setSelectedPrinter] = useState<PrinterDevice | null>(null);
  const [activeView, setActiveView] = useState<'printers' | 'templates' | 'test'>('printers');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddPrinterModal, setShowAddPrinterModal] = useState(false);

  // Load printer data
  useEffect(() => {
    const loadPrinterData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🖨️ Loading receipt printer data...');
        
        const [printersResponse, templatesResponse] = await Promise.all([
          apiCall('/api/hardware/printers'),
          apiCall('/api/hardware/receipt-templates')
        ]);
        
        if (printersResponse.ok && templatesResponse.ok) {
          const printersData = await printersResponse.json();
          const templatesData = await templatesResponse.json();
          setPrinters(printersData);
          setTemplates(templatesData);
          console.log('✅ Receipt printer data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading receipt printer data:', error);
        setError('Failed to load printer data. Using mock data.');
        
        // Fallback to mock data
        const mockPrinters: PrinterDevice[] = [
          {
            id: 'printer_1',
            name: 'Main Counter Printer',
            brand: 'epson',
            model: 'TM-T88VI',
            connection_type: 'usb',
            status: 'connected',
            paper_width: '80mm',
            capabilities: {
              auto_cut: true,
              partial_cut: true,
              logo_printing: true,
              barcode_printing: true,
              qr_printing: true,
              color_printing: false
            },
            settings: {
              auto_print: true,
              print_logo: true,
              print_footer: true,
              copies: 1,
              cut_type: 'full'
            },
            last_print: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            print_queue: 0
          },
          {
            id: 'printer_2',
            name: 'Kitchen Receipt Printer',
            brand: 'star',
            model: 'TSP143IIIU',
            connection_type: 'wifi',
            status: 'connected',
            ip_address: '*************',
            paper_width: '80mm',
            capabilities: {
              auto_cut: true,
              partial_cut: false,
              logo_printing: false,
              barcode_printing: true,
              qr_printing: false,
              color_printing: false
            },
            settings: {
              auto_print: true,
              print_logo: false,
              print_footer: false,
              copies: 1,
              cut_type: 'full'
            },
            last_print: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            print_queue: 2
          },
          {
            id: 'printer_3',
            name: 'Mobile Bluetooth Printer',
            brand: 'sunmi',
            model: 'NT311',
            connection_type: 'bluetooth',
            status: 'disconnected',
            mac_address: '00:11:22:33:44:55',
            paper_width: '58mm',
            capabilities: {
              auto_cut: false,
              partial_cut: false,
              logo_printing: true,
              barcode_printing: true,
              qr_printing: true,
              color_printing: false
            },
            settings: {
              auto_print: false,
              print_logo: true,
              print_footer: true,
              copies: 1,
              cut_type: 'none'
            },
            last_print: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            print_queue: 0,
            error_message: 'Bluetooth connection lost'
          }
        ];

        const mockTemplates: ReceiptTemplate[] = [
          {
            id: 'template_1',
            name: 'Standard Sale Receipt',
            type: 'sale',
            header: {
              logo: true,
              business_name: 'Restaurant POS',
              address: ['123 Main Street', 'City, State 12345'],
              phone: '(*************',
              email: '<EMAIL>'
            },
            body: {
              show_customer_info: true,
              show_cashier: true,
              show_table_number: true,
              item_details: 'full',
              show_modifiers: true
            },
            footer: {
              tax_breakdown: true,
              payment_details: true,
              qr_code: true,
              barcode: true,
              thank_you_message: 'Thank you for your visit!',
              return_policy: 'Returns accepted within 30 days with receipt.'
            },
            formatting: {
              font_size: 'medium',
              alignment: 'center',
              line_spacing: 1,
              margin: 2
            }
          },
          {
            id: 'template_2',
            name: 'Kitchen Order Ticket',
            type: 'sale',
            header: {
              logo: false,
              business_name: 'Kitchen Order',
              address: [],
              phone: '',
              email: ''
            },
            body: {
              show_customer_info: false,
              show_cashier: false,
              show_table_number: true,
              item_details: 'full',
              show_modifiers: true
            },
            footer: {
              tax_breakdown: false,
              payment_details: false,
              qr_code: false,
              barcode: false,
              thank_you_message: '',
              return_policy: ''
            },
            formatting: {
              font_size: 'large',
              alignment: 'left',
              line_spacing: 2,
              margin: 1
            }
          }
        ];

        setPrinters(mockPrinters);
        setTemplates(mockTemplates);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPrinterData();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadPrinterData, 30000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getStatusColor = (status: PrinterDevice['status']) => {
    switch (status) {
      case 'connected': return 'bg-green-100 text-green-800';
      case 'disconnected': return 'bg-red-100 text-red-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'printing': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: PrinterDevice['status']) => {
    switch (status) {
      case 'connected': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'disconnected': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'printing': return <Printer className="h-4 w-4 text-blue-500 animate-pulse" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getConnectionIcon = (type: PrinterDevice['connection_type']) => {
    switch (type) {
      case 'usb': return <Usb className="h-4 w-4 text-purple-500" />;
      case 'wifi': return <Wifi className="h-4 w-4 text-blue-500" />;
      case 'bluetooth': return <Bluetooth className="h-4 w-4 text-blue-400" />;
      case 'ethernet': return <Wifi className="h-4 w-4 text-green-500" />;
      default: return <Settings className="h-4 w-4 text-gray-500" />;
    }
  };

  const testPrint = async (printerId: string) => {
    try {
      console.log(`🖨️ Testing printer ${printerId}...`);
      
      const response = await apiCall(`/api/hardware/printers/${printerId}/test`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log('✅ Test print successful');
        alert('Test print sent successfully!');
      }
    } catch (error) {
      console.error('❌ Error testing printer:', error);
      alert('Failed to send test print. Please check printer connection.');
    }
  };

  const printReceipt = async (printerId: string, templateId: string, orderData: any) => {
    try {
      console.log(`🖨️ Printing receipt with template ${templateId}...`);
      
      const response = await apiCall(`/api/hardware/printers/${printerId}/print`, {
        method: 'POST',
        body: JSON.stringify({
          template_id: templateId,
          order_data: orderData
        })
      });
      
      if (response.ok) {
        console.log('✅ Receipt printed successfully');
        return true;
      }
    } catch (error) {
      console.error('❌ Error printing receipt:', error);
      return false;
    }
  };

  const getPrinterStats = () => {
    const connectedPrinters = printers.filter(p => p.status === 'connected').length;
    const totalQueue = printers.reduce((sum, p) => sum + p.print_queue, 0);
    const errorPrinters = printers.filter(p => p.status === 'error' || p.status === 'disconnected').length;
    
    return { connectedPrinters, totalQueue, errorPrinters };
  };

  const stats = getPrinterStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading receipt printers...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Receipt Printer Manager</h2>
            <p className="text-sm text-gray-500">USB, Bluetooth, Wi-Fi printer support</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddPrinterModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Printer</span>
            </button>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Printers"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-3 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Connected Printers</p>
                <p className="text-2xl font-bold text-gray-900">{stats.connectedPrinters}/{printers.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Print Queue</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalQueue}</p>
              </div>
              <Printer className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Error Printers</p>
                <p className="text-2xl font-bold text-red-600">{stats.errorPrinters}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'printers', label: 'Printers', icon: Printer },
            { id: 'templates', label: 'Receipt Templates', icon: Settings },
            { id: 'test', label: 'Test Print', icon: CheckCircle }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'printers' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {printers.map((printer) => (
              <div
                key={printer.id}
                onClick={() => setSelectedPrinter(printer)}
                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{printer.name}</h3>
                    <p className="text-sm text-gray-600">{printer.brand.toUpperCase()} {printer.model}</p>
                    <p className="text-xs text-gray-500">{printer.paper_width} paper</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(printer.status)}
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(printer.status)}`}>
                      {printer.status.toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Connection</p>
                    <div className="flex items-center space-x-1">
                      {getConnectionIcon(printer.connection_type)}
                      <span className="text-sm font-medium capitalize">{printer.connection_type}</span>
                    </div>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Queue</p>
                    <p className="text-sm font-medium">{printer.print_queue} jobs</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Auto Print</p>
                    <p className="text-sm font-medium">{printer.settings.auto_print ? 'Enabled' : 'Disabled'}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Last Print</p>
                    <p className="text-sm font-medium">{new Date(printer.last_print).toLocaleTimeString()}</p>
                  </div>
                </div>

                {printer.error_message && (
                  <div className="bg-red-50 border border-red-200 rounded p-2">
                    <p className="text-xs text-red-800">{printer.error_message}</p>
                  </div>
                )}

                <div className="flex space-x-2 mt-3">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      testPrint(printer.id);
                    }}
                    disabled={printer.status !== 'connected'}
                    className="flex-1 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-300 text-white text-xs py-2 px-3 rounded transition-colors"
                  >
                    Test Print
                  </button>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedPrinter(printer);
                    }}
                    className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 text-xs py-2 px-3 rounded transition-colors"
                  >
                    Settings
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'templates' && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {templates.map((template) => (
              <div key={template.id} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{template.name}</h3>
                    <p className="text-sm text-gray-600 capitalize">{template.type} receipt</p>
                  </div>
                  <button className="text-blue-600 hover:text-blue-800 text-sm">
                    Edit
                  </button>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-500">Logo:</span>
                    <span className="text-gray-900">{template.header.logo ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Tax Breakdown:</span>
                    <span className="text-gray-900">{template.footer.tax_breakdown ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">QR Code:</span>
                    <span className="text-gray-900">{template.footer.qr_code ? 'Yes' : 'No'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-500">Font Size:</span>
                    <span className="text-gray-900 capitalize">{template.formatting.font_size}</span>
                  </div>
                </div>

                <div className="mt-3 pt-3 border-t border-gray-200">
                  <button className="w-full bg-blue-600 hover:bg-blue-700 text-white text-sm py-2 px-3 rounded transition-colors">
                    Preview Template
                  </button>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'test' && (
          <div className="max-w-md mx-auto">
            <div className="bg-white border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Test Print</h3>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Select Printer</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Choose a printer...</option>
                    {printers.filter(p => p.status === 'connected').map((printer) => (
                      <option key={printer.id} value={printer.id}>
                        {printer.name} ({printer.connection_type})
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">Select Template</label>
                  <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                    <option value="">Choose a template...</option>
                    {templates.map((template) => (
                      <option key={template.id} value={template.id}>
                        {template.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="space-y-2">
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                    <span className="ml-2 text-sm text-gray-700">Include logo</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                    <span className="ml-2 text-sm text-gray-700">Include QR code</span>
                  </label>
                  <label className="flex items-center">
                    <input type="checkbox" className="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
                    <span className="ml-2 text-sm text-gray-700">Auto cut paper</span>
                  </label>
                </div>

                <button className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md transition-colors">
                  Send Test Print
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Printer Details Modal */}
      {selectedPrinter && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedPrinter.name}</h3>
                <p className="text-gray-600">{selectedPrinter.brand.toUpperCase()} {selectedPrinter.model}</p>
              </div>
              <button
                onClick={() => setSelectedPrinter(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedPrinter.status)}
                  <span className={`text-sm px-2 py-1 rounded-full font-medium ${getStatusColor(selectedPrinter.status)}`}>
                    {selectedPrinter.status.toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Connection</p>
                <div className="flex items-center space-x-2">
                  {getConnectionIcon(selectedPrinter.connection_type)}
                  <span className="text-sm font-medium capitalize">{selectedPrinter.connection_type}</span>
                </div>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-2">Capabilities</p>
              <div className="grid grid-cols-2 gap-2">
                {Object.entries(selectedPrinter.capabilities).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-sm">
                    <span className="text-gray-600 capitalize">{key.replace('_', ' ')}</span>
                    <span className={value ? 'text-green-600' : 'text-red-600'}>
                      {value ? 'Yes' : 'No'}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            <button
              onClick={() => setSelectedPrinter(null)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Add Printer Modal */}
      {showAddPrinterModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Printer</h3>
            <p className="text-gray-600 mb-4">Printer setup wizard coming soon...</p>
            <button
              onClick={() => setShowAddPrinterModal(false)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default ReceiptPrinterManager;
