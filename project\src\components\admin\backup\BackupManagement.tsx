import React, { useState, useEffect } from 'react';
import { 
  Database, 
  HardDrive, 
  Shield, 
  Clock, 
  Play, 
  Pause, 
  Download,
  Upload,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Settings,
  Calendar,
  FileText,
  Archive
} from 'lucide-react';

interface BackupJob {
  id: number;
  job_name: string;
  backup_type: 'full' | 'incremental' | 'differential';
  target_type: 'database' | 'files' | 'configuration' | 'full_system';
  schedule_cron: string;
  retention_days: number;
  compression_enabled: boolean;
  encryption_enabled: boolean;
  storage_location: string;
  is_active: boolean;
  last_run_at?: string;
  next_run_at?: string;
  created_at: string;
}

interface BackupExecution {
  id: number;
  job_id: number;
  job_name: string;
  execution_status: 'running' | 'completed' | 'failed' | 'cancelled';
  started_at: string;
  completed_at?: string;
  duration_seconds?: number;
  backup_size_bytes?: number;
  compressed_size_bytes?: number;
  file_count?: number;
  backup_file_path?: string;
  error_message?: string;
}

interface DisasterRecoveryPlan {
  id: number;
  plan_name: string;
  disaster_type: string;
  priority_level: number;
  rto_minutes: number; // Recovery Time Objective
  rpo_minutes: number; // Recovery Point Objective
  recovery_steps: any[];
  last_tested_at?: string;
  test_results?: string;
  is_active: boolean;
}

export function BackupManagement() {
  const [backupJobs, setBackupJobs] = useState<BackupJob[]>([]);
  const [executions, setExecutions] = useState<BackupExecution[]>([]);
  const [drPlans, setDrPlans] = useState<DisasterRecoveryPlan[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'jobs' | 'executions' | 'recovery'>('jobs');

  useEffect(() => {
    fetchBackupData();
  }, []);

  const fetchBackupData = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock backup jobs
      const mockJobs: BackupJob[] = [
        {
          id: 1,
          job_name: 'Daily Database Backup',
          backup_type: 'full',
          target_type: 'database',
          schedule_cron: '0 2 * * *',
          retention_days: 30,
          compression_enabled: true,
          encryption_enabled: true,
          storage_location: '/backups/database/',
          is_active: true,
          last_run_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          next_run_at: new Date(Date.now() + 18 * 60 * 60 * 1000).toISOString(),
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 2,
          job_name: 'Weekly Full System Backup',
          backup_type: 'full',
          target_type: 'full_system',
          schedule_cron: '0 1 * * 0',
          retention_days: 90,
          compression_enabled: true,
          encryption_enabled: true,
          storage_location: '/backups/system/',
          is_active: true,
          last_run_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          next_run_at: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000).toISOString(),
          created_at: '2024-01-01T00:00:00Z'
        },
        {
          id: 3,
          job_name: 'Hourly Incremental Backup',
          backup_type: 'incremental',
          target_type: 'database',
          schedule_cron: '0 * * * *',
          retention_days: 7,
          compression_enabled: true,
          encryption_enabled: false,
          storage_location: '/backups/incremental/',
          is_active: true,
          last_run_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          next_run_at: new Date(Date.now() + 1 * 60 * 60 * 1000).toISOString(),
          created_at: '2024-01-01T00:00:00Z'
        }
      ];

      // Mock executions
      const mockExecutions: BackupExecution[] = [
        {
          id: 1,
          job_id: 1,
          job_name: 'Daily Database Backup',
          execution_status: 'completed',
          started_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 6 * 60 * 60 * 1000 + 15 * 60 * 1000).toISOString(),
          duration_seconds: 900,
          backup_size_bytes: 2147483648, // 2GB
          compressed_size_bytes: 536870912, // 512MB
          file_count: 1,
          backup_file_path: '/backups/database/db_backup_2024_12_05.sql.gz'
        },
        {
          id: 2,
          job_id: 3,
          job_name: 'Hourly Incremental Backup',
          execution_status: 'completed',
          started_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 1 * 60 * 60 * 1000 + 2 * 60 * 1000).toISOString(),
          duration_seconds: 120,
          backup_size_bytes: 104857600, // 100MB
          compressed_size_bytes: 52428800, // 50MB
          file_count: 1,
          backup_file_path: '/backups/incremental/inc_backup_2024_12_05_14.sql.gz'
        },
        {
          id: 3,
          job_id: 2,
          job_name: 'Weekly Full System Backup',
          execution_status: 'failed',
          started_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
          completed_at: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 30 * 60 * 1000).toISOString(),
          duration_seconds: 1800,
          error_message: 'Insufficient disk space in backup location'
        }
      ];

      // Mock disaster recovery plans
      const mockDrPlans: DisasterRecoveryPlan[] = [
        {
          id: 1,
          plan_name: 'Database Failure Recovery',
          disaster_type: 'hardware_failure',
          priority_level: 1,
          rto_minutes: 60,
          rpo_minutes: 15,
          recovery_steps: [
            { step: 1, action: 'Assess damage and identify failure cause', duration: 10 },
            { step: 2, action: 'Restore database from latest backup', duration: 30 },
            { step: 3, action: 'Verify data integrity and consistency', duration: 15 },
            { step: 4, action: 'Resume normal operations', duration: 5 }
          ],
          last_tested_at: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
          test_results: 'Successful recovery in 45 minutes',
          is_active: true
        },
        {
          id: 2,
          plan_name: 'Security Breach Response',
          disaster_type: 'security_breach',
          priority_level: 1,
          rto_minutes: 30,
          rpo_minutes: 5,
          recovery_steps: [
            { step: 1, action: 'Isolate affected systems', duration: 5 },
            { step: 2, action: 'Assess breach scope and impact', duration: 10 },
            { step: 3, action: 'Restore from clean backup', duration: 10 },
            { step: 4, action: 'Implement additional security measures', duration: 5 }
          ],
          last_tested_at: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000).toISOString(),
          test_results: 'Recovery completed within RTO',
          is_active: true
        }
      ];

      setBackupJobs(mockJobs);
      setExecutions(mockExecutions);
      setDrPlans(mockDrPlans);
    } catch (error) {
      console.error('Error fetching backup data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-700 bg-green-100';
      case 'running': return 'text-blue-700 bg-blue-100';
      case 'failed': return 'text-red-700 bg-red-100';
      case 'cancelled': return 'text-gray-700 bg-gray-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />;
      case 'running': return <Play className="h-4 w-4" />;
      case 'failed': return <XCircle className="h-4 w-4" />;
      case 'cancelled': return <Pause className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTargetTypeIcon = (type: string) => {
    switch (type) {
      case 'database': return <Database className="h-5 w-5" />;
      case 'files': return <FileText className="h-5 w-5" />;
      case 'configuration': return <Settings className="h-5 w-5" />;
      case 'full_system': return <HardDrive className="h-5 w-5" />;
      default: return <Archive className="h-5 w-5" />;
    }
  };

  const getPriorityColor = (level: number) => {
    switch (level) {
      case 1: return 'text-red-700 bg-red-100';
      case 2: return 'text-orange-700 bg-orange-100';
      case 3: return 'text-yellow-700 bg-yellow-100';
      case 4: return 'text-blue-700 bg-blue-100';
      case 5: return 'text-gray-700 bg-gray-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="bg-gray-200 h-32 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-96 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Backup & Disaster Recovery</h2>
          <p className="text-gray-600 mt-1">
            Manage automated backups and disaster recovery plans
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <Play className="h-4 w-4 mr-2" />
            Run Backup
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4 mr-2" />
            New Job
          </button>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Database className="h-8 w-8 text-blue-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Active Jobs</p>
              <p className="text-2xl font-semibold text-gray-900">
                {backupJobs.filter(job => job.is_active).length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <CheckCircle className="h-8 w-8 text-green-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Successful Backups</p>
              <p className="text-2xl font-semibold text-gray-900">
                {executions.filter(exec => exec.execution_status === 'completed').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <XCircle className="h-8 w-8 text-red-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">Failed Backups</p>
              <p className="text-2xl font-semibold text-gray-900">
                {executions.filter(exec => exec.execution_status === 'failed').length}
              </p>
            </div>
          </div>
        </div>
        
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <div className="flex items-center">
            <Shield className="h-8 w-8 text-purple-600" />
            <div className="ml-3">
              <p className="text-sm font-medium text-gray-500">DR Plans</p>
              <p className="text-2xl font-semibold text-gray-900">{drPlans.length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('jobs')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'jobs'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Database className="h-4 w-4 inline mr-2" />
            Backup Jobs ({backupJobs.length})
          </button>
          <button
            onClick={() => setActiveTab('executions')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'executions'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Clock className="h-4 w-4 inline mr-2" />
            Execution History ({executions.length})
          </button>
          <button
            onClick={() => setActiveTab('recovery')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'recovery'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Shield className="h-4 w-4 inline mr-2" />
            Disaster Recovery ({drPlans.length})
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'jobs' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {backupJobs.map(job => (
            <div key={job.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-600">
                    {getTargetTypeIcon(job.target_type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{job.job_name}</h3>
                    <p className="text-sm text-gray-500 capitalize">
                      {job.backup_type} {job.target_type.replace('_', ' ')} backup
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    job.is_active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                  }`}>
                    {job.is_active ? 'Active' : 'Inactive'}
                  </span>
                  <button className="p-2 text-gray-400 hover:text-gray-600 rounded-md hover:bg-gray-100">
                    <Settings className="h-4 w-4" />
                  </button>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Schedule</span>
                  <span className="font-medium">{job.schedule_cron}</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Retention</span>
                  <span className="font-medium">{job.retention_days} days</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Last Run</span>
                  <span className="font-medium">
                    {job.last_run_at ? new Date(job.last_run_at).toLocaleString() : 'Never'}
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Next Run</span>
                  <span className="font-medium">
                    {job.next_run_at ? new Date(job.next_run_at).toLocaleString() : 'Not scheduled'}
                  </span>
                </div>

                <div className="flex items-center space-x-4 text-sm">
                  {job.compression_enabled && (
                    <span className="flex items-center text-green-600">
                      <Archive className="h-4 w-4 mr-1" />
                      Compressed
                    </span>
                  )}
                  {job.encryption_enabled && (
                    <span className="flex items-center text-blue-600">
                      <Shield className="h-4 w-4 mr-1" />
                      Encrypted
                    </span>
                  )}
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <button className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                    <Play className="h-4 w-4 inline mr-1" />
                    Run Now
                  </button>
                  <button className="flex-1 px-3 py-2 text-sm bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <Download className="h-4 w-4 inline mr-1" />
                    Download
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'executions' && (
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Job
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Duration
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Size
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Started
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {executions.map(execution => (
                <tr key={execution.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">{execution.job_name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(execution.execution_status)}`}>
                      {getStatusIcon(execution.execution_status)}
                      <span className="ml-1 capitalize">{execution.execution_status}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {execution.duration_seconds ? formatDuration(execution.duration_seconds) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {execution.backup_size_bytes ? (
                      <div>
                        <div>{formatFileSize(execution.backup_size_bytes)}</div>
                        {execution.compressed_size_bytes && (
                          <div className="text-xs text-gray-500">
                            Compressed: {formatFileSize(execution.compressed_size_bytes)}
                          </div>
                        )}
                      </div>
                    ) : '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(execution.started_at).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    {execution.execution_status === 'completed' && execution.backup_file_path && (
                      <button className="text-blue-600 hover:text-blue-900">
                        <Download className="h-4 w-4" />
                      </button>
                    )}
                    {execution.execution_status === 'failed' && execution.error_message && (
                      <button className="text-red-600 hover:text-red-900" title={execution.error_message}>
                        <AlertTriangle className="h-4 w-4" />
                      </button>
                    )}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}

      {activeTab === 'recovery' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {drPlans.map(plan => (
            <div key={plan.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h3 className="font-semibold text-gray-900">{plan.plan_name}</h3>
                  <p className="text-sm text-gray-500 capitalize">
                    {plan.disaster_type.replace('_', ' ')} response
                  </p>
                </div>
                <span className={`px-2 py-1 text-xs rounded-full font-medium ${getPriorityColor(plan.priority_level)}`}>
                  Priority {plan.priority_level}
                </span>
              </div>

              <div className="space-y-3 mb-4">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">RTO (Recovery Time)</span>
                  <span className="font-medium">{plan.rto_minutes} minutes</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">RPO (Recovery Point)</span>
                  <span className="font-medium">{plan.rpo_minutes} minutes</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Recovery Steps</span>
                  <span className="font-medium">{plan.recovery_steps.length} steps</span>
                </div>
                
                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Last Tested</span>
                  <span className="font-medium">
                    {plan.last_tested_at ? new Date(plan.last_tested_at).toLocaleDateString() : 'Never'}
                  </span>
                </div>
              </div>

              {plan.test_results && (
                <div className="mb-4 p-3 bg-green-50 border border-green-200 rounded-md">
                  <p className="text-sm text-green-800">{plan.test_results}</p>
                </div>
              )}

              <div className="flex space-x-2">
                <button className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                  <Play className="h-4 w-4 inline mr-1" />
                  Test Plan
                </button>
                <button className="flex-1 px-3 py-2 text-sm bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                  <FileText className="h-4 w-4 inline mr-1" />
                  View Steps
                </button>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
