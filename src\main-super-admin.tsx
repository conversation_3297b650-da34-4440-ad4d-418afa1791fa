import React from 'react';
import ReactDOM from 'react-dom/client';
import EnterpriseSecurityApp from './components/EnterpriseSecurityApp';
import './index.css';

// Enhanced security logging
console.log('🔒 RestroFlow Enterprise Security Center - Initializing...');
console.log('🛡️ Security Level: MAXIMUM');
console.log('🔐 Access Control: SUPER ADMIN ONLY');

// Security validation
const validateSecurityAccess = (): boolean => {
  const allowedHosts = ['localhost:5173', 'localhost:5174', '127.0.0.1:5173', '127.0.0.1:5174'];
  const currentHost = window.location.host;
  
  if (!allowedHosts.includes(currentHost)) {
    console.error('🚨 SECURITY VIOLATION: Unauthorized host access detected:', currentHost);
    return false;
  }
  
  // Check for required security headers
  const hasSecureContext = window.isSecureContext || window.location.protocol === 'http:'; // Allow HTTP for localhost
  if (!hasSecureContext && window.location.hostname !== 'localhost') {
    console.error('🚨 SECURITY VIOLATION: Insecure context detected');
    return false;
  }
  
  return true;
};

// Enhanced error boundary for security
class SecurityErrorBoundary extends React.Component<
  { children: React.ReactNode },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    console.error('🚨 SECURITY ERROR BOUNDARY TRIGGERED:', error);
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('🚨 CRITICAL SECURITY ERROR:', error, errorInfo);
    
    // Log security incident
    if (typeof window !== 'undefined') {
      (window as any).securityCenter = {
        ...(window as any).securityCenter,
        lastIncident: {
          type: 'APPLICATION_ERROR',
          timestamp: new Date().toISOString(),
          error: error.message,
          stack: error.stack
        },
        threatLevel: 'high'
      };
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-screen bg-gradient-to-br from-red-600 to-red-900 flex items-center justify-center text-white">
          <div className="text-center max-w-md mx-auto p-8">
            <div className="text-6xl mb-6">🚨</div>
            <h1 className="text-3xl font-bold mb-4">SECURITY ERROR</h1>
            <p className="text-red-100 mb-6">
              A critical security error has occurred. The system has been locked down for protection.
            </p>
            <div className="bg-red-800/50 p-4 rounded-lg mb-6">
              <p className="text-sm font-mono text-red-200">
                Error: {this.state.error?.message || 'Unknown security violation'}
              </p>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="bg-white text-red-600 px-6 py-3 rounded-lg font-bold hover:bg-red-50 transition-colors"
            >
              🔄 Restart Security System
            </button>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Initialize security monitoring
const initializeSecurityMonitoring = () => {
  // Global security center
  (window as any).securityCenter = {
    threatLevel: 'medium',
    lastCheck: new Date().toISOString(),
    activeThreats: [],
    securityEvents: [],
    systemStatus: 'initializing'
  };

  // Security event logging
  const logSecurityEvent = (event: string, level: 'info' | 'warning' | 'critical' = 'info') => {
    const securityEvent = {
      timestamp: new Date().toISOString(),
      event,
      level,
      userAgent: navigator.userAgent,
      url: window.location.href
    };

    (window as any).securityCenter.securityEvents.push(securityEvent);
    console.log(`🔒 SECURITY EVENT [${level.toUpperCase()}]:`, event);

    // Keep only last 100 events
    if ((window as any).securityCenter.securityEvents.length > 100) {
      (window as any).securityCenter.securityEvents = (window as any).securityCenter.securityEvents.slice(-100);
    }
  };

  // Monitor for security violations
  window.addEventListener('error', (event) => {
    logSecurityEvent(`JavaScript Error: ${event.message}`, 'warning');
  });

  window.addEventListener('unhandledrejection', (event) => {
    logSecurityEvent(`Unhandled Promise Rejection: ${event.reason}`, 'warning');
  });

  // Monitor for suspicious activity
  let clickCount = 0;
  let lastClickTime = 0;
  
  document.addEventListener('click', () => {
    const now = Date.now();
    if (now - lastClickTime < 100) {
      clickCount++;
      if (clickCount > 10) {
        logSecurityEvent('Suspicious rapid clicking detected', 'warning');
        (window as any).securityCenter.threatLevel = 'high';
      }
    } else {
      clickCount = 0;
    }
    lastClickTime = now;
  });

  logSecurityEvent('Security monitoring initialized', 'info');
};

// Main application initialization
const initializeApp = async () => {
  try {
    // Validate security access
    if (!validateSecurityAccess()) {
      throw new Error('Security validation failed');
    }

    // Initialize security monitoring
    initializeSecurityMonitoring();

    // Update security status
    (window as any).securityCenter.systemStatus = 'secure';
    (window as any).securityCenter.threatLevel = 'low';

    console.log('✅ Security validation passed');
    console.log('🚀 Initializing Enterprise Security App...');

    // Render the application
    const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
    
    root.render(
      <React.StrictMode>
        <SecurityErrorBoundary>
          <EnterpriseSecurityApp />
        </SecurityErrorBoundary>
      </React.StrictMode>
    );

    console.log('✅ Enterprise Security App initialized successfully');

  } catch (error) {
    console.error('🚨 CRITICAL INITIALIZATION ERROR:', error);
    
    // Show security error
    const root = document.getElementById('root');
    if (root) {
      root.innerHTML = `
        <div class="min-h-screen bg-gradient-to-br from-red-600 to-red-900 flex items-center justify-center text-white">
          <div class="text-center max-w-md mx-auto p-8">
            <div class="text-6xl mb-6">🚨</div>
            <h1 class="text-3xl font-bold mb-4">INITIALIZATION FAILED</h1>
            <p class="text-red-100 mb-6">
              The security system could not be initialized. Access has been denied for security reasons.
            </p>
            <div class="bg-red-800/50 p-4 rounded-lg mb-6">
              <p class="text-sm font-mono text-red-200">
                Error: ${error instanceof Error ? error.message : 'Unknown error'}
              </p>
            </div>
            <button
              onclick="window.location.reload()"
              class="bg-white text-red-600 px-6 py-3 rounded-lg font-bold hover:bg-red-50 transition-colors"
            >
              🔄 Retry Initialization
            </button>
          </div>
        </div>
      `;
    }
  }
};

// Start the application
initializeApp();
