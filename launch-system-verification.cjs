#!/usr/bin/env node

/**
 * RESTROFLOW System Launch Verification
 * Comprehensive system check before launch
 */

const http = require('http');
const fs = require('fs');
const path = require('path');

console.log('🚀 RESTROFLOW SYSTEM LAUNCH VERIFICATION');
console.log('========================================');
console.log(`⏰ Verification started: ${new Date().toLocaleString()}`);

async function verifySystemLaunch() {
  const verificationResults = {
    systemHealth: { score: 0, total: 5 },
    security: { score: 0, total: 4 },
    features: { score: 0, total: 6 },
    deployment: { score: 0, total: 5 },
    documentation: { score: 0, total: 4 }
  };

  // System Health Verification
  console.log('\n🏥 SYSTEM HEALTH VERIFICATION');
  console.log('=============================');

  try {
    // Check frontend accessibility
    const frontendResponse = await makeRequest('http://localhost:5173');
    if (frontendResponse.status === 200) {
      console.log('✅ Main Frontend (5173): ACCESSIBLE');
      verificationResults.systemHealth.score++;
    } else {
      console.log('❌ Main Frontend (5173): NOT ACCESSIBLE');
    }
  } catch (error) {
    console.log('❌ Main Frontend (5173): ERROR -', error.message);
  }

  try {
    // Check security system
    const securityResponse = await makeRequest('http://localhost:5174');
    if (securityResponse.status === 200) {
      console.log('✅ Security System (5174): ACCESSIBLE');
      verificationResults.systemHealth.score++;
    } else {
      console.log('⚠️ Security System (5174): NOT RUNNING (optional)');
    }
  } catch (error) {
    console.log('⚠️ Security System (5174): NOT RUNNING (use npm run super-admin)');
  }

  try {
    // Check backend health
    const backendResponse = await makeRequest('http://localhost:4000/api/health');
    if (backendResponse.status === 200) {
      console.log('✅ Backend API (4000): HEALTHY');
      verificationResults.systemHealth.score++;
    } else {
      console.log('❌ Backend API (4000): UNHEALTHY');
    }
  } catch (error) {
    console.log('❌ Backend API (4000): ERROR -', error.message);
  }

  try {
    // Check database connectivity
    const dbResponse = await makeRequest('http://localhost:4000/api/health/database');
    if (dbResponse.status === 200) {
      console.log('✅ Database: CONNECTED');
      verificationResults.systemHealth.score++;
    } else {
      console.log('❌ Database: DISCONNECTED');
    }
  } catch (error) {
    console.log('❌ Database: ERROR -', error.message);
  }

  try {
    // Check authentication
    const authResponse = await makeRequest('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ pin: '123456' })
    });
    if (authResponse.status === 200 && authResponse.data.token) {
      console.log('✅ Authentication: WORKING');
      verificationResults.systemHealth.score++;
    } else {
      console.log('❌ Authentication: FAILED');
    }
  } catch (error) {
    console.log('❌ Authentication: ERROR -', error.message);
  }

  // Security Verification
  console.log('\n🛡️ SECURITY VERIFICATION');
  console.log('========================');

  // Check security files
  const securityFiles = [
    'src/components/EnterpriseSecurityApp.tsx',
    'src/components/AdvancedSecurityDashboard.tsx',
    'nginx-security.conf',
    'Dockerfile.security'
  ];

  securityFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}: PRESENT`);
      verificationResults.security.score++;
    } else {
      console.log(`❌ ${file}: MISSING`);
    }
  });

  // Features Verification
  console.log('\n🎯 FEATURES VERIFICATION');
  console.log('========================');

  const featureComponents = [
    'src/components/SimplePOSSystem.tsx',
    'src/components/SimpleSuperAdminDashboard.tsx',
    'src/components/SystemDebugger.tsx',
    'src/components/POSInterfaceDemo.tsx',
    'src/components/EndpointDashboard.tsx',
    'src/components/OriginalInterfaceSwitcher.tsx'
  ];

  featureComponents.forEach(component => {
    if (fs.existsSync(component)) {
      console.log(`✅ ${path.basename(component)}: AVAILABLE`);
      verificationResults.features.score++;
    } else {
      console.log(`❌ ${path.basename(component)}: MISSING`);
    }
  });

  // Deployment Verification
  console.log('\n📦 DEPLOYMENT VERIFICATION');
  console.log('==========================');

  const deploymentFiles = [
    'package.json',
    'vite.config.ts',
    'vite.super-admin.config.ts',
    'docker-compose.restroflow.yml',
    'scripts/build-production.js'
  ];

  deploymentFiles.forEach(file => {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file}: PRESENT`);
      verificationResults.deployment.score++;
    } else {
      console.log(`❌ ${file}: MISSING`);
    }
  });

  // Documentation Verification
  console.log('\n📚 DOCUMENTATION VERIFICATION');
  console.log('=============================');

  const keyDocs = [
    'SYSTEM_COMPLETION_REPORT.md',
    'FINAL_DEPLOYMENT_GUIDE.md',
    'ENTERPRISE_SECURITY_SYSTEM.md',
    'FINAL_SYSTEM_STATUS.md'
  ];

  keyDocs.forEach(doc => {
    if (fs.existsSync(doc)) {
      console.log(`✅ ${doc}: AVAILABLE`);
      verificationResults.documentation.score++;
    } else {
      console.log(`❌ ${doc}: MISSING`);
    }
  });

  // Calculate overall score
  const totalScore = Object.values(verificationResults).reduce((sum, category) => sum + category.score, 0);
  const totalPossible = Object.values(verificationResults).reduce((sum, category) => sum + category.total, 0);
  const overallPercentage = Math.round((totalScore / totalPossible) * 100);

  // Results Summary
  console.log('\n🎉 LAUNCH VERIFICATION RESULTS');
  console.log('==============================');

  Object.entries(verificationResults).forEach(([category, result]) => {
    const percentage = Math.round((result.score / result.total) * 100);
    const status = percentage >= 80 ? '✅' : percentage >= 60 ? '⚠️' : '❌';
    console.log(`${status} ${category.toUpperCase()}: ${result.score}/${result.total} (${percentage}%)`);
  });

  console.log(`\n📊 OVERALL SYSTEM READINESS: ${overallPercentage}%`);

  if (overallPercentage >= 90) {
    console.log('🎉 EXCELLENT: System is ready for immediate launch!');
  } else if (overallPercentage >= 75) {
    console.log('✅ GOOD: System is mostly ready with minor issues');
  } else if (overallPercentage >= 60) {
    console.log('⚠️ FAIR: System needs some improvements');
  } else {
    console.log('❌ POOR: System requires significant work');
  }

  // Launch Instructions
  console.log('\n🚀 LAUNCH INSTRUCTIONS');
  console.log('======================');
  
  if (verificationResults.systemHealth.score >= 3) {
    console.log('✅ SYSTEM READY FOR LAUNCH');
    console.log('\n📋 Quick Launch Steps:');
    console.log('1. Backend: cd backend && npm start');
    console.log('2. Frontend: npm start');
    console.log('3. Access: http://localhost:5173');
    console.log('4. Login: PIN 123456 (Super Admin)');
    console.log('5. Optional Security: npm run super-admin');
  } else {
    console.log('⚠️ SYSTEM NEEDS SETUP');
    console.log('\n📋 Setup Required:');
    console.log('1. Install dependencies: npm install');
    console.log('2. Setup backend: cd backend && npm install');
    console.log('3. Start backend: npm start (in backend folder)');
    console.log('4. Start frontend: npm start (in root folder)');
  }

  console.log('\n🎯 ACCESS METHODS AVAILABLE');
  console.log('===========================');
  console.log('🔑 PIN 123456: Super Admin Dashboard');
  console.log('🔑 PIN 111222: Employee POS');
  console.log('🔑 PIN 567890: Manager POS');
  console.log('🔑 PIN 999999: Original Interfaces');
  console.log('🔑 PIN 000000: Debug Mode');

  console.log('\n✨ RESTROFLOW SYSTEM LAUNCH VERIFICATION COMPLETE!');
  console.log(`⏰ Verification completed: ${new Date().toLocaleString()}`);

  return overallPercentage;
}

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const client = require('http');
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || 80,
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {},
      timeout: 5000
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    req.on('timeout', () => reject(new Error('Request timeout')));
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

// Run verification
verifySystemLaunch().then(score => {
  process.exit(score >= 75 ? 0 : 1);
}).catch(error => {
  console.error('\n❌ Verification failed:', error);
  process.exit(1);
});
