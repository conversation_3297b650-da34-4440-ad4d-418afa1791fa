import React, { useState, useEffect, useCallback, useMemo } from 'react';
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Receipt,
  CreditCard,
  Users,
  Package,
  Home,
  Clock,
  Edit3,
  Copy,
  Save,
  AlertCircle,
  CheckCircle,
  Percent,
  DollarSign,
  Calculator
} from 'lucide-react';
import { Button, Card, Badge, Flex, Text, Heading, Input } from '../ui/DesignSystem';

interface OrderItem {
  id: string;
  productId: string;
  name: string;
  price: number;
  quantity: number;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
  specialInstructions?: string;
  total: number;
}

interface ProductVariant {
  id: string;
  name: string;
  priceAdjustment: number;
}

interface ProductModifier {
  id: string;
  name: string;
  price: number;
  category: string;
  required: boolean;
}

interface Order {
  id: string;
  items: OrderItem[];
  subtotal: number;
  tax: number;
  tip: number;
  discount: number;
  total: number;
  tableNumber?: string;
  customerName?: string;
  orderType: 'dine-in' | 'takeout' | 'delivery';
  status: 'draft' | 'confirmed' | 'preparing' | 'ready' | 'completed' | 'cancelled';
  timestamp: Date;
  paymentStatus: 'pending' | 'processing' | 'paid' | 'failed' | 'refunded';
  specialInstructions?: string;
}

interface EnhancedOrderPanelProps {
  order: Order;
  onUpdateOrder: (order: Order) => void;
  onProcessPayment: () => void;
  onClearOrder: () => void;
  onSaveOrder: () => void;
  isDarkMode?: boolean;
  isProcessing?: boolean;
}

const EnhancedOrderPanel: React.FC<EnhancedOrderPanelProps> = ({
  order,
  onUpdateOrder,
  onProcessPayment,
  onClearOrder,
  onSaveOrder,
  isDarkMode = false,
  isProcessing = false
}) => {
  const [editingItem, setEditingItem] = useState<string | null>(null);
  const [discountType, setDiscountType] = useState<'amount' | 'percentage'>('percentage');
  const [discountValue, setDiscountValue] = useState('');
  const [showDiscountInput, setShowDiscountInput] = useState(false);
  const [orderNotes, setOrderNotes] = useState(order.specialInstructions || '');

  // Calculate totals
  const calculations = useMemo(() => {
    const subtotal = order.items.reduce((sum, item) => sum + item.total, 0);
    const taxRate = 0.1; // 10% tax
    const tax = subtotal * taxRate;
    const total = subtotal + tax + order.tip - order.discount;
    
    return { subtotal, tax, total };
  }, [order.items, order.tip, order.discount]);

  // Update order when calculations change
  useEffect(() => {
    if (calculations.subtotal !== order.subtotal || 
        calculations.tax !== order.tax || 
        calculations.total !== order.total) {
      onUpdateOrder({
        ...order,
        subtotal: calculations.subtotal,
        tax: calculations.tax,
        total: calculations.total
      });
    }
  }, [calculations, order, onUpdateOrder]);

  // Handle item quantity update
  const updateItemQuantity = useCallback((itemId: string, newQuantity: number) => {
    if (newQuantity < 1) return;
    
    const updatedItems = order.items.map(item => 
      item.id === itemId 
        ? { ...item, quantity: newQuantity, total: item.price * newQuantity }
        : item
    );
    
    onUpdateOrder({ ...order, items: updatedItems });
  }, [order, onUpdateOrder]);

  // Handle item removal
  const removeItem = useCallback((itemId: string) => {
    const updatedItems = order.items.filter(item => item.id !== itemId);
    onUpdateOrder({ ...order, items: updatedItems });
  }, [order, onUpdateOrder]);

  // Handle order type change
  const updateOrderType = useCallback((orderType: 'dine-in' | 'takeout' | 'delivery') => {
    onUpdateOrder({ ...order, orderType });
  }, [order, onUpdateOrder]);

  // Handle discount application
  const applyDiscount = useCallback(() => {
    const value = parseFloat(discountValue);
    if (isNaN(value) || value < 0) return;

    let discountAmount = 0;
    if (discountType === 'percentage') {
      discountAmount = (calculations.subtotal * value) / 100;
    } else {
      discountAmount = value;
    }

    onUpdateOrder({ ...order, discount: discountAmount });
    setShowDiscountInput(false);
    setDiscountValue('');
  }, [discountValue, discountType, calculations.subtotal, order, onUpdateOrder]);

  // Handle special instructions update
  const updateOrderNotes = useCallback(() => {
    onUpdateOrder({ ...order, specialInstructions: orderNotes });
  }, [order, orderNotes, onUpdateOrder]);

  // Get order type icon
  const getOrderTypeIcon = (type: string) => {
    switch (type) {
      case 'dine-in': return <Users className="w-4 h-4" />;
      case 'takeout': return <Package className="w-4 h-4" />;
      case 'delivery': return <Home className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  return (
    <div className={`w-96 border-l flex flex-col transition-colors duration-300 ${
      isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
    }`}>
      {/* Order Header */}
      <div className={`p-4 border-b transition-colors duration-300 ${
        isDarkMode ? 'border-gray-700' : 'border-gray-200'
      }`}>
        <Flex justify="between" align="center" className="mb-4">
          <Heading level={3}>Current Order</Heading>
          <Flex gap="sm">
            <Badge variant={order.items.length > 0 ? 'success' : 'secondary'}>
              {order.items.length} items
            </Badge>
            {order.status !== 'draft' && (
              <Badge variant="info">{order.status}</Badge>
            )}
          </Flex>
        </Flex>

        {/* Order Type Selector */}
        <div className="grid grid-cols-3 gap-2">
          {(['dine-in', 'takeout', 'delivery'] as const).map((type) => (
            <Button
              key={type}
              variant={order.orderType === type ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => updateOrderType(type)}
              icon={() => getOrderTypeIcon(type)}
              className="text-xs"
            >
              {type.charAt(0).toUpperCase() + type.slice(1).replace('-', ' ')}
            </Button>
          ))}
        </div>

        {/* Table/Customer Info */}
        {order.orderType === 'dine-in' && (
          <div className="mt-3">
            <Input
              placeholder="Table number"
              value={order.tableNumber || ''}
              onChange={(value) => onUpdateOrder({ ...order, tableNumber: value })}
              fullWidth
            />
          </div>
        )}

        {(order.orderType === 'takeout' || order.orderType === 'delivery') && (
          <div className="mt-3">
            <Input
              placeholder="Customer name"
              value={order.customerName || ''}
              onChange={(value) => onUpdateOrder({ ...order, customerName: value })}
              fullWidth
            />
          </div>
        )}
      </div>

      {/* Order Items */}
      <div className="flex-1 overflow-y-auto p-4">
        {order.items.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <ShoppingCart className={`w-16 h-16 mb-4 ${
              isDarkMode ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <Text variant="body" color="muted" className="mb-2">
              No items in order
            </Text>
            <Text variant="caption" color="muted">
              Select products to add to order
            </Text>
          </div>
        ) : (
          <div className="space-y-3">
            {order.items.map((item) => (
              <Card key={item.id} variant="outlined" padding="sm">
                <Flex justify="between" align="start" className="mb-2">
                  <div className="flex-1">
                    <Text variant="body" className="font-medium">{item.name}</Text>
                    {item.variants && item.variants.length > 0 && (
                      <Text variant="caption" color="muted">
                        {item.variants.map(v => v.name).join(', ')}
                      </Text>
                    )}
                    {item.modifiers && item.modifiers.length > 0 && (
                      <Text variant="caption" color="muted">
                        + {item.modifiers.map(m => m.name).join(', ')}
                      </Text>
                    )}
                    {item.specialInstructions && (
                      <Text variant="caption" color="muted" className="italic">
                        Note: {item.specialInstructions}
                      </Text>
                    )}
                  </div>
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => removeItem(item.id)}
                    icon={Trash2}
                    className="text-red-500 hover:text-red-700"
                  />
                </Flex>
                
                <Flex justify="between" align="center">
                  <Flex gap="sm" align="center">
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => updateItemQuantity(item.id, item.quantity - 1)}
                      icon={Minus}
                      disabled={item.quantity <= 1}
                    />
                    <span className="w-8 text-center font-medium">{item.quantity}</span>
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => updateItemQuantity(item.id, item.quantity + 1)}
                      icon={Plus}
                    />
                  </Flex>
                  
                  <div className="text-right">
                    <Text variant="body" className="font-semibold">
                      ${item.total.toFixed(2)}
                    </Text>
                    <Text variant="caption" color="muted">
                      ${item.price.toFixed(2)} each
                    </Text>
                  </div>
                </Flex>
              </Card>
            ))}
          </div>
        )}
      </div>

      {/* Order Notes */}
      {order.items.length > 0 && (
        <div className={`p-4 border-t ${isDarkMode ? 'border-gray-700' : 'border-gray-200'}`}>
          <div className="mb-3">
            <Text variant="caption" className="font-medium mb-1 block">Special Instructions</Text>
            <textarea
              value={orderNotes}
              onChange={(e) => setOrderNotes(e.target.value)}
              onBlur={updateOrderNotes}
              placeholder="Add special instructions..."
              className={`w-full px-3 py-2 text-sm rounded-lg border resize-none ${
                isDarkMode 
                  ? 'bg-gray-700 border-gray-600 text-white placeholder-gray-400' 
                  : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
              }`}
              rows={2}
            />
          </div>
        </div>
      )}

      {/* Order Summary & Actions */}
      {order.items.length > 0 && (
        <div className={`border-t p-4 transition-colors duration-300 ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          {/* Order Totals */}
          <div className="space-y-2 mb-4">
            <Flex justify="between">
              <Text variant="body">Subtotal:</Text>
              <Text variant="body">${calculations.subtotal.toFixed(2)}</Text>
            </Flex>
            <Flex justify="between">
              <Text variant="body">Tax (10%):</Text>
              <Text variant="body">${calculations.tax.toFixed(2)}</Text>
            </Flex>
            
            {/* Discount Section */}
            <Flex justify="between" align="center">
              <Text variant="body">Discount:</Text>
              <Flex gap="sm" align="center">
                {order.discount > 0 ? (
                  <>
                    <Text variant="body" className="text-green-600">
                      -${order.discount.toFixed(2)}
                    </Text>
                    <Button
                      variant="ghost"
                      size="xs"
                      onClick={() => onUpdateOrder({ ...order, discount: 0 })}
                      icon={Trash2}
                    />
                  </>
                ) : (
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => setShowDiscountInput(true)}
                    icon={Percent}
                  >
                    Add
                  </Button>
                )}
              </Flex>
            </Flex>

            {/* Discount Input */}
            {showDiscountInput && (
              <Card variant="outlined" padding="sm">
                <Flex gap="sm" align="center">
                  <select
                    value={discountType}
                    onChange={(e) => setDiscountType(e.target.value as 'amount' | 'percentage')}
                    className={`px-2 py-1 text-xs rounded border ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white' 
                        : 'bg-white border-gray-300 text-gray-900'
                    }`}
                  >
                    <option value="percentage">%</option>
                    <option value="amount">$</option>
                  </select>
                  <Input
                    type="number"
                    placeholder="0"
                    value={discountValue}
                    onChange={setDiscountValue}
                    className="flex-1"
                  />
                  <Button variant="primary" size="xs" onClick={applyDiscount}>
                    Apply
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="xs" 
                    onClick={() => setShowDiscountInput(false)}
                  >
                    Cancel
                  </Button>
                </Flex>
              </Card>
            )}

            {order.tip > 0 && (
              <Flex justify="between">
                <Text variant="body">Tip:</Text>
                <Text variant="body">${order.tip.toFixed(2)}</Text>
              </Flex>
            )}
            
            <div className={`flex justify-between text-lg font-bold pt-2 border-t ${
              isDarkMode ? 'border-gray-600' : 'border-gray-200'
            }`}>
              <Text variant="body" className="font-bold">Total:</Text>
              <Text variant="body" className="font-bold">${calculations.total.toFixed(2)}</Text>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Flex gap="sm">
              <Button
                variant="ghost"
                size="sm"
                onClick={onSaveOrder}
                icon={Save}
                className="flex-1"
              >
                Save
              </Button>
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => {/* Handle duplicate order */}}
                icon={Copy}
                className="flex-1"
              >
                Copy
              </Button>
              
              <Button
                variant="danger"
                size="sm"
                onClick={onClearOrder}
                icon={Trash2}
                className="flex-1"
              >
                Clear
              </Button>
            </Flex>
            
            <Button
              variant="primary"
              size="lg"
              onClick={onProcessPayment}
              icon={CreditCard}
              loading={isProcessing}
              disabled={isProcessing}
              fullWidth
            >
              {isProcessing ? 'Processing...' : 'Process Payment'}
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedOrderPanel;
