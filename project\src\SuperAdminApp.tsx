import React, { useState, useEffect } from 'react';
import { EnhancedAppProvider, useEnhancedAppContext } from './context/EnhancedAppContext';
import SuperAdminLogin from './components/SuperAdminLogin';
import SuperAdminLandingPage from './components/SuperAdminLandingPage';

interface SuperAdminAppProps {
  initialTheme?: 'light' | 'dark';
}

const SuperAdminApp: React.FC<SuperAdminAppProps> = ({ 
  initialTheme = 'light' 
}) => {
  const [isDarkMode, setIsDarkMode] = useState(initialTheme === 'dark');

  // Initialize theme
  useEffect(() => {
    const savedTheme = localStorage.getItem('theme');
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    
    const shouldUseDark = savedTheme === 'dark' || 
                         (savedTheme === null && prefersDark) || 
                         initialTheme === 'dark';
    
    setIsDarkMode(shouldUseDark);
    
    if (shouldUseDark) {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, [initialTheme]);

  const handleThemeToggle = () => {
    const newDarkMode = !isDarkMode;
    setIsDarkMode(newDarkMode);
    
    if (newDarkMode) {
      document.documentElement.classList.add('dark');
      localStorage.setItem('theme', 'dark');
    } else {
      document.documentElement.classList.remove('dark');
      localStorage.setItem('theme', 'light');
    }
  };

  return (
    <EnhancedAppProvider>
      <SuperAdminContent 
        isDarkMode={isDarkMode}
        onThemeToggle={handleThemeToggle}
      />
    </EnhancedAppProvider>
  );
};

interface SuperAdminContentProps {
  isDarkMode: boolean;
  onThemeToggle: () => void;
}

const SuperAdminContent: React.FC<SuperAdminContentProps> = ({
  isDarkMode,
  onThemeToggle
}) => {
  const { state } = useEnhancedAppContext();
  const [isLoggedIn, setIsLoggedIn] = useState(false);

  // Check authentication state
  useEffect(() => {
    if (state.isAuthenticated && state.currentEmployee?.role === 'super_admin') {
      setIsLoggedIn(true);
    } else {
      setIsLoggedIn(false);
    }
  }, [state.isAuthenticated, state.currentEmployee]);

  // Show login screen if not authenticated as super admin
  if (!isLoggedIn) {
    return (
      <div className={`min-h-screen transition-colors duration-300 ${
        isDarkMode 
          ? 'bg-gradient-to-br from-gray-900 via-red-900 to-purple-900' 
          : 'bg-gradient-to-br from-red-50 via-pink-50 to-purple-50'
      }`}>
        <SuperAdminLogin 
          onLogin={(success) => {
            if (success) {
              setIsLoggedIn(true);
            }
          }}
        />
      </div>
    );
  }

  // Show Super Admin Dashboard
  return (
    <div className={`min-h-screen transition-colors duration-300 ${
      isDarkMode 
        ? 'bg-gray-900 text-white' 
        : 'bg-gray-50 text-gray-900'
    }`}>
      <SuperAdminLandingPage 
        isDarkMode={isDarkMode}
        onThemeToggle={onThemeToggle}
      />
    </div>
  );
};

export default SuperAdminApp;
