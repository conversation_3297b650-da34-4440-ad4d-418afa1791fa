/**
 * Simple API Test for RESTROFLOW POS System
 */

const https = require('https');
const http = require('http');

function makeRequest(url, options = {}) {
  return new Promise((resolve, reject) => {
    const urlObj = new URL(url);
    const isHttps = urlObj.protocol === 'https:';
    const client = isHttps ? https : http;
    
    const requestOptions = {
      hostname: urlObj.hostname,
      port: urlObj.port || (isHttps ? 443 : 80),
      path: urlObj.pathname + urlObj.search,
      method: options.method || 'GET',
      headers: options.headers || {}
    };

    const req = client.request(requestOptions, (res) => {
      let data = '';
      res.on('data', chunk => data += chunk);
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data);
          resolve({ status: res.statusCode, data: jsonData });
        } catch (e) {
          resolve({ status: res.statusCode, data: data });
        }
      });
    });

    req.on('error', reject);
    
    if (options.body) {
      req.write(options.body);
    }
    
    req.end();
  });
}

async function testAPI() {
  console.log('🚀 RESTROFLOW API Testing');
  console.log('=========================');
  
  try {
    // Test 1: Health Check
    console.log('🧪 Testing Health Endpoint...');
    const healthResponse = await makeRequest('http://localhost:4000/api/health');
    
    if (healthResponse.status === 200) {
      console.log('✅ Health Check: PASSED');
      console.log(`   Status: ${healthResponse.data.status}`);
      console.log(`   Version: ${healthResponse.data.version}`);
    } else {
      console.log('❌ Health Check: FAILED');
      console.log(`   Status Code: ${healthResponse.status}`);
    }

    // Test 2: Authentication
    console.log('\n🧪 Testing Authentication...');
    const authResponse = await makeRequest('http://localhost:4000/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '123456'
      })
    });

    let authToken = null;
    if (authResponse.status === 200 && authResponse.data.token) {
      console.log('✅ Authentication: PASSED');
      console.log(`   User: ${authResponse.data.user?.name || 'Unknown'}`);
      console.log(`   Role: ${authResponse.data.user?.role || 'Unknown'}`);
      authToken = authResponse.data.token;
    } else {
      console.log('❌ Authentication: FAILED');
      console.log(`   Status Code: ${authResponse.status}`);
      console.log(`   Response: ${JSON.stringify(authResponse.data)}`);
    }

    // Test 3: Protected Endpoint (Products)
    console.log('\n🧪 Testing Products Endpoint...');
    const headers = { 'Content-Type': 'application/json' };
    if (authToken) {
      headers['Authorization'] = `Bearer ${authToken}`;
    }

    const productsResponse = await makeRequest('http://localhost:4000/api/products', {
      headers
    });

    if (productsResponse.status === 200) {
      console.log('✅ Products API: PASSED');
      console.log(`   Products found: ${Array.isArray(productsResponse.data) ? productsResponse.data.length : 'Unknown'}`);
    } else {
      console.log('❌ Products API: FAILED');
      console.log(`   Status Code: ${productsResponse.status}`);
    }

    // Test 4: Global Features
    console.log('\n🧪 Testing Global Features...');
    const globalResponse = await makeRequest('http://localhost:4000/api/global/currencies/supported', {
      headers
    });

    if (globalResponse.status === 200) {
      console.log('✅ Global Features: PASSED');
      console.log(`   Supported currencies: ${Array.isArray(globalResponse.data) ? globalResponse.data.length : 'Unknown'}`);
    } else {
      console.log('❌ Global Features: FAILED');
      console.log(`   Status Code: ${globalResponse.status}`);
    }

    // Summary
    console.log('\n📊 Test Summary');
    console.log('================');
    console.log(`✅ Backend Server: RUNNING on http://localhost:4000`);
    console.log(`✅ Health Check: ${healthResponse.status === 200 ? 'PASSED' : 'FAILED'}`);
    console.log(`${authToken ? '✅' : '❌'} Authentication: ${authToken ? 'WORKING' : 'FAILED'}`);
    console.log(`${productsResponse.status === 200 ? '✅' : '❌'} API Endpoints: ${productsResponse.status === 200 ? 'FUNCTIONAL' : 'ISSUES'}`);
    console.log(`${globalResponse.status === 200 ? '✅' : '❌'} Enhanced Features: ${globalResponse.status === 200 ? 'WORKING' : 'ISSUES'}`);

  } catch (error) {
    console.error('💥 Test failed with error:', error.message);
  }
}

testAPI();
