#!/usr/bin/env node

// Production Deployment Script for RESTROFLOW
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

class DeploymentManager {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.backupDir = path.join(this.projectRoot, 'backups');
    this.deploymentLog = [];
    
    console.log('🚀 RESTROFLOW Production Deployment Manager');
    console.log('==========================================');
  }

  // Log deployment step
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = { timestamp, type, message };
    this.deploymentLog.push(logEntry);
    
    const emoji = {
      info: 'ℹ️',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      step: '🔄'
    };
    
    console.log(`${emoji[type] || 'ℹ️'} ${message}`);
  }

  // Execute command with error handling
  exec(command, description) {
    try {
      this.log(`${description}...`, 'step');
      const result = execSync(command, { 
        cwd: this.projectRoot, 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      this.log(`${description} completed`, 'success');
      return result;
    } catch (error) {
      this.log(`${description} failed: ${error.message}`, 'error');
      throw error;
    }
  }

  // Pre-deployment checks
  async preDeploymentChecks() {
    this.log('Running pre-deployment checks...', 'step');
    
    // Check Node.js version
    const nodeVersion = process.version;
    this.log(`Node.js version: ${nodeVersion}`, 'info');
    
    if (parseInt(nodeVersion.slice(1)) < 16) {
      throw new Error('Node.js version 16 or higher is required');
    }

    // Check if package.json exists
    if (!fs.existsSync(path.join(this.projectRoot, 'package.json'))) {
      throw new Error('package.json not found');
    }

    // Check if .env.production exists
    if (!fs.existsSync(path.join(this.projectRoot, '.env.production'))) {
      this.log('.env.production not found, creating from template...', 'warning');
      // Copy from .env.example if it exists
      if (fs.existsSync(path.join(this.projectRoot, '.env.example'))) {
        fs.copyFileSync(
          path.join(this.projectRoot, '.env.example'),
          path.join(this.projectRoot, '.env.production')
        );
      }
    }

    // Check database connection
    try {
      const { pool } = require('../src/database/config/connection');
      await pool.query('SELECT 1');
      this.log('Database connection verified', 'success');
    } catch (error) {
      this.log(`Database connection failed: ${error.message}`, 'error');
      throw new Error('Database connection required for deployment');
    }

    this.log('Pre-deployment checks completed', 'success');
  }

  // Create backup
  async createBackup() {
    this.log('Creating deployment backup...', 'step');
    
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const backupName = `backup-${timestamp}`;
    const backupPath = path.join(this.backupDir, backupName);

    // Ensure backup directory exists
    if (!fs.existsSync(this.backupDir)) {
      fs.mkdirSync(this.backupDir, { recursive: true });
    }

    // Create database backup
    try {
      const dbBackupPath = path.join(backupPath, 'database.sql');
      fs.mkdirSync(backupPath, { recursive: true });
      
      this.exec(
        `pg_dump -h localhost -U BARPOS -d RESTROFLOW > "${dbBackupPath}"`,
        'Database backup'
      );
    } catch (error) {
      this.log(`Database backup failed: ${error.message}`, 'warning');
    }

    // Create application backup (exclude node_modules and logs)
    const excludePatterns = [
      'node_modules',
      'logs',
      'backups',
      '.git',
      'tmp',
      '*.log'
    ];

    const tarCommand = `tar -czf "${backupPath}/application.tar.gz" --exclude-from=<(printf '%s\\n' ${excludePatterns.map(p => `"${p}"`).join(' ')}) .`;
    
    try {
      this.exec(tarCommand, 'Application backup');
    } catch (error) {
      this.log(`Application backup failed: ${error.message}`, 'warning');
    }

    this.log(`Backup created: ${backupPath}`, 'success');
    return backupPath;
  }

  // Install dependencies
  async installDependencies() {
    this.log('Installing production dependencies...', 'step');
    
    // Clean install for production
    this.exec('npm ci --only=production', 'Clean install dependencies');
    
    // Audit for security vulnerabilities
    try {
      this.exec('npm audit --audit-level moderate', 'Security audit');
    } catch (error) {
      this.log('Security vulnerabilities found - review npm audit output', 'warning');
    }

    this.log('Dependencies installed', 'success');
  }

  // Run database migrations
  async runMigrations() {
    this.log('Running database migrations...', 'step');
    
    try {
      // Check if migrations directory exists
      const migrationsDir = path.join(this.projectRoot, 'src/database/migrations');
      if (fs.existsSync(migrationsDir)) {
        const migrationFiles = fs.readdirSync(migrationsDir)
          .filter(file => file.endsWith('.sql'))
          .sort();

        for (const file of migrationFiles) {
          const migrationPath = path.join(migrationsDir, file);
          this.log(`Running migration: ${file}`, 'info');
          
          try {
            this.exec(
              `psql -h localhost -U BARPOS -d RESTROFLOW -f "${migrationPath}"`,
              `Migration ${file}`
            );
          } catch (error) {
            this.log(`Migration ${file} failed: ${error.message}`, 'warning');
          }
        }
      }
    } catch (error) {
      this.log(`Migration process failed: ${error.message}`, 'error');
    }

    this.log('Database migrations completed', 'success');
  }

  // Build application
  async buildApplication() {
    this.log('Building application...', 'step');
    
    // If there's a build script in package.json
    const packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json')));
    
    if (packageJson.scripts && packageJson.scripts.build) {
      this.exec('npm run build', 'Build application');
    } else {
      this.log('No build script found, skipping build step', 'info');
    }

    this.log('Application build completed', 'success');
  }

  // Setup production environment
  async setupProductionEnvironment() {
    this.log('Setting up production environment...', 'step');
    
    // Set NODE_ENV
    process.env.NODE_ENV = 'production';
    
    // Create necessary directories
    const dirs = ['logs', 'uploads', 'tmp'];
    dirs.forEach(dir => {
      const dirPath = path.join(this.projectRoot, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        this.log(`Created directory: ${dir}`, 'info');
      }
    });

    // Set proper file permissions (Unix-like systems)
    if (process.platform !== 'win32') {
      try {
        this.exec('chmod +x scripts/*.js', 'Set script permissions');
        this.exec('chmod 600 .env.production', 'Set environment file permissions');
      } catch (error) {
        this.log(`Permission setting failed: ${error.message}`, 'warning');
      }
    }

    this.log('Production environment setup completed', 'success');
  }

  // Health check
  async performHealthCheck() {
    this.log('Performing post-deployment health check...', 'step');
    
    try {
      // Start server in background for testing
      const { spawn } = require('child_process');
      const serverProcess = spawn('node', ['src/server.js'], {
        cwd: this.projectRoot,
        env: { ...process.env, NODE_ENV: 'production' },
        stdio: 'pipe'
      });

      // Wait for server to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          serverProcess.kill();
          reject(new Error('Server startup timeout'));
        }, 10000);

        serverProcess.stdout.on('data', (data) => {
          if (data.toString().includes('Server running on')) {
            clearTimeout(timeout);
            resolve();
          }
        });

        serverProcess.stderr.on('data', (data) => {
          this.log(`Server error: ${data.toString()}`, 'warning');
        });
      });

      // Test health endpoint
      const http = require('http');
      const healthCheck = new Promise((resolve, reject) => {
        const req = http.get('http://localhost:4000/api/health', (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            if (res.statusCode === 200) {
              resolve(JSON.parse(data));
            } else {
              reject(new Error(`Health check failed: ${res.statusCode}`));
            }
          });
        });
        
        req.on('error', reject);
        req.setTimeout(5000, () => reject(new Error('Health check timeout')));
      });

      const healthResult = await healthCheck;
      serverProcess.kill();

      this.log(`Health check passed: ${healthResult.status}`, 'success');
    } catch (error) {
      this.log(`Health check failed: ${error.message}`, 'error');
      throw error;
    }
  }

  // Generate deployment report
  generateReport() {
    const report = {
      deployment_id: Math.random().toString(36).substr(2, 9),
      timestamp: new Date().toISOString(),
      node_version: process.version,
      environment: 'production',
      status: 'completed',
      steps: this.deploymentLog,
      duration: Date.now() - this.startTime
    };

    const reportPath = path.join(this.backupDir, `deployment-report-${report.deployment_id}.json`);
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    this.log(`Deployment report saved: ${reportPath}`, 'info');
    return report;
  }

  // Main deployment process
  async deploy() {
    this.startTime = Date.now();
    
    try {
      await this.preDeploymentChecks();
      const backupPath = await this.createBackup();
      await this.installDependencies();
      await this.runMigrations();
      await this.buildApplication();
      await this.setupProductionEnvironment();
      await this.performHealthCheck();
      
      const report = this.generateReport();
      
      console.log('\n🎉 DEPLOYMENT COMPLETED SUCCESSFULLY!');
      console.log('=====================================');
      console.log(`Deployment ID: ${report.deployment_id}`);
      console.log(`Duration: ${(report.duration / 1000).toFixed(2)} seconds`);
      console.log(`Backup Location: ${backupPath}`);
      console.log('\n🚀 Your RESTROFLOW application is ready for production!');
      
    } catch (error) {
      this.log(`Deployment failed: ${error.message}`, 'error');
      console.log('\n💥 DEPLOYMENT FAILED!');
      console.log('====================');
      console.log('Check the logs above for details.');
      console.log('You may need to restore from backup if necessary.');
      process.exit(1);
    }
  }
}

// Run deployment if called directly
if (require.main === module) {
  const deployment = new DeploymentManager();
  deployment.deploy().catch(console.error);
}

module.exports = DeploymentManager;
