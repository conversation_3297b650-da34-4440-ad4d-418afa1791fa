import React, { useState, useEffect } from 'react';
import POSSystem from './components/core/POSSystem';
import SuperAdminDashboard from './components/admin/SuperAdminDashboard';

const App: React.FC = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [user, setUser] = useState<any>(null);
  const [pin, setPin] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  // Check for existing authentication
  useEffect(() => {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('user');
    if (token && userData) {
      setIsAuthenticated(true);
      setUser(JSON.parse(userData));
    }
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    if (pin.length < 6) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ pin }),
      });

      const data = await response.json();

      if (response.ok && data.token) {
        localStorage.setItem('authToken', data.token);
        localStorage.setItem('user', JSON.stringify(data.user));
        setIsAuthenticated(true);
        setUser(data.user || { name: 'Development User', role: 'super_admin' });
      } else {
        setError(data.error || 'Login failed');
      }
    } catch (err) {
      setError('Connection error. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
    setIsAuthenticated(false);
    setUser(null);
    setPin('');
  };

  // Show login screen if not authenticated
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 flex items-center justify-center p-4">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="mx-auto h-20 w-20 bg-white rounded-full flex items-center justify-center mb-6 shadow-2xl">
              <span className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                R
              </span>
            </div>
            <h2 className="text-4xl font-bold text-white mb-2">RESTROFLOW</h2>
            <p className="text-xl text-blue-100 mb-2">Restaurant POS System</p>
            <p className="text-sm text-blue-200">Enter your PIN to access the system</p>
          </div>

          <div className="bg-white/10 backdrop-blur-lg rounded-2xl p-8 shadow-2xl border border-white/20">
            <form onSubmit={handleLogin} className="space-y-6">
              <div>
                <label htmlFor="pin" className="block text-sm font-medium text-white mb-2">
                  Employee PIN
                </label>
                <input
                  id="pin"
                  type="password"
                  value={pin}
                  onChange={(e) => setPin(e.target.value)}
                  className="w-full px-4 py-3 bg-white/20 border border-white/30 rounded-lg text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent"
                  placeholder="Enter 6-digit PIN"
                  maxLength={6}
                  required
                />
                <p className="text-xs text-blue-200 mt-2">Demo PIN: 123456 (Super Admin)</p>
              </div>

              {error && (
                <div className="text-red-300 text-sm text-center bg-red-500/20 p-2 rounded">
                  {error}
                </div>
              )}

              <button
                type="submit"
                disabled={pin.length < 6 || loading}
                className="w-full bg-gradient-to-r from-green-500 to-emerald-600 text-white py-3 px-4 rounded-lg font-medium hover:from-green-600 hover:to-emerald-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-transparent disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
              >
                {loading ? (
                  <span className="flex items-center justify-center">
                    <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Authenticating...
                  </span>
                ) : (
                  'Access System'
                )}
              </button>
            </form>
          </div>
        </div>
      </div>
    );
  }

  // Role-based interface rendering
  const renderInterface = () => {
    const userRole = user?.role || 'employee';
    
    switch (userRole) {
      case 'super_admin':
        return <SuperAdminDashboard onLogout={handleLogout} />;
      case 'tenant_admin':
        return <SuperAdminDashboard onLogout={handleLogout} />; // For now, use same dashboard
      case 'manager':
        return <POSSystem onLogout={handleLogout} user={user} />;
      case 'employee':
      default:
        return <POSSystem onLogout={handleLogout} user={user} />;
    }
  };

  return (
    <div className="App">
      {renderInterface()}
    </div>
  );
};

export default App;
