import React, { useState, useEffect } from 'react';
import { 
  CheckCircle, 
  Receipt, 
  Printer, 
  Mail, 
  MessageSquare,
  Clock,
  User,
  MapPin,
  Hash,
  CreditCard,
  Star,
  ArrowRight,
  Download,
  Eye,
  RefreshCw,
  Kitchen,
  Bell
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface OrderCompletionProps {
  orderData: {
    id: string;
    transaction_id: string;
    order_id: string;
    total_amount: number;
    payment_method: string;
    authorization_code?: string;
    status: string;
    receipt?: any;
    completion_steps?: Array<{
      step: string;
      status: string;
      completed_at: string;
    }>;
  };
  onNewOrder: () => void;
  onViewReceipt: () => void;
  onClose: () => void;
}

const OrderCompletionScreen: React.FC<OrderCompletionProps> = ({
  orderData,
  onNewOrder,
  onViewReceipt,
  onClose
}) => {
  const { apiCall } = useEnhancedAppContext();
  const [receiptData, setReceiptData] = useState<any>(null);
  const [isLoadingReceipt, setIsLoadingReceipt] = useState(false);
  const [completionSteps, setCompletionSteps] = useState(orderData.completion_steps || []);
  const [customerFeedback, setCustomerFeedback] = useState({
    rating: 0,
    comment: ''
  });
  const [showFeedback, setShowFeedback] = useState(false);

  useEffect(() => {
    if (orderData.receipt) {
      setReceiptData(orderData.receipt);
    } else {
      fetchReceiptData();
    }
  }, [orderData]);

  const fetchReceiptData = async () => {
    if (!orderData.order_id) return;
    
    setIsLoadingReceipt(true);
    try {
      const response = await apiCall(`/api/receipts/${orderData.order_id}`);
      if (response.ok) {
        const receipt = await response.json();
        setReceiptData(receipt);
      }
    } catch (error) {
      console.error('Error fetching receipt:', error);
    } finally {
      setIsLoadingReceipt(false);
    }
  };

  const handleReprintReceipt = async () => {
    try {
      const response = await apiCall(`/api/receipts/${orderData.order_id}/reprint`, {
        method: 'POST'
      });
      
      if (response.ok) {
        console.log('Receipt reprinted successfully');
        // Show success message or update UI
      }
    } catch (error) {
      console.error('Error reprinting receipt:', error);
    }
  };

  const handleEmailReceipt = async () => {
    const email = prompt('Enter email address:');
    if (!email) return;

    try {
      const response = await apiCall(`/api/receipts/${orderData.order_id}/email`, {
        method: 'POST',
        body: JSON.stringify({ email })
      });
      
      if (response.ok) {
        console.log('Receipt emailed successfully');
      }
    } catch (error) {
      console.error('Error emailing receipt:', error);
    }
  };

  const handleSMSReceipt = async () => {
    const phone = prompt('Enter phone number:');
    if (!phone) return;

    try {
      const response = await apiCall(`/api/receipts/${orderData.order_id}/sms`, {
        method: 'POST',
        body: JSON.stringify({ phone })
      });
      
      if (response.ok) {
        console.log('Receipt sent via SMS successfully');
      }
    } catch (error) {
      console.error('Error sending SMS receipt:', error);
    }
  };

  const submitFeedback = async () => {
    if (customerFeedback.rating === 0) return;

    try {
      const response = await apiCall('/api/feedback', {
        method: 'POST',
        body: JSON.stringify({
          order_id: orderData.order_id,
          rating: customerFeedback.rating,
          comment: customerFeedback.comment
        })
      });
      
      if (response.ok) {
        setShowFeedback(false);
        console.log('Feedback submitted successfully');
      }
    } catch (error) {
      console.error('Error submitting feedback:', error);
    }
  };

  const getStepIcon = (step: string) => {
    switch (step) {
      case 'payment_processed': return <CreditCard className="h-5 w-5" />;
      case 'order_created': return <Receipt className="h-5 w-5" />;
      case 'receipt_generated': return <Printer className="h-5 w-5" />;
      case 'kitchen_notified': return <Kitchen className="h-5 w-5" />;
      case 'email_sent': return <Mail className="h-5 w-5" />;
      case 'sms_sent': return <MessageSquare className="h-5 w-5" />;
      default: return <CheckCircle className="h-5 w-5" />;
    }
  };

  const getStepLabel = (step: string) => {
    switch (step) {
      case 'payment_processed': return 'Payment Processed';
      case 'order_created': return 'Order Created';
      case 'receipt_generated': return 'Receipt Generated';
      case 'kitchen_notified': return 'Kitchen Notified';
      case 'email_sent': return 'Email Receipt Sent';
      case 'sms_sent': return 'SMS Receipt Sent';
      default: return step.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="bg-green-600 text-white p-6 rounded-t-lg">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                <CheckCircle className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-2xl font-bold">Order Completed Successfully!</h1>
                <p className="text-green-100">Payment processed and receipt generated</p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-green-100 hover:text-white text-2xl"
            >
              ×
            </button>
          </div>
        </div>

        <div className="p-6 space-y-6">
          {/* Order Summary */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Details */}
            <div className="bg-gray-50 rounded-lg p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">Payment Details</h2>
              <div className="space-y-3">
                <div className="flex justify-between">
                  <span className="text-gray-600">Transaction ID:</span>
                  <span className="font-mono text-sm">{orderData.transaction_id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Order ID:</span>
                  <span className="font-mono text-sm">{orderData.order_id}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Payment Method:</span>
                  <span className="font-medium capitalize">{orderData.payment_method.replace('_', ' ')}</span>
                </div>
                {orderData.authorization_code && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Authorization:</span>
                    <span className="font-mono text-sm">{orderData.authorization_code}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Status:</span>
                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                    {orderData.status}
                  </span>
                </div>
                <div className="border-t pt-3">
                  <div className="flex justify-between">
                    <span className="font-semibold text-gray-900">Total Amount:</span>
                    <span className="font-bold text-lg text-green-600">
                      {formatCurrency(orderData.total_amount)}
                    </span>
                  </div>
                </div>
              </div>
            </div>

            {/* Order Information */}
            {receiptData && (
              <div className="bg-gray-50 rounded-lg p-6">
                <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Information</h2>
                <div className="space-y-3">
                  {receiptData.order_info?.table_number && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Table:</span>
                      <span className="font-medium">#{receiptData.order_info.table_number}</span>
                    </div>
                  )}
                  {receiptData.order_info?.server_name && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Server:</span>
                      <span className="font-medium">{receiptData.order_info.server_name}</span>
                    </div>
                  )}
                  {receiptData.order_info?.guest_count && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Guests:</span>
                      <span className="font-medium">{receiptData.order_info.guest_count}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Time:</span>
                    <span className="font-medium">
                      {new Date(receiptData.generated_at).toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Receipt #:</span>
                    <span className="font-mono text-sm">{receiptData.receipt_number}</span>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Completion Steps */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Order Processing Steps</h2>
            <div className="space-y-3">
              {completionSteps.map((step, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className={`p-2 rounded-full ${
                    step.status === 'completed' ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-400'
                  }`}>
                    {getStepIcon(step.step)}
                  </div>
                  <div className="flex-1">
                    <span className="font-medium text-gray-900">{getStepLabel(step.step)}</span>
                    {step.completed_at && (
                      <span className="text-sm text-gray-500 ml-2">
                        {new Date(step.completed_at).toLocaleTimeString()}
                      </span>
                    )}
                  </div>
                  <div className={`w-3 h-3 rounded-full ${
                    step.status === 'completed' ? 'bg-green-500' : 'bg-gray-300'
                  }`} />
                </div>
              ))}
            </div>
          </div>

          {/* Receipt Actions */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-4">Receipt Options</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <button
                onClick={onViewReceipt}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Eye className="h-6 w-6 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">View Receipt</span>
              </button>
              
              <button
                onClick={handleReprintReceipt}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Printer className="h-6 w-6 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Print Receipt</span>
              </button>
              
              <button
                onClick={handleEmailReceipt}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <Mail className="h-6 w-6 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">Email Receipt</span>
              </button>
              
              <button
                onClick={handleSMSReceipt}
                className="flex flex-col items-center p-4 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                <MessageSquare className="h-6 w-6 text-gray-600 mb-2" />
                <span className="text-sm font-medium text-gray-900">SMS Receipt</span>
              </button>
            </div>
          </div>

          {/* Customer Feedback */}
          <div className="bg-white border border-gray-200 rounded-lg p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg font-semibold text-gray-900">Customer Feedback</h2>
              <button
                onClick={() => setShowFeedback(!showFeedback)}
                className="text-blue-600 hover:text-blue-700 text-sm font-medium"
              >
                {showFeedback ? 'Hide' : 'Add Feedback'}
              </button>
            </div>
            
            {showFeedback && (
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    How was your experience?
                  </label>
                  <div className="flex space-x-2">
                    {[1, 2, 3, 4, 5].map((rating) => (
                      <button
                        key={rating}
                        onClick={() => setCustomerFeedback({...customerFeedback, rating})}
                        className={`p-2 rounded-full transition-colors ${
                          customerFeedback.rating >= rating
                            ? 'text-yellow-500'
                            : 'text-gray-300 hover:text-yellow-400'
                        }`}
                      >
                        <Star className="h-6 w-6 fill-current" />
                      </button>
                    ))}
                  </div>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional Comments (Optional)
                  </label>
                  <textarea
                    value={customerFeedback.comment}
                    onChange={(e) => setCustomerFeedback({...customerFeedback, comment: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    rows={3}
                    placeholder="Tell us about your experience..."
                  />
                </div>
                
                <button
                  onClick={submitFeedback}
                  disabled={customerFeedback.rating === 0}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Submit Feedback
                </button>
              </div>
            )}
          </div>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-4">
            <button
              onClick={onNewOrder}
              className="flex-1 flex items-center justify-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
            >
              <ArrowRight className="h-5 w-5 mr-2" />
              Start New Order
            </button>
            
            <button
              onClick={onClose}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default OrderCompletionScreen;
