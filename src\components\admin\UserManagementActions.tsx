import React, { useState } from 'react';
import {
  Key,
  ToggleLeft,
  ToggleRight,
  Trash2,
  <PERSON><PERSON>resh<PERSON><PERSON>,
  <PERSON>ertTriangle,
  CheckCircle,
  XCircle,
  Shield,
  UserX,
  UserCheck,
  Lock,
  Unlock
} from 'lucide-react';
import { adminApiService } from '../../services/adminApiService';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  tenantId: number;
}

interface UserManagementActionsProps {
  user: User;
  onUserUpdated: () => void;
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
}

export const UserManagementActions: React.FC<UserManagementActionsProps> = ({
  user,
  onUserUpdated,
  onError,
  onSuccess
}) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState<string | null>(null);
  const [reason, setReason] = useState('');

  const handleResetPassword = async () => {
    try {
      setLoading('reset-password');
      // Generate random 6-digit PIN on frontend
      const randomPin = Math.floor(100000 + Math.random() * 900000).toString();

      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newPin: randomPin })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess(`Password reset successfully. New PIN: ${randomPin}`);
      onUserUpdated();
    } catch (error) {
      onError('Failed to reset password');
    } finally {
      setLoading(null);
    }
  };

  const handleToggleStatus = async () => {
    console.log('🔄 handleToggleStatus called for user:', user.id, 'current status:', user.isActive);
    try {
      setLoading('toggle-status');
      const newStatus = !user.isActive;

      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('📡 Making API call to toggle user status...');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: newStatus, reason: reason || 'Admin action' })
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ API Error:', response.status, errorData);
        throw new Error(`HTTP ${response.status}: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ User status updated successfully:', result);

      onSuccess(`User ${newStatus ? 'activated' : 'deactivated'} successfully`);
      onUserUpdated();
    } catch (error) {
      console.error('❌ Error toggling user status:', error);
      onError(`Failed to ${user.isActive ? 'deactivate' : 'activate'} user: ${error.message}`);
      throw error; // Re-throw to be handled by confirm dialog
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteUser = async (permanent = false) => {
    console.log('🗑️ handleDeleteUser called for user:', user.id, 'permanent:', permanent);
    try {
      setLoading('delete-user');

      const token = localStorage.getItem('authToken');
      if (!token) {
        throw new Error('No authentication token found');
      }

      console.log('📡 Making API call to delete user...');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ permanent, reason: reason || 'Admin deletion' })
      });

      if (!response.ok) {
        const errorData = await response.text();
        console.error('❌ API Error:', response.status, errorData);
        throw new Error(`HTTP ${response.status}: ${errorData}`);
      }

      const result = await response.json();
      console.log('✅ User deleted successfully:', result);

      onSuccess(`User ${permanent ? 'permanently deleted' : 'deactivated'} successfully`);
      onUserUpdated();
    } catch (error) {
      console.error('❌ Error deleting user:', error);
      onError(`Failed to ${permanent ? 'delete' : 'deactivate'} user: ${error.message}`);
      throw error; // Re-throw to be handled by confirm dialog
    } finally {
      setLoading(null);
    }
  };

  const ConfirmDialog = ({ action, onConfirm, onCancel }: {
    action: string;
    onConfirm: () => void;
    onCancel: () => void;
  }) => {
    const handleConfirm = async (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('🔄 Confirm button clicked for action:', action);

      try {
        // Close dialog immediately to prevent double-clicks
        setShowConfirmDialog(null);

        // Execute the action
        await onConfirm();

        // Clear reason after successful action
        setReason('');
      } catch (error) {
        console.error('❌ Error in confirm action:', error);
        // Reopen dialog if there was an error
        setShowConfirmDialog(action.toLowerCase().includes('delete') ? 'delete-user' : 'toggle-status');
      }
    };

    const handleCancel = (e: React.MouseEvent) => {
      e.preventDefault();
      e.stopPropagation();
      console.log('❌ Cancel button clicked for action:', action);
      onCancel();
    };

    const handleBackdropClick = (e: React.MouseEvent) => {
      if (e.target === e.currentTarget) {
        handleCancel(e);
      }
    };

    return (
      <div
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
        onClick={handleBackdropClick}
      >
        <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
          <div className="flex items-center space-x-3 mb-4">
            <AlertTriangle className="h-6 w-6 text-yellow-500" />
            <h3 className="text-lg font-semibold text-gray-900">
              Confirm Action
            </h3>
          </div>

          <p className="text-gray-600 mb-4">
            Are you sure you want to perform this action? This may affect user access and permissions.
          </p>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Reason (optional)
            </label>
            <textarea
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
              placeholder="Enter reason for this action..."
            />
          </div>

          <div className="flex space-x-3">
            <button
              type="button"
              onClick={handleCancel}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-gray-500"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirm}
              disabled={loading !== null}
              className={`flex-1 px-4 py-2 text-white rounded-md focus:outline-none focus:ring-2 transition-all duration-200 font-medium ${
                action.includes('Delete')
                  ? 'bg-red-600 hover:bg-red-700 focus:ring-red-500 active:bg-red-800'
                  : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500 active:bg-blue-800'
              } ${loading !== null ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer hover:shadow-lg'}`}
            >
              {loading !== null ? (
                <div className="flex items-center justify-center space-x-2">
                  <RefreshCw className="h-4 w-4 animate-spin" />
                  <span>Processing...</span>
                </div>
              ) : (
                'Confirm'
              )}
            </button>
          </div>
        </div>
      </div>
    );
  };

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {/* Reset Password Button */}
        <button
          onClick={handleResetPassword}
          disabled={loading === 'reset-password'}
          className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 disabled:opacity-50 text-sm"
          title="Reset Password"
        >
          {loading === 'reset-password' ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : (
            <Key className="h-3 w-3" />
          )}
          <span className="hidden sm:inline">Reset Password</span>
        </button>

        {/* Toggle Status Button */}
        <button
          onClick={() => {
            console.log('Toggle status button clicked for user:', user.id);
            setShowConfirmDialog('toggle-status');
          }}
          disabled={loading === 'toggle-status'}
          className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm ${
            user.isActive
              ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          } disabled:opacity-50`}
          title={user.isActive ? 'Deactivate User' : 'Activate User'}
        >
          {loading === 'toggle-status' ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : user.isActive ? (
            <UserX className="h-3 w-3" />
          ) : (
            <UserCheck className="h-3 w-3" />
          )}
          <span className="hidden sm:inline">
            {user.isActive ? 'Deactivate' : 'Activate'}
          </span>
        </button>

        {/* Delete User Button */}
        {user.role !== 'super_admin' && (
          <button
            onClick={() => {
              console.log('Delete button clicked for user:', user.id);
              setShowConfirmDialog('delete-user');
            }}
            disabled={loading === 'delete-user'}
            className="flex items-center space-x-1 px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 disabled:opacity-50 text-sm"
            title="Delete User"
          >
            {loading === 'delete-user' ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <Trash2 className="h-3 w-3" />
            )}
            <span className="hidden sm:inline">Delete</span>
          </button>
        )}

        {/* Status Indicator */}
        <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
          user.isActive
            ? 'bg-green-100 text-green-800'
            : 'bg-red-100 text-red-800'
        }`}>
          {user.isActive ? (
            <CheckCircle className="h-3 w-3" />
          ) : (
            <XCircle className="h-3 w-3" />
          )}
          <span>{user.isActive ? 'Active' : 'Inactive'}</span>
        </div>
      </div>

      {/* Confirmation Dialogs */}
      {showConfirmDialog === 'toggle-status' && (
        <ConfirmDialog
          action={user.isActive ? 'Deactivate User' : 'Activate User'}
          onConfirm={handleToggleStatus}
          onCancel={() => {
            setShowConfirmDialog(null);
            setReason('');
          }}
        />
      )}

      {showConfirmDialog === 'delete-user' && (
        <ConfirmDialog
          action="Delete User"
          onConfirm={() => handleDeleteUser(false)}
          onCancel={() => {
            setShowConfirmDialog(null);
            setReason('');
          }}
        />
      )}
    </>
  );
};

export default UserManagementActions;
