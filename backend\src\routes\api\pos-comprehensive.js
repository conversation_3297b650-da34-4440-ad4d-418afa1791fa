// Comprehensive POS System Routes for RESTROFLOW
const express = require('express');
const { pool } = require('../../database/config/connection');
const { authenticateToken, requireTenantAccess } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication to all POS routes
router.use(authenticateToken);

// Utility functions
const handleDatabaseError = (error, res, operation) => {
  console.error(`Database error in ${operation}:`, error);
  res.status(500).json({
    success: false,
    message: `Database error during ${operation}`,
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
  });
};

const validateTenantAccess = async (tenantId, userId) => {
  try {
    const result = await pool.query(
      'SELECT id FROM tenants WHERE id = $1 AND (owner_id = $2 OR id IN (SELECT tenant_id FROM employees WHERE user_id = $2))',
      [tenantId, userId]
    );
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error validating tenant access:', error);
    return false;
  }
};

// ==================== PRODUCTS ====================

// Get all products
router.get('/products', requireTenantAccess, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        p.*,
        c.name as category_name,
        c.color as category_color,
        COALESCE(i.current_stock, 0) as current_stock,
        COALESCE(i.low_stock_threshold, 0) as low_stock_threshold
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE p.tenant_id = $1 AND p.is_active = true 
      ORDER BY c.sort_order, p.sort_order, p.name
    `, [req.user.tenantId]);

    res.json({
      success: true,
      products: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching products');
  }
});

// Get product by ID
router.get('/products/:id', requireTenantAccess, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        p.*,
        c.name as category_name,
        c.color as category_color,
        COALESCE(i.current_stock, 0) as current_stock,
        COALESCE(i.low_stock_threshold, 0) as low_stock_threshold
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE p.id = $1 AND p.tenant_id = $2 AND p.is_active = true
    `, [req.params.id, req.user.tenantId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Product not found'
      });
    }

    res.json({
      success: true,
      product: result.rows[0]
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching product');
  }
});

// ==================== CATEGORIES ====================

// Get all categories
router.get('/categories', requireTenantAccess, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT 
        c.*,
        COUNT(p.id) as product_count
      FROM categories c
      LEFT JOIN products p ON c.id = p.category_id AND p.is_active = true
      WHERE c.tenant_id = $1 AND c.is_active = true
      GROUP BY c.id
      ORDER BY c.sort_order, c.name
    `, [req.user.tenantId]);

    res.json({
      success: true,
      categories: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching categories');
  }
});

// ==================== ORDERS ====================

// Create new order
router.post('/orders', requireTenantAccess, async (req, res) => {
  const client = await pool.connect();
  
  try {
    await client.query('BEGIN');
    
    const { table_id, items, customer_info, order_type = 'dine_in', notes } = req.body;
    
    // Validate required fields
    if (!items || items.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'Order must contain at least one item'
      });
    }

    // Calculate totals
    let subtotal = 0;
    for (const item of items) {
      subtotal += item.price * item.quantity;
    }
    
    const tax_rate = 0.13; // 13% HST for Canada
    const tax_amount = subtotal * tax_rate;
    const total_amount = subtotal + tax_amount;

    // Create order
    const orderResult = await client.query(`
      INSERT INTO orders (
        tenant_id, table_id, order_type, status, subtotal, 
        tax_amount, total_amount, customer_info, notes, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10)
      RETURNING *
    `, [
      req.user.tenantId, table_id, order_type, 'pending', 
      subtotal, tax_amount, total_amount, customer_info, notes, req.user.id
    ]);

    const order = orderResult.rows[0];

    // Add order items
    for (const item of items) {
      await client.query(`
        INSERT INTO order_items (
          order_id, product_id, quantity, unit_price, total_price, 
          customizations, notes
        ) VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        order.id, item.product_id, item.quantity, item.price,
        item.price * item.quantity, item.customizations, item.notes
      ]);

      // Update inventory if tracking is enabled
      await client.query(`
        INSERT INTO inventory_transactions (
          tenant_id, product_id, transaction_type, quantity_change, 
          reference_id, reference_type, processed_by
        ) VALUES ($1, $2, 'sale', $3, $4, 'order', $5)
      `, [
        req.user.tenantId, item.product_id, -item.quantity, 
        order.id, req.user.id
      ]);
    }

    // Update table status if applicable
    if (table_id) {
      await client.query(`
        UPDATE tables 
        SET status = 'occupied', current_order_id = $1, updated_at = NOW()
        WHERE id = $2 AND tenant_id = $3
      `, [order.id, table_id, req.user.tenantId]);
    }

    await client.query('COMMIT');

    res.status(201).json({
      success: true,
      order: order,
      message: 'Order created successfully'
    });

  } catch (error) {
    await client.query('ROLLBACK');
    handleDatabaseError(error, res, 'creating order');
  } finally {
    client.release();
  }
});

// Get orders for tenant
router.get('/orders', requireTenantAccess, async (req, res) => {
  try {
    const { status, limit = 50, offset = 0, date_from, date_to } = req.query;
    
    let query = `
      SELECT 
        o.*,
        t.table_number,
        u.name as created_by_name,
        COUNT(oi.id) as item_count
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.created_by = u.id
      LEFT JOIN order_items oi ON o.id = oi.order_id
      WHERE o.tenant_id = $1
    `;
    
    const params = [req.user.tenantId];
    let paramIndex = 2;
    
    if (status) {
      query += ` AND o.status = $${paramIndex}`;
      params.push(status);
      paramIndex++;
    }
    
    if (date_from) {
      query += ` AND o.created_at >= $${paramIndex}`;
      params.push(date_from);
      paramIndex++;
    }
    
    if (date_to) {
      query += ` AND o.created_at <= $${paramIndex}`;
      params.push(date_to);
      paramIndex++;
    }
    
    query += `
      GROUP BY o.id, t.table_number, u.name
      ORDER BY o.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;
    
    params.push(limit, offset);
    
    const result = await pool.query(query, params);

    res.json({
      success: true,
      orders: result.rows,
      count: result.rows.length,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching orders');
  }
});

// Get order by ID with items
router.get('/orders/:id', requireTenantAccess, async (req, res) => {
  try {
    // Get order details
    const orderResult = await pool.query(`
      SELECT 
        o.*,
        t.table_number,
        u.name as created_by_name
      FROM orders o
      LEFT JOIN tables t ON o.table_id = t.id
      LEFT JOIN users u ON o.created_by = u.id
      WHERE o.id = $1 AND o.tenant_id = $2
    `, [req.params.id, req.user.tenantId]);

    if (orderResult.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Get order items
    const itemsResult = await pool.query(`
      SELECT 
        oi.*,
        p.name as product_name,
        p.category,
        c.name as category_name
      FROM order_items oi
      JOIN products p ON oi.product_id = p.id
      LEFT JOIN categories c ON p.category_id = c.id
      WHERE oi.order_id = $1
      ORDER BY oi.id
    `, [req.params.id]);

    const order = orderResult.rows[0];
    order.items = itemsResult.rows;

    res.json({
      success: true,
      order: order
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching order details');
  }
});

// Update order status
router.patch('/orders/:id/status', requireTenantAccess, async (req, res) => {
  try {
    const { status } = req.body;
    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'served', 'completed', 'cancelled'];
    
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid order status'
      });
    }

    const result = await pool.query(`
      UPDATE orders 
      SET status = $1, updated_at = NOW()
      WHERE id = $2 AND tenant_id = $3
      RETURNING *
    `, [status, req.params.id, req.user.tenantId]);

    if (result.rows.length === 0) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    res.json({
      success: true,
      order: result.rows[0],
      message: `Order status updated to ${status}`
    });
  } catch (error) {
    handleDatabaseError(error, res, 'updating order status');
  }
});

// ==================== TABLES ====================

// Get all tables
router.get('/tables', requireTenantAccess, async (req, res) => {
  try {
    const result = await pool.query(`
      SELECT
        t.*,
        o.id as current_order_id,
        o.total_amount as current_order_total,
        ts.party_size,
        ts.session_start
      FROM tables t
      LEFT JOIN orders o ON t.current_order_id = o.id
      LEFT JOIN table_sessions ts ON t.id = ts.table_id AND ts.status = 'active'
      WHERE t.tenant_id = $1 AND t.is_active = true
      ORDER BY t.table_number
    `, [req.user.tenantId]);

    res.json({
      success: true,
      tables: result.rows,
      count: result.rows.length
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching tables');
  }
});

// Update table status
router.patch('/tables/:id/status', requireTenantAccess, async (req, res) => {
  try {
    const { status, party_size } = req.body;
    const validStatuses = ['available', 'occupied', 'reserved', 'cleaning', 'maintenance', 'unavailable'];

    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid table status'
      });
    }

    const client = await pool.connect();

    try {
      await client.query('BEGIN');

      // Update table status
      const tableResult = await client.query(`
        UPDATE tables
        SET status = $1, updated_at = NOW()
        WHERE id = $2 AND tenant_id = $3
        RETURNING *
      `, [status, req.params.id, req.user.tenantId]);

      if (tableResult.rows.length === 0) {
        await client.query('ROLLBACK');
        return res.status(404).json({
          success: false,
          message: 'Table not found'
        });
      }

      // Create table session if table is being occupied
      if (status === 'occupied' && party_size) {
        await client.query(`
          INSERT INTO table_sessions (tenant_id, table_id, party_size, status)
          VALUES ($1, $2, $3, 'active')
        `, [req.user.tenantId, req.params.id, party_size]);
      }

      // End table session if table is becoming available
      if (status === 'available') {
        await client.query(`
          UPDATE table_sessions
          SET status = 'completed', session_end = NOW()
          WHERE table_id = $1 AND status = 'active'
        `, [req.params.id]);
      }

      await client.query('COMMIT');

      res.json({
        success: true,
        table: tableResult.rows[0],
        message: `Table status updated to ${status}`
      });
    } catch (error) {
      await client.query('ROLLBACK');
      throw error;
    } finally {
      client.release();
    }
  } catch (error) {
    handleDatabaseError(error, res, 'updating table status');
  }
});

// ==================== PAYMENTS ====================

// Process payment
router.post('/payments', requireTenantAccess, async (req, res) => {
  const client = await pool.connect();

  try {
    await client.query('BEGIN');

    const {
      order_id,
      payment_method,
      payment_processor,
      amount,
      tip_amount = 0,
      payment_data = {}
    } = req.body;

    // Validate order exists and belongs to tenant
    const orderResult = await client.query(`
      SELECT * FROM orders
      WHERE id = $1 AND tenant_id = $2
    `, [order_id, req.user.tenantId]);

    if (orderResult.rows.length === 0) {
      await client.query('ROLLBACK');
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    const order = orderResult.rows[0];
    const total_amount = parseFloat(amount) + parseFloat(tip_amount);

    // Create payment record
    const paymentResult = await client.query(`
      INSERT INTO payments (
        tenant_id, order_id, payment_method, payment_processor,
        amount, tip_amount, total_amount, status, payment_data,
        processed_at, created_by
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, NOW(), $10)
      RETURNING *
    `, [
      req.user.tenantId, order_id, payment_method, payment_processor,
      amount, tip_amount, total_amount, 'completed', payment_data, req.user.id
    ]);

    // Update order payment status
    await client.query(`
      UPDATE orders
      SET payment_status = 'completed', status = 'completed', updated_at = NOW()
      WHERE id = $1
    `, [order_id]);

    // Free up table if applicable
    if (order.table_id) {
      await client.query(`
        UPDATE tables
        SET status = 'cleaning', current_order_id = NULL, updated_at = NOW()
        WHERE id = $1
      `, [order.table_id]);
    }

    await client.query('COMMIT');

    res.status(201).json({
      success: true,
      payment: paymentResult.rows[0],
      message: 'Payment processed successfully'
    });

  } catch (error) {
    await client.query('ROLLBACK');
    handleDatabaseError(error, res, 'processing payment');
  } finally {
    client.release();
  }
});

// Get payment history
router.get('/payments', requireTenantAccess, async (req, res) => {
  try {
    const { limit = 50, offset = 0, date_from, date_to } = req.query;

    let query = `
      SELECT
        p.*,
        o.id as order_number,
        o.total_amount as order_total,
        t.table_number
      FROM payments p
      LEFT JOIN orders o ON p.order_id = o.id
      LEFT JOIN tables t ON o.table_id = t.id
      WHERE p.tenant_id = $1
    `;

    const params = [req.user.tenantId];
    let paramIndex = 2;

    if (date_from) {
      query += ` AND p.created_at >= $${paramIndex}`;
      params.push(date_from);
      paramIndex++;
    }

    if (date_to) {
      query += ` AND p.created_at <= $${paramIndex}`;
      params.push(date_to);
      paramIndex++;
    }

    query += `
      ORDER BY p.created_at DESC
      LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
    `;

    params.push(limit, offset);

    const result = await pool.query(query, params);

    res.json({
      success: true,
      payments: result.rows,
      count: result.rows.length,
      pagination: {
        limit: parseInt(limit),
        offset: parseInt(offset)
      }
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching payments');
  }
});

// ==================== ANALYTICS ====================

// Get sales analytics
router.get('/analytics/sales', requireTenantAccess, async (req, res) => {
  try {
    const { period = 'today' } = req.query;

    let dateFilter = '';
    switch (period) {
      case 'today':
        dateFilter = "AND DATE(o.created_at) = CURRENT_DATE";
        break;
      case 'week':
        dateFilter = "AND o.created_at >= CURRENT_DATE - INTERVAL '7 days'";
        break;
      case 'month':
        dateFilter = "AND o.created_at >= CURRENT_DATE - INTERVAL '30 days'";
        break;
      default:
        dateFilter = "AND DATE(o.created_at) = CURRENT_DATE";
    }

    const result = await pool.query(`
      SELECT
        COUNT(o.id) as total_orders,
        COALESCE(SUM(o.total_amount), 0) as total_revenue,
        COALESCE(AVG(o.total_amount), 0) as average_order_value,
        COUNT(DISTINCT o.table_id) as tables_served,
        COUNT(CASE WHEN o.status = 'completed' THEN 1 END) as completed_orders,
        COUNT(CASE WHEN o.status = 'cancelled' THEN 1 END) as cancelled_orders
      FROM orders o
      WHERE o.tenant_id = $1 ${dateFilter}
    `, [req.user.tenantId]);

    const topProductsResult = await pool.query(`
      SELECT
        p.name,
        SUM(oi.quantity) as total_sold,
        SUM(oi.total_price) as total_revenue
      FROM order_items oi
      JOIN orders o ON oi.order_id = o.id
      JOIN products p ON oi.product_id = p.id
      WHERE o.tenant_id = $1 ${dateFilter}
      GROUP BY p.id, p.name
      ORDER BY total_sold DESC
      LIMIT 10
    `, [req.user.tenantId]);

    res.json({
      success: true,
      analytics: {
        summary: result.rows[0],
        top_products: topProductsResult.rows,
        period: period
      }
    });
  } catch (error) {
    handleDatabaseError(error, res, 'fetching analytics');
  }
});

module.exports = router;
