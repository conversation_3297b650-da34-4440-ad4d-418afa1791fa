// Phase 3J: Advanced Voice Recognition & Natural Language Processing
// Advanced Voice Recognition Component with Conversational AI

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { 
  MicrophoneIcon, 
  StopIcon, 
  SpeakerWaveIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  XMarkIcon,
  UserIcon,
  ChatBubbleLeftRightIcon,
  CpuChipIcon,
  ShieldCheckIcon
} from '@heroicons/react/24/outline';

const AdvancedVoiceRecognition = ({ 
  onCommand,
  onTranscript,
  onAuthentication,
  className = '',
  mode = 'recognition', // 'recognition', 'authentication', 'conversation'
  showAnalytics = true,
  enableNLP = true,
  userId = null
}) => {
  const { currentLanguage, t } = useTranslation();
  
  const [isListening, setIsListening] = useState(false);
  const [transcript, setTranscript] = useState('');
  const [confidence, setConfidence] = useState(0);
  const [error, setError] = useState(null);
  const [isSupported, setIsSupported] = useState(false);
  const [volume, setVolume] = useState(0);
  const [conversationId, setConversationId] = useState(null);
  const [conversationHistory, setConversationHistory] = useState([]);
  const [currentIntent, setCurrentIntent] = useState(null);
  const [voiceAuthenticated, setVoiceAuthenticated] = useState(false);
  const [processingNLP, setProcessingNLP] = useState(false);
  const [aiResponse, setAiResponse] = useState('');
  
  const recognitionRef = useRef(null);
  const audioContextRef = useRef(null);
  const analyserRef = useRef(null);
  const microphoneRef = useRef(null);
  const animationRef = useRef(null);

  // Initialize speech recognition
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      
      if (SpeechRecognition) {
        setIsSupported(true);
        recognitionRef.current = new SpeechRecognition();
        
        const recognition = recognitionRef.current;
        recognition.continuous = true;
        recognition.interimResults = true;
        recognition.maxAlternatives = 3;
        recognition.lang = currentLanguage;
        
        recognition.onstart = () => {
          setIsListening(true);
          setError(null);
          console.log('🎤 Advanced voice recognition started');
        };
        
        recognition.onend = () => {
          setIsListening(false);
          console.log('🎤 Advanced voice recognition ended');
        };
        
        recognition.onerror = (event) => {
          setError(event.error);
          setIsListening(false);
          console.error('🎤 Advanced voice recognition error:', event.error);
        };
        
        recognition.onresult = async (event) => {
          let finalTranscript = '';
          let interimTranscript = '';
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const result = event.results[i];
            const transcript = result[0].transcript;
            const confidence = result[0].confidence;
            
            if (result.isFinal) {
              finalTranscript += transcript;
              setConfidence(confidence);
              
              // Process with NLP if enabled
              if (enableNLP && finalTranscript.trim()) {
                await processWithNLP(finalTranscript, confidence);
              }
              
              // Handle different modes
              if (mode === 'authentication') {
                await handleVoiceAuthentication(finalTranscript);
              } else if (mode === 'conversation') {
                await handleConversation(finalTranscript);
              }
              
            } else {
              interimTranscript += transcript;
            }
          }
          
          const fullTranscript = finalTranscript || interimTranscript;
          setTranscript(fullTranscript);
          
          if (onTranscript) {
            onTranscript(fullTranscript, confidence);
          }
        };
      } else {
        setIsSupported(false);
        setError('Advanced speech recognition not supported in this browser');
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort();
      }
      if (audioContextRef.current) {
        audioContextRef.current.close();
      }
    };
  }, [currentLanguage, enableNLP, mode, onTranscript]);

  // Process transcript with NLP
  const processWithNLP = async (text, confidence) => {
    if (!enableNLP) return;
    
    setProcessingNLP(true);
    try {
      const response = await fetch('/api/nlp/process', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          text,
          context: 'restaurant',
          language: currentLanguage
        })
      });
      
      const nlpResult = await response.json();
      setCurrentIntent(nlpResult.intent);
      setAiResponse(nlpResult.response);
      
      if (onCommand && nlpResult.intent.confidence > 0.7) {
        onCommand({
          intent: nlpResult.intent,
          entities: nlpResult.entities,
          originalText: text,
          confidence: confidence,
          nlpConfidence: nlpResult.intent.confidence,
          conversationId: nlpResult.conversationId
        });
      }
      
      console.log('🧠 NLP processed:', nlpResult);
    } catch (error) {
      console.error('NLP processing failed:', error);
    } finally {
      setProcessingNLP(false);
    }
  };

  // Handle voice authentication
  const handleVoiceAuthentication = async (voiceSample) => {
    try {
      const response = await fetch('/api/voice/authenticate', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          voiceSample,
          userId,
          enrollmentMode: false
        })
      });
      
      const authResult = await response.json();
      setVoiceAuthenticated(authResult.authenticated);
      
      if (onAuthentication) {
        onAuthentication(authResult);
      }
      
      console.log('🔐 Voice authentication:', authResult);
    } catch (error) {
      console.error('Voice authentication failed:', error);
    }
  };

  // Handle conversation flow
  const handleConversation = async (message) => {
    try {
      const response = await fetch('/api/voice/conversation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          conversationId,
          message,
          context: 'restaurant',
          userId
        })
      });
      
      const conversationResult = await response.json();
      
      setConversationId(conversationResult.conversationId);
      setConversationHistory(prev => [
        ...prev,
        {
          type: 'user',
          message,
          timestamp: new Date().toISOString()
        },
        {
          type: 'ai',
          message: conversationResult.response,
          timestamp: conversationResult.timestamp,
          confidence: conversationResult.confidence
        }
      ]);
      
      // Speak the AI response
      if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(conversationResult.response);
        utterance.lang = currentLanguage;
        speechSynthesis.speak(utterance);
      }
      
      console.log('💬 Conversation:', conversationResult);
    } catch (error) {
      console.error('Conversation processing failed:', error);
    }
  };

  const startListening = async () => {
    if (!isSupported || !recognitionRef.current) {
      setError(t('voice.not_supported', 'Advanced voice recognition not supported'));
      return;
    }

    try {
      // Request microphone permission
      const stream = await navigator.mediaDevices.getUserMedia({ 
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100
        }
      });
      
      // Setup advanced audio analysis
      audioContextRef.current = new (window.AudioContext || window.webkitAudioContext)();
      analyserRef.current = audioContextRef.current.createAnalyser();
      microphoneRef.current = audioContextRef.current.createMediaStreamSource(stream);
      
      microphoneRef.current.connect(analyserRef.current);
      analyserRef.current.fftSize = 2048;
      analyserRef.current.smoothingTimeConstant = 0.8;
      
      // Start volume monitoring
      monitorVolume();
      
      // Start speech recognition
      recognitionRef.current.start();
      setTranscript('');
      setError(null);
      setAiResponse('');
    } catch (error) {
      setError(t('voice.permission_denied', 'Microphone permission denied'));
      console.error('Microphone access denied:', error);
    }
  };

  const stopListening = () => {
    if (recognitionRef.current) {
      recognitionRef.current.stop();
    }
    
    if (animationRef.current) {
      cancelAnimationFrame(animationRef.current);
    }
    
    setVolume(0);
  };

  const monitorVolume = () => {
    if (!analyserRef.current) return;
    
    const bufferLength = analyserRef.current.frequencyBinCount;
    const dataArray = new Uint8Array(bufferLength);
    
    const updateVolume = () => {
      analyserRef.current.getByteFrequencyData(dataArray);
      
      let sum = 0;
      for (let i = 0; i < bufferLength; i++) {
        sum += dataArray[i];
      }
      
      const average = sum / bufferLength;
      setVolume(average / 255); // Normalize to 0-1
      
      if (isListening) {
        animationRef.current = requestAnimationFrame(updateVolume);
      }
    };
    
    updateVolume();
  };

  const clearConversation = () => {
    setConversationHistory([]);
    setConversationId(null);
    setCurrentIntent(null);
    setAiResponse('');
  };

  if (!isSupported) {
    return (
      <div className={`flex items-center space-x-2 text-gray-500 dark:text-gray-400 ${className}`}>
        <ExclamationTriangleIcon className="w-5 h-5" />
        <span className="text-sm">{t('voice.not_supported', 'Advanced voice recognition not supported')}</span>
      </div>
    );
  }

  return (
    <div className={`flex flex-col space-y-4 ${className}`}>
      {/* Voice Control Panel */}
      <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center space-x-2">
            <CpuChipIcon className="w-5 h-5 text-blue-500" />
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
              {t('voice.advanced_recognition', 'Advanced Voice Recognition')}
            </h3>
          </div>
          
          {mode === 'authentication' && (
            <div className="flex items-center space-x-2">
              <ShieldCheckIcon className={`w-5 h-5 ${voiceAuthenticated ? 'text-green-500' : 'text-gray-400'}`} />
              <span className={`text-sm ${voiceAuthenticated ? 'text-green-600' : 'text-gray-500'}`}>
                {voiceAuthenticated ? t('voice.authenticated', 'Authenticated') : t('voice.not_authenticated', 'Not Authenticated')}
              </span>
            </div>
          )}
        </div>

        {/* Voice Recognition Button */}
        <div className="flex items-center justify-center mb-4">
          <div className="relative">
            <button
              onClick={isListening ? stopListening : startListening}
              disabled={!!error}
              className={`
                w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300
                ${isListening 
                  ? 'bg-red-500 hover:bg-red-600 text-white shadow-lg' 
                  : 'bg-blue-500 hover:bg-blue-600 text-white shadow-md hover:shadow-lg'
                }
                ${error ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
                ${isListening ? 'animate-pulse' : ''}
              `}
              style={{
                transform: isListening ? `scale(${1 + volume * 0.3})` : 'scale(1)'
              }}
            >
              {isListening ? (
                <StopIcon className="w-8 h-8" />
              ) : (
                <MicrophoneIcon className="w-8 h-8" />
              )}
            </button>
            
            {/* Volume Indicator */}
            {isListening && (
              <div className="absolute inset-0 rounded-full border-4 border-red-300 animate-ping opacity-75"></div>
            )}
          </div>
        </div>

        {/* Status and Controls */}
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            {isListening && (
              <div className="flex items-center space-x-1 text-red-500">
                <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium">{t('voice.listening', 'Listening...')}</span>
              </div>
            )}
            
            {processingNLP && (
              <div className="flex items-center space-x-1 text-blue-500">
                <CpuChipIcon className="w-4 h-4 animate-spin" />
                <span className="text-sm">{t('voice.processing', 'Processing...')}</span>
              </div>
            )}
          </div>
          
          {mode === 'conversation' && (
            <button
              onClick={clearConversation}
              className="px-3 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 rounded hover:bg-gray-200 dark:hover:bg-gray-600"
            >
              {t('voice.clear_conversation', 'Clear')}
            </button>
          )}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
          <XMarkIcon className="w-5 h-5 text-red-500" />
          <span className="text-sm text-red-700 dark:text-red-300">{error}</span>
        </div>
      )}

      {/* Current Transcript */}
      {transcript && (
        <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border">
          <div className="flex items-start space-x-2">
            <SpeakerWaveIcon className="w-4 h-4 text-gray-500 dark:text-gray-400 mt-0.5 flex-shrink-0" />
            <div className="flex-1">
              <p className="text-sm text-gray-900 dark:text-white">{transcript}</p>
              {confidence > 0 && (
                <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400">
                  <span>{t('voice.confidence', 'Confidence')}: {Math.round(confidence * 100)}%</span>
                  {currentIntent && (
                    <span>Intent: {currentIntent.name} ({Math.round(currentIntent.confidence * 100)}%)</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* AI Response */}
      {aiResponse && (
        <div className="p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
          <div className="flex items-start space-x-2">
            <ChatBubbleLeftRightIcon className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" />
            <p className="text-sm text-blue-900 dark:text-blue-100">{aiResponse}</p>
          </div>
        </div>
      )}

      {/* Conversation History */}
      {mode === 'conversation' && conversationHistory.length > 0 && (
        <div className="max-h-64 overflow-y-auto space-y-2">
          {conversationHistory.slice(-6).map((entry, index) => (
            <div
              key={index}
              className={`p-2 rounded-lg text-sm ${
                entry.type === 'user'
                  ? 'bg-green-50 dark:bg-green-900/20 text-green-900 dark:text-green-100 ml-8'
                  : 'bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100 mr-8'
              }`}
            >
              <div className="flex items-start space-x-2">
                {entry.type === 'user' ? (
                  <UserIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
                ) : (
                  <CpuChipIcon className="w-4 h-4 mt-0.5 flex-shrink-0" />
                )}
                <span>{entry.message}</span>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default AdvancedVoiceRecognition;
