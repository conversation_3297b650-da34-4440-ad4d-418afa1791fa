import React, { useState, useEffect } from 'react';
import { 
  X, 
  CreditCard, 
  Smartphone, 
  DollarSign, 
  Receipt, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  Printer,
  Mail,
  MessageSquare,
  QrCode,
  Calculator,
  Percent
} from 'lucide-react';

interface PaymentModalProps {
  isOpen: boolean;
  onClose: () => void;
  orderData: {
    items: any[];
    subtotal: number;
    tax: number;
    total: number;
    tableId?: string;
    tableNumber?: number;
    guestCount?: number;
    serverName?: string;
  };
  onPaymentComplete: (paymentData: any) => void;
}

interface PaymentMethod {
  id: string;
  name: string;
  icon: React.ReactNode;
  type: 'cash' | 'card' | 'digital' | 'other';
  enabled: boolean;
  processingFee?: number;
}

const EnhancedPaymentModal: React.FC<PaymentModalProps> = ({
  isOpen,
  onClose,
  orderData,
  onPaymentComplete
}) => {
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('');
  const [tip, setTip] = useState(0);
  const [customTip, setCustomTip] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showReceiptOptions, setShowReceiptOptions] = useState(false);
  const [receiptOptions, setReceiptOptions] = useState({
    print: true,
    email: false,
    sms: false,
    digital: false
  });
  const [customerInfo, setCustomerInfo] = useState({
    email: '',
    phone: ''
  });
  const [splitPayment, setSplitPayment] = useState(false);
  const [splitAmounts, setSplitAmounts] = useState<{ method: string; amount: number }[]>([]);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [paymentResult, setPaymentResult] = useState<any>(null);

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'cash',
      name: 'Cash',
      icon: <DollarSign className="h-6 w-6" />,
      type: 'cash',
      enabled: true
    },
    {
      id: 'card',
      name: 'Credit/Debit Card',
      icon: <CreditCard className="h-6 w-6" />,
      type: 'card',
      enabled: true,
      processingFee: 0.029 // 2.9%
    },
    {
      id: 'apple_pay',
      name: 'Apple Pay',
      icon: <Smartphone className="h-6 w-6" />,
      type: 'digital',
      enabled: true,
      processingFee: 0.029
    },
    {
      id: 'google_pay',
      name: 'Google Pay',
      icon: <Smartphone className="h-6 w-6" />,
      type: 'digital',
      enabled: true,
      processingFee: 0.029
    },
    {
      id: 'tap_to_pay',
      name: 'Tap to Pay',
      icon: <QrCode className="h-6 w-6" />,
      type: 'digital',
      enabled: true,
      processingFee: 0.025
    }
  ];

  const tipPercentages = [15, 18, 20, 25];

  useEffect(() => {
    if (!isOpen) {
      resetModal();
    }
  }, [isOpen]);

  const resetModal = () => {
    setSelectedPaymentMethod('');
    setTip(0);
    setCustomTip('');
    setIsProcessing(false);
    setShowReceiptOptions(false);
    setPaymentSuccess(false);
    setPaymentResult(null);
    setSplitPayment(false);
    setSplitAmounts([]);
    setCustomerInfo({ email: '', phone: '' });
    setReceiptOptions({ print: true, email: false, sms: false, digital: false });
  };

  const calculateTipAmount = (percentage: number) => {
    return Math.round(orderData.subtotal * (percentage / 100) * 100) / 100;
  };

  const handleTipSelection = (percentage: number) => {
    const tipAmount = calculateTipAmount(percentage);
    setTip(tipAmount);
    setCustomTip(tipAmount.toString());
  };

  const handleCustomTipChange = (value: string) => {
    setCustomTip(value);
    const numValue = parseFloat(value) || 0;
    setTip(numValue);
  };

  const calculateProcessingFee = () => {
    const method = paymentMethods.find(m => m.id === selectedPaymentMethod);
    if (!method?.processingFee) return 0;
    return Math.round((orderData.total + tip) * method.processingFee * 100) / 100;
  };

  const getFinalTotal = () => {
    const processingFee = calculateProcessingFee();
    return orderData.total + tip + processingFee;
  };

  const processPayment = async () => {
    if (!selectedPaymentMethod) {
      alert('Please select a payment method');
      return;
    }

    setIsProcessing(true);

    try {
      const token = localStorage.getItem('authToken');
      const finalTotal = getFinalTotal();
      const processingFee = calculateProcessingFee();

      // Create payment data
      const paymentData = {
        orderId: `order_${Date.now()}`,
        tableId: orderData.tableId,
        tableNumber: orderData.tableNumber,
        items: orderData.items,
        subtotal: orderData.subtotal,
        tax: orderData.tax,
        tip: tip,
        processingFee: processingFee,
        total: finalTotal,
        paymentMethod: selectedPaymentMethod,
        guestCount: orderData.guestCount,
        serverName: orderData.serverName,
        customerInfo: customerInfo,
        receiptOptions: receiptOptions,
        timestamp: new Date().toISOString()
      };

      // Process payment based on method
      let paymentResponse;
      
      if (selectedPaymentMethod === 'cash') {
        // Cash payment - immediate success
        paymentResponse = {
          success: true,
          transactionId: `cash_${Date.now()}`,
          authCode: null,
          message: 'Cash payment received'
        };
      } else if (selectedPaymentMethod === 'card') {
        // Card payment via Stripe
        const stripeResponse = await fetch('http://localhost:4000/api/payments/stripe/create-intent', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            amount: finalTotal,
            order_id: paymentData.orderId
          })
        });
        
        if (stripeResponse.ok) {
          const stripeData = await stripeResponse.json();
          paymentResponse = {
            success: true,
            transactionId: stripeData.id,
            authCode: stripeData.client_secret,
            message: 'Card payment processed successfully'
          };
        } else {
          throw new Error('Card payment failed');
        }
      } else {
        // Digital wallet payments
        paymentResponse = {
          success: true,
          transactionId: `digital_${Date.now()}`,
          authCode: `AUTH${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          message: `${paymentMethods.find(m => m.id === selectedPaymentMethod)?.name} payment successful`
        };
      }

      if (paymentResponse.success) {
        // Create order in backend
        const orderResponse = await fetch('http://localhost:4000/api/orders', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            ...paymentData,
            transactionId: paymentResponse.transactionId,
            authCode: paymentResponse.authCode,
            status: 'completed'
          })
        });

        if (orderResponse.ok) {
          const completedOrder = await orderResponse.json();
          
          // Generate and handle receipt
          await handleReceiptGeneration(completedOrder, paymentResponse);
          
          setPaymentResult({
            ...paymentResponse,
            orderId: completedOrder.id,
            total: finalTotal
          });
          setPaymentSuccess(true);
          
          // Call completion callback after a delay to show success
          setTimeout(() => {
            onPaymentComplete({
              ...completedOrder,
              paymentResult: paymentResponse
            });
          }, 2000);
        } else {
          throw new Error('Failed to create order');
        }
      } else {
        throw new Error(paymentResponse.message || 'Payment failed');
      }
    } catch (error) {
      console.error('Payment error:', error);
      alert(`Payment failed: ${error.message}`);
    } finally {
      setIsProcessing(false);
    }
  };

  const handleReceiptGeneration = async (order: any, paymentResult: any) => {
    try {
      const token = localStorage.getItem('authToken');
      
      // Generate receipt
      const receiptData = {
        orderId: order.id,
        receiptOptions: receiptOptions,
        customerInfo: customerInfo,
        paymentResult: paymentResult
      };

      if (receiptOptions.print) {
        // Print receipt
        await fetch('http://localhost:4000/api/receipts/print', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptData)
        });
      }

      if (receiptOptions.email && customerInfo.email) {
        // Email receipt
        await fetch('http://localhost:4000/api/receipts/email', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptData)
        });
      }

      if (receiptOptions.sms && customerInfo.phone) {
        // SMS receipt
        await fetch('http://localhost:4000/api/receipts/sms', {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(receiptData)
        });
      }
    } catch (error) {
      console.error('Receipt generation error:', error);
    }
  };

  if (!isOpen) return null;

  if (paymentSuccess) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <div className="bg-white rounded-lg w-full max-w-md p-6 text-center">
          <div className="mb-6">
            <CheckCircle className="h-16 w-16 text-green-500 mx-auto mb-4" />
            <h2 className="text-2xl font-bold text-gray-900 mb-2">Payment Successful!</h2>
            <p className="text-gray-600">Order #{paymentResult?.orderId}</p>
          </div>
          
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="flex justify-between items-center mb-2">
              <span className="text-gray-600">Total Paid:</span>
              <span className="text-xl font-bold text-green-600">${paymentResult?.total?.toFixed(2)}</span>
            </div>
            <div className="flex justify-between items-center text-sm text-gray-500">
              <span>Transaction ID:</span>
              <span className="font-mono">{paymentResult?.transactionId}</span>
            </div>
          </div>

          <div className="space-y-3">
            {receiptOptions.print && (
              <div className="flex items-center justify-center text-green-600">
                <Printer className="h-4 w-4 mr-2" />
                <span className="text-sm">Receipt printed</span>
              </div>
            )}
            {receiptOptions.email && customerInfo.email && (
              <div className="flex items-center justify-center text-blue-600">
                <Mail className="h-4 w-4 mr-2" />
                <span className="text-sm">Receipt emailed to {customerInfo.email}</span>
              </div>
            )}
            {receiptOptions.sms && customerInfo.phone && (
              <div className="flex items-center justify-center text-purple-600">
                <MessageSquare className="h-4 w-4 mr-2" />
                <span className="text-sm">Receipt sent to {customerInfo.phone}</span>
              </div>
            )}
          </div>

          <button
            onClick={onClose}
            className="mt-6 w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700"
          >
            Continue
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Process Payment</h2>
            {orderData.tableNumber && (
              <p className="text-gray-600">Table {orderData.tableNumber} • {orderData.guestCount} guests</p>
            )}
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        <div className="p-6 space-y-6">
          {/* Order Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h3 className="font-medium text-gray-900 mb-3">Order Summary</h3>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Subtotal ({orderData.items.length} items):</span>
                <span>${orderData.subtotal.toFixed(2)}</span>
              </div>
              <div className="flex justify-between">
                <span>Tax:</span>
                <span>${orderData.tax.toFixed(2)}</span>
              </div>
              {tip > 0 && (
                <div className="flex justify-between">
                  <span>Tip:</span>
                  <span>${tip.toFixed(2)}</span>
                </div>
              )}
              {calculateProcessingFee() > 0 && (
                <div className="flex justify-between text-gray-500">
                  <span>Processing Fee:</span>
                  <span>${calculateProcessingFee().toFixed(2)}</span>
                </div>
              )}
              <div className="border-t border-gray-300 pt-2 flex justify-between font-semibold">
                <span>Total:</span>
                <span>${getFinalTotal().toFixed(2)}</span>
              </div>
            </div>
          </div>

          {/* Tip Selection */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3 flex items-center">
              <Percent className="h-4 w-4 mr-2" />
              Add Tip
            </h3>
            <div className="grid grid-cols-4 gap-2 mb-3">
              {tipPercentages.map(percentage => (
                <button
                  key={percentage}
                  onClick={() => handleTipSelection(percentage)}
                  className={`py-2 px-3 rounded-md text-sm font-medium ${
                    Math.abs(tip - calculateTipAmount(percentage)) < 0.01
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                  }`}
                >
                  {percentage}%
                </button>
              ))}
            </div>
            <div className="flex items-center space-x-2">
              <Calculator className="h-4 w-4 text-gray-400" />
              <input
                type="number"
                step="0.01"
                min="0"
                value={customTip}
                onChange={(e) => handleCustomTipChange(e.target.value)}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                placeholder="Custom tip amount"
              />
            </div>
          </div>

          {/* Payment Methods */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3">Payment Method</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {paymentMethods.filter(method => method.enabled).map(method => (
                <button
                  key={method.id}
                  onClick={() => setSelectedPaymentMethod(method.id)}
                  className={`p-4 rounded-lg border-2 transition-colors ${
                    selectedPaymentMethod === method.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <div className={`${selectedPaymentMethod === method.id ? 'text-blue-600' : 'text-gray-400'}`}>
                      {method.icon}
                    </div>
                    <div className="text-left">
                      <div className="font-medium text-gray-900">{method.name}</div>
                      {method.processingFee && (
                        <div className="text-xs text-gray-500">
                          {(method.processingFee * 100).toFixed(1)}% processing fee
                        </div>
                      )}
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Receipt Options */}
          <div>
            <h3 className="font-medium text-gray-900 mb-3 flex items-center">
              <Receipt className="h-4 w-4 mr-2" />
              Receipt Options
            </h3>
            <div className="space-y-3">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={receiptOptions.print}
                    onChange={(e) => setReceiptOptions(prev => ({ ...prev, print: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">Print</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={receiptOptions.email}
                    onChange={(e) => setReceiptOptions(prev => ({ ...prev, email: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">Email</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={receiptOptions.sms}
                    onChange={(e) => setReceiptOptions(prev => ({ ...prev, sms: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">SMS</span>
                </label>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={receiptOptions.digital}
                    onChange={(e) => setReceiptOptions(prev => ({ ...prev, digital: e.target.checked }))}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                  <span className="text-sm">Digital</span>
                </label>
              </div>

              {(receiptOptions.email || receiptOptions.sms) && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                  {receiptOptions.email && (
                    <input
                      type="email"
                      value={customerInfo.email}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, email: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Customer email"
                    />
                  )}
                  {receiptOptions.sms && (
                    <input
                      type="tel"
                      value={customerInfo.phone}
                      onChange={(e) => setCustomerInfo(prev => ({ ...prev, phone: e.target.value }))}
                      className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Customer phone"
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400"
            disabled={isProcessing}
          >
            Cancel
          </button>
          <button
            onClick={processPayment}
            disabled={!selectedPaymentMethod || isProcessing}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isProcessing ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processing...</span>
              </>
            ) : (
              <>
                <CheckCircle className="h-4 w-4" />
                <span>Process Payment ${getFinalTotal().toFixed(2)}</span>
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  );
};

export default EnhancedPaymentModal;
