import React from 'react';
import { QRCodeSVG } from 'qrcode.react';
import { Download } from 'lucide-react';

const QRCodeGenerator: React.FC = () => {
  const menuUrl = window.location.origin + '/menu';
  
  const downloadQRCode = () => {
    const svg = document.getElementById('qr-code');
    if (svg) {
      const svgData = new XMLSerializer().serializeToString(svg);
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      const img = new Image();
      
      img.onload = () => {
        canvas.width = img.width;
        canvas.height = img.height;
        ctx?.drawImage(img, 0, 0);
        const pngFile = canvas.toDataURL('image/png');
        
        const downloadLink = document.createElement('a');
        downloadLink.download = 'menu-qr-code.png';
        downloadLink.href = pngFile;
        downloadLink.click();
      };
      
      img.src = 'data:image/svg+xml;base64,' + btoa(svgData);
    }
  };

  return (
    <div className="p-6">
      <h2 className="text-xl font-semibold text-white mb-6">Menu QR Code</h2>
      
      <div className="bg-gray-800 rounded-lg p-6 max-w-md">
        <div className="bg-white p-4 rounded-lg mb-4">
          <QRCodeSVG
            id="qr-code"
            value={menuUrl}
            size={256}
            level="H"
            includeMargin={true}
          />
        </div>
        
        <div className="space-y-4">
          <p className="text-gray-300">
            Share this QR code with your customers to let them view your menu online.
          </p>
          
          <div className="flex flex-col space-y-2">
            <p className="text-sm text-gray-400">Menu URL:</p>
            <code className="bg-gray-700 px-3 py-2 rounded text-amber-400 break-all">
              {menuUrl}
            </code>
          </div>
          
          <button
            onClick={downloadQRCode}
            className="w-full bg-purple-600 hover:bg-purple-500 text-white py-2 px-4 rounded-lg flex items-center justify-center space-x-2 transition-colors"
          >
            <Download className="h-5 w-5" />
            <span>Download QR Code</span>
          </button>
        </div>
      </div>
    </div>
  );
};

export default QRCodeGenerator;