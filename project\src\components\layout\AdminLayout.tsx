import React, { useState } from 'react';
import { cn } from "@/lib/utils";
import { ThemeToggle } from '../ui/ThemeToggle';
import { useTheme, getThemeClasses } from '../../contexts/ThemeContext';
import {
  LayoutDashboard,
  Users,
  Settings,
  BarChart3,
  Shield,
  Bell,
  Menu,
  X,
  LogOut,
  User,
  MapPin,
  Activity,
  Database,
  CreditCard,
  Globe,
  Bot,
  Smartphone,
  Server,
  Brain,
  Target,
  TrendingUp,
  ExternalLink,
  DollarSign
} from "lucide-react";

const navigation = [
  { name: 'Dashboard', href: '#dashboard', icon: LayoutDashboard, current: true, category: 'main' },
  { name: 'Tenants', href: '#tenant-management', icon: Users, current: false, category: 'main' },
  { name: 'Multi-Location', href: '#multi-location', icon: MapPin, current: false, category: 'main' },
  { name: 'Analytics', href: '#analytics', icon: BarChart3, current: false, category: 'main' },
  { name: 'User Management', href: '#user-management', icon: Shield, current: false, category: 'main' },
  { name: 'System Health', href: '#system-health', icon: Activity, current: false, category: 'monitoring' },
  { name: 'Security Audit', href: '#security-audit', icon: Shield, current: false, category: 'phase2b', isNew: true },
  { name: 'Backup & Recovery', href: '#backup-recovery', icon: Database, current: false, category: 'phase2b', isNew: true },
  { name: 'API Management', href: '#api-management', icon: Settings, current: false, category: 'phase2b', isNew: true },
  { name: 'Performance Monitor', href: '#performance-monitor', icon: Activity, current: false, category: 'phase2c', isNew: true },
  { name: 'System Optimization', href: '#system-optimization', icon: BarChart3, current: false, category: 'phase2c', isNew: true },
  { name: 'Enterprise Integrations', href: '#enterprise-integrations', icon: Globe, current: false, category: 'phase2d', isNew: true },
  { name: 'Advanced Automation', href: '#advanced-automation', icon: Bot, current: false, category: 'phase2d', isNew: true },
  { name: 'Mobile & API', href: '#mobile-api-management', icon: Smartphone, current: false, category: 'phase2d', isNew: true },
  { name: 'Omnichannel', href: '#omnichannel-experience', icon: Users, current: false, category: 'phase2d', isNew: true },
  { name: 'Deployment', href: '#deployment-readiness', icon: Server, current: false, category: 'phase2d', isNew: true },
  { name: 'AI Analytics', href: '#ai-analytics-dashboard', icon: Brain, current: false, category: 'phase3', isNew: true },
  { name: 'Sales Forecasting', href: '#predictive-sales-forecasting', icon: TrendingUp, current: false, category: 'phase3', isNew: true },
  { name: 'Inventory AI', href: '#intelligent-inventory-optimization', icon: Target, current: false, category: 'phase3', isNew: true },
  { name: 'Customer AI', href: '#ai-customer-behavior-analysis', icon: Users, current: false, category: 'phase3', isNew: true },
  { name: 'Smart Pricing', href: '#smart-pricing-optimization', icon: DollarSign, current: false, category: 'phase3', isNew: true },
  { name: 'Staff Scheduling', href: '#automated-staff-scheduling', icon: Users, current: false, category: 'phase3', isNew: true },
  { name: 'Billing & Plans', href: '#billing-plans', icon: CreditCard, current: false, category: 'admin' },
  { name: 'Notifications', href: '#notifications', icon: Bell, current: false, category: 'admin' },
  { name: 'Settings', href: '#settings', icon: Settings, current: false, category: 'admin' },
];

interface AdminLayoutProps {
  children: React.ReactNode;
}

// Safe theme hook with fallback
function useSafeTheme() {
  try {
    return useTheme();
  } catch (error) {
    console.warn('ThemeProvider not found, using default theme');
    return { theme: 'light' as const, toggleTheme: () => {}, setTheme: () => {} };
  }
}

export function AdminLayout({ children }: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const { theme } = useSafeTheme();
  const themeClasses = getThemeClasses(theme);
  const [currentPath, setCurrentPath] = useState(window.location.hash.replace('#', '') || 'dashboard');

  // Update current path when hash changes
  React.useEffect(() => {
    const handleHashChange = () => {
      const hash = window.location.hash.replace('#', '') || 'dashboard';
      setCurrentPath(hash);
    };

    window.addEventListener('hashchange', handleHashChange);
    return () => window.removeEventListener('hashchange', handleHashChange);
  }, []);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Sidebar */}
      <div className={cn(
        "fixed inset-y-0 left-0 z-50 bg-white border-r border-gray-200 transition-all duration-300 shadow-sm",
        sidebarOpen ? "w-64" : "w-16"
      )}>
        <div className="flex flex-col h-full">
          {/* Logo */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200">
            {sidebarOpen && (
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg flex items-center justify-center">
                  <Shield className="h-5 w-5 text-white" />
                </div>
                <h1 className="text-xl font-bold text-gray-900">Super Admin</h1>
              </div>
            )}
            <button
              onClick={() => setSidebarOpen(!sidebarOpen)}
              className="p-2 rounded-md text-gray-400 hover:text-gray-600 hover:bg-gray-100 transition-colors"
            >
              {sidebarOpen ? <X className="h-4 w-4" /> : <Menu className="h-4 w-4" />}
            </button>
          </div>
          
          {/* Navigation */}
          <nav className="flex-1 px-2 py-4 space-y-1">
            {navigation.map((item) => {
              const isActive = currentPath === item.href.replace('#', '');
              return (
                <a
                  key={item.name}
                  href={item.href}
                  className={cn(
                    "flex items-center justify-between px-3 py-2 text-sm font-medium rounded-md transition-all duration-200 relative",
                    isActive
                      ? "bg-blue-50 text-blue-700 border-r-2 border-blue-700"
                      : "text-gray-600 hover:text-gray-900 hover:bg-gray-50",
                    item.category === 'phase2b' && "hover:bg-gradient-to-r hover:from-blue-50 hover:to-purple-50",
                    item.category === 'phase2c' && "hover:bg-gradient-to-r hover:from-green-50 hover:to-blue-50",
                    item.category === 'phase2d' && "hover:bg-gradient-to-r hover:from-purple-50 hover:to-pink-50",
                    item.category === 'phase3' && "hover:bg-gradient-to-r hover:from-cyan-50 hover:to-emerald-50"
                  )}
                >
                <div className="flex items-center">
                  <item.icon className={cn(
                    "flex-shrink-0",
                    sidebarOpen ? "h-5 w-5 mr-3" : "h-5 w-5",
                    item.category === 'phase2b' && "text-blue-600",
                    item.category === 'phase2c' && "text-green-600",
                    item.category === 'phase2d' && "text-purple-600",
                    item.category === 'phase3' && "text-cyan-600"
                  )} />
                  {sidebarOpen && (
                    <span className={cn(
                      item.category === 'phase2b' && "font-semibold",
                      item.category === 'phase2c' && "font-semibold text-green-700",
                      item.category === 'phase2d' && "font-semibold text-purple-700",
                      item.category === 'phase3' && "font-semibold text-cyan-700"
                    )}>
                      {item.name}
                    </span>
                  )}
                </div>
                {sidebarOpen && item.isNew && (
                  <span className={cn(
                    "inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium text-white",
                    item.category === 'phase2b' && "bg-gradient-to-r from-blue-500 to-purple-500",
                    item.category === 'phase2c' && "bg-gradient-to-r from-green-500 to-blue-500",
                    item.category === 'phase2d' && "bg-gradient-to-r from-purple-500 to-pink-500",
                    item.category === 'phase3' && "bg-gradient-to-r from-cyan-500 to-emerald-500"
                  )}>
                    {item.category === 'phase2c' ? 'PHASE 2C' :
                     item.category === 'phase2d' ? 'PHASE 2D' :
                     item.category === 'phase3' ? 'PHASE 3' : 'NEW'}
                  </span>
                )}
                {!sidebarOpen && item.isNew && (
                  <div className={cn(
                    "absolute -top-1 -right-1 w-3 h-3 rounded-full",
                    item.category === 'phase2b' && "bg-gradient-to-r from-blue-500 to-purple-500",
                    item.category === 'phase2c' && "bg-gradient-to-r from-green-500 to-blue-500",
                    item.category === 'phase2d' && "bg-gradient-to-r from-purple-500 to-pink-500",
                    item.category === 'phase3' && "bg-gradient-to-r from-cyan-500 to-emerald-500"
                  )}></div>
                )}
              </a>
            );
            })}
          </nav>

          {/* User Profile */}
          <div className="border-t border-gray-200 p-4">
            <div className={cn(
              "flex items-center",
              sidebarOpen ? "space-x-3" : "justify-center"
            )}>
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                <User className="h-4 w-4 text-gray-600" />
              </div>
              {sidebarOpen && (
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    Enhanced Admin
                  </p>
                  <p className="text-xs text-gray-500 truncate">
                    Super Administrator
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main content */}
      <div className={cn(
        "transition-all duration-300",
        sidebarOpen ? "ml-64" : "ml-16"
      )}>
        {/* Header */}
        <header className="bg-white border-b border-gray-200 px-6 py-4 shadow-sm">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Dashboard</h1>
              <p className="text-sm text-gray-500 mt-1">
                Welcome back, manage your multi-tenant POS system
              </p>
            </div>
            <div className="flex items-center space-x-4">
              {/* Super Admin Dashboard Link */}
              <a
                href="/super-admin"
                className="flex items-center space-x-2 bg-gradient-to-r from-purple-600 to-blue-600 text-white px-3 py-2 rounded-lg text-sm font-medium hover:from-purple-700 hover:to-blue-700 transition-all duration-200"
                title="Modern Super Admin Dashboard"
              >
                <Shield className="h-4 w-4" />
                <span className="hidden sm:inline">Super Admin</span>
                <ExternalLink className="h-3 w-3" />
              </a>

              {/* Theme Toggle */}
              <ThemeToggle />

              <button className="relative p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-md transition-colors">
                <Bell className="h-5 w-5" />
                <span className="absolute top-1 right-1 w-2 h-2 bg-red-500 rounded-full"></span>
              </button>
              <button
                onClick={() => window.location.reload()}
                className="flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors"
              >
                <LogOut className="h-4 w-4 mr-2" />
                Logout
              </button>
            </div>
          </div>
        </header>

        {/* Page content */}
        <main className="p-6">
          {children}
        </main>
      </div>
    </div>
  );
}
