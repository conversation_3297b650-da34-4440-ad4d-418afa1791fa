import React, { useState, useEffect } from 'react';
import { sessionManager } from '../utils/SessionManager';
import UnifiedAuthenticationSystem from './UnifiedAuthenticationSystem';
import { 
  Shield, 
  Lock, 
  Database, 
  CheckCircle, 
  AlertTriangle,
  Wifi,
  WifiOff,
  Server
} from 'lucide-react';

interface ProductionAuthSystemProps {
  onAuthenticationComplete: (userType: 'employee' | 'admin') => void;
}

const ProductionAuthSystem: React.FC<ProductionAuthSystemProps> = ({
  onAuthenticationComplete
}) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userType, setUserType] = useState<'employee' | 'admin'>('employee');
  const [systemStatus, setSystemStatus] = useState({
    database: 'checking' as 'connected' | 'disconnected' | 'checking',
    backend: 'checking' as 'connected' | 'disconnected' | 'checking',
    security: 'maximum' as 'maximum' | 'high' | 'medium'
  });
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    checkSystemHealth();
    
    // Check system health every 30 seconds
    const healthInterval = setInterval(checkSystemHealth, 30000);
    
    return () => clearInterval(healthInterval);
  }, []);

  const checkSystemHealth = async () => {
    // Check backend connectivity
    try {
      const backendResponse = await fetch('http://localhost:4000/api/health', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' }
      });

      if (backendResponse.ok) {
        setSystemStatus(prev => ({ ...prev, backend: 'connected' }));
      } else {
        setSystemStatus(prev => ({ ...prev, backend: 'disconnected' }));
      }
    } catch (error) {
      setSystemStatus(prev => ({ ...prev, backend: 'disconnected' }));
    }

    // Check database connectivity (if authenticated)
    const session = sessionManager.getSession();
    if (session?.token) {
      try {
        const dbResponse = await fetch('http://localhost:4000/api/admin/health/database', {
          headers: {
            'Authorization': `Bearer ${session.token}`,
            'Content-Type': 'application/json'
          }
        });

        if (dbResponse.ok) {
          setSystemStatus(prev => ({ ...prev, database: 'connected' }));
        } else {
          setSystemStatus(prev => ({ ...prev, database: 'disconnected' }));
        }
      } catch (error) {
        setSystemStatus(prev => ({ ...prev, database: 'disconnected' }));
      }
    }

    // Set security level based on system status
    const securityLevel = 
      systemStatus.backend === 'connected' && systemStatus.database === 'connected' 
        ? 'maximum' 
        : systemStatus.backend === 'connected' 
        ? 'high' 
        : 'medium';
    
    setSystemStatus(prev => ({ ...prev, security: securityLevel }));
  };

  const handleAuthenticationSuccess = (type: 'employee' | 'admin') => {
    setIsAuthenticated(true);
    setUserType(type);
    
    // Perform final security checks
    performSecurityValidation();
    
    onAuthenticationComplete(type);
  };

  const performSecurityValidation = async () => {
    const session = sessionManager.getSession();
    if (!session) return;

    try {
      // Validate session with backend
      const response = await fetch('http://localhost:4000/api/auth/verify', {
        headers: {
          'Authorization': `Bearer ${session.token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        console.error('Security validation failed');
        sessionManager.logout();
        setIsAuthenticated(false);
        return;
      }

      // Log security event (production logging)
      const securityEvent = {
        timestamp: new Date().toISOString(),
        event: 'authentication_success',
        user_id: session.employee.id,
        user_role: session.employee.role,
        tenant_id: session.tenant.id,
        ip_address: 'client', // Would be actual IP in production
        user_agent: navigator.userAgent.substring(0, 100),
        admin_access: session.adminAccess || false
      };

      // Send security log to backend (in production)
      fetch('http://localhost:4000/api/security/log', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${session.token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(securityEvent)
      }).catch(() => {
        // Silent fail for security logging
      });

    } catch (error) {
      console.error('Security validation error:', error);
    }
  };

  const getSecurityIndicatorColor = () => {
    switch (systemStatus.security) {
      case 'maximum':
        return 'text-green-600 dark:text-green-400';
      case 'high':
        return 'text-yellow-600 dark:text-yellow-400';
      case 'medium':
        return 'text-red-600 dark:text-red-400';
      default:
        return 'text-gray-600 dark:text-gray-400';
    }
  };

  const getSecurityBadgeColor = () => {
    switch (systemStatus.security) {
      case 'maximum':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'high':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'medium':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  if (isAuthenticated) {
    return null; // Let parent component handle authenticated state
  }

  return (
    <div className="relative">
      
      {/* System Status Indicators */}
      <div className="fixed top-4 left-4 space-y-2 z-50">
        
        {/* Security Level */}
        <div className={`px-3 py-2 rounded-lg text-sm font-medium shadow-lg ${getSecurityBadgeColor()}`}>
          <div className="flex items-center space-x-2">
            <Shield className={`w-4 h-4 ${getSecurityIndicatorColor()}`} />
            <span>
              {systemStatus.security === 'maximum' ? 'Maximum Security' : 
               systemStatus.security === 'high' ? 'High Security' : 'Standard Security'}
            </span>
          </div>
        </div>

        {/* Backend Status */}
        <div className={`px-3 py-1 rounded-lg text-xs font-medium shadow-md ${
          systemStatus.backend === 'connected'
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : systemStatus.backend === 'disconnected'
            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
        }`}>
          <div className="flex items-center space-x-1">
            {systemStatus.backend === 'connected' ? (
              <Wifi className="w-3 h-3" />
            ) : systemStatus.backend === 'disconnected' ? (
              <WifiOff className="w-3 h-3" />
            ) : (
              <Server className="w-3 h-3 animate-pulse" />
            )}
            <span>
              {systemStatus.backend === 'connected' ? 'Backend Online' : 
               systemStatus.backend === 'disconnected' ? 'Backend Offline' : 'Connecting...'}
            </span>
          </div>
        </div>

        {/* Database Status */}
        <div className={`px-3 py-1 rounded-lg text-xs font-medium shadow-md ${
          systemStatus.database === 'connected'
            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
            : systemStatus.database === 'disconnected'
            ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
            : 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200'
        }`}>
          <div className="flex items-center space-x-1">
            <Database className="w-3 h-3" />
            <span>
              {systemStatus.database === 'connected' ? 'DB Connected' : 
               systemStatus.database === 'disconnected' ? 'DB Offline' : 'DB Status Unknown'}
            </span>
          </div>
        </div>
      </div>

      {/* Security Warnings */}
      {systemStatus.security !== 'maximum' && (
        <div className="fixed bottom-4 left-4 right-4 z-50">
          <div className={`p-4 rounded-lg shadow-lg ${
            isDarkMode 
              ? 'bg-yellow-900/90 border border-yellow-700' 
              : 'bg-yellow-50 border border-yellow-200'
          }`}>
            <div className="flex items-center space-x-3">
              <AlertTriangle className={`w-6 h-6 ${
                isDarkMode ? 'text-yellow-400' : 'text-yellow-600'
              }`} />
              <div>
                <h3 className={`font-medium ${
                  isDarkMode ? 'text-yellow-300' : 'text-yellow-800'
                }`}>
                  Security Notice
                </h3>
                <p className={`text-sm ${
                  isDarkMode ? 'text-yellow-400' : 'text-yellow-700'
                }`}>
                  {systemStatus.backend === 'disconnected' 
                    ? 'Backend server connection failed. Please check your network connection.'
                    : systemStatus.database === 'disconnected'
                    ? 'Database connection unavailable. Some features may be limited.'
                    : 'System operating in reduced security mode.'}
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Data Isolation Notice */}
      <div className="fixed bottom-4 right-4 z-50">
        <div className={`px-4 py-2 rounded-lg text-xs font-medium shadow-lg ${
          isDarkMode 
            ? 'bg-blue-900/90 border border-blue-700 text-blue-300' 
            : 'bg-blue-50 border border-blue-200 text-blue-700'
        }`}>
          <div className="flex items-center space-x-2">
            <Lock className="w-3 h-3" />
            <span>Tenant Data Isolation Active</span>
          </div>
        </div>
      </div>

      {/* Main Authentication Interface */}
      <UnifiedAuthenticationSystem
        onAuthenticationSuccess={handleAuthenticationSuccess}
        defaultMode="employee"
      />

      {/* Production Footer */}
      <div className="fixed bottom-0 left-0 right-0 z-40">
        <div className={`text-center py-2 text-xs ${
          isDarkMode ? 'text-gray-500' : 'text-gray-400'
        }`}>
          <div className="flex items-center justify-center space-x-4">
            <span>RestroFlow POS System</span>
            <span>•</span>
            <span>© 2024 RestroFlow. All rights reserved.</span>
            <span>•</span>
            <div className="flex items-center space-x-1">
              <CheckCircle className="w-3 h-3 text-green-500" />
              <span>Secure Authentication</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductionAuthSystem;
