const { Pool } = require('pg');

console.log('🔍 PostgreSQL Connection Diagnostic Tool');
console.log('==========================================');

// Test connection with exact same parameters as server
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
});

async function diagnoseConnection() {
  console.log('\n📋 Connection Parameters:');
  console.log('  User: BARPOS');
  console.log('  Host: localhost');
  console.log('  Database: BARPOS');
  console.log('  Port: 5432');
  console.log('  Password: [PROTECTED]');
  
  try {
    console.log('\n🔄 Attempting to connect to PostgreSQL...');
    const client = await pool.connect();
    console.log('✅ Successfully connected to PostgreSQL!');
    
    // Test basic query
    console.log('\n🧪 Testing basic query...');
    const result = await client.query('SELECT NOW() as current_time, current_database(), current_user');
    console.log('✅ Query successful!');
    console.log('  Current Time:', result.rows[0].current_time);
    console.log('  Database:', result.rows[0].current_database);
    console.log('  User:', result.rows[0].current_user);
    
    // Test table existence
    console.log('\n📋 Checking for required tables...');
    const tablesQuery = `
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name
    `;
    const tablesResult = await client.query(tablesQuery);
    
    console.log('✅ Available tables:');
    const requiredTables = ['tenants', 'users', 'transactions', 'security_audits', 'ai_analytics', 'system_activity'];
    const existingTables = tablesResult.rows.map(row => row.table_name);
    
    requiredTables.forEach(table => {
      if (existingTables.includes(table)) {
        console.log(`  ✅ ${table}`);
      } else {
        console.log(`  ❌ ${table} (MISSING)`);
      }
    });
    
    // Test a sample query from the server
    console.log('\n🧪 Testing server-style query...');
    try {
      const testQuery = 'SELECT COUNT(*) as total_tenants FROM tenants';
      const testResult = await client.query(testQuery);
      console.log('✅ Tenants query successful:', testResult.rows[0].total_tenants, 'tenants found');
    } catch (error) {
      console.log('❌ Tenants query failed:', error.message);
    }
    
    client.release();
    console.log('\n🎉 Database connection diagnostic completed successfully!');
    console.log('✅ PostgreSQL is properly configured and accessible');
    
  } catch (error) {
    console.error('\n❌ Database connection failed!');
    console.error('Error details:', error.message);
    console.error('Error code:', error.code);
    
    if (error.code === 'ECONNREFUSED') {
      console.error('\n🔧 Troubleshooting suggestions:');
      console.error('  1. Check if PostgreSQL service is running');
      console.error('  2. Verify PostgreSQL is listening on port 5432');
      console.error('  3. Check firewall settings');
    } else if (error.code === '28P01') {
      console.error('\n🔧 Authentication failed:');
      console.error('  1. Verify username and password');
      console.error('  2. Check pg_hba.conf authentication settings');
    } else if (error.code === '3D000') {
      console.error('\n🔧 Database does not exist:');
      console.error('  1. Create the BARPOS database');
      console.error('  2. Verify database name spelling');
    }
  } finally {
    await pool.end();
    process.exit(0);
  }
}

// Handle process termination
process.on('SIGINT', async () => {
  console.log('\n🛑 Diagnostic interrupted');
  await pool.end();
  process.exit(0);
});

diagnoseConnection();
