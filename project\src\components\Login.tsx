import React, { useState, useEffect } from 'react';
import { useAppContext } from '../context/AppContext';
import { User, LogOut, AlertCircle } from 'lucide-react';

const Login: React.FC = () => {
  const { state, dispatch, validateEmployeePin } = useAppContext();
  const [pin, setPin] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [rememberSession, setRememberSession] = useState(false);

  // Auto-logout timer
  useEffect(() => {
    if (state.isAuthenticated) {
      const logoutTimer = setTimeout(() => {
        dispatch({ type: 'LOGOUT' });
      }, 30 * 60 * 1000); // 30 minutes

      return () => clearTimeout(logoutTimer);
    }
  }, [state.isAuthenticated, dispatch]);

  const handlePinChange = (value: string) => {
    if (value.length <= 4) {
      setPin(value);
      setError('');
    }
  };

  const handleLogin = async () => {
    if (pin.length !== 4) {
      setError('PIN must be 4 digits');
      return;
    }

    setIsLoading(true);
    try {
      const employee = await validateEmployeePin(pin);
      if (employee) {
        dispatch({ type: 'LOGIN', payload: employee });
        setPin('');
        // Store session if remember is checked
        if (rememberSession) {
          localStorage.setItem('sessionEmployee', JSON.stringify(employee));
        }
      } else {
        setError('Invalid PIN. Please try again.');
      }
    } catch (err) {
      setError('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('sessionEmployee');
    dispatch({ type: 'LOGOUT' });
  };

  const handleKeypad = (num: string) => {
    if (pin.length < 4) {
      handlePinChange(pin + num);
    }
  };

  const handleClear = () => {
    handlePinChange('');
  };

  if (state.isAuthenticated) {
    return (
      <div className="bg-gradient-to-r from-slate-800 to-slate-900 rounded-lg p-4 text-slate-50 shadow-xl">
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className="bg-indigo-600 p-2 rounded-full">
              <User className="h-6 w-6" />
            </div>
            <div className="ml-3">
              <p className="font-medium">{state.currentEmployee?.name}</p>
              <p className="text-sm text-indigo-400">{state.currentEmployee?.role}</p>
            </div>
          </div>
          <button 
            onClick={handleLogout}
            className="bg-slate-700 hover:bg-slate-600 rounded-full p-2 transition-all duration-200 transform hover:scale-105"
          >
            <LogOut className="h-5 w-5 text-red-500" />
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center bg-gradient-to-b from-slate-800 to-slate-900 rounded-lg p-8 w-full max-w-md mx-auto text-slate-50 shadow-2xl">
      {/* Logo/Brand Section */}
      <div className="w-20 h-20 bg-indigo-600 rounded-full mb-6 flex items-center justify-center">
        <User className="h-12 w-12" />
      </div>
      
      <h2 className="text-2xl font-bold mb-8 text-center">Employee Login</h2>
      
      <div className="w-full mb-8">
        <div className="bg-slate-700/50 px-4 py-3 rounded-lg text-center backdrop-blur-sm">
          <span className="text-2xl tracking-wider font-mono">
            {pin ? '•'.repeat(pin.length) + '◦'.repeat(4 - pin.length) : 'Enter PIN'}
          </span>
        </div>
        {error && (
          <div className="flex items-center justify-center mt-2 text-red-500 text-sm">
            <AlertCircle className="h-4 w-4 mr-1" />
            {error}
          </div>
        )}
      </div>
      
      <div className="grid grid-cols-3 gap-4 mb-6 w-full">
        {[1, 2, 3, 4, 5, 6, 7, 8, 9].map((num) => (
          <button
            key={num}
            className="bg-slate-700/50 hover:bg-indigo-600 text-slate-50 py-4 rounded-lg text-2xl font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
            onClick={() => handleKeypad(num.toString())}
            disabled={isLoading}
          >
            {num}
          </button>
        ))}
        <button
          className="bg-slate-700/50 hover:bg-slate-600 text-slate-50 py-4 rounded-lg text-xl font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
          onClick={handleClear}
          disabled={isLoading}
        >
          Clear
        </button>
        <button
          className="bg-slate-700/50 hover:bg-slate-600 text-slate-50 py-4 rounded-lg text-xl font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed backdrop-blur-sm"
          onClick={() => handleKeypad('0')}
          disabled={isLoading}
        >
          0
        </button>
        <button
          className="bg-indigo-600 hover:bg-indigo-500 text-slate-50 py-4 rounded-lg text-xl font-semibold transition-all duration-200 transform hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
          onClick={handleLogin}
          disabled={isLoading || !pin}
        >
          {isLoading ? (
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-slate-50" />
          ) : (
            'Enter'
          )}
        </button>
      </div>
      
      <div className="flex items-center mt-2">
        <input
          type="checkbox"
          id="remember"
          checked={rememberSession}
          onChange={(e) => setRememberSession(e.target.checked)}
          className="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-slate-600 rounded"
        />
        <label htmlFor="remember" className="ml-2 text-sm text-slate-400">
          Remember session
        </label>
      </div>
    </div>
  );
};

export default Login;
