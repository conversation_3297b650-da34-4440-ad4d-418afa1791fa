// Phase 3H: Multi-Currency Support System
// Real-time Exchange Rates & International Payment Processing

import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Globe, 
  TrendingUp, 
  TrendingDown,
  RefreshCw, 
  CreditCard, 
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  Shield,
  Settings,
  Eye,
  ArrowUpDown,
  Banknote,
  Calculator,
  PieChart,
  Activity
} from 'lucide-react';

// Enhanced Currency Interface
interface Currency {
  code: string;
  name: string;
  symbol: string;
  flag: string;
  exchangeRate: number;
  previousRate: number;
  change: number;
  changePercent: number;
  lastUpdated: number;
  isBaseCurrency: boolean;
  isActive: boolean;
  region: string;
  volatility: 'low' | 'medium' | 'high';
}

// Multi-Currency Transaction Interface
interface MultiCurrencyTransaction {
  id: string;
  transactionId: string;
  originalAmount: number;
  originalCurrency: string;
  convertedAmount: number;
  convertedCurrency: string;
  exchangeRate: number;
  exchangeFee: number;
  timestamp: number;
  paymentMethod: string;
  status: 'pending' | 'completed' | 'failed' | 'refunded';
  customerLocation: string;
  riskScore: number;
}

// Currency Analytics Interface
interface CurrencyAnalytics {
  totalRevenue: { [currency: string]: number };
  transactionCount: { [currency: string]: number };
  averageOrderValue: { [currency: string]: number };
  exchangeRateImpact: number;
  hedgingRecommendations: string[];
  riskAssessment: {
    level: 'low' | 'medium' | 'high';
    factors: string[];
    mitigation: string[];
  };
  forecastData: {
    currency: string;
    predictedRate: number;
    confidence: number;
    timeframe: string;
  }[];
}

// Exchange Rate Provider Interface
interface ExchangeRateProvider {
  name: string;
  status: 'active' | 'inactive' | 'error';
  lastUpdate: number;
  reliability: number;
  latency: number;
  cost: number;
}

const Phase3HMultiCurrencySupport: React.FC = () => {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [transactions, setTransactions] = useState<MultiCurrencyTransaction[]>([]);
  const [analytics, setAnalytics] = useState<CurrencyAnalytics | null>(null);
  const [providers, setProviders] = useState<ExchangeRateProvider[]>([]);
  const [currentTime, setCurrentTime] = useState(new Date());
  const [activeView, setActiveView] = useState<'overview' | 'rates' | 'transactions' | 'analytics' | 'settings'>('overview');
  const [baseCurrency, setBaseCurrency] = useState('USD');
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [loading, setLoading] = useState(false);

  // Initialize multi-currency data
  useEffect(() => {
    loadMultiCurrencyData();
    const interval = setInterval(() => {
      setCurrentTime(new Date());
      if (autoRefresh) {
        loadMultiCurrencyData();
      }
    }, 30000); // Update every 30 seconds for real-time rates

    return () => clearInterval(interval);
  }, [autoRefresh, baseCurrency]);

  const loadMultiCurrencyData = async () => {
    setLoading(true);
    
    // Mock currency data with real-time exchange rates
    const mockCurrencies: Currency[] = [
      {
        code: 'USD',
        name: 'US Dollar',
        symbol: '$',
        flag: '🇺🇸',
        exchangeRate: 1.0000,
        previousRate: 1.0000,
        change: 0.0000,
        changePercent: 0.00,
        lastUpdated: Date.now(),
        isBaseCurrency: true,
        isActive: true,
        region: 'North America',
        volatility: 'low'
      },
      {
        code: 'EUR',
        name: 'Euro',
        symbol: '€',
        flag: '🇪🇺',
        exchangeRate: 0.8456,
        previousRate: 0.8423,
        change: 0.0033,
        changePercent: 0.39,
        lastUpdated: Date.now() - 15000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Europe',
        volatility: 'low'
      },
      {
        code: 'GBP',
        name: 'British Pound',
        symbol: '£',
        flag: '🇬🇧',
        exchangeRate: 0.7234,
        previousRate: 0.7198,
        change: 0.0036,
        changePercent: 0.50,
        lastUpdated: Date.now() - 8000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Europe',
        volatility: 'medium'
      },
      {
        code: 'CAD',
        name: 'Canadian Dollar',
        symbol: 'C$',
        flag: '🇨🇦',
        exchangeRate: 1.3567,
        previousRate: 1.3542,
        change: 0.0025,
        changePercent: 0.18,
        lastUpdated: Date.now() - 12000,
        isBaseCurrency: false,
        isActive: true,
        region: 'North America',
        volatility: 'low'
      },
      {
        code: 'JPY',
        name: 'Japanese Yen',
        symbol: '¥',
        flag: '🇯🇵',
        exchangeRate: 149.85,
        previousRate: 150.12,
        change: -0.27,
        changePercent: -0.18,
        lastUpdated: Date.now() - 5000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Asia',
        volatility: 'medium'
      },
      {
        code: 'AUD',
        name: 'Australian Dollar',
        symbol: 'A$',
        flag: '🇦🇺',
        exchangeRate: 1.5234,
        previousRate: 1.5198,
        change: 0.0036,
        changePercent: 0.24,
        lastUpdated: Date.now() - 18000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Oceania',
        volatility: 'medium'
      },
      {
        code: 'CHF',
        name: 'Swiss Franc',
        symbol: 'Fr',
        flag: '🇨🇭',
        exchangeRate: 0.8923,
        previousRate: 0.8945,
        change: -0.0022,
        changePercent: -0.25,
        lastUpdated: Date.now() - 22000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Europe',
        volatility: 'low'
      },
      {
        code: 'CNY',
        name: 'Chinese Yuan',
        symbol: '¥',
        flag: '🇨🇳',
        exchangeRate: 7.2456,
        previousRate: 7.2389,
        change: 0.0067,
        changePercent: 0.09,
        lastUpdated: Date.now() - 25000,
        isBaseCurrency: false,
        isActive: true,
        region: 'Asia',
        volatility: 'medium'
      }
    ];

    setCurrencies(mockCurrencies);

    // Mock multi-currency transactions
    const mockTransactions: MultiCurrencyTransaction[] = [
      {
        id: 'tx-001',
        transactionId: 'TXN-2024-001247',
        originalAmount: 45.50,
        originalCurrency: 'EUR',
        convertedAmount: 53.82,
        convertedCurrency: 'USD',
        exchangeRate: 1.1829,
        exchangeFee: 0.32,
        timestamp: Date.now() - 300000,
        paymentMethod: 'Credit Card',
        status: 'completed',
        customerLocation: 'Paris, France',
        riskScore: 0.15
      },
      {
        id: 'tx-002',
        transactionId: 'TXN-2024-001248',
        originalAmount: 78.25,
        originalCurrency: 'GBP',
        convertedAmount: 108.15,
        convertedCurrency: 'USD',
        exchangeRate: 1.3823,
        exchangeFee: 0.54,
        timestamp: Date.now() - 180000,
        paymentMethod: 'Digital Wallet',
        status: 'completed',
        customerLocation: 'London, UK',
        riskScore: 0.08
      },
      {
        id: 'tx-003',
        transactionId: 'TXN-2024-001249',
        originalAmount: 12500,
        originalCurrency: 'JPY',
        convertedAmount: 83.42,
        convertedCurrency: 'USD',
        exchangeRate: 0.00668,
        exchangeFee: 0.42,
        timestamp: Date.now() - 120000,
        paymentMethod: 'Mobile Payment',
        status: 'completed',
        customerLocation: 'Tokyo, Japan',
        riskScore: 0.12
      },
      {
        id: 'tx-004',
        transactionId: 'TXN-2024-001250',
        originalAmount: 95.75,
        originalCurrency: 'CAD',
        convertedAmount: 70.58,
        convertedCurrency: 'USD',
        exchangeRate: 0.7369,
        exchangeFee: 0.35,
        timestamp: Date.now() - 60000,
        paymentMethod: 'Contactless',
        status: 'pending',
        customerLocation: 'Toronto, Canada',
        riskScore: 0.05
      }
    ];

    setTransactions(mockTransactions);

    // Mock currency analytics
    const mockAnalytics: CurrencyAnalytics = {
      totalRevenue: {
        'USD': 15420.75,
        'EUR': 8945.32,
        'GBP': 6234.18,
        'CAD': 4567.89,
        'JPY': 892345,
        'AUD': 3456.78,
        'CHF': 2345.67,
        'CNY': 45678.90
      },
      transactionCount: {
        'USD': 156,
        'EUR': 89,
        'GBP': 67,
        'CAD': 45,
        'JPY': 78,
        'AUD': 34,
        'CHF': 23,
        'CNY': 56
      },
      averageOrderValue: {
        'USD': 98.85,
        'EUR': 100.51,
        'GBP': 93.04,
        'CAD': 101.51,
        'JPY': 11440.32,
        'AUD': 101.67,
        'CHF': 101.99,
        'CNY': 815.34
      },
      exchangeRateImpact: 2.34,
      hedgingRecommendations: [
        'Consider hedging EUR exposure due to ECB policy uncertainty',
        'GBP volatility suggests short-term hedging strategy',
        'JPY showing stability - maintain current exposure'
      ],
      riskAssessment: {
        level: 'medium',
        factors: [
          'Increased EUR volatility',
          'GBP political uncertainty',
          'Global inflation concerns'
        ],
        mitigation: [
          'Implement dynamic hedging strategy',
          'Diversify payment processor exposure',
          'Monitor real-time rate fluctuations'
        ]
      },
      forecastData: [
        { currency: 'EUR', predictedRate: 0.8523, confidence: 87.5, timeframe: '7 days' },
        { currency: 'GBP', predictedRate: 0.7298, confidence: 82.3, timeframe: '7 days' },
        { currency: 'JPY', predictedRate: 148.95, confidence: 91.2, timeframe: '7 days' },
        { currency: 'CAD', predictedRate: 1.3612, confidence: 89.7, timeframe: '7 days' }
      ]
    };

    setAnalytics(mockAnalytics);

    // Mock exchange rate providers
    const mockProviders: ExchangeRateProvider[] = [
      {
        name: 'XE Currency API',
        status: 'active',
        lastUpdate: Date.now() - 15000,
        reliability: 99.8,
        latency: 120,
        cost: 0.001
      },
      {
        name: 'Fixer.io',
        status: 'active',
        lastUpdate: Date.now() - 8000,
        reliability: 99.5,
        latency: 95,
        cost: 0.0008
      },
      {
        name: 'CurrencyLayer',
        status: 'active',
        lastUpdate: Date.now() - 22000,
        reliability: 99.2,
        latency: 150,
        cost: 0.0012
      },
      {
        name: 'Open Exchange Rates',
        status: 'error',
        lastUpdate: Date.now() - 300000,
        reliability: 98.9,
        latency: 200,
        cost: 0.0015
      }
    ];

    setProviders(mockProviders);
    setLoading(false);
  };

  const formatCurrency = (amount: number, currencyCode: string) => {
    const currency = currencies.find(c => c.code === currencyCode);
    if (!currency) return `${amount.toFixed(2)}`;
    
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: currencyCode === 'JPY' ? 0 : 2,
      maximumFractionDigits: currencyCode === 'JPY' ? 0 : 4
    }).format(amount);
  };

  const convertCurrency = (amount: number, fromCurrency: string, toCurrency: string) => {
    const fromRate = currencies.find(c => c.code === fromCurrency)?.exchangeRate || 1;
    const toRate = currencies.find(c => c.code === toCurrency)?.exchangeRate || 1;
    
    // Convert to base currency first, then to target currency
    const baseAmount = amount / fromRate;
    return baseAmount * toRate;
  };

  const getChangeColor = (change: number) => {
    if (change > 0) return 'text-green-600';
    if (change < 0) return 'text-red-600';
    return 'text-gray-600';
  };

  const getChangeIcon = (change: number) => {
    if (change > 0) return <TrendingUp className="h-4 w-4" />;
    if (change < 0) return <TrendingDown className="h-4 w-4" />;
    return <ArrowUpDown className="h-4 w-4" />;
  };

  const getVolatilityColor = (volatility: string) => {
    switch (volatility) {
      case 'low': return 'bg-green-100 text-green-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'high': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'refunded': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getRiskColor = (riskScore: number) => {
    if (riskScore < 0.1) return 'text-green-600';
    if (riskScore < 0.3) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="h-screen flex flex-col bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <div className="bg-white shadow-lg border-b border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <Globe className="h-8 w-8 text-blue-600" />
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Multi-Currency Support</h1>
              <p className="text-gray-600">
                {currentTime.toLocaleTimeString()} • Phase 3H: Global Payment Processing
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* View Toggle */}
            <div className="flex bg-gray-100 rounded-lg p-1">
              {[
                { key: 'overview', label: 'Overview', icon: BarChart3 },
                { key: 'rates', label: 'Exchange Rates', icon: TrendingUp },
                { key: 'transactions', label: 'Transactions', icon: CreditCard },
                { key: 'analytics', label: 'Analytics', icon: PieChart },
                { key: 'settings', label: 'Settings', icon: Settings }
              ].map(({ key, label, icon: Icon }) => (
                <button
                  key={key}
                  onClick={() => setActiveView(key as any)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                    activeView === key
                      ? 'bg-blue-600 text-white shadow-md'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-white'
                  }`}
                >
                  <Icon className="h-4 w-4" />
                  <span className="hidden sm:inline">{label}</span>
                </button>
              ))}
            </div>

            {/* Controls */}
            <div className="flex items-center space-x-2">
              <select
                value={baseCurrency}
                onChange={(e) => setBaseCurrency(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                {currencies.map((currency) => (
                  <option key={currency.code} value={currency.code}>
                    {currency.flag} {currency.code}
                  </option>
                ))}
              </select>

              <button
                onClick={() => setAutoRefresh(!autoRefresh)}
                className={`p-2 rounded-lg transition-colors ${
                  autoRefresh ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'
                }`}
                title={`${autoRefresh ? 'Disable' : 'Enable'} Auto-refresh`}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
              </button>

              <button
                onClick={loadMultiCurrencyData}
                disabled={loading}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {loading ? 'Updating...' : 'Refresh Rates'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden p-6">
        {activeView === 'overview' && (
          <div className="space-y-6">
            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm font-medium">Total Revenue</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(analytics?.totalRevenue[baseCurrency] || 0, baseCurrency)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-600" />
                </div>
                <div className="mt-2 flex items-center text-sm">
                  <span className="text-green-600">+12.5%</span>
                  <span className="text-gray-500 ml-1">vs last month</span>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm font-medium">Active Currencies</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {currencies.filter(c => c.isActive).length}
                    </p>
                  </div>
                  <Globe className="h-8 w-8 text-blue-600" />
                </div>
                <div className="mt-2 flex items-center text-sm">
                  <span className="text-blue-600">8 regions</span>
                  <span className="text-gray-500 ml-1">supported</span>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm font-medium">Exchange Rate Impact</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {analytics?.exchangeRateImpact.toFixed(2)}%
                    </p>
                  </div>
                  <TrendingUp className="h-8 w-8 text-orange-600" />
                </div>
                <div className="mt-2 flex items-center text-sm">
                  <span className="text-orange-600">Revenue impact</span>
                  <span className="text-gray-500 ml-1">this month</span>
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-gray-600 text-sm font-medium">Risk Level</p>
                    <p className="text-2xl font-bold text-gray-900 capitalize">
                      {analytics?.riskAssessment.level}
                    </p>
                  </div>
                  <Shield className={`h-8 w-8 ${
                    analytics?.riskAssessment.level === 'low' ? 'text-green-600' :
                    analytics?.riskAssessment.level === 'medium' ? 'text-yellow-600' : 'text-red-600'
                  }`} />
                </div>
                <div className="mt-2 flex items-center text-sm">
                  <span className={
                    analytics?.riskAssessment.level === 'low' ? 'text-green-600' :
                    analytics?.riskAssessment.level === 'medium' ? 'text-yellow-600' : 'text-red-600'
                  }>
                    {analytics?.riskAssessment.factors.length} factors
                  </span>
                  <span className="text-gray-500 ml-1">monitored</span>
                </div>
              </div>
            </div>

            {/* Currency Overview Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Top Currencies */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Banknote className="h-5 w-5 mr-2 text-blue-600" />
                  Top Performing Currencies
                </h3>
                <div className="space-y-4">
                  {currencies
                    .filter(c => !c.isBaseCurrency)
                    .sort((a, b) => Math.abs(b.changePercent) - Math.abs(a.changePercent))
                    .slice(0, 5)
                    .map((currency) => (
                      <div key={currency.code} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{currency.flag}</span>
                          <div>
                            <p className="font-medium text-gray-900">{currency.code}</p>
                            <p className="text-sm text-gray-500">{currency.name}</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-gray-900">
                            {currency.exchangeRate.toFixed(4)}
                          </p>
                          <div className={`flex items-center text-sm ${getChangeColor(currency.change)}`}>
                            {getChangeIcon(currency.change)}
                            <span className="ml-1">{currency.changePercent.toFixed(2)}%</span>
                          </div>
                        </div>
                      </div>
                    ))}
                </div>
              </div>

              {/* Recent Transactions */}
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-green-600" />
                  Recent Multi-Currency Transactions
                </h3>
                <div className="space-y-4">
                  {transactions.slice(0, 5).map((transaction) => (
                    <div key={transaction.id} className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${
                          transaction.status === 'completed' ? 'bg-green-500' :
                          transaction.status === 'pending' ? 'bg-yellow-500' : 'bg-red-500'
                        }`}></div>
                        <div>
                          <p className="font-medium text-gray-900">
                            {formatCurrency(transaction.originalAmount, transaction.originalCurrency)}
                          </p>
                          <p className="text-sm text-gray-500">{transaction.customerLocation}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {formatCurrency(transaction.convertedAmount, transaction.convertedCurrency)}
                        </p>
                        <p className="text-sm text-gray-500">
                          Rate: {transaction.exchangeRate.toFixed(4)}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'rates' && (
          <div className="space-y-6">
            {/* Exchange Rate Providers Status */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Zap className="h-5 w-5 mr-2 text-purple-600" />
                Exchange Rate Providers
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {providers.map((provider) => (
                  <div key={provider.name} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{provider.name}</h4>
                      <span className={`w-3 h-3 rounded-full ${
                        provider.status === 'active' ? 'bg-green-500' :
                        provider.status === 'error' ? 'bg-red-500' : 'bg-gray-500'
                      }`}></span>
                    </div>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>Reliability: {provider.reliability}%</p>
                      <p>Latency: {provider.latency}ms</p>
                      <p>Cost: ${provider.cost}/request</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Live Exchange Rates */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                Live Exchange Rates (Base: {baseCurrency})
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Currency</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Rate</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Change</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Volatility</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Last Updated</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {currencies.filter(c => c.isActive).map((currency) => (
                      <tr key={currency.code} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <div className="flex items-center space-x-3">
                            <span className="text-xl">{currency.flag}</span>
                            <div>
                              <p className="font-medium text-gray-900">{currency.code}</p>
                              <p className="text-sm text-gray-500">{currency.name}</p>
                            </div>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-mono text-gray-900">
                            {currency.isBaseCurrency ? '1.0000' : currency.exchangeRate.toFixed(4)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <div className={`flex items-center ${getChangeColor(currency.change)}`}>
                            {getChangeIcon(currency.change)}
                            <span className="ml-1 font-medium">
                              {currency.changePercent.toFixed(2)}%
                            </span>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getVolatilityColor(currency.volatility)}`}>
                            {currency.volatility}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <p className="text-sm text-gray-600">
                            {new Date(currency.lastUpdated).toLocaleTimeString()}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            View Chart
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeView === 'transactions' && (
          <div className="space-y-6">
            {/* Transaction Filters */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <div className="flex flex-wrap items-center gap-4">
                <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                  <option>All Currencies</option>
                  {currencies.map(c => (
                    <option key={c.code} value={c.code}>{c.code}</option>
                  ))}
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-lg text-sm">
                  <option>All Statuses</option>
                  <option value="completed">Completed</option>
                  <option value="pending">Pending</option>
                  <option value="failed">Failed</option>
                  <option value="refunded">Refunded</option>
                </select>
                <input
                  type="date"
                  className="px-3 py-2 border border-gray-300 rounded-lg text-sm"
                />
                <button className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 text-sm">
                  Apply Filters
                </button>
              </div>
            </div>

            {/* Transactions Table */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-green-600" />
                Multi-Currency Transactions
              </h3>
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Transaction ID</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Original Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Converted Amount</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Exchange Rate</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Fee</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Risk Score</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Location</th>
                      <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {transactions.map((transaction) => (
                      <tr key={transaction.id} className="border-b border-gray-100 hover:bg-gray-50">
                        <td className="py-3 px-4">
                          <p className="font-mono text-sm text-gray-900">{transaction.transactionId}</p>
                          <p className="text-xs text-gray-500">
                            {new Date(transaction.timestamp).toLocaleString()}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(transaction.originalAmount, transaction.originalCurrency)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-medium text-gray-900">
                            {formatCurrency(transaction.convertedAmount, transaction.convertedCurrency)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <p className="font-mono text-sm text-gray-900">
                            {transaction.exchangeRate.toFixed(4)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <p className="text-sm text-gray-900">
                            ${transaction.exchangeFee.toFixed(2)}
                          </p>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                            {transaction.status}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`font-medium ${getRiskColor(transaction.riskScore)}`}>
                            {(transaction.riskScore * 100).toFixed(1)}%
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <p className="text-sm text-gray-600">{transaction.customerLocation}</p>
                        </td>
                        <td className="py-3 px-4">
                          <button className="text-blue-600 hover:text-blue-800 text-sm font-medium">
                            <Eye className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeView === 'analytics' && analytics && (
          <div className="space-y-6">
            {/* Revenue by Currency */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <PieChart className="h-5 w-5 mr-2 text-blue-600" />
                  Revenue by Currency
                </h3>
                <div className="space-y-4">
                  {Object.entries(analytics.totalRevenue)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 6)
                    .map(([currency, revenue]) => {
                      const percentage = (revenue / Object.values(analytics.totalRevenue).reduce((a, b) => a + b, 0)) * 100;
                      return (
                        <div key={currency} className="flex items-center justify-between">
                          <div className="flex items-center space-x-3">
                            <span className="text-lg">
                              {currencies.find(c => c.code === currency)?.flag || '💰'}
                            </span>
                            <div>
                              <p className="font-medium text-gray-900">{currency}</p>
                              <p className="text-sm text-gray-500">{percentage.toFixed(1)}% of total</p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="font-medium text-gray-900">
                              {formatCurrency(revenue, currency)}
                            </p>
                            <p className="text-sm text-gray-500">
                              {analytics.transactionCount[currency]} transactions
                            </p>
                          </div>
                        </div>
                      );
                    })}
                </div>
              </div>

              <div className="bg-white rounded-xl shadow-sm border p-6">
                <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <Calculator className="h-5 w-5 mr-2 text-green-600" />
                  Average Order Value by Currency
                </h3>
                <div className="space-y-4">
                  {Object.entries(analytics.averageOrderValue)
                    .sort(([,a], [,b]) => b - a)
                    .slice(0, 6)
                    .map(([currency, aov]) => (
                      <div key={currency} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">
                            {currencies.find(c => c.code === currency)?.flag || '💰'}
                          </span>
                          <p className="font-medium text-gray-900">{currency}</p>
                        </div>
                        <p className="font-medium text-gray-900">
                          {formatCurrency(aov, currency)}
                        </p>
                      </div>
                    ))}
                </div>
              </div>
            </div>

            {/* Risk Assessment */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Shield className="h-5 w-5 mr-2 text-red-600" />
                Currency Risk Assessment
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Risk Level:
                    <span className={`ml-2 px-2 py-1 rounded-full text-xs font-medium ${
                      analytics.riskAssessment.level === 'low' ? 'bg-green-100 text-green-800' :
                      analytics.riskAssessment.level === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {analytics.riskAssessment.level.toUpperCase()}
                    </span>
                  </h4>
                  <div className="space-y-2">
                    <h5 className="text-sm font-medium text-gray-700">Risk Factors:</h5>
                    {analytics.riskAssessment.factors.map((factor, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <AlertTriangle className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm text-gray-600">{factor}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Mitigation Strategies:</h4>
                  <div className="space-y-2">
                    {analytics.riskAssessment.mitigation.map((strategy, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm text-gray-600">{strategy}</span>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Hedging Recommendations:</h4>
                  <div className="space-y-2">
                    {analytics.hedgingRecommendations.map((recommendation, index) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Target className="h-4 w-4 text-blue-600" />
                        <span className="text-sm text-gray-600">{recommendation}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>

            {/* Currency Forecasts */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <TrendingUp className="h-5 w-5 mr-2 text-purple-600" />
                AI Currency Forecasts
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {analytics.forecastData.map((forecast) => {
                  const currentRate = currencies.find(c => c.code === forecast.currency)?.exchangeRate || 0;
                  const change = ((forecast.predictedRate - currentRate) / currentRate) * 100;

                  return (
                    <div key={forecast.currency} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <div className="flex items-center space-x-2">
                          <span className="text-lg">
                            {currencies.find(c => c.code === forecast.currency)?.flag}
                          </span>
                          <span className="font-medium text-gray-900">{forecast.currency}</span>
                        </div>
                        <span className="text-xs text-gray-500">{forecast.timeframe}</span>
                      </div>
                      <div className="space-y-1">
                        <p className="text-sm text-gray-600">
                          Current: {currentRate.toFixed(4)}
                        </p>
                        <p className="text-sm text-gray-600">
                          Predicted: {forecast.predictedRate.toFixed(4)}
                        </p>
                        <div className="flex items-center justify-between">
                          <span className={`text-sm font-medium ${getChangeColor(change)}`}>
                            {change > 0 ? '+' : ''}{change.toFixed(2)}%
                          </span>
                          <span className="text-xs text-gray-500">
                            {forecast.confidence.toFixed(1)}% confidence
                          </span>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        )}

        {activeView === 'settings' && (
          <div className="space-y-6">
            {/* Currency Configuration */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <Settings className="h-5 w-5 mr-2 text-gray-600" />
                Currency Configuration
              </h3>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Active Currencies</h4>
                  <div className="space-y-3">
                    {currencies.map((currency) => (
                      <div key={currency.code} className="flex items-center justify-between p-3 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <span className="text-lg">{currency.flag}</span>
                          <div>
                            <p className="font-medium text-gray-900">{currency.code}</p>
                            <p className="text-sm text-gray-500">{currency.name}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <label className="relative inline-flex items-center cursor-pointer">
                            <input
                              type="checkbox"
                              checked={currency.isActive}
                              className="sr-only peer"
                              onChange={() => {
                                setCurrencies(prev => prev.map(c =>
                                  c.code === currency.code ? { ...c, isActive: !c.isActive } : c
                                ));
                              }}
                            />
                            <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                          </label>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-medium text-gray-900 mb-3">Exchange Rate Settings</h4>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Update Frequency
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="30">Every 30 seconds</option>
                        <option value="60">Every minute</option>
                        <option value="300">Every 5 minutes</option>
                        <option value="900">Every 15 minutes</option>
                      </select>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Exchange Fee Percentage
                      </label>
                      <input
                        type="number"
                        step="0.01"
                        min="0"
                        max="5"
                        defaultValue="0.5"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Risk Threshold
                      </label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        <option value="low">Low (0-10%)</option>
                        <option value="medium">Medium (10-30%)</option>
                        <option value="high">High (30%+)</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Payment Gateway Configuration */}
            <div className="bg-white rounded-xl shadow-sm border p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <CreditCard className="h-5 w-5 mr-2 text-blue-600" />
                Payment Gateway Configuration
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {['Stripe', 'PayPal', 'Square', 'Adyen', 'Worldpay', 'Authorize.Net'].map((gateway) => (
                  <div key={gateway} className="border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-medium text-gray-900">{gateway}</h4>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input type="checkbox" className="sr-only peer" />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                    <div className="space-y-2 text-sm text-gray-600">
                      <p>Supported currencies: 150+</p>
                      <p>Processing fee: 2.9% + $0.30</p>
                      <p>Settlement: T+2 days</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3HMultiCurrencySupport;
