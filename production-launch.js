#!/usr/bin/env node

/**
 * RESTROFLOW PRODUCTION LAUNCH SCRIPT
 * 
 * This script launches the RESTROFLOW system in production-ready mode
 * with optimized performance, monitoring, and error handling.
 */

const { spawn, exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class ProductionLauncher {
  constructor() {
    this.processes = new Map();
    this.isShuttingDown = false;
    this.healthCheckInterval = null;
    this.startTime = Date.now();
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      info: '🚀',
      success: '✅',
      warning: '⚠️',
      error: '❌',
      health: '💓'
    }[type] || '🚀';
    
    console.log(`${prefix} [${timestamp}] ${message}`);
  }

  async launch() {
    this.log('🌟 RESTROFLOW PRODUCTION LAUNCH INITIATED');
    this.log('🔧 Optimized for production performance and reliability');
    
    try {
      await this.performPreLaunchChecks();
      await this.startBackendServer();
      await this.startFrontendServer();
      await this.setupHealthMonitoring();
      await this.setupGracefulShutdown();
      
      this.log('🎉 RESTROFLOW PRODUCTION SYSTEM LAUNCHED SUCCESSFULLY!', 'success');
      this.displaySystemInfo();
      
    } catch (error) {
      this.log(`💥 Launch failed: ${error.message}`, 'error');
      await this.shutdown();
      process.exit(1);
    }
  }

  async performPreLaunchChecks() {
    this.log('🔍 Performing pre-launch system checks...');
    
    // Check if required files exist
    const requiredFiles = [
      'backend/working-server.js',
      'package.json',
      'backend/package.json'
    ];

    for (const file of requiredFiles) {
      if (!fs.existsSync(file)) {
        throw new Error(`Required file missing: ${file}`);
      }
    }

    // Check if ports are available
    await this.checkPortAvailability(4000, 'Backend');
    await this.checkPortAvailability(5173, 'Frontend');

    // Verify database optimization was applied
    if (fs.existsSync('database-optimization.sql')) {
      this.log('✅ Database optimization script found');
    } else {
      this.log('⚠️ Database optimization script not found', 'warning');
    }

    this.log('✅ Pre-launch checks completed', 'success');
  }

  async checkPortAvailability(port, service) {
    return new Promise((resolve, reject) => {
      const net = require('net');
      const server = net.createServer();
      
      server.listen(port, () => {
        server.once('close', () => {
          this.log(`✅ Port ${port} available for ${service}`);
          resolve();
        });
        server.close();
      });
      
      server.on('error', (err) => {
        if (err.code === 'EADDRINUSE') {
          this.log(`⚠️ Port ${port} already in use for ${service}`, 'warning');
          resolve(); // Continue anyway, might be our own process
        } else {
          reject(err);
        }
      });
    });
  }

  async startBackendServer() {
    this.log('🔧 Starting optimized backend server...');
    
    const backendProcess = spawn('node', ['working-server.js'], {
      cwd: 'backend',
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'production',
        PORT: '4000',
        // Memory optimization
        NODE_OPTIONS: '--max-old-space-size=1024 --optimize-for-size',
        // Enable garbage collection
        NODE_GC: '1'
      }
    });

    this.processes.set('backend', backendProcess);

    // Handle backend output
    backendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output) {
        console.log(`[BACKEND] ${output}`);
      }
    });

    backendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('WARNING ALERT')) {
        console.error(`[BACKEND ERROR] ${output}`);
      }
    });

    backendProcess.on('exit', (code) => {
      this.log(`Backend server exited with code ${code}`, code === 0 ? 'info' : 'error');
      if (!this.isShuttingDown && code !== 0) {
        this.log('🔄 Attempting to restart backend server...', 'warning');
        setTimeout(() => this.startBackendServer(), 5000);
      }
    });

    // Wait for backend to be ready
    await this.waitForService('http://localhost:4000/api/health', 'Backend');
    this.log('✅ Backend server started successfully', 'success');
  }

  async startFrontendServer() {
    this.log('🎨 Starting optimized frontend server...');
    
    const frontendProcess = spawn('npm', ['run', 'start'], {
      stdio: ['pipe', 'pipe', 'pipe'],
      env: {
        ...process.env,
        NODE_ENV: 'production',
        PORT: '5173',
        BROWSER: 'none' // Don't auto-open browser
      }
    });

    this.processes.set('frontend', frontendProcess);

    // Handle frontend output
    frontendProcess.stdout.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('webpack')) {
        console.log(`[FRONTEND] ${output}`);
      }
    });

    frontendProcess.stderr.on('data', (data) => {
      const output = data.toString().trim();
      if (output && !output.includes('webpack')) {
        console.error(`[FRONTEND ERROR] ${output}`);
      }
    });

    frontendProcess.on('exit', (code) => {
      this.log(`Frontend server exited with code ${code}`, code === 0 ? 'info' : 'error');
      if (!this.isShuttingDown && code !== 0) {
        this.log('🔄 Attempting to restart frontend server...', 'warning');
        setTimeout(() => this.startFrontendServer(), 5000);
      }
    });

    // Wait for frontend to be ready
    await this.waitForService('http://localhost:5173', 'Frontend');
    this.log('✅ Frontend server started successfully', 'success');
  }

  async waitForService(url, serviceName, maxAttempts = 30) {
    const axios = require('axios');
    
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        await axios.get(url, { timeout: 2000 });
        return;
      } catch (error) {
        if (attempt === maxAttempts) {
          throw new Error(`${serviceName} service failed to start after ${maxAttempts} attempts`);
        }
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
  }

  async setupHealthMonitoring() {
    this.log('💓 Setting up health monitoring...');
    
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 60000); // Check every minute

    this.log('✅ Health monitoring activated', 'success');
  }

  async performHealthCheck() {
    const axios = require('axios');
    const checks = [
      { name: 'Backend API', url: 'http://localhost:4000/api/health' },
      { name: 'Frontend', url: 'http://localhost:5173' }
    ];

    for (const check of checks) {
      try {
        const response = await axios.get(check.url, { timeout: 5000 });
        if (response.status === 200) {
          this.log(`💓 ${check.name}: Healthy`, 'health');
        } else {
          this.log(`⚠️ ${check.name}: Unhealthy (${response.status})`, 'warning');
        }
      } catch (error) {
        this.log(`❌ ${check.name}: Failed (${error.message})`, 'error');
      }
    }
  }

  async setupGracefulShutdown() {
    const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
    
    signals.forEach(signal => {
      process.on(signal, async () => {
        this.log(`🛑 Received ${signal}, initiating graceful shutdown...`);
        await this.shutdown();
        process.exit(0);
      });
    });

    process.on('uncaughtException', async (error) => {
      this.log(`💥 Uncaught exception: ${error.message}`, 'error');
      await this.shutdown();
      process.exit(1);
    });

    process.on('unhandledRejection', async (reason, promise) => {
      this.log(`💥 Unhandled rejection: ${reason}`, 'error');
      await this.shutdown();
      process.exit(1);
    });
  }

  async shutdown() {
    if (this.isShuttingDown) return;
    
    this.isShuttingDown = true;
    this.log('🛑 Shutting down RESTROFLOW system...');

    // Stop health monitoring
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Gracefully stop all processes
    for (const [name, process] of this.processes) {
      this.log(`🛑 Stopping ${name} server...`);
      
      try {
        process.kill('SIGTERM');
        
        // Wait for graceful shutdown
        await new Promise((resolve) => {
          const timeout = setTimeout(() => {
            this.log(`⚠️ Force killing ${name} server`, 'warning');
            process.kill('SIGKILL');
            resolve();
          }, 10000);
          
          process.on('exit', () => {
            clearTimeout(timeout);
            resolve();
          });
        });
        
        this.log(`✅ ${name} server stopped`);
      } catch (error) {
        this.log(`❌ Error stopping ${name}: ${error.message}`, 'error');
      }
    }

    this.log('✅ RESTROFLOW system shutdown completed', 'success');
  }

  displaySystemInfo() {
    const uptime = ((Date.now() - this.startTime) / 1000).toFixed(2);
    
    console.log('\n' + '='.repeat(80));
    console.log('🌟 RESTROFLOW PRODUCTION SYSTEM - OPERATIONAL');
    console.log('='.repeat(80));
    console.log(`📡 Backend Server:  http://localhost:4000`);
    console.log(`🎨 Frontend Server: http://localhost:5173`);
    console.log(`⏱️  System Uptime:   ${uptime} seconds`);
    console.log(`💓 Health Checks:   Every 60 seconds`);
    console.log('');
    console.log('🔑 Test Credentials:');
    console.log('   👑 Super Admin: PIN 123456');
    console.log('   👨‍💼 Manager:     PIN 567890');
    console.log('   👤 Employee:    PIN 111222, 555666');
    console.log('');
    console.log('🌐 Access Points:');
    console.log('   📱 Main POS:     http://localhost:5173/index.html');
    console.log('   🔐 Login:        http://localhost:5173/login.html');
    console.log('   📊 Dashboard:    http://localhost:5173/dashboard.html');
    console.log('   👑 Super Admin:  http://localhost:5173/project/super-admin.html');
    console.log('');
    console.log('📊 API Endpoints:');
    console.log('   🔍 Health Check: http://localhost:4000/api/health');
    console.log('   🔐 Authentication: http://localhost:4000/api/auth/login');
    console.log('   📦 Products:     http://localhost:4000/api/products');
    console.log('   🛒 Orders:       http://localhost:4000/api/orders');
    console.log('');
    console.log('🛑 To stop the system: Press Ctrl+C');
    console.log('='.repeat(80));
  }
}

// Main execution
if (require.main === module) {
  const launcher = new ProductionLauncher();
  launcher.launch().catch(error => {
    console.error('💥 Production launch failed:', error);
    process.exit(1);
  });
}

module.exports = ProductionLauncher;
