<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="ALL" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="302aab1b-b571-4576-a12b-221dedbda007" name="Changes" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/AugmentWebviewStateStore.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="GOROOT" url="file://$USER_HOME$/sdk/go1.24.4" />
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
    <option name="ROOT_SYNC" value="SYNC" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="PackageJsonUpdateNotifier">
    <dismissed value="$PROJECT_DIR$/frontend/package.json" />
    <dismissed value="$PROJECT_DIR$/package.json" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="QODANA_PROBLEMS_VIEW_TAB" />
    <option name="showPreview" value="true" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2z0RR2Nizud37MKlYWCCG0LBFiQ" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "ASKED_SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "JavaScript Debug.beta-management.html.executor": "Run",
    "JavaScript Debug.enhanced.html.executor": "Run",
    "JavaScript Debug.final-super-admin-dashboard.html.executor": "Run",
    "JavaScript Debug.index.html (1).executor": "Run",
    "JavaScript Debug.index.html.executor": "Debug",
    "JavaScript Debug.industry-standard-pos.html.executor": "Run",
    "JavaScript Debug.kitchen.html.executor": "Run",
    "JavaScript Debug.restructured-access.html.executor": "Run",
    "JavaScript Debug.restructured-pos.html.executor": "Run",
    "JavaScript Debug.super-admin.html.executor": "Coverage",
    "JavaScript Debug.tenant.html.executor": "Run",
    "JavaScript Debug.unified-pos.html.executor": "Run",
    "ModuleVcsDetector.initialDetectionPerformed": "true",
    "RunOnceActivity.GoLinterPluginOnboarding": "true",
    "RunOnceActivity.GoLinterPluginStorageMigration": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.TerminalTabsStorage.copyFrom.TerminalArrangementManager": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "RunOnceActivity.go.modules.go.list.on.any.changes.was.set": "true",
    "SHARE_PROJECT_CONFIGURATION_FILES": "true",
    "git-widget-placeholder": "main",
    "go.import.settings.migrated": "true",
    "go.sdk.automatically.set": "true",
    "junie.onboarding.icon.badge.shown": "true",
    "last_opened_file_path": "C:/Users/<USER>",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.standard": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.standard": "",
    "nodejs_package_manager_path": "yarn",
    "run.code.analysis.last.selected.profile": "pProject Default",
    "settings.editor.selected.configurable": "settings.javascript.linters.eslint"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "postgresql"
    ]
  }
}]]></component>
  <component name="QodanaHighlightedReportService">
    <option name="localRunNotPublishedPersistedInfo">
      <LocalReportPersistedInfo>
        <option name="path" value="C:\Users\<USER>\AppData\Local\Temp\qodana_output\qodana.sarif.json" />
        <option name="reportGuid" value="010135c2-13d7-4d57-bf94-7917afae6a65" />
        <option name="reportName" value="project-bolt-sb1-9ew4zohy (1)/qodana/2025-06-26" />
      </LocalReportPersistedInfo>
    </option>
  </component>
  <component name="QodanaIsSelectedPersistenceService">
    <option name="selectedOrLoading" value="true" />
  </component>
  <component name="QodanaReportsService">
    <option name="descriptions">
      <ReportDescription localRun="true" path="C:\Users\<USER>\AppData\Local\Temp\qodana_output\qodana.sarif.json" reportGuid="010135c2-13d7-4d57-bf94-7917afae6a65" reportId="project-bolt-sb1-9ew4zohy (1)/qodana/2025-06-26" />
    </option>
  </component>
  <component name="RunManager" selected="JavaScript Debug.super-admin.html">
    <configuration name="final-super-admin-dashboard.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/project-bolt-sb1-9ew4zohy (1)/final-super-admin-dashboard.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="restructured-access.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/project-bolt-sb1-9ew4zohy (1)/project/restructured-access.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="super-admin.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/project-bolt-sb1-9ew4zohy (1)/project/super-admin.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="tenant.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/project-bolt-sb1-9ew4zohy (1)/project/tenant.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <configuration name="unified-pos.html" type="JavascriptDebugType" temporary="true" nameIsGenerated="true" uri="http://localhost:63342/project-bolt-sb1-9ew4zohy (1)/project/unified-pos.html" useBuiltInWebServerPort="true">
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JavaScript Debug.super-admin.html" />
        <item itemvalue="JavaScript Debug.final-super-admin-dashboard.html" />
        <item itemvalue="JavaScript Debug.unified-pos.html" />
        <item itemvalue="JavaScript Debug.tenant.html" />
        <item itemvalue="JavaScript Debug.restructured-access.html" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-gosdk-3b128438d3f6-07d2d2d66b1e-org.jetbrains.plugins.go.sharedIndexes.bundled-GO-251.26927.50" />
        <option value="bundled-js-predefined-d6986cc7102b-09060db00ec0-JavaScript-GO-251.26927.50" />
      </set>
    </attachedChunks>
  </component>
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="302aab1b-b571-4576-a12b-221dedbda007" name="Changes" comment="" />
      <created>1750865992978</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1750865992978</updated>
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="main" />
    <option name="LAST_COMMIT_MESSAGE" value="" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>