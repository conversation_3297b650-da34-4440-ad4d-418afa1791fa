// Direct Database Test for Super Admin
// Tests PostgreSQL connection and data directly

import pkg from 'pg';
const { Pool } = pkg;

// Database configuration
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function testDatabaseConnection() {
  console.log('🔍 Testing Direct Database Connection...');
  
  try {
    const client = await pool.connect();
    const result = await client.query('SELECT NOW() as current_time, version() as db_version');
    client.release();
    
    console.log('✅ Database connection successful');
    console.log(`   Current time: ${result.rows[0].current_time}`);
    console.log(`   Database version: ${result.rows[0].db_version.substring(0, 50)}...`);
    return true;
  } catch (error) {
    console.log('❌ Database connection failed:', error.message);
    return false;
  }
}

async function checkTables() {
  console.log('📋 Checking Database Tables...');
  
  try {
    const client = await pool.connect();
    
    // Check if main tables exist
    const tablesResult = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      AND table_type = 'BASE TABLE'
      ORDER BY table_name
    `);
    
    console.log('✅ Found tables:');
    tablesResult.rows.forEach(row => {
      console.log(`   - ${row.table_name}`);
    });
    
    client.release();
    return tablesResult.rows.length > 0;
  } catch (error) {
    console.log('❌ Error checking tables:', error.message);
    return false;
  }
}

async function checkEmployees() {
  console.log('👥 Checking Employees Table...');
  
  try {
    const client = await pool.connect();
    
    // Check if employees table exists and has data
    const employeesResult = await client.query(`
      SELECT id, name, role, pin, is_active, created_at
      FROM employees 
      WHERE role = 'super_admin' OR role = 'manager'
      ORDER BY role, created_at
      LIMIT 10
    `);
    
    if (employeesResult.rows.length > 0) {
      console.log('✅ Found employees:');
      employeesResult.rows.forEach(emp => {
        console.log(`   - ID: ${emp.id}, Name: ${emp.name}, Role: ${emp.role}, PIN: ${emp.pin || 'Not set'}, Active: ${emp.is_active}`);
      });
    } else {
      console.log('⚠️ No employees found in database');
    }
    
    client.release();
    return employeesResult.rows.length > 0;
  } catch (error) {
    console.log('❌ Error checking employees:', error.message);
    
    // Table might not exist, try to create it
    if (error.message.includes('does not exist')) {
      console.log('🔧 Employees table does not exist, this explains the login failure');
      return false;
    }
    return false;
  }
}

async function checkTenants() {
  console.log('🏢 Checking Tenants Table...');
  
  try {
    const client = await pool.connect();
    
    // Check if tenants table exists and has data
    const tenantsResult = await client.query(`
      SELECT id, name, slug, status, created_at
      FROM tenants 
      ORDER BY created_at
      LIMIT 10
    `);
    
    if (tenantsResult.rows.length > 0) {
      console.log('✅ Found tenants:');
      tenantsResult.rows.forEach(tenant => {
        console.log(`   - ID: ${tenant.id}, Name: ${tenant.name}, Slug: ${tenant.slug}, Status: ${tenant.status}`);
      });
    } else {
      console.log('⚠️ No tenants found in database');
    }
    
    client.release();
    return tenantsResult.rows.length > 0;
  } catch (error) {
    console.log('❌ Error checking tenants:', error.message);
    
    if (error.message.includes('does not exist')) {
      console.log('🔧 Tenants table does not exist');
      return false;
    }
    return false;
  }
}

async function createSampleData() {
  console.log('🔧 Creating Sample Data for Testing...');
  
  try {
    const client = await pool.connect();
    
    // Create tenants table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS tenants (
        id SERIAL PRIMARY KEY,
        name VARCHAR(255) NOT NULL,
        slug VARCHAR(255) UNIQUE NOT NULL,
        status VARCHAR(50) DEFAULT 'active',
        business_name VARCHAR(255),
        email VARCHAR(255),
        phone VARCHAR(50),
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    
    // Create employees table if it doesn't exist
    await client.query(`
      CREATE TABLE IF NOT EXISTS employees (
        id SERIAL PRIMARY KEY,
        tenant_id INTEGER REFERENCES tenants(id),
        name VARCHAR(255) NOT NULL,
        email VARCHAR(255),
        role VARCHAR(50) NOT NULL,
        pin VARCHAR(10),
        pin_hash VARCHAR(255),
        is_active BOOLEAN DEFAULT true,
        created_at TIMESTAMP DEFAULT NOW(),
        updated_at TIMESTAMP DEFAULT NOW()
      )
    `);
    
    // Insert sample tenant
    const tenantResult = await client.query(`
      INSERT INTO tenants (name, slug, business_name, email, status)
      VALUES ('Demo Restaurant', 'demo-restaurant', 'Demo Restaurant LLC', '<EMAIL>', 'active')
      ON CONFLICT (slug) DO UPDATE SET updated_at = NOW()
      RETURNING id
    `);
    
    const tenantId = tenantResult.rows[0].id;
    
    // Insert super admin employee
    await client.query(`
      INSERT INTO employees (tenant_id, name, email, role, pin, is_active)
      VALUES ($1, 'Super Admin', '<EMAIL>', 'super_admin', '123456', true)
      ON CONFLICT DO NOTHING
    `, [tenantId]);
    
    // Insert manager employee
    await client.query(`
      INSERT INTO employees (tenant_id, name, email, role, pin, is_active)
      VALUES ($1, 'Demo Manager', '<EMAIL>', 'manager', '567890', true)
      ON CONFLICT DO NOTHING
    `, [tenantId]);
    
    console.log('✅ Sample data created successfully');
    console.log(`   Tenant ID: ${tenantId}`);
    console.log('   Super Admin PIN: 123456');
    console.log('   Manager PIN: 567890');
    
    client.release();
    return true;
  } catch (error) {
    console.log('❌ Error creating sample data:', error.message);
    return false;
  }
}

async function runDatabaseTests() {
  console.log('🚀 Starting Direct Database Tests');
  console.log('==================================');
  
  const results = {
    connection: false,
    tables: false,
    employees: false,
    tenants: false,
    sampleData: false
  };

  // Test database connection
  results.connection = await testDatabaseConnection();
  
  if (!results.connection) {
    console.log('❌ Cannot proceed without database connection');
    return results;
  }

  // Check tables
  results.tables = await checkTables();
  
  // Check employees
  results.employees = await checkEmployees();
  
  // Check tenants
  results.tenants = await checkTenants();
  
  // Create sample data if needed
  if (!results.employees || !results.tenants) {
    console.log('\n🔧 Database appears to be empty, creating sample data...');
    results.sampleData = await createSampleData();
    
    if (results.sampleData) {
      // Re-check after creating data
      results.employees = await checkEmployees();
      results.tenants = await checkTenants();
    }
  }

  console.log('\n📋 Database Test Results');
  console.log('=========================');
  console.log(`Database Connection: ${results.connection ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Tables Exist:        ${results.tables ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Employees Data:      ${results.employees ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Tenants Data:        ${results.tenants ? '✅ PASS' : '❌ FAIL'}`);
  
  if (results.sampleData) {
    console.log(`Sample Data Created: ${results.sampleData ? '✅ PASS' : '❌ FAIL'}`);
  }

  const allPassed = results.connection && results.employees && results.tenants;
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ DATABASE READY' : '❌ DATABASE ISSUES'}`);
  
  if (allPassed) {
    console.log('🎉 Database is properly configured with real data!');
    console.log('🔐 You can now test Super Admin login with PIN: 123456');
  } else {
    console.log('⚠️ Database needs setup. Check the logs above for details.');
  }

  return results;
}

// Run the tests
runDatabaseTests()
  .then(() => {
    pool.end();
  })
  .catch(error => {
    console.error('💥 Test failed:', error);
    pool.end();
  });
