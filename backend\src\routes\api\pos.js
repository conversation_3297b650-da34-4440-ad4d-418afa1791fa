// POS System Routes for RESTROFLOW
const express = require('express');
const { pool } = require('../../database/config/connection');
const { authenticateToken, requireTenantAccess } = require('../../middleware/auth');

const router = express.Router();

// Apply authentication to all POS routes
router.use(authenticateToken);

// Utility functions for database operations
const handleDatabaseError = (error, res, operation) => {
  console.error(`Database error in ${operation}:`, error);
  res.status(500).json({
    success: false,
    message: `Database error during ${operation}`,
    error: process.env.NODE_ENV === 'development' ? error.message : 'Internal server error'
  });
};

const validateTenantAccess = async (tenantId, userId) => {
  try {
    const result = await pool.query(
      'SELECT id FROM tenants WHERE id = $1 AND (owner_id = $2 OR id IN (SELECT tenant_id FROM employees WHERE user_id = $2))',
      [tenantId, userId]
    );
    return result.rows.length > 0;
  } catch (error) {
    console.error('Error validating tenant access:', error);
    return false;
  }
};

// Get all products for tenant
router.get('/products', requireTenantAccess, async (req, res) => {
  try {
    console.log(`🛍️ Fetching products for tenant: ${req.user.tenantId}`);

    // Validate tenant access
    const hasAccess = await validateTenantAccess(req.user.tenantId, req.user.id);
    if (!hasAccess) {
      return res.status(403).json({
        success: false,
        message: 'Access denied to tenant data'
      });
    }

    const result = await pool.query(`
      SELECT
        p.*,
        c.name as category_name,
        c.color as category_color,
        COALESCE(i.current_stock, 0) as current_stock,
        COALESCE(i.low_stock_threshold, 0) as low_stock_threshold
      FROM products p
      LEFT JOIN categories c ON p.category_id = c.id
      LEFT JOIN inventory i ON p.id = i.product_id
      WHERE p.tenant_id = $1 AND p.is_active = true
      ORDER BY c.sort_order, p.sort_order, p.name
    `, [req.user.tenantId]);

    res.json({
      success: true,
      products: result.rows,
      count: result.rows.length,
      source: 'database'
    });

  } catch (error) {
    handleDatabaseError(error, res, 'fetching products');
  }
});

// Get all categories for tenant
router.get('/categories', requireTenantAccess, async (req, res) => {
  try {
    console.log(`📂 Fetching categories for tenant: ${req.user.tenantId}`);

    try {
      const result = await pool.query(
        'SELECT * FROM categories WHERE tenant_id = $1 AND is_active = true ORDER BY sort_order, name',
        [req.user.tenantId]
      );

      if (result.rows.length === 0) {
        console.log('⚠️ No categories found in database, using mock data');
        return res.json({
          success: true,
          categories: mockData.categories.filter(c => c.tenant_id === req.user.tenantId),
          source: 'mock'
        });
      }

      res.json({
        success: true,
        categories: result.rows,
        source: 'database'
      });

    } catch (dbError) {
      console.log('⚠️ Database query failed, using mock data:', dbError.message);
      res.json({
        success: true,
        categories: mockData.categories.filter(c => c.tenant_id === req.user.tenantId),
        source: 'mock'
      });
    }

  } catch (error) {
    console.error('💥 Error fetching categories:', error);
    res.status(500).json({
      error: 'Failed to fetch categories',
      code: 'FETCH_CATEGORIES_ERROR'
    });
  }
});

// Create new order
router.post('/orders', requireTenantAccess, async (req, res) => {
  try {
    const { items, total, payment_method, table_id, customer_info } = req.body;

    console.log(`🛒 Creating order for tenant: ${req.user.tenantId}`);

    // Validate required fields
    if (!items || !Array.isArray(items) || items.length === 0) {
      return res.status(400).json({
        error: 'Order items are required',
        code: 'MISSING_ORDER_ITEMS'
      });
    }

    if (!total || total <= 0) {
      return res.status(400).json({
        error: 'Valid order total is required',
        code: 'INVALID_ORDER_TOTAL'
      });
    }

    const orderData = {
      id: 'order_' + Date.now(),
      tenant_id: req.user.tenantId,
      employee_id: req.user.employeeId,
      location_id: req.user.locationId,
      table_id: table_id || null,
      items,
      total: parseFloat(total),
      status: 'pending',
      payment_method: payment_method || 'cash',
      customer_info: customer_info || {},
      timestamp: new Date().toISOString(),
      created_at: new Date().toISOString()
    };

    try {
      // Try to insert into database
      const result = await pool.query(`
        INSERT INTO orders (id, tenant_id, employee_id, location_id, table_id, items, total, status, payment_method, customer_info, created_at)
        VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
        RETURNING *
      `, [
        orderData.id,
        orderData.tenant_id,
        orderData.employee_id,
        orderData.location_id,
        orderData.table_id,
        JSON.stringify(orderData.items),
        orderData.total,
        orderData.status,
        orderData.payment_method,
        JSON.stringify(orderData.customer_info),
        orderData.created_at
      ]);

      console.log('✅ Order created in database:', orderData.id);
      res.json({
        success: true,
        order: result.rows[0],
        source: 'database'
      });

    } catch (dbError) {
      console.log('⚠️ Database insert failed, returning mock order:', dbError.message);
      res.json({
        success: true,
        order: orderData,
        source: 'mock'
      });
    }

  } catch (error) {
    console.error('💥 Error creating order:', error);
    res.status(500).json({
      error: 'Failed to create order',
      code: 'CREATE_ORDER_ERROR'
    });
  }
});

// Get order history for tenant
router.get('/orders', requireTenantAccess, async (req, res) => {
  try {
    const { limit = 50, offset = 0, status } = req.query;

    console.log(`📋 Fetching order history for tenant: ${req.user.tenantId}`);

    try {
      let query = 'SELECT * FROM orders WHERE tenant_id = $1';
      let params = [req.user.tenantId];

      if (status) {
        query += ' AND status = $2';
        params.push(status);
      }

      query += ' ORDER BY created_at DESC LIMIT $' + (params.length + 1) + ' OFFSET $' + (params.length + 2);
      params.push(parseInt(limit), parseInt(offset));

      const result = await pool.query(query, params);

      res.json({
        success: true,
        orders: result.rows,
        total: result.rows.length,
        source: 'database'
      });

    } catch (dbError) {
      console.log('⚠️ Database query failed, using mock data:', dbError.message);
      res.json({
        success: true,
        orders: [],
        total: 0,
        source: 'mock'
      });
    }

  } catch (error) {
    console.error('💥 Error fetching orders:', error);
    res.status(500).json({
      error: 'Failed to fetch orders',
      code: 'FETCH_ORDERS_ERROR'
    });
  }
});

// Update order status
router.patch('/orders/:orderId/status', requireTenantAccess, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { status } = req.body;

    console.log(`📝 Updating order ${orderId} status to: ${status}`);

    // Validate status
    const validStatuses = ['pending', 'preparing', 'ready', 'completed', 'cancelled'];
    if (!validStatuses.includes(status)) {
      return res.status(400).json({
        error: 'Invalid order status',
        code: 'INVALID_ORDER_STATUS',
        valid_statuses: validStatuses
      });
    }

    try {
      const result = await pool.query(
        'UPDATE orders SET status = $1, updated_at = NOW() WHERE id = $2 AND tenant_id = $3 RETURNING *',
        [status, orderId, req.user.tenantId]
      );

      if (result.rows.length === 0) {
        return res.status(404).json({
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND'
        });
      }

      res.json({
        success: true,
        order: result.rows[0],
        source: 'database'
      });

    } catch (dbError) {
      console.log('⚠️ Database update failed:', dbError.message);
      res.json({
        success: true,
        order: { id: orderId, status, updated_at: new Date().toISOString() },
        source: 'mock'
      });
    }

  } catch (error) {
    console.error('💥 Error updating order status:', error);
    res.status(500).json({
      error: 'Failed to update order status',
      code: 'UPDATE_ORDER_ERROR'
    });
  }
});

// Process payment for order
router.post('/orders/:orderId/payment', requireTenantAccess, async (req, res) => {
  try {
    const { orderId } = req.params;
    const { payment_method, amount, card_details } = req.body;

    console.log(`💳 Processing payment for order: ${orderId}`);

    // Validate payment data
    if (!payment_method || !amount) {
      return res.status(400).json({
        error: 'Payment method and amount are required',
        code: 'MISSING_PAYMENT_DATA'
      });
    }

    const paymentData = {
      transaction_id: 'txn_' + Date.now(),
      order_id: orderId,
      method: payment_method,
      amount: parseFloat(amount),
      status: 'completed',
      processed_at: new Date().toISOString()
    };

    try {
      // Update order with payment info
      const result = await pool.query(`
        UPDATE orders
        SET payment_info = $1, status = 'completed', updated_at = NOW()
        WHERE id = $2 AND tenant_id = $3
        RETURNING *
      `, [JSON.stringify(paymentData), orderId, req.user.tenantId]);

      if (result.rows.length === 0) {
        return res.status(404).json({
          error: 'Order not found',
          code: 'ORDER_NOT_FOUND'
        });
      }

      res.json({
        success: true,
        payment: paymentData,
        order: result.rows[0],
        source: 'database'
      });

    } catch (dbError) {
      console.log('⚠️ Database update failed:', dbError.message);
      res.json({
        success: true,
        payment: paymentData,
        source: 'mock'
      });
    }

  } catch (error) {
    console.error('💥 Error processing payment:', error);
    res.status(500).json({
      error: 'Failed to process payment',
      code: 'PAYMENT_ERROR'
    });
  }
});

module.exports = router;
