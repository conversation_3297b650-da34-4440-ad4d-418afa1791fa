import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { Building, MapPin, Users, DollarSign, TrendingUp, AlertTriangle, Plus, Settings, BarChart3, RefreshCw } from 'lucide-react';

interface Location {
  id: string;
  name: string;
  address: {
    street: string;
    city: string;
    state: string;
    zip_code: string;
    country: string;
  };
  phone: string;
  email: string;
  manager_name: string;
  status: 'active' | 'inactive' | 'maintenance';
  timezone: string;
  currency: string;
  tax_rate: number;
  opening_hours: {
    [key: string]: { open: string; close: string; closed?: boolean };
  };
  metrics: {
    daily_revenue: number;
    daily_orders: number;
    staff_count: number;
    avg_order_value: number;
    customer_satisfaction: number;
  };
  alerts: Array<{
    id: string;
    type: 'warning' | 'error' | 'info';
    message: string;
    timestamp: string;
  }>;
  last_sync: string;
}

const MultiLocationDashboard: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [locations, setLocations] = useState<Location[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [showAddLocationModal, setShowAddLocationModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  // Load locations
  useEffect(() => {
    const loadLocations = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🏢 Loading locations...');
        
        const response = await apiCall('/api/enterprise/locations');
        if (response.ok) {
          const data = await response.json();
          setLocations(data);
          console.log('✅ Locations loaded:', data.length);
        }
      } catch (error) {
        console.error('❌ Error loading locations:', error);
        setError('Failed to load locations. Using mock data.');
        
        // Fallback to mock data
        const mockLocations: Location[] = [
          {
            id: 'loc_1',
            name: 'Downtown Restaurant',
            address: {
              street: '123 Main St',
              city: 'New York',
              state: 'NY',
              zip_code: '10001',
              country: 'USA'
            },
            phone: '555-0101',
            email: '<EMAIL>',
            manager_name: 'John Smith',
            status: 'active',
            timezone: 'America/New_York',
            currency: 'USD',
            tax_rate: 8.25,
            opening_hours: {
              monday: { open: '09:00', close: '22:00' },
              tuesday: { open: '09:00', close: '22:00' },
              wednesday: { open: '09:00', close: '22:00' },
              thursday: { open: '09:00', close: '22:00' },
              friday: { open: '09:00', close: '23:00' },
              saturday: { open: '10:00', close: '23:00' },
              sunday: { open: '10:00', close: '21:00' }
            },
            metrics: {
              daily_revenue: 4250.75,
              daily_orders: 89,
              staff_count: 12,
              avg_order_value: 47.76,
              customer_satisfaction: 4.6
            },
            alerts: [
              {
                id: 'alert_1',
                type: 'warning',
                message: 'Low inventory: Coffee beans',
                timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString()
              }
            ],
            last_sync: new Date(Date.now() - 5 * 60 * 1000).toISOString()
          },
          {
            id: 'loc_2',
            name: 'Airport Branch',
            address: {
              street: '456 Terminal Rd',
              city: 'New York',
              state: 'NY',
              zip_code: '11430',
              country: 'USA'
            },
            phone: '555-0102',
            email: '<EMAIL>',
            manager_name: 'Sarah Johnson',
            status: 'active',
            timezone: 'America/New_York',
            currency: 'USD',
            tax_rate: 8.25,
            opening_hours: {
              monday: { open: '06:00', close: '23:00' },
              tuesday: { open: '06:00', close: '23:00' },
              wednesday: { open: '06:00', close: '23:00' },
              thursday: { open: '06:00', close: '23:00' },
              friday: { open: '06:00', close: '23:00' },
              saturday: { open: '06:00', close: '23:00' },
              sunday: { open: '06:00', close: '23:00' }
            },
            metrics: {
              daily_revenue: 6180.25,
              daily_orders: 156,
              staff_count: 18,
              avg_order_value: 39.62,
              customer_satisfaction: 4.3
            },
            alerts: [
              {
                id: 'alert_2',
                type: 'info',
                message: 'Peak hours: 7-9 AM, 12-2 PM',
                timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString()
              }
            ],
            last_sync: new Date(Date.now() - 2 * 60 * 1000).toISOString()
          },
          {
            id: 'loc_3',
            name: 'Mall Food Court',
            address: {
              street: '789 Shopping Center',
              city: 'Brooklyn',
              state: 'NY',
              zip_code: '11201',
              country: 'USA'
            },
            phone: '555-0103',
            email: '<EMAIL>',
            manager_name: 'Mike Wilson',
            status: 'maintenance',
            timezone: 'America/New_York',
            currency: 'USD',
            tax_rate: 8.25,
            opening_hours: {
              monday: { open: '10:00', close: '21:00' },
              tuesday: { open: '10:00', close: '21:00' },
              wednesday: { open: '10:00', close: '21:00' },
              thursday: { open: '10:00', close: '21:00' },
              friday: { open: '10:00', close: '22:00' },
              saturday: { open: '10:00', close: '22:00' },
              sunday: { open: '11:00', close: '20:00' }
            },
            metrics: {
              daily_revenue: 0,
              daily_orders: 0,
              staff_count: 8,
              avg_order_value: 0,
              customer_satisfaction: 0
            },
            alerts: [
              {
                id: 'alert_3',
                type: 'error',
                message: 'POS system offline - maintenance required',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
              }
            ],
            last_sync: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
          }
        ];
        setLocations(mockLocations);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadLocations();
    
    // Refresh every 5 minutes
    const interval = setInterval(loadLocations, 5 * 60 * 1000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getStatusColor = (status: Location['status']) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'inactive': return 'bg-gray-100 text-gray-800';
      case 'maintenance': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (type: 'warning' | 'error' | 'info') => {
    switch (type) {
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info': return <AlertTriangle className="h-4 w-4 text-blue-500" />;
    }
  };

  const getTotalMetrics = () => {
    const activeLocations = locations.filter(loc => loc.status === 'active');
    return {
      totalRevenue: activeLocations.reduce((sum, loc) => sum + loc.metrics.daily_revenue, 0),
      totalOrders: activeLocations.reduce((sum, loc) => sum + loc.metrics.daily_orders, 0),
      totalStaff: locations.reduce((sum, loc) => sum + loc.metrics.staff_count, 0),
      avgSatisfaction: activeLocations.length > 0 
        ? activeLocations.reduce((sum, loc) => sum + loc.metrics.customer_satisfaction, 0) / activeLocations.length 
        : 0,
      activeCount: activeLocations.length,
      totalCount: locations.length
    };
  };

  const totalMetrics = getTotalMetrics();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading locations...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Multi-Location Dashboard</h2>
            <p className="text-sm text-gray-500">
              {totalMetrics.activeCount} of {totalMetrics.totalCount} locations active
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <div className="flex bg-gray-100 rounded-md p-1">
              <button
                onClick={() => setViewMode('grid')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'grid' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                Grid
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`px-3 py-1 rounded text-sm transition-colors ${
                  viewMode === 'list' ? 'bg-white text-gray-900 shadow-sm' : 'text-gray-600'
                }`}
              >
                List
              </button>
            </div>
            <button
              onClick={() => setShowAddLocationModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Location</span>
            </button>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Summary Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-5 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Revenue</p>
                <p className="text-2xl font-bold text-gray-900">${totalMetrics.totalRevenue.toFixed(2)}</p>
              </div>
              <DollarSign className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Orders</p>
                <p className="text-2xl font-bold text-gray-900">{totalMetrics.totalOrders}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Total Staff</p>
                <p className="text-2xl font-bold text-gray-900">{totalMetrics.totalStaff}</p>
              </div>
              <Users className="h-8 w-8 text-purple-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Avg Satisfaction</p>
                <p className="text-2xl font-bold text-gray-900">{totalMetrics.avgSatisfaction.toFixed(1)}</p>
              </div>
              <TrendingUp className="h-8 w-8 text-orange-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Active Locations</p>
                <p className="text-2xl font-bold text-gray-900">{totalMetrics.activeCount}/{totalMetrics.totalCount}</p>
              </div>
              <Building className="h-8 w-8 text-indigo-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Locations Grid/List */}
      <div className="flex-1 overflow-auto p-4">
        {viewMode === 'grid' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {locations.map((location) => (
              <LocationCard
                key={location.id}
                location={location}
                onSelect={setSelectedLocation}
                getStatusColor={getStatusColor}
                getAlertIcon={getAlertIcon}
              />
            ))}
          </div>
        ) : (
          <div className="space-y-3">
            {locations.map((location) => (
              <LocationListItem
                key={location.id}
                location={location}
                onSelect={setSelectedLocation}
                getStatusColor={getStatusColor}
                getAlertIcon={getAlertIcon}
              />
            ))}
          </div>
        )}
      </div>

      {/* Location Details Modal */}
      {selectedLocation && (
        <LocationDetailsModal
          location={selectedLocation}
          onClose={() => setSelectedLocation(null)}
          getStatusColor={getStatusColor}
          getAlertIcon={getAlertIcon}
        />
      )}

      {/* Add Location Modal */}
      {showAddLocationModal && (
        <AddLocationModal
          onClose={() => setShowAddLocationModal(false)}
          onAdd={(newLocation) => {
            setLocations(prev => [...prev, newLocation]);
            setShowAddLocationModal(false);
          }}
        />
      )}
    </div>
  );
};

// Location Card Component
const LocationCard: React.FC<{
  location: Location;
  onSelect: (location: Location) => void;
  getStatusColor: (status: Location['status']) => string;
  getAlertIcon: (type: 'warning' | 'error' | 'info') => React.ReactNode;
}> = ({ location, onSelect, getStatusColor, getAlertIcon }) => {
  return (
    <div
      onClick={() => onSelect(location)}
      className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
    >
      <div className="flex justify-between items-start mb-3">
        <div>
          <h3 className="font-semibold text-gray-900">{location.name}</h3>
          <p className="text-sm text-gray-600">{location.address.city}, {location.address.state}</p>
        </div>
        <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(location.status)}`}>
          {location.status.toUpperCase()}
        </span>
      </div>

      <div className="grid grid-cols-2 gap-3 mb-3">
        <div>
          <p className="text-xs text-gray-500">Revenue</p>
          <p className="font-semibold text-gray-900">${location.metrics.daily_revenue.toFixed(0)}</p>
        </div>
        <div>
          <p className="text-xs text-gray-500">Orders</p>
          <p className="font-semibold text-gray-900">{location.metrics.daily_orders}</p>
        </div>
        <div>
          <p className="text-xs text-gray-500">Staff</p>
          <p className="font-semibold text-gray-900">{location.metrics.staff_count}</p>
        </div>
        <div>
          <p className="text-xs text-gray-500">Rating</p>
          <p className="font-semibold text-gray-900">{location.metrics.customer_satisfaction.toFixed(1)}</p>
        </div>
      </div>

      {location.alerts.length > 0 && (
        <div className="flex items-center space-x-1 text-xs">
          {getAlertIcon(location.alerts[0].type)}
          <span className="text-gray-600">{location.alerts.length} alert(s)</span>
        </div>
      )}
    </div>
  );
};

// Location List Item Component
const LocationListItem: React.FC<{
  location: Location;
  onSelect: (location: Location) => void;
  getStatusColor: (status: Location['status']) => string;
  getAlertIcon: (type: 'warning' | 'error' | 'info') => React.ReactNode;
}> = ({ location, onSelect, getStatusColor, getAlertIcon }) => {
  return (
    <div
      onClick={() => onSelect(location)}
      className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div>
            <h3 className="font-semibold text-gray-900">{location.name}</h3>
            <p className="text-sm text-gray-600">
              {location.address.street}, {location.address.city}, {location.address.state}
            </p>
          </div>
        </div>

        <div className="flex items-center space-x-6">
          <div className="text-center">
            <p className="text-xs text-gray-500">Revenue</p>
            <p className="font-semibold text-gray-900">${location.metrics.daily_revenue.toFixed(0)}</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-gray-500">Orders</p>
            <p className="font-semibold text-gray-900">{location.metrics.daily_orders}</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-gray-500">Staff</p>
            <p className="font-semibold text-gray-900">{location.metrics.staff_count}</p>
          </div>
          <div className="text-center">
            <p className="text-xs text-gray-500">Rating</p>
            <p className="font-semibold text-gray-900">{location.metrics.customer_satisfaction.toFixed(1)}</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(location.status)}`}>
              {location.status.toUpperCase()}
            </span>
            {location.alerts.length > 0 && (
              <div className="flex items-center space-x-1">
                {getAlertIcon(location.alerts[0].type)}
                <span className="text-xs text-gray-600">{location.alerts.length}</span>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

// Placeholder components for modals (to be implemented)
const LocationDetailsModal: React.FC<any> = ({ location, onClose }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
      <h3 className="text-lg font-semibold mb-4">{location.name} - Details</h3>
      <p className="text-gray-600 mb-4">Detailed location management coming soon...</p>
      <button
        onClick={onClose}
        className="bg-gray-500 text-white px-4 py-2 rounded-md"
      >
        Close
      </button>
    </div>
  </div>
);

const AddLocationModal: React.FC<any> = ({ onClose }) => (
  <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
    <div className="bg-white rounded-lg p-6 w-full max-w-md">
      <h3 className="text-lg font-semibold mb-4">Add New Location</h3>
      <p className="text-gray-600 mb-4">Location creation form coming soon...</p>
      <button
        onClick={onClose}
        className="bg-gray-500 text-white px-4 py-2 rounded-md"
      >
        Close
      </button>
    </div>
  </div>
);

export default MultiLocationDashboard;
