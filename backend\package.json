{"name": "enterprise-pos-backend", "version": "2.0.0", "description": "Enterprise POS Backend Server with Multi-tenant Architecture", "main": "working-server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "build": "echo 'No build step required'", "test": "jest", "migrate": "node migrations/migrate.js", "prod:start": "node scripts/start-production.js start", "prod:stop": "node scripts/start-production.js stop", "prod:restart": "node scripts/start-production.js restart", "prod:status": "node scripts/start-production.js status", "prod:logs": "node scripts/start-production.js logs", "prod:deploy": "node scripts/deploy.js", "prod:health": "curl -s http://localhost:4000/api/health | json_pp || echo 'Health check failed'"}, "dependencies": {"axios": "^1.9.0", "bcrypt": "^6.0.0", "bcryptjs": "^3.0.2", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "helmet": "^7.2.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.0", "multer": "^2.0.0", "node-cache": "^5.1.2", "node-cron": "^3.0.3", "node-fetch": "^2.7.0", "nodemailer": "^6.9.8", "pg": "^8.16.0", "redis": "^4.6.12", "socket.io": "^4.8.1", "stripe": "^14.15.0", "twilio": "^4.20.0", "uuid": "^11.1.0", "winston": "^3.11.0", "xlsx": "^0.18.5"}, "devDependencies": {"@types/node": "^20.10.6", "jest": "^29.7.0", "nodemon": "^3.1.10", "supertest": "^6.3.4"}}