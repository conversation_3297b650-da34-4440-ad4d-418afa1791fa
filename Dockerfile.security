# RESTROFLOW Enterprise Security System Dockerfile
# Multi-stage build for maximum security

# Stage 1: Build stage
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy source code
COPY . .

# Build the security system
RUN npm run super-admin:build

# Stage 2: Production stage with enhanced security
FROM nginx:alpine AS production

# Install security updates and tools
RUN apk update && apk upgrade && apk add --no-cache \
    curl \
    openssl \
    ca-certificates \
    && rm -rf /var/cache/apk/*

# Copy built security application
COPY --from=builder /app/dist-super-admin /usr/share/nginx/html

# Copy security-enhanced nginx configuration
COPY nginx-security.conf /etc/nginx/nginx.conf

# Create security certificates directory
RUN mkdir -p /etc/nginx/ssl

# Generate self-signed certificate for development (replace with real certs in production)
RUN openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout /etc/nginx/ssl/nginx-selfsigned.key \
    -out /etc/nginx/ssl/nginx-selfsigned.crt \
    -subj "/C=US/ST=State/L=City/O=RESTROFLOW/OU=Security/CN=localhost"

# Create nginx user and set strict permissions
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx && \
    chown -R nginx:nginx /usr/share/nginx/html && \
    chown -R nginx:nginx /var/cache/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /etc/nginx/conf.d && \
    chown -R nginx:nginx /etc/nginx/ssl && \
    chmod 600 /etc/nginx/ssl/nginx-selfsigned.key && \
    chmod 644 /etc/nginx/ssl/nginx-selfsigned.crt

# Remove unnecessary packages and files
RUN apk del openssl && \
    rm -rf /var/cache/apk/* && \
    rm -rf /tmp/*

# Switch to non-root user
USER nginx

# Expose HTTPS port for security
EXPOSE 443

# Enhanced health check for security system
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f -k https://localhost/ || exit 1

# Start nginx with security configuration
CMD ["nginx", "-g", "daemon off;"]

# Security labels and metadata
LABEL maintainer="RESTROFLOW Security Team"
LABEL version="1.0.0"
LABEL description="RESTROFLOW Enterprise Security Center"
LABEL security.level="MAXIMUM"
LABEL org.opencontainers.image.title="RESTROFLOW Security System"
LABEL org.opencontainers.image.description="Enterprise Security Center with Real-time Threat Monitoring"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="RESTROFLOW"
LABEL org.opencontainers.image.licenses="Proprietary"
