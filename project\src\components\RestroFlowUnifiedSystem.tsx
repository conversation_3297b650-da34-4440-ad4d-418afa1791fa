import React, { useState, useEffect } from 'react';
import {
  Building, Users, Settings, BarChart3, Shield, Globe,
  Zap, Database, Wifi, CheckCircle, AlertTriangle, Clock,
  ArrowRight, ExternalLink, Play, Pause, RefreshCw
} from 'lucide-react';

interface SystemStatus {
  component: string;
  status: 'online' | 'offline' | 'warning' | 'maintenance';
  uptime: string;
  lastUpdate: string;
  connections: number;
}

interface SystemMetrics {
  totalTenants: number;
  activeSessions: number;
  totalOrders: number;
  systemLoad: number;
  databaseConnections: number;
  apiRequests: number;
}

const RestroFlowUnifiedSystem: React.FC = () => {
  const [systemStatus, setSystemStatus] = useState<SystemStatus[]>([
    {
      component: 'Super Admin Dashboard',
      status: 'online',
      uptime: '99.8%',
      lastUpdate: '2 min ago',
      connections: 5
    },
    {
      component: 'Tenant Admin System',
      status: 'online',
      uptime: '99.9%',
      lastUpdate: '1 min ago',
      connections: 23
    },
    {
      component: 'POS System',
      status: 'online',
      uptime: '99.7%',
      lastUpdate: '30 sec ago',
      connections: 67
    },
    {
      component: 'Authentication Service',
      status: 'online',
      uptime: '100%',
      lastUpdate: '15 sec ago',
      connections: 95
    },
    {
      component: 'Database (PostgreSQL)',
      status: 'online',
      uptime: '99.9%',
      lastUpdate: '10 sec ago',
      connections: 45
    },
    {
      component: 'WebSocket Service',
      status: 'online',
      uptime: '99.6%',
      lastUpdate: '5 sec ago',
      connections: 89
    }
  ]);

  const [metrics, setMetrics] = useState<SystemMetrics>({
    totalTenants: 47,
    activeSessions: 234,
    totalOrders: 1847,
    systemLoad: 23,
    databaseConnections: 45,
    apiRequests: 15420
  });

  const [isMonitoring, setIsMonitoring] = useState(true);

  useEffect(() => {
    if (isMonitoring) {
      const interval = setInterval(() => {
        // Simulate real-time updates
        setMetrics(prev => ({
          ...prev,
          activeSessions: prev.activeSessions + Math.floor(Math.random() * 10 - 5),
          totalOrders: prev.totalOrders + Math.floor(Math.random() * 5),
          systemLoad: Math.max(10, Math.min(90, prev.systemLoad + Math.floor(Math.random() * 10 - 5))),
          apiRequests: prev.apiRequests + Math.floor(Math.random() * 50)
        }));

        setSystemStatus(prev => prev.map(status => ({
          ...status,
          lastUpdate: 'Just now',
          connections: Math.max(0, status.connections + Math.floor(Math.random() * 6 - 3))
        })));
      }, 5000);

      return () => clearInterval(interval);
    }
  }, [isMonitoring]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'online': return 'text-green-600 bg-green-100';
      case 'warning': return 'text-yellow-600 bg-yellow-100';
      case 'offline': return 'text-red-600 bg-red-100';
      case 'maintenance': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4" />;
      case 'warning': return <AlertTriangle className="h-4 w-4" />;
      case 'offline': return <AlertTriangle className="h-4 w-4" />;
      case 'maintenance': return <Clock className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building className="h-8 w-8 text-blue-600" />
              </div>
              <div className="ml-4">
                <h1 className="text-2xl font-bold text-gray-900">RestroFlow</h1>
                <p className="text-sm text-gray-500">Unified System Management</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600">All Systems Operational</span>
              </div>
              
              <button
                onClick={() => setIsMonitoring(!isMonitoring)}
                className={`flex items-center px-3 py-2 rounded-md text-sm font-medium ${
                  isMonitoring 
                    ? 'bg-green-100 text-green-700 hover:bg-green-200' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {isMonitoring ? <Pause className="h-4 w-4 mr-2" /> : <Play className="h-4 w-4 mr-2" />}
                {isMonitoring ? 'Monitoring' : 'Paused'}
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* System Overview */}
        <div className="mb-8">
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold text-gray-900">System Overview</h2>
              <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh All
              </button>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4 mb-8">
              <div className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm">Total Tenants</p>
                    <p className="text-2xl font-bold">{metrics.totalTenants}</p>
                  </div>
                  <Building className="h-8 w-8 text-blue-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm">Active Sessions</p>
                    <p className="text-2xl font-bold">{metrics.activeSessions}</p>
                  </div>
                  <Users className="h-8 w-8 text-green-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm">Total Orders</p>
                    <p className="text-2xl font-bold">{metrics.totalOrders}</p>
                  </div>
                  <BarChart3 className="h-8 w-8 text-purple-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm">System Load</p>
                    <p className="text-2xl font-bold">{metrics.systemLoad}%</p>
                  </div>
                  <Zap className="h-8 w-8 text-orange-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-indigo-500 to-indigo-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-indigo-100 text-sm">DB Connections</p>
                    <p className="text-2xl font-bold">{metrics.databaseConnections}</p>
                  </div>
                  <Database className="h-8 w-8 text-indigo-200" />
                </div>
              </div>

              <div className="bg-gradient-to-r from-pink-500 to-pink-600 rounded-lg p-4 text-white">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-pink-100 text-sm">API Requests</p>
                    <p className="text-2xl font-bold">{(metrics.apiRequests / 1000).toFixed(1)}k</p>
                  </div>
                  <Globe className="h-8 w-8 text-pink-200" />
                </div>
              </div>
            </div>

            {/* System Status */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Component Status</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {systemStatus.map((component, index) => (
                  <div key={index} className="bg-gray-50 rounded-lg p-4 border border-gray-200">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{component.component}</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(component.status)}`}>
                        {getStatusIcon(component.status)}
                        <span className="ml-1 capitalize">{component.status}</span>
                      </span>
                    </div>
                    <div className="space-y-1 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Uptime:</span>
                        <span className="font-medium">{component.uptime}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Connections:</span>
                        <span className="font-medium">{component.connections}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Last Update:</span>
                        <span className="font-medium">{component.lastUpdate}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Quick Access */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Super Admin Access */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Super Admin Dashboard</h3>
                <p className="text-sm text-gray-600">Platform management & oversight</p>
              </div>
            </div>
            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div className="flex justify-between">
                <span>Active Tenants:</span>
                <span className="font-medium text-green-600">47</span>
              </div>
              <div className="flex justify-between">
                <span>System Health:</span>
                <span className="font-medium text-green-600">Excellent</span>
              </div>
            </div>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
              <span>Access Dashboard</span>
              <ArrowRight className="h-4 w-4 ml-2" />
            </button>
          </div>

        {/* System Integration Status */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Integration Status</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-medium text-gray-900 mb-3">Data Flow</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">POS → Tenant Admin</span>
                  </div>
                  <span className="text-xs text-green-600 font-medium">Real-time</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Tenant Admin → Super Admin</span>
                  </div>
                  <span className="text-xs text-green-600 font-medium">Real-time</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Database Sync</span>
                  </div>
                  <span className="text-xs text-green-600 font-medium">Live</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="font-medium text-gray-900 mb-3">Security Status</h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <Shield className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Multi-Factor Auth</span>
                  </div>
                  <span className="text-xs text-blue-600 font-medium">Active</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <Database className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">Data Encryption</span>
                  </div>
                  <span className="text-xs text-blue-600 font-medium">AES-256</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center">
                    <Wifi className="h-5 w-5 text-blue-600 mr-2" />
                    <span className="text-sm font-medium text-gray-900">SSL/TLS</span>
                  </div>
                  <span className="text-xs text-blue-600 font-medium">TLS 1.3</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent System Activity</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-900">New tenant "Bella Vista Restaurant" registered</span>
              </div>
              <span className="text-xs text-gray-500">2 min ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-900">System backup completed successfully</span>
              </div>
              <span className="text-xs text-gray-500">15 min ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-900">Database maintenance scheduled for tonight</span>
              </div>
              <span className="text-xs text-gray-500">1 hour ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-400 rounded-full mr-3"></div>
                <span className="text-sm text-gray-900">WebSocket service restarted - all connections restored</span>
              </div>
              <span className="text-xs text-gray-500">2 hours ago</span>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default RestroFlowUnifiedSystem;

          {/* Tenant Admin Access */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                <Building className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">Tenant Admin System</h3>
                <p className="text-sm text-gray-600">Restaurant management portal</p>
              </div>
            </div>
            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div className="flex justify-between">
                <span>Active Sessions:</span>
                <span className="font-medium text-green-600">{metrics.activeSessions}</span>
              </div>
              <div className="flex justify-between">
                <span>Authentication:</span>
                <span className="font-medium text-green-600">MFA Enabled</span>
              </div>
            </div>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors">
              <span>Access Portal</span>
              <ArrowRight className="h-4 w-4 ml-2" />
            </button>
          </div>

          {/* POS System Access */}
          <div className="bg-white rounded-lg shadow-lg p-6 hover:shadow-xl transition-shadow">
            <div className="flex items-center mb-4">
              <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                <BarChart3 className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <h3 className="text-lg font-semibold text-gray-900">POS System</h3>
                <p className="text-sm text-gray-600">Point of sale operations</p>
              </div>
            </div>
            <div className="space-y-2 text-sm text-gray-600 mb-4">
              <div className="flex justify-between">
                <span>Active Terminals:</span>
                <span className="font-medium text-green-600">67</span>
              </div>
              <div className="flex justify-between">
                <span>Today's Orders:</span>
                <span className="font-medium text-green-600">{metrics.totalOrders}</span>
              </div>
            </div>
            <button className="w-full flex items-center justify-center px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
              <span>Launch POS</span>
              <ArrowRight className="h-4 w-4 ml-2" />
            </button>
          </div>
        </div>
