import React, { useState, useEffect } from 'react';
import { 
  User, 
  DollarSign, 
  Users, 
  Star, 
  TrendingUp, 
  Clock,
  Target,
  Award,
  AlertTriangle,
  RefreshCw,
  BarChart3
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface ServerMetric {
  server_id: string;
  name: string;
  shift: string;
  tables_assigned: string[];
  sales_today: number;
  covers_today: number;
  avg_order_value: number;
  tips_today: number;
  efficiency_score: number;
  customer_satisfaction: number;
  order_accuracy: number;
  service_speed: number;
  upselling_success: number;
}

interface ServerPerformanceData {
  summary: {
    total_servers: number;
    avg_sales_per_server: number;
    avg_covers_per_server: number;
    top_performer: string;
    efficiency_score: number;
  };
  server_metrics: ServerMetric[];
  performance_trends: Array<{
    date: string;
    avg_sales: number;
    avg_covers: number;
    efficiency: number;
    satisfaction: number;
  }>;
  shift_comparison: Array<{
    shift: string;
    servers: number;
    avg_sales: number;
    avg_covers: number;
    efficiency: number;
  }>;
}

const ServerPerformanceTracker: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [performanceData, setPerformanceData] = useState<ServerPerformanceData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'today' | '7d' | '30d'>('today');
  const [selectedShift, setSelectedShift] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'efficiency' | 'sales' | 'satisfaction'>('efficiency');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchServerPerformance();
    const interval = setInterval(fetchServerPerformance, 60000); // Update every minute
    return () => clearInterval(interval);
  }, [selectedTimeRange, selectedShift]);

  const fetchServerPerformance = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall(`/api/analytics/server-performance?range=${selectedTimeRange}&shift=${selectedShift}`);
      
      if (response.ok) {
        const data = await response.json();
        setPerformanceData(data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching server performance:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 80) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getPerformanceIcon = (score: number) => {
    if (score >= 90) return <Award className="h-4 w-4" />;
    if (score >= 80) return <Target className="h-4 w-4" />;
    return <AlertTriangle className="h-4 w-4" />;
  };

  const filteredServers = performanceData?.server_metrics.filter(server => 
    selectedShift === 'all' || server.shift.toLowerCase().includes(selectedShift.toLowerCase())
  ).sort((a, b) => {
    switch (sortBy) {
      case 'efficiency':
        return b.efficiency_score - a.efficiency_score;
      case 'sales':
        return b.sales_today - a.sales_today;
      case 'satisfaction':
        return b.customer_satisfaction - a.customer_satisfaction;
      default:
        return 0;
    }
  }) || [];

  if (isLoading && !performanceData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading server performance...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Server Performance Tracker</h2>
          <p className="text-gray-600">Monitor server efficiency, sales performance, and customer satisfaction</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchServerPerformance}
            className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 bg-white p-4 rounded-lg shadow-sm border">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="today">Today</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Shift</label>
          <select
            value={selectedShift}
            onChange={(e) => setSelectedShift(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Shifts</option>
            <option value="morning">Morning</option>
            <option value="day">Day</option>
            <option value="evening">Evening</option>
            <option value="night">Night</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="efficiency">Efficiency Score</option>
            <option value="sales">Sales Performance</option>
            <option value="satisfaction">Customer Satisfaction</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <User className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Servers</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.total_servers}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Sales/Server</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(performanceData.summary.avg_sales_per_server)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Covers/Server</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.avg_covers_per_server}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Award className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Top Performer</p>
                <p className="text-lg font-bold text-gray-900">{performanceData.summary.top_performer}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Efficiency</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.efficiency_score.toFixed(1)}%</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Shift Comparison */}
      {performanceData && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Shift Performance Comparison</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {performanceData.shift_comparison.map((shift) => (
                <div key={shift.shift} className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">{shift.shift}</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Servers:</span>
                      <span className="font-medium">{shift.servers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Sales:</span>
                      <span className="font-medium">{formatCurrency(shift.avg_sales)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Covers:</span>
                      <span className="font-medium">{shift.avg_covers}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Efficiency:</span>
                      <span className="font-medium">{shift.efficiency.toFixed(1)}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Individual Server Performance */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Individual Server Performance</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Server</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Shift</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sales</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Covers</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">AOV</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Tips</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Satisfaction</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Accuracy</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredServers.map((server) => (
                <tr key={server.server_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-gray-300 flex items-center justify-center">
                          <User className="h-6 w-6 text-gray-600" />
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{server.name}</div>
                        <div className="text-sm text-gray-500">Tables: {server.tables_assigned.join(', ')}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                      {server.shift}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getPerformanceColor(server.efficiency_score)}`}>
                      {getPerformanceIcon(server.efficiency_score)}
                      <span className="ml-1">{server.efficiency_score}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(server.sales_today)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{server.covers_today}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(server.avg_order_value)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(server.tips_today)}</td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 text-yellow-400 mr-1" />
                      <span className="text-sm text-gray-900">{server.customer_satisfaction.toFixed(1)}</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{server.order_accuracy.toFixed(1)}%</td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default ServerPerformanceTracker;
