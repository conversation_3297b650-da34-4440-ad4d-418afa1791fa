import React, { useState } from 'react';
import { <PERSON>, User<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2, <PERSON><PERSON><PERSON><PERSON><PERSON>, Al<PERSON>Triangle } from 'lucide-react';

interface User {
  id: number;
  name: string;
  email: string;
  role: string;
  isActive: boolean;
  tenantId: number;
}

interface SimpleUserActionsProps {
  user: User;
  onUserUpdated: () => void;
  onError: (error: string) => void;
  onSuccess: (message: string) => void;
}

export const SimpleUserActions: React.FC<SimpleUserActionsProps> = ({
  user,
  onUserUpdated,
  onError,
  onSuccess
}) => {
  const [loading, setLoading] = useState<string | null>(null);
  const [showDialog, setShowDialog] = useState(false);
  const [dialogAction, setDialogAction] = useState<string>('');
  const [reason, setReason] = useState('');

  const handleResetPassword = async () => {
    try {
      setLoading('reset-password');
      const randomPin = Math.floor(100000 + Math.random() * 900000).toString();
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/reset-password`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ newPin: randomPin })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess(`Password reset successfully. New PIN: ${randomPin}`);
      onUserUpdated();
    } catch (error) {
      onError('Failed to reset password');
    } finally {
      setLoading(null);
    }
  };

  const handleToggleStatus = async () => {
    try {
      setLoading('toggle-status');
      const newStatus = !user.isActive;
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}/status`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ is_active: newStatus, reason })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess(`User ${newStatus ? 'activated' : 'deactivated'} successfully`);
      onUserUpdated();
      setShowDialog(false);
      setReason('');
    } catch (error) {
      onError(`Failed to ${user.isActive ? 'deactivate' : 'activate'} user`);
    } finally {
      setLoading(null);
    }
  };

  const handleDeleteUser = async () => {
    try {
      setLoading('delete-user');
      
      const token = localStorage.getItem('authToken');
      const response = await fetch(`http://localhost:4000/api/admin/users/${user.id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason })
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}`);
      }

      onSuccess('User deactivated successfully');
      onUserUpdated();
      setShowDialog(false);
      setReason('');
    } catch (error) {
      onError('Failed to delete user');
    } finally {
      setLoading(null);
    }
  };

  const openDialog = (action: string) => {
    console.log('🔧 Opening dialog for action:', action, 'for user:', user.id);
    setDialogAction(action);
    setShowDialog(true);
  };

  const closeDialog = () => {
    console.log('🔧 Closing dialog');
    setShowDialog(false);
    setDialogAction('');
    setReason('');
  };

  const confirmAction = () => {
    console.log('🔧 Confirming action:', dialogAction, 'for user:', user.id);
    if (dialogAction === 'toggle-status') {
      handleToggleStatus();
    } else if (dialogAction === 'delete-user') {
      handleDeleteUser();
    }
  };

  return (
    <>
      <div className="flex flex-wrap gap-2">
        {/* Reset Password Button */}
        <button
          onClick={handleResetPassword}
          disabled={loading === 'reset-password'}
          className="flex items-center space-x-1 px-3 py-1 bg-blue-100 text-blue-700 rounded-md hover:bg-blue-200 disabled:opacity-50 text-sm"
        >
          {loading === 'reset-password' ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : (
            <Key className="h-3 w-3" />
          )}
          <span>Reset PIN</span>
        </button>

        {/* Toggle Status Button */}
        <button
          onClick={() => openDialog('toggle-status')}
          disabled={loading === 'toggle-status'}
          className={`flex items-center space-x-1 px-3 py-1 rounded-md text-sm ${
            user.isActive
              ? 'bg-yellow-100 text-yellow-700 hover:bg-yellow-200'
              : 'bg-green-100 text-green-700 hover:bg-green-200'
          } disabled:opacity-50`}
        >
          {loading === 'toggle-status' ? (
            <RefreshCw className="h-3 w-3 animate-spin" />
          ) : user.isActive ? (
            <UserX className="h-3 w-3" />
          ) : (
            <UserCheck className="h-3 w-3" />
          )}
          <span>{user.isActive ? 'Deactivate' : 'Activate'}</span>
        </button>

        {/* Delete User Button */}
        {user.role !== 'super_admin' && (
          <button
            onClick={() => openDialog('delete-user')}
            disabled={loading === 'delete-user'}
            className="flex items-center space-x-1 px-3 py-1 bg-red-100 text-red-700 rounded-md hover:bg-red-200 disabled:opacity-50 text-sm"
          >
            {loading === 'delete-user' ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <Trash2 className="h-3 w-3" />
            )}
            <span>Delete</span>
          </button>
        )}
      </div>

      {/* Simple Confirmation Dialog */}
      {showDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 shadow-xl">
            <div className="flex items-center space-x-3 mb-4">
              <AlertTriangle className="h-6 w-6 text-yellow-500" />
              <h3 className="text-lg font-semibold text-gray-900">
                Confirm Action
              </h3>
            </div>
            
            <p className="text-gray-600 mb-4">
              Are you sure you want to perform this action? This may affect user access and permissions.
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Reason (optional)
              </label>
              <textarea
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                rows={3}
                placeholder="Enter reason for this action..."
              />
            </div>

            <div className="flex space-x-3">
              <button
                type="button"
                onClick={closeDialog}
                className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-md hover:bg-gray-200"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmAction}
                className="flex-1 px-4 py-2 text-white bg-blue-600 rounded-md hover:bg-blue-700"
              >
                Confirm
              </button>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default SimpleUserActions;
