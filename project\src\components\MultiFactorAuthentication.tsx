import React, { useState, useEffect, useRef } from 'react';
import { 
  Shield, 
  Smartphone, 
  Mail, 
  Key, 
  Clock, 
  CheckCircle, 
  AlertCircle,
  Loader2,
  RefreshCw,
  Copy,
  Download,
  QrCode
} from 'lucide-react';

interface MultiFactorAuthenticationProps {
  onMFAComplete: (success: boolean, method: string) => void;
  employeeId: number;
  tenantId: number;
  primaryAuthToken: string;
}

interface MFAMethod {
  type: 'sms' | 'email' | 'authenticator' | 'backup_code';
  enabled: boolean;
  verified: boolean;
  lastUsed?: string;
  identifier?: string; // phone number or email
}

interface OTPData {
  code: string;
  expiresAt: number;
  attempts: number;
  method: 'sms' | 'email';
}

const MultiFactorAuthentication: React.FC<MultiFactorAuthenticationProps> = ({
  onMFAComplete,
  employeeId,
  tenantId,
  primaryAuthToken
}) => {
  const [availableMethods, setAvailableMethods] = useState<MFAMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [otpCode, setOtpCode] = useState('');
  const [otpData, setOtpData] = useState<OTPData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [error, setError] = useState('');
  const [timeRemaining, setTimeRemaining] = useState(0);
  const [backupCodes, setBackupCodes] = useState<string[]>([]);
  const [showBackupCodes, setShowBackupCodes] = useState(false);
  const [authenticatorQR, setAuthenticatorQR] = useState('');
  const [isDarkMode, setIsDarkMode] = useState(false);
  const otpInputRefs = useRef<(HTMLInputElement | null)[]>([]);
  const countdownInterval = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadMFAMethods();
  }, [employeeId]);

  useEffect(() => {
    if (timeRemaining > 0) {
      countdownInterval.current = setInterval(() => {
        setTimeRemaining(prev => {
          if (prev <= 1) {
            setOtpData(null);
            return 0;
          }
          return prev - 1;
        });
      }, 1000);
    } else if (countdownInterval.current) {
      clearInterval(countdownInterval.current);
    }

    return () => {
      if (countdownInterval.current) {
        clearInterval(countdownInterval.current);
      }
    };
  }, [timeRemaining]);

  const loadMFAMethods = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`http://localhost:4000/api/auth/mfa/methods/${employeeId}`, {
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const methods = await response.json();
        setAvailableMethods(methods);
        
        // Auto-select first available method
        const enabledMethod = methods.find((m: MFAMethod) => m.enabled);
        if (enabledMethod) {
          setSelectedMethod(enabledMethod.type);
        }
      } else {
        setError('Failed to load MFA methods');
      }
    } catch (error) {
      console.error('Error loading MFA methods:', error);
      setError('Connection failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const sendOTP = async (method: 'sms' | 'email') => {
    try {
      setIsSendingOTP(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/mfa/send-otp', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          employeeId,
          tenantId,
          method
        })
      });

      const data = await response.json();

      if (response.ok) {
        setOtpData({
          code: '', // Server doesn't send actual code for security
          expiresAt: Date.now() + (5 * 60 * 1000), // 5 minutes
          attempts: 0,
          method
        });
        setTimeRemaining(300); // 5 minutes in seconds
        console.log(`✅ OTP sent via ${method}`);
      } else {
        setError(data.error || `Failed to send OTP via ${method}`);
      }
    } catch (error) {
      console.error(`Error sending OTP via ${method}:`, error);
      setError('Failed to send verification code. Please try again.');
    } finally {
      setIsSendingOTP(false);
    }
  };

  const verifyOTP = async () => {
    if (otpCode.length !== 6) {
      setError('Please enter a 6-digit verification code');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/mfa/verify-otp', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          employeeId,
          tenantId,
          code: otpCode,
          method: otpData?.method
        })
      });

      const data = await response.json();

      if (response.ok) {
        console.log(`✅ MFA verification successful via ${otpData?.method}`);
        onMFAComplete(true, otpData?.method || 'otp');
      } else {
        setError(data.error || 'Invalid verification code');
        
        // Update attempt count
        if (otpData) {
          setOtpData({
            ...otpData,
            attempts: otpData.attempts + 1
          });
        }
      }
    } catch (error) {
      console.error('Error verifying OTP:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyAuthenticatorCode = async () => {
    if (otpCode.length !== 6) {
      setError('Please enter a 6-digit authenticator code');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/mfa/verify-authenticator', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          employeeId,
          tenantId,
          code: otpCode
        })
      });

      const data = await response.json();

      if (response.ok) {
        console.log('✅ Authenticator verification successful');
        onMFAComplete(true, 'authenticator');
      } else {
        setError(data.error || 'Invalid authenticator code');
      }
    } catch (error) {
      console.error('Error verifying authenticator code:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const verifyBackupCode = async () => {
    if (otpCode.length < 8) {
      setError('Please enter a valid backup code');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/mfa/verify-backup', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          employeeId,
          tenantId,
          code: otpCode.replace(/\s/g, '') // Remove spaces
        })
      });

      const data = await response.json();

      if (response.ok) {
        console.log('✅ Backup code verification successful');
        onMFAComplete(true, 'backup_code');
      } else {
        setError(data.error || 'Invalid backup code');
      }
    } catch (error) {
      console.error('Error verifying backup code:', error);
      setError('Verification failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const setupAuthenticator = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/auth/mfa/setup-authenticator', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${primaryAuthToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ employeeId, tenantId })
      });

      const data = await response.json();

      if (response.ok) {
        setAuthenticatorQR(data.qrCode);
        setBackupCodes(data.backupCodes);
      } else {
        setError('Failed to setup authenticator');
      }
    } catch (error) {
      console.error('Error setting up authenticator:', error);
      setError('Setup failed. Please try again.');
    }
  };

  const handleOTPInput = (index: number, value: string) => {
    if (value.length > 1) return;

    const newOTP = otpCode.split('');
    newOTP[index] = value;
    setOtpCode(newOTP.join(''));

    // Auto-focus next input
    if (value && index < 5) {
      otpInputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (index: number, e: React.KeyboardEvent) => {
    if (e.key === 'Backspace' && !otpCode[index] && index > 0) {
      otpInputRefs.current[index - 1]?.focus();
    }
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'sms':
        return <Smartphone className="w-5 h-5" />;
      case 'email':
        return <Mail className="w-5 h-5" />;
      case 'authenticator':
        return <Key className="w-5 h-5" />;
      case 'backup_code':
        return <Shield className="w-5 h-5" />;
      default:
        return <Shield className="w-5 h-5" />;
    }
  };

  const getMethodName = (type: string) => {
    switch (type) {
      case 'sms':
        return 'SMS Text Message';
      case 'email':
        return 'Email';
      case 'authenticator':
        return 'Authenticator App';
      case 'backup_code':
        return 'Backup Code';
      default:
        return type;
    }
  };

  if (isLoading && availableMethods.length === 0) {
    return (
      <div className={`w-full max-w-md mx-auto p-6 rounded-lg ${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="text-center">
          <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
            Loading authentication methods...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`w-full max-w-md mx-auto p-6 rounded-lg ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    } shadow-lg`}>
      
      {/* Header */}
      <div className="text-center mb-6">
        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-green-500 to-blue-600 flex items-center justify-center">
          <Shield className="w-8 h-8 text-white" />
        </div>
        <h2 className={`text-2xl font-bold ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Two-Factor Authentication
        </h2>
        <p className={`text-sm ${
          isDarkMode ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Please verify your identity to continue
        </p>
      </div>

      {/* Method Selection */}
      {!selectedMethod && (
        <div className="space-y-3 mb-6">
          <h3 className={`text-lg font-semibold ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Choose verification method:
          </h3>
          
          {availableMethods.filter(method => method.enabled).map((method) => (
            <button
              key={method.type}
              onClick={() => setSelectedMethod(method.type)}
              className={`w-full p-4 rounded-lg border-2 transition-colors text-left ${
                isDarkMode 
                  ? 'border-gray-600 hover:border-blue-500 bg-gray-700 hover:bg-gray-600' 
                  : 'border-gray-300 hover:border-blue-500 bg-gray-50 hover:bg-gray-100'
              }`}
            >
              <div className="flex items-center space-x-3">
                {getMethodIcon(method.type)}
                <div>
                  <p className={`font-medium ${
                    isDarkMode ? 'text-gray-200' : 'text-gray-700'
                  }`}>
                    {getMethodName(method.type)}
                  </p>
                  {method.identifier && (
                    <p className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-500'
                    }`}>
                      {method.type === 'sms' ? `••• ••• ${method.identifier.slice(-4)}` : 
                       method.type === 'email' ? `${method.identifier.charAt(0)}${'•'.repeat(method.identifier.indexOf('@') - 1)}@${method.identifier.split('@')[1]}` :
                       'Configured'}
                    </p>
                  )}
                </div>
              </div>
            </button>
          ))}
        </div>
      )}

      {/* Selected Method Interface */}
      {selectedMethod && (
        <div className="space-y-6">
          
          {/* Method Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              {getMethodIcon(selectedMethod)}
              <h3 className={`text-lg font-semibold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                {getMethodName(selectedMethod)}
              </h3>
            </div>
            <button
              onClick={() => {
                setSelectedMethod('');
                setOtpCode('');
                setError('');
                setOtpData(null);
              }}
              className={`text-sm ${
                isDarkMode ? 'text-gray-400 hover:text-gray-200' : 'text-gray-600 hover:text-gray-800'
              }`}
            >
              Change method
            </button>
          </div>

          {/* SMS/Email OTP */}
          {(selectedMethod === 'sms' || selectedMethod === 'email') && (
            <>
              {!otpData ? (
                <div className="text-center">
                  <p className={`mb-4 ${
                    isDarkMode ? 'text-gray-300' : 'text-gray-700'
                  }`}>
                    We'll send a verification code to your {selectedMethod === 'sms' ? 'phone' : 'email'}
                  </p>
                  <button
                    onClick={() => sendOTP(selectedMethod as 'sms' | 'email')}
                    disabled={isSendingOTP}
                    className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2 mx-auto"
                  >
                    {isSendingOTP ? (
                      <Loader2 className="w-5 h-5 animate-spin" />
                    ) : (
                      selectedMethod === 'sms' ? <Smartphone className="w-5 h-5" /> : <Mail className="w-5 h-5" />
                    )}
                    <span>{isSendingOTP ? 'Sending...' : `Send ${selectedMethod.toUpperCase()} Code`}</span>
                  </button>
                </div>
              ) : (
                <div>
                  <div className="flex items-center justify-between mb-4">
                    <p className={`text-sm ${
                      isDarkMode ? 'text-gray-300' : 'text-gray-700'
                    }`}>
                      Enter the 6-digit code sent to your {selectedMethod}
                    </p>
                    {timeRemaining > 0 && (
                      <div className="flex items-center space-x-1 text-sm text-blue-600">
                        <Clock className="w-4 h-4" />
                        <span>{formatTime(timeRemaining)}</span>
                      </div>
                    )}
                  </div>

                  {/* OTP Input */}
                  <div className="flex space-x-2 mb-4">
                    {[0, 1, 2, 3, 4, 5].map((index) => (
                      <input
                        key={index}
                        ref={(el) => otpInputRefs.current[index] = el}
                        type="text"
                        maxLength={1}
                        value={otpCode[index] || ''}
                        onChange={(e) => handleOTPInput(index, e.target.value)}
                        onKeyDown={(e) => handleKeyDown(index, e)}
                        className={`w-12 h-12 text-center text-lg font-mono border-2 rounded-lg ${
                          isDarkMode 
                            ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                            : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
                        } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                      />
                    ))}
                  </div>

                  <div className="flex space-x-3">
                    <button
                      onClick={verifyOTP}
                      disabled={otpCode.length !== 6 || isLoading}
                      className="flex-1 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                    >
                      {isLoading ? (
                        <Loader2 className="w-5 h-5 animate-spin" />
                      ) : (
                        <CheckCircle className="w-5 h-5" />
                      )}
                      <span>{isLoading ? 'Verifying...' : 'Verify'}</span>
                    </button>
                    
                    <button
                      onClick={() => sendOTP(selectedMethod as 'sms' | 'email')}
                      disabled={isSendingOTP || timeRemaining > 240} // Allow resend after 1 minute
                      className="px-4 py-3 border border-gray-300 rounded-lg hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                    >
                      <RefreshCw className="w-5 h-5" />
                    </button>
                  </div>
                </div>
              )}
            </>
          )}

          {/* Authenticator App */}
          {selectedMethod === 'authenticator' && (
            <div>
              <p className={`mb-4 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Enter the 6-digit code from your authenticator app
              </p>

              {/* OTP Input */}
              <div className="flex space-x-2 mb-4">
                {[0, 1, 2, 3, 4, 5].map((index) => (
                  <input
                    key={index}
                    ref={(el) => otpInputRefs.current[index] = el}
                    type="text"
                    maxLength={1}
                    value={otpCode[index] || ''}
                    onChange={(e) => handleOTPInput(index, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(index, e)}
                    className={`w-12 h-12 text-center text-lg font-mono border-2 rounded-lg ${
                      isDarkMode 
                        ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                        : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500/20`}
                  />
                ))}
              </div>

              <button
                onClick={verifyAuthenticatorCode}
                disabled={otpCode.length !== 6 || isLoading}
                className="w-full py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <CheckCircle className="w-5 h-5" />
                )}
                <span>{isLoading ? 'Verifying...' : 'Verify Code'}</span>
              </button>
            </div>
          )}

          {/* Backup Code */}
          {selectedMethod === 'backup_code' && (
            <div>
              <p className={`mb-4 ${
                isDarkMode ? 'text-gray-300' : 'text-gray-700'
              }`}>
                Enter one of your backup codes
              </p>

              <input
                type="text"
                value={otpCode}
                onChange={(e) => setOtpCode(e.target.value)}
                placeholder="Enter backup code"
                className={`w-full px-4 py-3 border-2 rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-blue-400' 
                    : 'bg-white border-gray-300 text-gray-900 focus:border-blue-500'
                } focus:outline-none focus:ring-2 focus:ring-blue-500/20 mb-4`}
              />

              <button
                onClick={verifyBackupCode}
                disabled={otpCode.length < 8 || isLoading}
                className="w-full py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <CheckCircle className="w-5 h-5" />
                )}
                <span>{isLoading ? 'Verifying...' : 'Verify Code'}</span>
              </button>
            </div>
          )}

          {/* Error Message */}
          {error && (
            <div className={`p-3 rounded-lg flex items-center space-x-2 ${
              isDarkMode 
                ? 'bg-red-900/50 border border-red-700 text-red-300' 
                : 'bg-red-50 border border-red-200 text-red-700'
            }`}>
              <AlertCircle className="w-5 h-5 flex-shrink-0" />
              <span className="text-sm">{error}</span>
            </div>
          )}
        </div>
      )}

      {/* Alternative Methods */}
      {selectedMethod && availableMethods.filter(m => m.enabled && m.type !== selectedMethod).length > 0 && (
        <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
          <p className={`text-sm text-center mb-3 ${
            isDarkMode ? 'text-gray-400' : 'text-gray-600'
          }`}>
            Having trouble? Try another method:
          </p>
          <div className="flex flex-wrap gap-2 justify-center">
            {availableMethods.filter(m => m.enabled && m.type !== selectedMethod).map((method) => (
              <button
                key={method.type}
                onClick={() => {
                  setSelectedMethod(method.type);
                  setOtpCode('');
                  setError('');
                  setOtpData(null);
                }}
                className={`px-3 py-1 text-sm rounded-lg border ${
                  isDarkMode 
                    ? 'border-gray-600 text-gray-300 hover:bg-gray-700' 
                    : 'border-gray-300 text-gray-600 hover:bg-gray-50'
                } transition-colors`}
              >
                {getMethodName(method.type)}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default MultiFactorAuthentication;
