const fetch = require('node-fetch');

async function testEndToEndAuthFlow() {
  console.log('🔐 Testing End-to-End Authentication Flow...\n');

  const testScenarios = [
    {
      name: 'Super Admin Authentication',
      pin: '999999',
      tenant_slug: 'barpos-system',
      expectedRole: 'super_admin',
      expectedAccess: ['health', 'tenants', 'admin']
    },
    {
      name: 'Manager Authentication',
      pin: '567890',
      tenant_slug: 'barpos-system',
      expectedRole: 'manager',
      expectedAccess: ['health', 'products', 'orders']
    },
    {
      name: 'Employee Authentication',
      pin: '567890',
      tenant_slug: 'barpos-system',
      expectedRole: 'employee',
      expectedAccess: ['health', 'products']
    },
    {
      name: 'Kitchen Staff Authentication',
      pin: '111222',
      tenant_slug: 'barpos-system',
      expectedRole: 'kitchen',
      expectedAccess: ['health', 'kitchen']
    }
  ];

  let totalTests = 0;
  let passedTests = 0;

  for (const scenario of testScenarios) {
    console.log(`🧪 Testing: ${scenario.name}`);
    console.log(`   PIN: ${scenario.pin}, Tenant: ${scenario.tenant_slug}`);

    try {
      // Step 1: Login Test
      const loginResponse = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pin: scenario.pin,
          tenant_slug: scenario.tenant_slug
        }),
      });

      const loginData = await loginResponse.json();
      totalTests++;

      if (!loginResponse.ok) {
        console.log(`   ❌ FAIL - Login failed: ${loginData.error}`);
        continue;
      }

      if (loginData.employee.role !== scenario.expectedRole) {
        console.log(`   ❌ FAIL - Wrong role: expected ${scenario.expectedRole}, got ${loginData.employee.role}`);
        continue;
      }

      console.log(`   ✅ PASS - Login successful`);
      console.log(`   👤 User: ${loginData.employee.name} (${loginData.employee.role})`);
      passedTests++;

      const authToken = loginData.token;

      // Step 2: Access Control Test
      console.log(`   🔐 Testing access control...`);
      
      const accessTests = [
        { name: 'health', url: '/api/health' },
        { name: 'products', url: '/api/products' },
        { name: 'orders', url: '/api/orders' },
        { name: 'tenants', url: '/api/tenants' },
        { name: 'kitchen', url: '/api/kitchen/orders' }
      ];

      let accessPassed = 0;
      let accessTotal = 0;

      for (const accessTest of accessTests) {
        if (scenario.expectedAccess.includes(accessTest.name)) {
          accessTotal++;
          try {
            const accessResponse = await fetch(`http://localhost:4000${accessTest.url}`, {
              method: 'GET',
              headers: { 'Authorization': `Bearer ${authToken}` },
            });

            if (accessResponse.ok || accessResponse.status === 404) {
              console.log(`     ✅ ${accessTest.name} - Accessible`);
              accessPassed++;
            } else {
              console.log(`     ❌ ${accessTest.name} - Access denied (${accessResponse.status})`);
            }
          } catch (error) {
            console.log(`     ❌ ${accessTest.name} - Network error`);
          }
        }
      }

      console.log(`   📊 Access Control: ${accessPassed}/${accessTotal} endpoints accessible`);

      // Step 3: Session Persistence Test
      console.log(`   💾 Testing session persistence...`);
      
      const sessionData = {
        token: authToken,
        employee: loginData.employee,
        tenant: loginData.tenant,
        loginTime: new Date().toISOString(),
        expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString()
      };

      console.log(`   ✅ Session data prepared for storage`);
      console.log(`   ⏰ Valid until: ${sessionData.expiresAt}`);

    } catch (error) {
      console.log(`   ❌ FAIL - Network/Server Error: ${error.message}`);
      totalTests++;
    }

    console.log('');
  }

  // Error Scenario Tests
  console.log('🚫 Testing Error Scenarios...\n');

  const errorScenarios = [
    {
      name: 'Invalid PIN',
      pin: '000000',
      tenant_slug: 'barpos-system',
      expectedError: 'Invalid PIN'
    },
    {
      name: 'Invalid Tenant',
      pin: '999999',
      tenant_slug: 'nonexistent-tenant',
      expectedError: 'Invalid tenant'
    },
    {
      name: 'Missing PIN',
      pin: '',
      tenant_slug: 'barpos-system',
      expectedError: 'PIN required'
    }
  ];

  let errorTestsPassed = 0;

  for (const errorScenario of errorScenarios) {
    console.log(`🧪 Testing: ${errorScenario.name}`);
    
    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          pin: errorScenario.pin,
          tenant_slug: errorScenario.tenant_slug
        }),
      });

      const data = await response.json();
      totalTests++;

      if (!response.ok && data.error) {
        console.log(`   ✅ PASS - Error correctly handled: ${data.error}`);
        errorTestsPassed++;
        passedTests++;
      } else {
        console.log(`   ❌ FAIL - Should have failed but succeeded`);
      }
    } catch (error) {
      console.log(`   ❌ FAIL - Network error: ${error.message}`);
      totalTests++;
    }
  }

  // Final Results
  console.log('\n' + '='.repeat(70));
  console.log('📊 END-TO-END AUTHENTICATION FLOW TEST RESULTS');
  console.log('='.repeat(70));
  console.log(`✅ Total Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('\n🎉 All end-to-end authentication tests PASSED!');
    console.log('✅ Authentication flow is working correctly across all user roles.');
    console.log('✅ Access control is properly enforced.');
    console.log('✅ Error handling is working as expected.');
    console.log('✅ Session management is ready for frontend integration.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the authentication system.');
  }

  return passedTests === totalTests;
}

testEndToEndAuthFlow().catch(console.error);
