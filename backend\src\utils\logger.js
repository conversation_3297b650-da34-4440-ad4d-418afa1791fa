// Production Logging System for RESTROFLOW
const fs = require('fs');
const path = require('path');

class Logger {
  constructor() {
    this.logLevels = {
      ERROR: 0,
      WARN: 1,
      INFO: 2,
      DEBUG: 3
    };

    this.currentLevel = this.logLevels[process.env.LOG_LEVEL?.toUpperCase()] || this.logLevels.INFO;
    this.logFilePath = process.env.LOG_FILE_PATH || './logs/restroflow.log';
    this.maxFileSize = this.parseSize(process.env.LOG_MAX_SIZE || '10m');
    this.maxFiles = parseInt(process.env.LOG_MAX_FILES) || 5;

    // Ensure logs directory exists
    this.ensureLogDirectory();

    console.log(`📝 Logger initialized - Level: ${this.getLevelName(this.currentLevel)}, File: ${this.logFilePath}`);
  }

  // Parse size string (e.g., '10m', '1g', '500k')
  parseSize(sizeStr) {
    const units = { k: 1024, m: 1024 * 1024, g: 1024 * 1024 * 1024 };
    const match = sizeStr.toLowerCase().match(/^(\d+)([kmg]?)$/);
    if (!match) return 10 * 1024 * 1024; // Default 10MB
    
    const [, size, unit] = match;
    return parseInt(size) * (units[unit] || 1);
  }

  // Ensure log directory exists
  ensureLogDirectory() {
    const logDir = path.dirname(this.logFilePath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
  }

  // Get level name from number
  getLevelName(level) {
    return Object.keys(this.logLevels).find(key => this.logLevels[key] === level) || 'UNKNOWN';
  }

  // Format log message
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const levelName = this.getLevelName(level);
    
    const logEntry = {
      timestamp,
      level: levelName,
      message,
      ...meta
    };

    // Add request context if available
    if (meta.req) {
      logEntry.request = {
        method: meta.req.method,
        url: meta.req.url,
        ip: meta.req.ip,
        userAgent: meta.req.get('User-Agent'),
        requestId: meta.req.requestId
      };
      delete logEntry.req;
    }

    // Add error details if available
    if (meta.error && meta.error instanceof Error) {
      logEntry.error = {
        name: meta.error.name,
        message: meta.error.message,
        stack: meta.error.stack
      };
      delete logEntry.error;
    }

    return JSON.stringify(logEntry);
  }

  // Write to log file with rotation
  async writeToFile(message) {
    try {
      // Check if file needs rotation
      if (fs.existsSync(this.logFilePath)) {
        const stats = fs.statSync(this.logFilePath);
        if (stats.size >= this.maxFileSize) {
          await this.rotateLogFile();
        }
      }

      // Append to log file
      fs.appendFileSync(this.logFilePath, message + '\n');
    } catch (error) {
      console.error('Failed to write to log file:', error);
    }
  }

  // Rotate log files
  async rotateLogFile() {
    try {
      const logDir = path.dirname(this.logFilePath);
      const logName = path.basename(this.logFilePath, path.extname(this.logFilePath));
      const logExt = path.extname(this.logFilePath);

      // Shift existing log files
      for (let i = this.maxFiles - 1; i >= 1; i--) {
        const oldFile = path.join(logDir, `${logName}.${i}${logExt}`);
        const newFile = path.join(logDir, `${logName}.${i + 1}${logExt}`);
        
        if (fs.existsSync(oldFile)) {
          if (i === this.maxFiles - 1) {
            fs.unlinkSync(oldFile); // Delete oldest file
          } else {
            fs.renameSync(oldFile, newFile);
          }
        }
      }

      // Move current log to .1
      const firstRotated = path.join(logDir, `${logName}.1${logExt}`);
      if (fs.existsSync(this.logFilePath)) {
        fs.renameSync(this.logFilePath, firstRotated);
      }

      console.log('📝 Log file rotated');
    } catch (error) {
      console.error('Failed to rotate log file:', error);
    }
  }

  // Log methods
  error(message, meta = {}) {
    if (this.currentLevel >= this.logLevels.ERROR) {
      const formatted = this.formatMessage(this.logLevels.ERROR, message, meta);
      console.error(`❌ ${message}`, meta);
      
      if (process.env.NODE_ENV === 'production') {
        this.writeToFile(formatted);
      }
    }
  }

  warn(message, meta = {}) {
    if (this.currentLevel >= this.logLevels.WARN) {
      const formatted = this.formatMessage(this.logLevels.WARN, message, meta);
      console.warn(`⚠️ ${message}`, meta);
      
      if (process.env.NODE_ENV === 'production') {
        this.writeToFile(formatted);
      }
    }
  }

  info(message, meta = {}) {
    if (this.currentLevel >= this.logLevels.INFO) {
      const formatted = this.formatMessage(this.logLevels.INFO, message, meta);
      console.log(`ℹ️ ${message}`, meta);
      
      if (process.env.NODE_ENV === 'production') {
        this.writeToFile(formatted);
      }
    }
  }

  debug(message, meta = {}) {
    if (this.currentLevel >= this.logLevels.DEBUG) {
      const formatted = this.formatMessage(this.logLevels.DEBUG, message, meta);
      console.log(`🔍 ${message}`, meta);
      
      if (process.env.NODE_ENV === 'production') {
        this.writeToFile(formatted);
      }
    }
  }

  // Request logging middleware
  requestLogger() {
    return (req, res, next) => {
      const startTime = Date.now();
      
      // Log request start
      this.info('Request started', {
        req,
        method: req.method,
        url: req.url,
        ip: req.ip
      });

      // Override res.end to log response
      const originalEnd = res.end;
      res.end = function(...args) {
        const responseTime = Date.now() - startTime;
        const level = res.statusCode >= 400 ? 'error' : 'info';
        
        this[level]('Request completed', {
          req,
          method: req.method,
          url: req.url,
          statusCode: res.statusCode,
          responseTime,
          ip: req.ip
        });
        
        originalEnd.apply(res, args);
      }.bind(this);

      next();
    };
  }

  // Error logging middleware
  errorLogger() {
    return (error, req, res, next) => {
      this.error('Request error', {
        req,
        error,
        method: req.method,
        url: req.url,
        ip: req.ip
      });
      
      next(error);
    };
  }

  // Database query logging
  logQuery(query, params, executionTime, error = null) {
    if (process.env.ENABLE_QUERY_LOGGING === 'true') {
      if (error) {
        this.error('Database query failed', {
          query: query.substring(0, 200) + '...',
          params,
          executionTime,
          error
        });
      } else if (executionTime > (process.env.SLOW_QUERY_THRESHOLD || 1000)) {
        this.warn('Slow database query', {
          query: query.substring(0, 200) + '...',
          params,
          executionTime
        });
      } else {
        this.debug('Database query executed', {
          query: query.substring(0, 100) + '...',
          executionTime
        });
      }
    }
  }

  // Performance logging
  logPerformance(metric, value, meta = {}) {
    if (process.env.ENABLE_PERFORMANCE_MONITORING === 'true') {
      this.info('Performance metric', {
        metric,
        value,
        ...meta
      });
    }
  }

  // Security event logging
  logSecurity(event, details = {}) {
    this.warn('Security event', {
      event,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  // Business event logging
  logBusiness(event, details = {}) {
    this.info('Business event', {
      event,
      ...details,
      timestamp: new Date().toISOString()
    });
  }

  // Get log statistics
  getStats() {
    try {
      const stats = {
        logFile: this.logFilePath,
        currentLevel: this.getLevelName(this.currentLevel),
        maxFileSize: this.maxFileSize,
        maxFiles: this.maxFiles
      };

      if (fs.existsSync(this.logFilePath)) {
        const fileStats = fs.statSync(this.logFilePath);
        stats.currentFileSize = fileStats.size;
        stats.lastModified = fileStats.mtime;
      }

      return stats;
    } catch (error) {
      return { error: error.message };
    }
  }

  // Health check
  healthCheck() {
    try {
      // Test write to log file
      const testMessage = this.formatMessage(this.logLevels.DEBUG, 'Health check test');
      this.writeToFile(testMessage);
      
      return {
        status: 'healthy',
        logFile: this.logFilePath,
        level: this.getLevelName(this.currentLevel)
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message
      };
    }
  }
}

// Production Monitoring System
class ProductionMonitor {
  constructor(logger) {
    this.logger = logger;
    this.alerts = [];
    this.metrics = {
      requests: 0,
      errors: 0,
      responseTime: [],
      memoryUsage: [],
      cpuUsage: []
    };

    this.thresholds = {
      errorRate: 0.05, // 5%
      responseTime: 2000, // 2 seconds
      memoryUsage: 0.8, // 80%
      cpuUsage: 0.8 // 80%
    };

    // Start monitoring
    this.startMonitoring();
    console.log('📊 Production Monitor initialized');
  }

  // Start monitoring intervals
  startMonitoring() {
    // Monitor system resources every 30 seconds
    setInterval(() => {
      this.collectSystemMetrics();
    }, 30000);

    // Check alerts every minute
    setInterval(() => {
      this.checkAlerts();
    }, 60000);

    // Clean old metrics every hour
    setInterval(() => {
      this.cleanOldMetrics();
    }, 3600000);
  }

  // Collect system metrics
  collectSystemMetrics() {
    const memUsage = process.memoryUsage();
    const memPercent = memUsage.heapUsed / memUsage.heapTotal;

    this.metrics.memoryUsage.push({
      timestamp: Date.now(),
      percent: memPercent,
      heapUsed: memUsage.heapUsed,
      heapTotal: memUsage.heapTotal
    });

    // Log high memory usage
    if (memPercent > this.thresholds.memoryUsage) {
      this.createAlert('HIGH_MEMORY_USAGE', {
        current: (memPercent * 100).toFixed(2) + '%',
        threshold: (this.thresholds.memoryUsage * 100) + '%'
      });
    }
  }

  // Create alert
  createAlert(type, data) {
    const alert = {
      id: Math.random().toString(36).substr(2, 9),
      type,
      timestamp: new Date().toISOString(),
      data,
      resolved: false
    };

    this.alerts.push(alert);
    this.logger.warn(`Alert created: ${type}`, alert);

    // Send notification if configured
    this.sendNotification(alert);
  }

  // Send notification
  async sendNotification(alert) {
    if (process.env.ALERT_WEBHOOK_URL) {
      try {
        const fetch = require('node-fetch');
        await fetch(process.env.ALERT_WEBHOOK_URL, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            text: `🚨 RESTROFLOW Alert: ${alert.type}`,
            alert
          })
        });
      } catch (error) {
        this.logger.error('Failed to send alert notification', { error });
      }
    }
  }

  // Check for alert conditions
  checkAlerts() {
    const now = Date.now();
    const fiveMinutesAgo = now - (5 * 60 * 1000);

    // Check error rate
    const recentRequests = this.metrics.requests;
    const recentErrors = this.metrics.errors;

    if (recentRequests > 0) {
      const errorRate = recentErrors / recentRequests;
      if (errorRate > this.thresholds.errorRate) {
        this.createAlert('HIGH_ERROR_RATE', {
          current: (errorRate * 100).toFixed(2) + '%',
          threshold: (this.thresholds.errorRate * 100) + '%',
          requests: recentRequests,
          errors: recentErrors
        });
      }
    }

    // Check average response time
    const recentResponseTimes = this.metrics.responseTime
      .filter(rt => rt.timestamp > fiveMinutesAgo)
      .map(rt => rt.time);

    if (recentResponseTimes.length > 0) {
      const avgResponseTime = recentResponseTimes.reduce((a, b) => a + b, 0) / recentResponseTimes.length;
      if (avgResponseTime > this.thresholds.responseTime) {
        this.createAlert('HIGH_RESPONSE_TIME', {
          current: Math.round(avgResponseTime) + 'ms',
          threshold: this.thresholds.responseTime + 'ms'
        });
      }
    }
  }

  // Clean old metrics
  cleanOldMetrics() {
    const oneHourAgo = Date.now() - (60 * 60 * 1000);

    this.metrics.responseTime = this.metrics.responseTime.filter(rt => rt.timestamp > oneHourAgo);
    this.metrics.memoryUsage = this.metrics.memoryUsage.filter(mu => mu.timestamp > oneHourAgo);
    this.metrics.cpuUsage = this.metrics.cpuUsage.filter(cu => cu.timestamp > oneHourAgo);

    // Reset counters
    this.metrics.requests = 0;
    this.metrics.errors = 0;
  }

  // Record request
  recordRequest(responseTime, isError = false) {
    this.metrics.requests++;
    if (isError) this.metrics.errors++;

    this.metrics.responseTime.push({
      timestamp: Date.now(),
      time: responseTime
    });
  }

  // Get monitoring stats
  getStats() {
    const now = Date.now();
    const oneHourAgo = now - (60 * 60 * 1000);

    const recentAlerts = this.alerts.filter(alert =>
      new Date(alert.timestamp).getTime() > oneHourAgo
    );

    const recentResponseTimes = this.metrics.responseTime
      .filter(rt => rt.timestamp > oneHourAgo)
      .map(rt => rt.time);

    const avgResponseTime = recentResponseTimes.length > 0
      ? recentResponseTimes.reduce((a, b) => a + b, 0) / recentResponseTimes.length
      : 0;

    return {
      uptime: Math.floor(process.uptime()),
      requests_last_hour: this.metrics.requests,
      errors_last_hour: this.metrics.errors,
      error_rate: this.metrics.requests > 0 ? (this.metrics.errors / this.metrics.requests * 100).toFixed(2) + '%' : '0%',
      avg_response_time: Math.round(avgResponseTime) + 'ms',
      memory_usage: process.memoryUsage(),
      active_alerts: recentAlerts.filter(alert => !alert.resolved).length,
      total_alerts: recentAlerts.length
    };
  }

  // Health check
  healthCheck() {
    const stats = this.getStats();
    const activeAlerts = this.alerts.filter(alert => !alert.resolved);

    return {
      status: activeAlerts.length === 0 ? 'healthy' : 'degraded',
      monitoring: stats,
      alerts: activeAlerts.slice(0, 5) // Last 5 alerts
    };
  }
}

// Create singleton instances
const logger = new Logger();
const monitor = new ProductionMonitor(logger);

module.exports = {
  Logger,
  logger,
  ProductionMonitor,
  monitor
};
