import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { <PERSON>er, <PERSON>an, DollarSign, Tablet, Wifi, WifiOff, <PERSON>tings, Plus, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';

interface POSDevice {
  id: string;
  name: string;
  type: 'receipt_printer' | 'barcode_scanner' | 'cash_drawer' | 'tablet' | 'scale' | 'display';
  brand: string;
  model: string;
  serial_number: string;
  location_id: string;
  location_name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  connection_type: 'usb' | 'ethernet' | 'wifi' | 'bluetooth' | 'serial';
  ip_address?: string;
  last_activity: string;
  firmware_version: string;
  driver_version: string;
  configuration: {
    [key: string]: any;
  };
  health_metrics: {
    uptime_percentage: number;
    error_rate: number;
    avg_response_time: number;
    last_maintenance: string;
  };
}

interface DeviceLog {
  id: string;
  device_id: string;
  device_name: string;
  event_type: 'connected' | 'disconnected' | 'error' | 'maintenance' | 'print_job' | 'scan';
  message: string;
  timestamp: string;
  severity: 'info' | 'warning' | 'error';
}

const POSHardwareManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [devices, setDevices] = useState<POSDevice[]>([]);
  const [deviceLogs, setDeviceLogs] = useState<DeviceLog[]>([]);
  const [selectedDevice, setSelectedDevice] = useState<POSDevice | null>(null);
  const [showAddDeviceModal, setShowAddDeviceModal] = useState(false);
  const [activeView, setActiveView] = useState<'devices' | 'logs' | 'settings'>('devices');
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load POS hardware data
  useEffect(() => {
    const loadHardwareData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('🖨️ Loading POS hardware data...');
        
        const [devicesResponse, logsResponse] = await Promise.all([
          apiCall('/api/hardware/devices'),
          apiCall('/api/hardware/logs')
        ]);
        
        if (devicesResponse.ok && logsResponse.ok) {
          const devicesData = await devicesResponse.json();
          const logsData = await logsResponse.json();
          setDevices(devicesData);
          setDeviceLogs(logsData);
          console.log('✅ POS hardware data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading POS hardware data:', error);
        setError('Failed to load hardware data. Using mock data.');
        
        // Fallback to mock data
        const mockDevices: POSDevice[] = [
          {
            id: 'dev_1',
            name: 'Main Receipt Printer',
            type: 'receipt_printer',
            brand: 'Epson',
            model: 'TM-T88VI',
            serial_number: 'EPS-001-2024',
            location_id: 'loc_1',
            location_name: 'Downtown Restaurant',
            status: 'online',
            connection_type: 'ethernet',
            ip_address: '*************',
            last_activity: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            firmware_version: '2.1.0',
            driver_version: '6.2.1',
            configuration: {
              paper_width: '80mm',
              print_speed: 'high',
              auto_cut: true,
              logo_enabled: true
            },
            health_metrics: {
              uptime_percentage: 99.2,
              error_rate: 0.1,
              avg_response_time: 0.8,
              last_maintenance: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          {
            id: 'dev_2',
            name: 'Barcode Scanner',
            type: 'barcode_scanner',
            brand: 'Honeywell',
            model: 'Voyager 1470g',
            serial_number: 'HON-002-2024',
            location_id: 'loc_1',
            location_name: 'Downtown Restaurant',
            status: 'online',
            connection_type: 'usb',
            last_activity: new Date(Date.now() - 30 * 1000).toISOString(),
            firmware_version: '1.8.3',
            driver_version: '3.1.2',
            configuration: {
              scan_mode: 'auto',
              beep_enabled: true,
              led_enabled: true,
              decode_types: ['UPC', 'EAN', 'Code128', 'QR']
            },
            health_metrics: {
              uptime_percentage: 98.7,
              error_rate: 0.3,
              avg_response_time: 0.2,
              last_maintenance: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          {
            id: 'dev_3',
            name: 'Cash Drawer',
            type: 'cash_drawer',
            brand: 'APG',
            model: 'Vasario 1616',
            serial_number: 'APG-003-2024',
            location_id: 'loc_1',
            location_name: 'Downtown Restaurant',
            status: 'online',
            connection_type: 'serial',
            last_activity: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            firmware_version: '1.0.0',
            driver_version: '2.3.1',
            configuration: {
              auto_open: true,
              lock_mode: 'electronic',
              bill_slots: 5,
              coin_slots: 8
            },
            health_metrics: {
              uptime_percentage: 99.8,
              error_rate: 0.0,
              avg_response_time: 0.1,
              last_maintenance: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          {
            id: 'dev_4',
            name: 'Kitchen Display Tablet',
            type: 'tablet',
            brand: 'Samsung',
            model: 'Galaxy Tab A8',
            serial_number: 'SAM-004-2024',
            location_id: 'loc_2',
            location_name: 'Airport Branch',
            status: 'offline',
            connection_type: 'wifi',
            ip_address: '*************',
            last_activity: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            firmware_version: 'Android 12',
            driver_version: 'N/A',
            configuration: {
              screen_timeout: '30min',
              brightness: 'auto',
              orientation: 'landscape',
              kiosk_mode: true
            },
            health_metrics: {
              uptime_percentage: 95.4,
              error_rate: 1.2,
              avg_response_time: 1.5,
              last_maintenance: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000).toISOString()
            }
          },
          {
            id: 'dev_5',
            name: 'Customer Display',
            type: 'display',
            brand: 'Elo',
            model: 'EloPOS 15-inch',
            serial_number: 'ELO-005-2024',
            location_id: 'loc_3',
            location_name: 'Mall Food Court',
            status: 'error',
            connection_type: 'usb',
            last_activity: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            firmware_version: '3.2.1',
            driver_version: '1.9.4',
            configuration: {
              resolution: '1024x768',
              brightness: 80,
              screensaver: 'enabled',
              touch_enabled: true
            },
            health_metrics: {
              uptime_percentage: 87.3,
              error_rate: 3.8,
              avg_response_time: 2.1,
              last_maintenance: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString()
            }
          }
        ];

        const mockLogs: DeviceLog[] = [
          {
            id: 'log_1',
            device_id: 'dev_1',
            device_name: 'Main Receipt Printer',
            event_type: 'print_job',
            message: 'Receipt printed successfully for order #102',
            timestamp: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
            severity: 'info'
          },
          {
            id: 'log_2',
            device_id: 'dev_2',
            device_name: 'Barcode Scanner',
            event_type: 'scan',
            message: 'Product scanned: UPC 123456789012',
            timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            severity: 'info'
          },
          {
            id: 'log_3',
            device_id: 'dev_4',
            device_name: 'Kitchen Display Tablet',
            event_type: 'disconnected',
            message: 'Device lost WiFi connection',
            timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            severity: 'warning'
          },
          {
            id: 'log_4',
            device_id: 'dev_5',
            device_name: 'Customer Display',
            event_type: 'error',
            message: 'Display driver error - requires restart',
            timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
            severity: 'error'
          }
        ];

        setDevices(mockDevices);
        setDeviceLogs(mockLogs);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadHardwareData();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadHardwareData, 30000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getStatusColor = (status: POSDevice['status']) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800';
      case 'offline': return 'bg-red-100 text-red-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: POSDevice['status']) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline': return <WifiOff className="h-4 w-4 text-red-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDeviceIcon = (type: POSDevice['type']) => {
    switch (type) {
      case 'receipt_printer': return <Printer className="h-6 w-6 text-blue-500" />;
      case 'barcode_scanner': return <Scan className="h-6 w-6 text-green-500" />;
      case 'cash_drawer': return <DollarSign className="h-6 w-6 text-yellow-500" />;
      case 'tablet': return <Tablet className="h-6 w-6 text-purple-500" />;
      case 'display': return <Tablet className="h-6 w-6 text-indigo-500" />;
      case 'scale': return <Settings className="h-6 w-6 text-orange-500" />;
      default: return <Settings className="h-6 w-6 text-gray-500" />;
    }
  };

  const getConnectionIcon = (type: string) => {
    switch (type) {
      case 'wifi': return <Wifi className="h-4 w-4 text-blue-500" />;
      case 'ethernet': return <Wifi className="h-4 w-4 text-green-500" />;
      case 'usb': return <Settings className="h-4 w-4 text-purple-500" />;
      case 'bluetooth': return <Wifi className="h-4 w-4 text-blue-400" />;
      case 'serial': return <Settings className="h-4 w-4 text-gray-500" />;
      default: return <Settings className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityColor = (severity: DeviceLog['severity']) => {
    switch (severity) {
      case 'info': return 'bg-blue-100 text-blue-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeviceStats = () => {
    const onlineDevices = devices.filter(d => d.status === 'online').length;
    const errorDevices = devices.filter(d => d.status === 'error').length;
    const avgUptime = devices.length > 0 
      ? devices.reduce((sum, d) => sum + d.health_metrics.uptime_percentage, 0) / devices.length 
      : 0;
    const recentErrors = deviceLogs.filter(log => 
      log.severity === 'error' && 
      new Date(log.timestamp) > new Date(Date.now() - 24 * 60 * 60 * 1000)
    ).length;
    
    return { onlineDevices, errorDevices, avgUptime, recentErrors };
  };

  const stats = getDeviceStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading POS hardware...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">POS Hardware Manager</h2>
            <p className="text-sm text-gray-500">Manage receipt printers, scanners, and POS devices</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddDeviceModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Device</span>
            </button>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Online Devices</p>
                <p className="text-2xl font-bold text-gray-900">{stats.onlineDevices}/{devices.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Error Devices</p>
                <p className="text-2xl font-bold text-red-600">{stats.errorDevices}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Avg Uptime</p>
                <p className="text-2xl font-bold text-gray-900">{stats.avgUptime.toFixed(1)}%</p>
              </div>
              <Settings className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">24h Errors</p>
                <p className="text-2xl font-bold text-orange-600">{stats.recentErrors}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'devices', label: 'Devices', icon: Printer },
            { id: 'logs', label: 'Activity Logs', icon: Settings },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'devices' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {devices.map((device) => (
              <div
                key={device.id}
                onClick={() => setSelectedDevice(device)}
                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    {getDeviceIcon(device.type)}
                    <div>
                      <h3 className="font-semibold text-gray-900">{device.name}</h3>
                      <p className="text-sm text-gray-600">{device.brand} {device.model}</p>
                      <p className="text-xs text-gray-500">{device.location_name}</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(device.status)}
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(device.status)}`}>
                      {device.status.toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Uptime</p>
                    <p className="font-semibold text-gray-900">{device.health_metrics.uptime_percentage}%</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Error Rate</p>
                    <p className="font-semibold text-gray-900">{device.health_metrics.error_rate}%</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Response Time</p>
                    <p className="font-semibold text-gray-900">{device.health_metrics.avg_response_time}s</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Connection</p>
                    <div className="flex items-center space-x-1">
                      {getConnectionIcon(device.connection_type)}
                      <span className="text-xs font-medium">{device.connection_type}</span>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Firmware: {device.firmware_version}</span>
                  <span>Last: {new Date(device.last_activity).toLocaleTimeString()}</span>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'logs' && (
          <div className="space-y-3">
            {deviceLogs.map((log) => (
              <div
                key={log.id}
                className="bg-white border border-gray-200 rounded-lg p-4"
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-4">
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getSeverityColor(log.severity)}`}>
                      {log.severity.toUpperCase()}
                    </span>
                    <div>
                      <h4 className="font-medium text-gray-900">{log.device_name}</h4>
                      <p className="text-sm text-gray-600">{log.message}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm text-gray-500">
                      {new Date(log.timestamp).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-400 capitalize">{log.event_type.replace('_', ' ')}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'settings' && (
          <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Hardware Settings</h3>
            <p className="text-gray-600">Device configuration and management tools coming soon...</p>
          </div>
        )}
      </div>

      {/* Device Details Modal */}
      {selectedDevice && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center space-x-3">
                {getDeviceIcon(selectedDevice.type)}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">{selectedDevice.name}</h3>
                  <p className="text-gray-600">{selectedDevice.brand} {selectedDevice.model}</p>
                  <p className="text-sm text-gray-500">Serial: {selectedDevice.serial_number}</p>
                </div>
              </div>
              <button
                onClick={() => setSelectedDevice(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedDevice.status)}
                  <span className={`text-sm px-2 py-1 rounded-full font-medium ${getStatusColor(selectedDevice.status)}`}>
                    {selectedDevice.status.toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Connection</p>
                <div className="flex items-center space-x-2">
                  {getConnectionIcon(selectedDevice.connection_type)}
                  <span className="text-sm font-medium">{selectedDevice.connection_type}</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Firmware</p>
                <p className="text-sm font-medium">{selectedDevice.firmware_version}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Driver</p>
                <p className="text-sm font-medium">{selectedDevice.driver_version}</p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-2">Health Metrics</p>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-xs text-gray-500">Uptime</p>
                  <p className="text-lg font-bold text-green-600">{selectedDevice.health_metrics.uptime_percentage}%</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Error Rate</p>
                  <p className="text-lg font-bold text-red-600">{selectedDevice.health_metrics.error_rate}%</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Response Time</p>
                  <p className="text-lg font-bold text-blue-600">{selectedDevice.health_metrics.avg_response_time}s</p>
                </div>
                <div>
                  <p className="text-xs text-gray-500">Last Maintenance</p>
                  <p className="text-sm font-medium">{new Date(selectedDevice.health_metrics.last_maintenance).toLocaleDateString()}</p>
                </div>
              </div>
            </div>

            <button
              onClick={() => setSelectedDevice(null)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Add Device Modal */}
      {showAddDeviceModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Device</h3>
            <p className="text-gray-600 mb-4">Device provisioning interface coming soon...</p>
            <button
              onClick={() => setShowAddDeviceModal(false)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default POSHardwareManager;
