import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Package,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Brain,
  Zap,
  BarChart3,
  ShoppingCart,
  DollarSign,
  RefreshCw,
  Eye,
  Settings,
  Plus,
  Minus,
  Calendar,
  Truck
} from 'lucide-react';

interface InventoryItem {
  id: string;
  name: string;
  category: string;
  currentStock: number;
  optimalStock: number;
  reorderPoint: number;
  maxStock: number;
  unitCost: number;
  demandForecast: number;
  stockStatus: 'optimal' | 'low' | 'critical' | 'overstock';
  aiRecommendation: string;
  confidence: number;
  lastUpdated: Date;
  supplier: string;
  leadTime: number; // days
}

interface OptimizationMetrics {
  totalItems: number;
  optimalItems: number;
  lowStockItems: number;
  criticalItems: number;
  overstockItems: number;
  totalValue: number;
  optimizationScore: number;
  potentialSavings: number;
}

export function Phase3CIntelligentInventoryOptimization() {
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [optimizationMetrics, setOptimizationMetrics] = useState<OptimizationMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const mockInventoryItems: InventoryItem[] = [
    {
      id: 'item-1',
      name: 'Premium Beer',
      category: 'beverages',
      currentStock: 45,
      optimalStock: 120,
      reorderPoint: 80,
      maxStock: 200,
      unitCost: 3.50,
      demandForecast: 85,
      stockStatus: 'low',
      aiRecommendation: 'Increase order by 75 units. High demand predicted for weekend.',
      confidence: 94.2,
      lastUpdated: new Date(Date.now() - 1800000),
      supplier: 'Premium Beverages Co.',
      leadTime: 2
    },
    {
      id: 'item-2',
      name: 'Truffle Pasta',
      category: 'ingredients',
      currentStock: 25,
      optimalStock: 30,
      reorderPoint: 15,
      maxStock: 50,
      unitCost: 12.00,
      demandForecast: 28,
      stockStatus: 'optimal',
      aiRecommendation: 'Stock levels optimal. Monitor for seasonal trends.',
      confidence: 89.7,
      lastUpdated: new Date(Date.now() - 3600000),
      supplier: 'Gourmet Ingredients Ltd.',
      leadTime: 1
    },
    {
      id: 'item-3',
      name: 'House Wine',
      category: 'beverages',
      currentStock: 8,
      optimalStock: 40,
      reorderPoint: 20,
      maxStock: 80,
      unitCost: 15.00,
      demandForecast: 35,
      stockStatus: 'critical',
      aiRecommendation: 'URGENT: Reorder 32 units immediately. Stock critically low.',
      confidence: 96.8,
      lastUpdated: new Date(Date.now() - 900000),
      supplier: 'Local Winery',
      leadTime: 3
    },
    {
      id: 'item-4',
      name: 'Napkins',
      category: 'supplies',
      currentStock: 500,
      optimalStock: 200,
      reorderPoint: 100,
      maxStock: 300,
      unitCost: 0.05,
      demandForecast: 150,
      stockStatus: 'overstock',
      aiRecommendation: 'Reduce future orders. Current stock exceeds optimal by 150%.',
      confidence: 87.3,
      lastUpdated: new Date(Date.now() - 7200000),
      supplier: 'Restaurant Supplies Inc.',
      leadTime: 5
    },
    {
      id: 'item-5',
      name: 'Salmon Fillet',
      category: 'ingredients',
      currentStock: 12,
      optimalStock: 25,
      reorderPoint: 15,
      maxStock: 40,
      unitCost: 18.50,
      demandForecast: 22,
      stockStatus: 'low',
      aiRecommendation: 'Order 13 units. Consider supplier lead time for weekend rush.',
      confidence: 91.5,
      lastUpdated: new Date(Date.now() - 5400000),
      supplier: 'Fresh Seafood Co.',
      leadTime: 1
    },
    {
      id: 'item-6',
      name: 'Coffee Beans',
      category: 'beverages',
      currentStock: 35,
      optimalStock: 40,
      reorderPoint: 25,
      maxStock: 60,
      unitCost: 8.00,
      demandForecast: 38,
      stockStatus: 'optimal',
      aiRecommendation: 'Stock levels good. Small reorder of 5 units recommended.',
      confidence: 88.9,
      lastUpdated: new Date(Date.now() - 10800000),
      supplier: 'Artisan Coffee Roasters',
      leadTime: 2
    }
  ];

  const mockOptimizationMetrics: OptimizationMetrics = {
    totalItems: 6,
    optimalItems: 2,
    lowStockItems: 2,
    criticalItems: 1,
    overstockItems: 1,
    totalValue: 2847.50,
    optimizationScore: 73.2,
    potentialSavings: 485.00
  };

  useEffect(() => {
    setTimeout(() => {
      setInventoryItems(mockInventoryItems);
      setOptimizationMetrics(mockOptimizationMetrics);
      setLoading(false);
    }, 1000);
  }, []);

  const categories = [
    { id: 'all', name: 'All Items', count: mockInventoryItems.length },
    { id: 'beverages', name: 'Beverages', count: mockInventoryItems.filter(i => i.category === 'beverages').length },
    { id: 'ingredients', name: 'Ingredients', count: mockInventoryItems.filter(i => i.category === 'ingredients').length },
    { id: 'supplies', name: 'Supplies', count: mockInventoryItems.filter(i => i.category === 'supplies').length },
  ];

  const filteredItems = selectedCategory === 'all' 
    ? inventoryItems 
    : inventoryItems.filter(item => item.category === selectedCategory);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'bg-green-100 text-green-800';
      case 'low': return 'bg-yellow-100 text-yellow-800';
      case 'critical': return 'bg-red-100 text-red-800';
      case 'overstock': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'optimal': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'low': return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'overstock': return <TrendingUp className="h-4 w-4 text-blue-500" />;
      default: return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStockPercentage = (current: number, optimal: number) => {
    return Math.min((current / optimal) * 100, 100);
  };

  const getStockBarColor = (status: string) => {
    switch (status) {
      case 'optimal': return 'bg-green-500';
      case 'low': return 'bg-yellow-500';
      case 'critical': return 'bg-red-500';
      case 'overstock': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold text-gray-900">Intelligent Inventory Optimization</h2>
          <div className="animate-spin">
            <RefreshCw className="h-5 w-5" />
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Phase 3C Intelligent Inventory Optimization</h2>
          <p className="text-gray-600">AI-powered inventory management and optimization recommendations</p>
        </div>
        <div className="flex items-center space-x-3">
          <Button variant="outline" size="sm">
            <Brain className="h-4 w-4 mr-2" />
            Optimize All
          </Button>
          <Button variant="outline" size="sm">
            <Truck className="h-4 w-4 mr-2" />
            Generate Orders
          </Button>
        </div>
      </div>

      {/* Category Filters */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="flex items-center space-x-2"
          >
            <span>{category.name}</span>
            <Badge variant="secondary" className="ml-2">
              {category.count}
            </Badge>
          </Button>
        ))}
      </div>

      {/* Optimization Overview */}
      {optimizationMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Optimization Score</p>
                  <p className="text-2xl font-bold text-blue-600">
                    {optimizationMetrics.optimizationScore}%
                  </p>
                </div>
                <Target className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Inventory Value</p>
                  <p className="text-2xl font-bold text-green-600">
                    ${optimizationMetrics.totalValue.toLocaleString()}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Potential Savings</p>
                  <p className="text-2xl font-bold text-purple-600">
                    ${optimizationMetrics.potentialSavings.toLocaleString()}
                  </p>
                </div>
                <Zap className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Items Needing Action</p>
                  <p className="text-2xl font-bold text-red-600">
                    {optimizationMetrics.lowStockItems + optimizationMetrics.criticalItems + optimizationMetrics.overstockItems}
                  </p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-500" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Stock Status Summary */}
      {optimizationMetrics && (
        <Card>
          <CardHeader>
            <CardTitle>Inventory Status Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{optimizationMetrics.optimalItems}</div>
                <div className="text-sm text-gray-600">Optimal Stock</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-yellow-600">{optimizationMetrics.lowStockItems}</div>
                <div className="text-sm text-gray-600">Low Stock</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{optimizationMetrics.criticalItems}</div>
                <div className="text-sm text-gray-600">Critical</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{optimizationMetrics.overstockItems}</div>
                <div className="text-sm text-gray-600">Overstock</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Inventory Items */}
      <div className="space-y-4">
        <h3 className="text-lg font-semibold text-gray-900">Inventory Items</h3>
        <div className="space-y-4">
          {filteredItems.map((item) => (
            <Card key={item.id} className="hover:shadow-lg transition-shadow">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      <Package className="h-6 w-6" />
                    </div>
                    <div>
                      <CardTitle className="text-lg">{item.name}</CardTitle>
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge variant="outline" className="capitalize">
                          {item.category}
                        </Badge>
                        <Badge className={getStatusColor(item.stockStatus)}>
                          {item.stockStatus}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(item.stockStatus)}
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{item.confidence}%</div>
                      <div className="text-xs text-gray-500">confidence</div>
                    </div>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">Current Stock Level</p>
                      <div className="flex items-center space-x-2">
                        <div className="flex-1 bg-gray-200 rounded-full h-3">
                          <div 
                            className={`h-3 rounded-full transition-all duration-300 ${getStockBarColor(item.stockStatus)}`}
                            style={{ width: `${getStockPercentage(item.currentStock, item.optimalStock)}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-medium">
                          {item.currentStock} / {item.optimalStock}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Reorder Point</p>
                        <p className="font-semibold">{item.reorderPoint}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Max Stock</p>
                        <p className="font-semibold">{item.maxStock}</p>
                      </div>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">Unit Cost</p>
                        <p className="font-semibold">${item.unitCost}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Demand Forecast</p>
                        <p className="font-semibold">{item.demandForecast}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Lead Time</p>
                        <p className="font-semibold">{item.leadTime} days</p>
                      </div>
                      <div>
                        <p className="text-gray-600">Total Value</p>
                        <p className="font-semibold">${(item.currentStock * item.unitCost).toFixed(2)}</p>
                      </div>
                    </div>
                    <div>
                      <p className="text-gray-600 text-sm">Supplier</p>
                      <p className="font-medium text-sm">{item.supplier}</p>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <p className="text-sm text-gray-600 mb-2">AI Recommendation</p>
                      <p className="text-sm bg-blue-50 p-3 rounded-lg border-l-4 border-blue-400">
                        {item.aiRecommendation}
                      </p>
                    </div>
                    <div className="text-xs text-gray-500">
                      Last updated: {item.lastUpdated.toLocaleString()}
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
                  <div className="flex items-center space-x-2">
                    <Button size="sm" variant="outline">
                      <Minus className="h-3 w-3 mr-1" />
                      Adjust Stock
                    </Button>
                    <Button size="sm" variant="outline">
                      <Plus className="h-3 w-3 mr-1" />
                      Reorder
                    </Button>
                  </div>
                  <div className="flex space-x-2">
                    <Button size="sm" variant="outline">
                      <Eye className="h-3 w-3 mr-1" />
                      Details
                    </Button>
                    <Button size="sm" variant="outline">
                      <Settings className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  );
}
