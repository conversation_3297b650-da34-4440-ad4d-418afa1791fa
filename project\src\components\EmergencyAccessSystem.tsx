import React, { useState, useEffect } from 'react';
import { 
  AlertTriangle, 
  Shield, 
  Key, 
  Clock, 
  Phone, 
  Mail,
  CheckCircle, 
  AlertCircle,
  Loader2,
  Lock,
  Unlock,
  User<PERSON>heck,
  Settings
} from 'lucide-react';
import { auditTrailManager } from '../utils/AuditTrailManager';

interface EmergencyAccessSystemProps {
  onEmergencyAccess: (success: boolean, method: string) => void;
  tenantSlug?: string;
}

interface EmergencyMethod {
  type: 'master_key' | 'admin_override' | 'support_code' | 'offline_mode';
  name: string;
  description: string;
  available: boolean;
  requiresApproval: boolean;
  estimatedTime: string;
}

interface EmergencyRequest {
  id: string;
  type: string;
  reason: string;
  requestedBy: string;
  requestedAt: string;
  status: 'pending' | 'approved' | 'denied' | 'expired';
  approvedBy?: string;
  approvedAt?: string;
  expiresAt: string;
}

const EmergencyAccessSystem: React.FC<EmergencyAccessSystemProps> = ({
  onEmergencyAccess,
  tenantSlug
}) => {
  const [emergencyMethods, setEmergencyMethods] = useState<EmergencyMethod[]>([]);
  const [selectedMethod, setSelectedMethod] = useState<string>('');
  const [emergencyCode, setEmergencyCode] = useState('');
  const [emergencyReason, setEmergencyReason] = useState('');
  const [contactInfo, setContactInfo] = useState({ name: '', phone: '', email: '' });
  const [isLoading, setIsLoading] = useState(false);
  const [isRequestingAccess, setIsRequestingAccess] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [emergencyRequest, setEmergencyRequest] = useState<EmergencyRequest | null>(null);
  const [isDarkMode, setIsDarkMode] = useState(false);
  const [systemStatus, setSystemStatus] = useState<'online' | 'offline' | 'maintenance'>('online');

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadEmergencyMethods();
    checkSystemStatus();
  }, []);

  const loadEmergencyMethods = async () => {
    try {
      const methods: EmergencyMethod[] = [
        {
          type: 'master_key',
          name: 'Master Emergency Key',
          description: 'Use the master emergency access code provided during setup',
          available: true,
          requiresApproval: false,
          estimatedTime: 'Immediate'
        },
        {
          type: 'admin_override',
          name: 'Administrator Override',
          description: 'Request emergency access approval from system administrator',
          available: true,
          requiresApproval: true,
          estimatedTime: '5-15 minutes'
        },
        {
          type: 'support_code',
          name: 'Support Access Code',
          description: 'Contact technical support for emergency access assistance',
          available: systemStatus !== 'offline',
          requiresApproval: true,
          estimatedTime: '10-30 minutes'
        },
        {
          type: 'offline_mode',
          name: 'Offline Emergency Mode',
          description: 'Enable limited offline functionality with local authentication',
          available: true,
          requiresApproval: false,
          estimatedTime: 'Immediate'
        }
      ];

      setEmergencyMethods(methods);
    } catch (error) {
      console.error('Error loading emergency methods:', error);
    }
  };

  const checkSystemStatus = async () => {
    try {
      const response = await fetch('http://localhost:4000/api/system/status');
      if (response.ok) {
        const data = await response.json();
        setSystemStatus(data.status || 'online');
      } else {
        setSystemStatus('offline');
      }
    } catch (error) {
      setSystemStatus('offline');
    }
  };

  const handleMasterKeyAccess = async () => {
    if (!emergencyCode.trim()) {
      setError('Please enter the master emergency key');
      return;
    }

    try {
      setIsLoading(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/emergency/master-key', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          masterKey: emergencyCode.trim(),
          tenantSlug,
          reason: emergencyReason,
          contactInfo
        })
      });

      const data = await response.json();

      if (response.ok && data.token) {
        // Log emergency access
        auditTrailManager.logSecurityIncident({
          action: 'emergency_access_granted',
          success: true,
          metadata: {
            method: 'master_key',
            reason: emergencyReason,
            tenantSlug,
            contactInfo
          }
        });

        setSuccess('Emergency access granted successfully');
        onEmergencyAccess(true, 'master_key');
      } else {
        setError(data.error || 'Invalid master emergency key');
        
        // Log failed attempt
        auditTrailManager.logSecurityIncident({
          action: 'emergency_access_denied',
          success: false,
          metadata: {
            method: 'master_key',
            reason: 'invalid_key',
            tenantSlug
          }
        });
      }
    } catch (error) {
      console.error('Master key access error:', error);
      setError('Emergency access failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const requestAdminOverride = async () => {
    if (!emergencyReason.trim() || !contactInfo.name || !contactInfo.phone) {
      setError('Please provide reason and contact information');
      return;
    }

    try {
      setIsRequestingAccess(true);
      setError('');

      const response = await fetch('http://localhost:4000/api/auth/emergency/request-override', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          tenantSlug,
          reason: emergencyReason,
          contactInfo,
          urgency: 'high'
        })
      });

      const data = await response.json();

      if (response.ok) {
        setEmergencyRequest(data.request);
        setSuccess('Emergency access request submitted. Administrator will be notified.');
        
        // Log emergency request
        auditTrailManager.logSecurityIncident({
          action: 'emergency_access_requested',
          success: true,
          metadata: {
            method: 'admin_override',
            reason: emergencyReason,
            requestId: data.request.id,
            contactInfo
          }
        });

        // Start polling for approval
        startPollingForApproval(data.request.id);
      } else {
        setError(data.error || 'Failed to submit emergency access request');
      }
    } catch (error) {
      console.error('Admin override request error:', error);
      setError('Failed to submit request. Please try again.');
    } finally {
      setIsRequestingAccess(false);
    }
  };

  const startPollingForApproval = (requestId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const response = await fetch(`http://localhost:4000/api/auth/emergency/request-status/${requestId}`);
        const data = await response.json();

        if (response.ok) {
          setEmergencyRequest(data.request);

          if (data.request.status === 'approved') {
            clearInterval(pollInterval);
            setSuccess('Emergency access approved! You can now access the system.');
            onEmergencyAccess(true, 'admin_override');
          } else if (data.request.status === 'denied') {
            clearInterval(pollInterval);
            setError('Emergency access request was denied.');
          } else if (data.request.status === 'expired') {
            clearInterval(pollInterval);
            setError('Emergency access request has expired.');
          }
        }
      } catch (error) {
        console.error('Error polling for approval:', error);
      }
    }, 10000); // Poll every 10 seconds

    // Stop polling after 30 minutes
    setTimeout(() => {
      clearInterval(pollInterval);
    }, 30 * 60 * 1000);
  };

  const enableOfflineMode = async () => {
    try {
      setIsLoading(true);
      setError('');

      // Enable offline mode with limited functionality
      const offlineToken = `offline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      localStorage.setItem('offlineMode', 'true');
      localStorage.setItem('offlineToken', offlineToken);
      localStorage.setItem('offlineReason', emergencyReason);

      // Log offline mode activation
      auditTrailManager.logSecurityIncident({
        action: 'offline_mode_enabled',
        success: true,
        metadata: {
          method: 'offline_mode',
          reason: emergencyReason,
          tenantSlug,
          offlineToken
        }
      });

      setSuccess('Offline emergency mode enabled. Limited functionality available.');
      onEmergencyAccess(true, 'offline_mode');
    } catch (error) {
      console.error('Offline mode error:', error);
      setError('Failed to enable offline mode.');
    } finally {
      setIsLoading(false);
    }
  };

  const contactSupport = () => {
    const supportMessage = encodeURIComponent(
      `Emergency Access Request\n\n` +
      `Tenant: ${tenantSlug || 'Unknown'}\n` +
      `Reason: ${emergencyReason}\n` +
      `Contact: ${contactInfo.name} (${contactInfo.phone})\n` +
      `Time: ${new Date().toLocaleString()}\n\n` +
      `Please provide emergency access assistance.`
    );

    // Open support contact options
    const supportPhone = '******-RESTRO-HELP';
    const supportEmail = '<EMAIL>';

    if (confirm(`Contact support for emergency access?\n\nPhone: ${supportPhone}\nEmail: ${supportEmail}`)) {
      window.open(`mailto:${supportEmail}?subject=Emergency Access Request&body=${supportMessage}`, '_blank');
    }
  };

  const getMethodIcon = (type: string) => {
    switch (type) {
      case 'master_key':
        return <Key className="w-6 h-6" />;
      case 'admin_override':
        return <UserCheck className="w-6 h-6" />;
      case 'support_code':
        return <Phone className="w-6 h-6" />;
      case 'offline_mode':
        return <Settings className="w-6 h-6" />;
      default:
        return <Shield className="w-6 h-6" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'approved':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'denied':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'expired':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  return (
    <div className={`w-full max-w-2xl mx-auto p-6 rounded-lg ${
      isDarkMode ? 'bg-gray-800' : 'bg-white'
    } shadow-lg border-2 border-red-200 dark:border-red-800`}>
      
      {/* Emergency Header */}
      <div className="text-center mb-6">
        <div className="w-20 h-20 mx-auto mb-4 rounded-2xl bg-gradient-to-r from-red-500 to-orange-600 flex items-center justify-center animate-pulse">
          <AlertTriangle className="w-10 h-10 text-white" />
        </div>
        <h2 className={`text-2xl font-bold ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Emergency Access System
        </h2>
        <p className={`text-sm ${
          isDarkMode ? 'text-gray-400' : 'text-gray-600'
        }`}>
          Use only in case of emergency when normal authentication is unavailable
        </p>
      </div>

      {/* System Status */}
      <div className={`mb-6 p-4 rounded-lg ${
        systemStatus === 'online' 
          ? 'bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-700'
          : systemStatus === 'offline'
          ? 'bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-700'
          : 'bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-700'
      }`}>
        <div className="flex items-center space-x-3">
          {systemStatus === 'online' ? (
            <CheckCircle className="w-5 h-5 text-green-600" />
          ) : (
            <AlertCircle className="w-5 h-5 text-red-600" />
          )}
          <div>
            <p className={`font-medium ${
              systemStatus === 'online' 
                ? 'text-green-800 dark:text-green-200'
                : 'text-red-800 dark:text-red-200'
            }`}>
              System Status: {systemStatus.charAt(0).toUpperCase() + systemStatus.slice(1)}
            </p>
            <p className={`text-sm ${
              systemStatus === 'online' 
                ? 'text-green-700 dark:text-green-300'
                : 'text-red-700 dark:text-red-300'
            }`}>
              {systemStatus === 'online' 
                ? 'All emergency access methods are available'
                : 'Limited emergency access methods available'}
            </p>
          </div>
        </div>
      </div>

      {/* Emergency Reason */}
      <div className="mb-6">
        <label className={`block text-sm font-medium mb-2 ${
          isDarkMode ? 'text-gray-200' : 'text-gray-700'
        }`}>
          Emergency Reason *
        </label>
        <textarea
          value={emergencyReason}
          onChange={(e) => setEmergencyReason(e.target.value)}
          placeholder="Describe the emergency situation requiring access..."
          className={`w-full px-4 py-3 border-2 rounded-lg ${
            isDarkMode 
              ? 'bg-gray-700 border-gray-600 text-white focus:border-red-400' 
              : 'bg-white border-gray-300 text-gray-900 focus:border-red-500'
          } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
          rows={3}
          required
        />
      </div>

      {/* Contact Information */}
      <div className="mb-6 grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <label className={`block text-sm font-medium mb-2 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            Your Name *
          </label>
          <input
            type="text"
            value={contactInfo.name}
            onChange={(e) => setContactInfo({...contactInfo, name: e.target.value})}
            className={`w-full px-3 py-2 border-2 rounded-lg ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white focus:border-red-400' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-red-500'
            } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
            required
          />
        </div>
        <div>
          <label className={`block text-sm font-medium mb-2 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            Phone Number *
          </label>
          <input
            type="tel"
            value={contactInfo.phone}
            onChange={(e) => setContactInfo({...contactInfo, phone: e.target.value})}
            className={`w-full px-3 py-2 border-2 rounded-lg ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white focus:border-red-400' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-red-500'
            } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
            required
          />
        </div>
        <div>
          <label className={`block text-sm font-medium mb-2 ${
            isDarkMode ? 'text-gray-200' : 'text-gray-700'
          }`}>
            Email
          </label>
          <input
            type="email"
            value={contactInfo.email}
            onChange={(e) => setContactInfo({...contactInfo, email: e.target.value})}
            className={`w-full px-3 py-2 border-2 rounded-lg ${
              isDarkMode 
                ? 'bg-gray-700 border-gray-600 text-white focus:border-red-400' 
                : 'bg-white border-gray-300 text-gray-900 focus:border-red-500'
            } focus:outline-none focus:ring-2 focus:ring-red-500/20`}
          />
        </div>
      </div>

      {/* Emergency Methods */}
      <div className="space-y-4 mb-6">
        <h3 className={`text-lg font-semibold ${
          isDarkMode ? 'text-white' : 'text-gray-900'
        }`}>
          Available Emergency Access Methods:
        </h3>
        
        {emergencyMethods.filter(method => method.available).map((method) => (
          <div
            key={method.type}
            className={`p-4 rounded-lg border-2 cursor-pointer transition-colors ${
              selectedMethod === method.type
                ? 'border-red-500 bg-red-50 dark:bg-red-900/20'
                : isDarkMode 
                ? 'border-gray-600 hover:border-red-400 bg-gray-700 hover:bg-gray-600' 
                : 'border-gray-300 hover:border-red-400 bg-gray-50 hover:bg-gray-100'
            }`}
            onClick={() => setSelectedMethod(method.type)}
          >
            <div className="flex items-start space-x-4">
              <div className={`p-2 rounded-lg ${
                selectedMethod === method.type
                  ? 'bg-red-500 text-white'
                  : 'bg-gray-200 text-gray-600 dark:bg-gray-600 dark:text-gray-300'
              }`}>
                {getMethodIcon(method.type)}
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className={`font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {method.name}
                  </h4>
                  <div className="flex items-center space-x-2">
                    <Clock className="w-4 h-4 text-gray-500" />
                    <span className="text-sm text-gray-500">{method.estimatedTime}</span>
                  </div>
                </div>
                <p className={`text-sm mt-1 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {method.description}
                </p>
                {method.requiresApproval && (
                  <div className="mt-2">
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200">
                      Requires Approval
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Selected Method Interface */}
      {selectedMethod && (
        <div className="space-y-4">
          
          {/* Master Key */}
          {selectedMethod === 'master_key' && (
            <div>
              <label className={`block text-sm font-medium mb-2 ${
                isDarkMode ? 'text-gray-200' : 'text-gray-700'
              }`}>
                Master Emergency Key
              </label>
              <input
                type="password"
                value={emergencyCode}
                onChange={(e) => setEmergencyCode(e.target.value)}
                placeholder="Enter master emergency key"
                className={`w-full px-4 py-3 border-2 rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white focus:border-red-400' 
                    : 'bg-white border-gray-300 text-gray-900 focus:border-red-500'
                } focus:outline-none focus:ring-2 focus:ring-red-500/20 mb-4`}
              />
              <button
                onClick={handleMasterKeyAccess}
                disabled={!emergencyCode.trim() || !emergencyReason.trim() || isLoading}
                className="w-full py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Unlock className="w-5 h-5" />
                )}
                <span>{isLoading ? 'Verifying...' : 'Grant Emergency Access'}</span>
              </button>
            </div>
          )}

          {/* Admin Override */}
          {selectedMethod === 'admin_override' && (
            <div>
              {!emergencyRequest ? (
                <button
                  onClick={requestAdminOverride}
                  disabled={!emergencyReason.trim() || !contactInfo.name || !contactInfo.phone || isRequestingAccess}
                  className="w-full py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
                >
                  {isRequestingAccess ? (
                    <Loader2 className="w-5 h-5 animate-spin" />
                  ) : (
                    <UserCheck className="w-5 h-5" />
                  )}
                  <span>{isRequestingAccess ? 'Submitting Request...' : 'Request Admin Override'}</span>
                </button>
              ) : (
                <div className={`p-4 rounded-lg border ${
                  isDarkMode ? 'border-gray-600 bg-gray-700' : 'border-gray-300 bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      Request Status:
                    </span>
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(emergencyRequest.status)}`}>
                      {emergencyRequest.status.charAt(0).toUpperCase() + emergencyRequest.status.slice(1)}
                    </span>
                  </div>
                  <p className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Request ID: {emergencyRequest.id}
                  </p>
                  <p className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Submitted: {new Date(emergencyRequest.requestedAt).toLocaleString()}
                  </p>
                </div>
              )}
            </div>
          )}

          {/* Support Code */}
          {selectedMethod === 'support_code' && (
            <div>
              <button
                onClick={contactSupport}
                className="w-full py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center justify-center space-x-2"
              >
                <Phone className="w-5 h-5" />
                <span>Contact Emergency Support</span>
              </button>
            </div>
          )}

          {/* Offline Mode */}
          {selectedMethod === 'offline_mode' && (
            <div>
              <div className={`p-4 rounded-lg mb-4 ${
                isDarkMode ? 'bg-yellow-900/20 border border-yellow-700' : 'bg-yellow-50 border border-yellow-200'
              }`}>
                <p className={`text-sm ${
                  isDarkMode ? 'text-yellow-300' : 'text-yellow-700'
                }`}>
                  <strong>Warning:</strong> Offline mode provides limited functionality. Some features may not be available.
                </p>
              </div>
              <button
                onClick={enableOfflineMode}
                disabled={!emergencyReason.trim() || isLoading}
                className="w-full py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center space-x-2"
              >
                {isLoading ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <Settings className="w-5 h-5" />
                )}
                <span>{isLoading ? 'Enabling...' : 'Enable Offline Mode'}</span>
              </button>
            </div>
          )}
        </div>
      )}

      {/* Status Messages */}
      {error && (
        <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
          isDarkMode 
            ? 'bg-red-900/50 border border-red-700 text-red-300' 
            : 'bg-red-50 border border-red-200 text-red-700'
        }`}>
          <AlertCircle className="w-5 h-5 flex-shrink-0" />
          <span className="text-sm">{error}</span>
        </div>
      )}

      {success && (
        <div className={`mt-4 p-3 rounded-lg flex items-center space-x-2 ${
          isDarkMode 
            ? 'bg-green-900/50 border border-green-700 text-green-300' 
            : 'bg-green-50 border border-green-200 text-green-700'
        }`}>
          <CheckCircle className="w-5 h-5 flex-shrink-0" />
          <span className="text-sm">{success}</span>
        </div>
      )}

      {/* Emergency Notice */}
      <div className={`mt-6 p-4 rounded-lg ${
        isDarkMode ? 'bg-red-900/20 border border-red-700' : 'bg-red-50 border border-red-200'
      }`}>
        <div className="flex items-start space-x-3">
          <AlertTriangle className={`w-5 h-5 mt-0.5 ${
            isDarkMode ? 'text-red-400' : 'text-red-600'
          }`} />
          <div>
            <h4 className={`font-medium ${
              isDarkMode ? 'text-red-300' : 'text-red-800'
            }`}>
              Emergency Access Notice
            </h4>
            <p className={`text-sm mt-1 ${
              isDarkMode ? 'text-red-400' : 'text-red-700'
            }`}>
              All emergency access attempts are logged and monitored. Misuse of emergency access may result in account suspension and legal action.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default EmergencyAccessSystem;
