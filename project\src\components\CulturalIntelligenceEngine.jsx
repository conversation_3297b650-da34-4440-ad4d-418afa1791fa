// Phase 3K: AI Cultural Intelligence System
// Cultural Intelligence Engine Component

import React, { useState, useEffect, useCallback } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { 
  GlobeAltIcon,
  HeartIcon,
  BrainIcon,
  UserGroupIcon,
  SparklesIcon,
  EyeIcon,
  ChatBubbleLeftRightIcon,
  MapIcon,
  CalendarIcon,
  TrendingUpIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  CpuChipIcon
} from '@heroicons/react/24/outline';

const CulturalIntelligenceEngine = ({ 
  customerData,
  region = 'north-america',
  onCulturalInsight,
  onEmotionDetected,
  onAdaptationRecommended,
  className = '',
  showAnalytics = true,
  enableEmotionRecognition = true,
  enableMarketIntelligence = true
}) => {
  const { currentLanguage, t } = useTranslation();
  
  const [culturalProfile, setCulturalProfile] = useState(null);
  const [emotionalState, setEmotionalState] = useState(null);
  const [culturalAdaptations, setCulturalAdaptations] = useState(null);
  const [marketIntelligence, setMarketIntelligence] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [analysisHistory, setAnalysisHistory] = useState([]);
  const [culturalInsights, setCulturalInsights] = useState([]);

  // Analyze cultural behavior
  const analyzeCulturalBehavior = useCallback(async (data) => {
    if (!data) return;
    
    setLoading(true);
    try {
      const response = await fetch('/api/cultural/analyze', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          customerData: data,
          region,
          context: 'restaurant',
          interactionHistory: analysisHistory
        })
      });
      
      const result = await response.json();
      setCulturalProfile(result.culturalProfile);
      setCulturalAdaptations(result.adaptations);
      
      // Generate cultural insights
      const insights = [
        {
          type: 'communication',
          title: t('cultural.communication_style', 'Communication Style'),
          description: `Customer prefers ${result.culturalProfile.communicationStyle} communication`,
          recommendation: result.behaviorAnalysis.communicationRecommendations[0],
          confidence: result.confidence,
          priority: 'high'
        },
        {
          type: 'service',
          title: t('cultural.service_adaptation', 'Service Adaptation'),
          description: `Adapt service to ${result.culturalProfile.timeOrientation} time orientation`,
          recommendation: result.behaviorAnalysis.serviceAdaptations.pace,
          confidence: result.confidence,
          priority: 'medium'
        },
        {
          type: 'menu',
          title: t('cultural.menu_recommendations', 'Menu Recommendations'),
          description: `Customer prefers ${result.culturalProfile.diningPreferences.join(', ')} dining`,
          recommendation: result.behaviorAnalysis.menuRecommendations[0],
          confidence: result.confidence,
          priority: 'medium'
        }
      ];
      
      setCulturalInsights(insights);
      
      if (onCulturalInsight) {
        onCulturalInsight(result);
      }
      
      console.log('🧠 Cultural analysis completed:', result);
    } catch (error) {
      console.error('Cultural analysis failed:', error);
      setError('Failed to analyze cultural behavior');
    } finally {
      setLoading(false);
    }
  }, [region, analysisHistory, onCulturalInsight, t]);

  // Analyze emotion with cultural context
  const analyzeEmotion = useCallback(async (voiceData, textData) => {
    if (!enableEmotionRecognition) return;
    
    try {
      const response = await fetch('/api/cultural/emotion', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          voiceData,
          textData,
          culturalContext: region,
          language: currentLanguage
        })
      });
      
      const result = await response.json();
      setEmotionalState(result);
      
      if (onEmotionDetected) {
        onEmotionDetected(result);
      }
      
      console.log('💭 Emotion analysis completed:', result);
    } catch (error) {
      console.error('Emotion analysis failed:', error);
    }
  }, [enableEmotionRecognition, region, currentLanguage, onEmotionDetected]);

  // Get cultural adaptations
  const getCulturalAdaptations = useCallback(async () => {
    if (!culturalProfile) return;
    
    try {
      const response = await fetch('/api/cultural/adapt', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          culturalProfile,
          adaptationType: 'full'
        })
      });
      
      const result = await response.json();
      setCulturalAdaptations(result);
      
      if (onAdaptationRecommended) {
        onAdaptationRecommended(result);
      }
      
      console.log('🎨 Cultural adaptations generated:', result);
    } catch (error) {
      console.error('Cultural adaptation failed:', error);
    }
  }, [culturalProfile, onAdaptationRecommended]);

  // Load market intelligence
  const loadMarketIntelligence = useCallback(async () => {
    if (!enableMarketIntelligence) return;
    
    try {
      const response = await fetch(`/api/cultural/market/${region}`);
      const result = await response.json();
      setMarketIntelligence(result);
      
      console.log('📊 Market intelligence loaded:', result);
    } catch (error) {
      console.error('Market intelligence failed:', error);
    }
  }, [enableMarketIntelligence, region]);

  // Initialize cultural analysis
  useEffect(() => {
    if (customerData) {
      analyzeCulturalBehavior(customerData);
    }
  }, [customerData, analyzeCulturalBehavior]);

  // Load market intelligence on region change
  useEffect(() => {
    loadMarketIntelligence();
  }, [loadMarketIntelligence]);

  // Get cultural adaptations when profile changes
  useEffect(() => {
    if (culturalProfile) {
      getCulturalAdaptations();
    }
  }, [culturalProfile, getCulturalAdaptations]);

  const CulturalInsightCard = ({ insight }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-start justify-between mb-2">
        <div className="flex items-center space-x-2">
          {insight.type === 'communication' && <ChatBubbleLeftRightIcon className="w-5 h-5 text-blue-500" />}
          {insight.type === 'service' && <UserGroupIcon className="w-5 h-5 text-green-500" />}
          {insight.type === 'menu' && <SparklesIcon className="w-5 h-5 text-purple-500" />}
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">{insight.title}</h4>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full ${
          insight.priority === 'high' 
            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
            : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-300'
        }`}>
          {insight.priority}
        </span>
      </div>
      <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{insight.description}</p>
      <p className="text-sm text-blue-600 dark:text-blue-400 mb-2">{insight.recommendation}</p>
      <div className="flex items-center justify-between">
        <span className="text-xs text-gray-500 dark:text-gray-400">
          Confidence: {Math.round(insight.confidence * 100)}%
        </span>
        <button className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
          Apply
        </button>
      </div>
    </div>
  );

  const EmotionalStateIndicator = ({ emotion }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center space-x-2">
          <HeartIcon className="w-5 h-5 text-red-500" />
          <h4 className="text-sm font-medium text-gray-900 dark:text-white">
            {t('cultural.emotional_state', 'Emotional State')}
          </h4>
        </div>
        <span className={`px-2 py-1 text-xs rounded-full ${
          emotion.detectedEmotion === 'happy' || emotion.detectedEmotion === 'excited'
            ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300'
            : emotion.detectedEmotion === 'angry' || emotion.detectedEmotion === 'frustrated'
            ? 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-300'
            : 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-300'
        }`}>
          {emotion.detectedEmotion}
        </span>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Confidence</span>
          <span className="text-sm font-medium">{Math.round(emotion.confidence * 100)}%</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Intensity</span>
          <span className="text-sm font-medium">{Math.round(emotion.emotionalState.intensity * 100)}%</span>
        </div>
        <div className="mt-3">
          <p className="text-sm text-blue-600 dark:text-blue-400">{emotion.recommendedResponse}</p>
        </div>
      </div>
    </div>
  );

  const MarketIntelligenceCard = ({ intelligence }) => (
    <div className="bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-4">
      <div className="flex items-center space-x-2 mb-3">
        <TrendingUpIcon className="w-5 h-5 text-green-500" />
        <h4 className="text-sm font-medium text-gray-900 dark:text-white">
          {t('cultural.market_intelligence', 'Market Intelligence')}
        </h4>
      </div>
      <div className="space-y-2">
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Market Size</span>
          <span className="text-sm font-medium">{intelligence.marketIntelligence.marketSize}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Growth Rate</span>
          <span className="text-sm font-medium text-green-600">{intelligence.marketIntelligence.growthRate}</span>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-sm text-gray-600 dark:text-gray-400">Opportunity Score</span>
          <span className="text-sm font-medium text-blue-600">{Math.round(intelligence.opportunityScore)}/100</span>
        </div>
        <div className="mt-3">
          <h5 className="text-xs font-medium text-gray-900 dark:text-white mb-1">Top Trends</h5>
          <div className="space-y-1">
            {intelligence.marketIntelligence.culturalTrends.slice(0, 3).map((trend, index) => (
              <span key={index} className="inline-block text-xs bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded mr-1">
                {trend}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2">
          <BrainIcon className="w-5 h-5 text-blue-500 animate-spin" />
          <span className="text-gray-600 dark:text-gray-400">
            {t('cultural.analyzing', 'Analyzing cultural intelligence...')}
          </span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`flex items-center justify-center p-8 ${className}`}>
        <div className="flex items-center space-x-2 text-red-500">
          <ExclamationTriangleIcon className="w-5 h-5" />
          <span>{error}</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <BrainIcon className="w-6 h-6 text-blue-500" />
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            {t('cultural.intelligence_engine', 'Cultural Intelligence Engine')}
          </h3>
        </div>
        <div className="flex items-center space-x-2">
          <GlobeAltIcon className="w-4 h-4 text-gray-500" />
          <span className="text-sm text-gray-600 dark:text-gray-400 capitalize">
            {region.replace('-', ' ')}
          </span>
        </div>
      </div>

      {/* Cultural Profile Summary */}
      {culturalProfile && (
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
          <div className="flex items-center space-x-2 mb-3">
            <UserGroupIcon className="w-5 h-5 text-blue-600" />
            <h4 className="text-sm font-medium text-blue-900 dark:text-blue-100">
              Cultural Profile: {culturalProfile.region}
            </h4>
          </div>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-gray-600 dark:text-gray-400">Communication</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">{culturalProfile.communicationStyle}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Time Orientation</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">{culturalProfile.timeOrientation}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Social Dynamics</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">{culturalProfile.socialDynamics}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">Expression</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">{culturalProfile.emotionalExpression}</p>
            </div>
          </div>
        </div>
      )}

      {/* Cultural Insights */}
      {culturalInsights.length > 0 && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            {t('cultural.insights', 'Cultural Insights')}
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {culturalInsights.map((insight, index) => (
              <CulturalInsightCard key={index} insight={insight} />
            ))}
          </div>
        </div>
      )}

      {/* Emotional State */}
      {emotionalState && enableEmotionRecognition && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            {t('cultural.emotional_intelligence', 'Emotional Intelligence')}
          </h4>
          <EmotionalStateIndicator emotion={emotionalState} />
        </div>
      )}

      {/* Market Intelligence */}
      {marketIntelligence && enableMarketIntelligence && (
        <div>
          <h4 className="text-md font-medium text-gray-900 dark:text-white mb-3">
            {t('cultural.market_intelligence', 'Market Intelligence')}
          </h4>
          <MarketIntelligenceCard intelligence={marketIntelligence} />
        </div>
      )}

      {/* Quick Actions */}
      <div className="flex flex-wrap gap-2">
        <button
          onClick={() => analyzeCulturalBehavior(customerData)}
          className="px-3 py-2 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
        >
          {t('cultural.reanalyze', 'Re-analyze')}
        </button>
        <button
          onClick={() => analyzeEmotion(null, 'Sample text for emotion analysis')}
          className="px-3 py-2 text-sm bg-purple-500 text-white rounded hover:bg-purple-600 transition-colors"
        >
          {t('cultural.analyze_emotion', 'Analyze Emotion')}
        </button>
        <button
          onClick={loadMarketIntelligence}
          className="px-3 py-2 text-sm bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
        >
          {t('cultural.refresh_market', 'Refresh Market Data')}
        </button>
      </div>
    </div>
  );
};

export default CulturalIntelligenceEngine;
