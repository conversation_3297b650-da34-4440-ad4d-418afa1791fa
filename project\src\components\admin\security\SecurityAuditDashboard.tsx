import React, { useState, useEffect } from 'react';
import { 
  Shield, 
  AlertTriangle, 
  Eye, 
  Lock, 
  Unlock,
  UserX,
  Activity,
  Clock,
  MapPin,
  Filter,
  Download,
  Search,
  TrendingUp,
  TrendingDown
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>C<PERSON>, Line, <PERSON>hart, Pie, Cell, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';

interface SecurityEvent {
  id: number;
  event_type: string;
  severity: 'info' | 'warning' | 'high' | 'critical';
  user_id?: number;
  user_name?: string;
  source_ip: string;
  event_details: any;
  risk_score: number;
  is_suspicious: boolean;
  is_blocked: boolean;
  geolocation?: any;
  created_at: string;
}

interface AuditEntry {
  id: number;
  audit_type: string;
  action: string;
  resource_type: string;
  resource_id: string;
  user_name: string;
  tenant_name?: string;
  change_summary: string;
  ip_address: string;
  compliance_tags: string[];
  created_at: string;
}

interface SecurityMetrics {
  total_events: number;
  critical_events: number;
  blocked_attempts: number;
  suspicious_activities: number;
  unique_ips: number;
  avg_risk_score: number;
}

export function SecurityAuditDashboard() {
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([]);
  const [auditEntries, setAuditEntries] = useState<AuditEntry[]>([]);
  const [metrics, setMetrics] = useState<SecurityMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('24h');
  const [severityFilter, setSeverityFilter] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchSecurityData();
  }, [timeRange, severityFilter]);

  const fetchSecurityData = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock security events
      const mockEvents: SecurityEvent[] = [
        {
          id: 1,
          event_type: 'login_attempt',
          severity: 'warning',
          user_name: '<EMAIL>',
          source_ip: '*************',
          event_details: { success: false, attempts: 3, method: 'pin' },
          risk_score: 65,
          is_suspicious: true,
          is_blocked: false,
          geolocation: { country: 'Canada', city: 'Toronto' },
          created_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          event_type: 'permission_denied',
          severity: 'high',
          user_name: '<EMAIL>',
          source_ip: '*********',
          event_details: { resource: 'admin_users', action: 'delete', reason: 'insufficient_permissions' },
          risk_score: 45,
          is_suspicious: false,
          is_blocked: true,
          geolocation: { country: 'Canada', city: 'Vancouver' },
          created_at: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          event_type: 'data_access',
          severity: 'info',
          user_name: '<EMAIL>',
          source_ip: '*************',
          event_details: { table: 'tenants', action: 'select', rows: 156, query_time: 245 },
          risk_score: 15,
          is_suspicious: false,
          is_blocked: false,
          geolocation: { country: 'Canada', city: 'Toronto' },
          created_at: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 4,
          event_type: 'configuration_change',
          severity: 'critical',
          user_name: '<EMAIL>',
          source_ip: '*************',
          event_details: { setting: 'security_policy', old_value: 'standard', new_value: 'strict' },
          risk_score: 85,
          is_suspicious: false,
          is_blocked: false,
          geolocation: { country: 'Canada', city: 'Toronto' },
          created_at: new Date(Date.now() - 8 * 60 * 60 * 1000).toISOString()
        }
      ];

      // Mock audit entries
      const mockAuditEntries: AuditEntry[] = [
        {
          id: 1,
          audit_type: 'user_management',
          action: 'create_admin_user',
          resource_type: 'admin_user',
          resource_id: '123',
          user_name: 'Enhanced Admin',
          tenant_name: 'Demo Restaurant',
          change_summary: 'Created new admin user with tenant_manager role',
          ip_address: '*************',
          compliance_tags: ['gdpr', 'access_control'],
          created_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 2,
          audit_type: 'data_access',
          action: 'export_analytics',
          resource_type: 'analytics',
          resource_id: 'revenue_report_2024',
          user_name: 'Main Admin',
          tenant_name: 'Demo Restaurant',
          change_summary: 'Exported revenue analytics for Q4 2024',
          ip_address: '*************',
          compliance_tags: ['gdpr', 'data_export'],
          created_at: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString()
        },
        {
          id: 3,
          audit_type: 'configuration',
          action: 'update_tenant_settings',
          resource_type: 'tenant',
          resource_id: '1',
          user_name: 'Enhanced Admin',
          tenant_name: 'Demo Restaurant',
          change_summary: 'Updated payment gateway configuration',
          ip_address: '*************',
          compliance_tags: ['pci_dss', 'configuration'],
          created_at: new Date(Date.now() - 5 * 60 * 60 * 1000).toISOString()
        }
      ];

      // Mock metrics
      const mockMetrics: SecurityMetrics = {
        total_events: 247,
        critical_events: 3,
        blocked_attempts: 12,
        suspicious_activities: 8,
        unique_ips: 45,
        avg_risk_score: 32.5
      };

      setSecurityEvents(mockEvents);
      setAuditEntries(mockAuditEntries);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching security data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-700 bg-red-100 border-red-200';
      case 'high': return 'text-orange-700 bg-orange-100 border-orange-200';
      case 'warning': return 'text-yellow-700 bg-yellow-100 border-yellow-200';
      case 'info': return 'text-blue-700 bg-blue-100 border-blue-200';
      default: return 'text-gray-700 bg-gray-100 border-gray-200';
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical': return <AlertTriangle className="h-4 w-4" />;
      case 'high': return <Shield className="h-4 w-4" />;
      case 'warning': return <Eye className="h-4 w-4" />;
      case 'info': return <Activity className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getEventTypeIcon = (eventType: string) => {
    switch (eventType) {
      case 'login_attempt': return <Lock className="h-4 w-4" />;
      case 'permission_denied': return <UserX className="h-4 w-4" />;
      case 'data_access': return <Eye className="h-4 w-4" />;
      case 'configuration_change': return <Shield className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const filteredEvents = securityEvents.filter(event => {
    const matchesSeverity = severityFilter === 'all' || event.severity === severityFilter;
    const matchesSearch = searchTerm === '' || 
      event.user_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      event.source_ip.includes(searchTerm) ||
      event.event_type.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesSeverity && matchesSearch;
  });

  // Chart data
  const eventsByType = securityEvents.reduce((acc, event) => {
    acc[event.event_type] = (acc[event.event_type] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  const chartData = Object.entries(eventsByType).map(([type, count]) => ({
    name: type.replace(/_/g, ' '),
    value: count
  }));

  const COLORS = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6'];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-96 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Security Audit Dashboard</h2>
          <p className="text-gray-600 mt-1">
            Monitor security events, audit trails, and compliance status
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <Shield className="h-4 w-4 mr-2" />
            Security Scan
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Activity className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Events</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.total_events}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <AlertTriangle className="h-8 w-8 text-red-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Critical Events</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.critical_events}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <UserX className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Blocked Attempts</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.blocked_attempts}</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Avg Risk Score</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.avg_risk_score.toFixed(1)}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      <div className="bg-white p-6 rounded-lg border border-gray-200">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4 text-gray-500" />
            <span className="text-sm font-medium text-gray-700">Filters:</span>
          </div>
          
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          
          <select
            value={severityFilter}
            onChange={(e) => setSeverityFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="all">All Severities</option>
            <option value="critical">Critical</option>
            <option value="high">High</option>
            <option value="warning">Warning</option>
            <option value="info">Info</option>
          </select>
          
          <div className="relative">
            <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search events..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Event Types Distribution */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Security Events by Type</h3>
          <ResponsiveContainer width="100%" height={300}>
            <PieChart>
              <Pie
                data={chartData}
                cx="50%"
                cy="50%"
                labelLine={false}
                label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                outerRadius={80}
                fill="#8884d8"
                dataKey="value"
              >
                {chartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <Tooltip />
            </PieChart>
          </ResponsiveContainer>
        </div>

        {/* Risk Score Trend */}
        <div className="bg-white p-6 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Risk Score Trend</h3>
          <ResponsiveContainer width="100%" height={300}>
            <LineChart data={securityEvents.map(event => ({
              time: new Date(event.created_at).toLocaleTimeString(),
              risk_score: event.risk_score
            }))}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="time" />
              <YAxis domain={[0, 100]} />
              <Tooltip />
              <Line 
                type="monotone" 
                dataKey="risk_score" 
                stroke="#EF4444" 
                strokeWidth={2}
                dot={{ fill: '#EF4444' }}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Security Events Table */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Recent Security Events</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Event
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Severity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Source IP
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Risk Score
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Time
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredEvents.map(event => (
                <tr key={event.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-blue-600 mr-3">
                        {getEventTypeIcon(event.event_type)}
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">
                          {event.event_type.replace(/_/g, ' ')}
                        </div>
                        <div className="text-sm text-gray-500">
                          {event.event_details.reason || 'Standard event'}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${getSeverityColor(event.severity)}`}>
                      {getSeverityIcon(event.severity)}
                      <span className="ml-1 capitalize">{event.severity}</span>
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {event.user_name || 'Unknown'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 text-gray-400 mr-1" />
                      {event.source_ip}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="text-sm font-medium text-gray-900">{event.risk_score}</div>
                      <div className={`ml-2 ${event.risk_score > 70 ? 'text-red-500' : event.risk_score > 40 ? 'text-yellow-500' : 'text-green-500'}`}>
                        {event.risk_score > 70 ? <TrendingUp className="h-4 w-4" /> : <TrendingDown className="h-4 w-4" />}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {new Date(event.created_at).toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex space-x-2">
                      {event.is_suspicious && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                          Suspicious
                        </span>
                      )}
                      {event.is_blocked && (
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                          Blocked
                        </span>
                      )}
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
