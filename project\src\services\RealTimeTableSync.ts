import { io, Socket } from 'socket.io-client';
import { Table, Employee, Order } from '../types';

export interface TableStatus {
  id: string;
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order' | 'being-seated';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  currentOrderId?: string;
  employeeId?: string;
  employeeName?: string;
  guestCount?: number;
  seatedTime?: Date;
  estimatedTurnTime?: Date;
}

export interface EmployeeTableAssignment {
  id: string;
  employeeId: string;
  employeeName: string;
  tableId: string;
  assignmentTime: Date;
  sessionId: string;
  permissions: string[];
  assignmentType: 'dine_in' | 'reservation' | 'maintenance';
  status: 'active' | 'completed' | 'cancelled';
}

export interface OrderUpdate {
  id: string;
  tableId?: string;
  status: string;
  items: any[];
  total: number;
  employeeId: string;
  timestamp: Date;
}

export interface ConcurrencyLock {
  tableId: string;
  employeeId: string;
  sessionId: string;
  lockTime: Date;
  expiryTime: Date;
  isActive: boolean;
}

class RealTimeTableSync {
  private socket: Socket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private eventListeners: Map<string, Function[]> = new Map();
  private activeLocks: Map<string, ConcurrencyLock> = new Map();

  constructor() {
    this.connect();
  }

  // Connection Management
  async connect(): Promise<Socket> {
    return new Promise((resolve, reject) => {
      if (this.socket?.connected) {
        resolve(this.socket);
        return;
      }

      this.socket = io('http://localhost:4000', {
        transports: ['websocket'],
        autoConnect: true,
        reconnection: true,
        reconnectionAttempts: this.maxReconnectAttempts,
        reconnectionDelay: this.reconnectDelay,
      });

      this.socket.on('connect', () => {
        console.log('🔗 RealTimeTableSync: WebSocket connected');
        this.reconnectAttempts = 0;
        this.joinTableRoom();
        resolve(this.socket!);
      });

      this.socket.on('disconnect', (reason) => {
        console.log('🔌 RealTimeTableSync: WebSocket disconnected:', reason);
        this.handleDisconnection();
      });

      this.socket.on('connect_error', (error) => {
        console.error('❌ RealTimeTableSync: Connection error:', error);
        this.handleConnectionError();
      });

      // Set up event listeners
      this.setupEventListeners();
    });
  }

  private setupEventListeners() {
    if (!this.socket) return;

    // Table status events
    this.socket.on('table:status:changed', (data: TableStatus) => {
      this.emit('tableStatusChanged', data);
    });

    this.socket.on('table:assignment:created', (data: EmployeeTableAssignment) => {
      this.emit('tableAssignmentCreated', data);
    });

    this.socket.on('table:assignment:released', (data: { tableId: string; employeeId: string }) => {
      this.emit('tableAssignmentReleased', data);
    });

    this.socket.on('table:concurrent:blocked', (data: { tableId: string; blockingEmployee: string }) => {
      this.emit('concurrentAccessBlocked', data);
    });

    // Order events
    this.socket.on('order:created', (data: { orderId: string; tableId: string; employeeId: string }) => {
      this.emit('orderCreated', data);
    });

    this.socket.on('order:updated', (data: { orderId: string; updates: Partial<Order> }) => {
      this.emit('orderUpdated', data);
    });

    this.socket.on('order:status:changed', (data: { orderId: string; status: string; tableId: string }) => {
      this.emit('orderStatusChanged', data);
    });

    // Lock management events
    this.socket.on('table:lock:acquired', (data: ConcurrencyLock) => {
      this.activeLocks.set(data.tableId, data);
      this.emit('tableLockAcquired', data);
    });

    this.socket.on('table:lock:released', (data: { tableId: string; sessionId: string }) => {
      this.activeLocks.delete(data.tableId);
      this.emit('tableLockReleased', data);
    });

    this.socket.on('table:lock:expired', (data: { tableId: string; sessionId: string }) => {
      this.activeLocks.delete(data.tableId);
      this.emit('tableLockExpired', data);
    });
  }

  private handleDisconnection() {
    // Clear active locks on disconnection
    this.activeLocks.clear();
    this.emit('connectionLost', { timestamp: new Date() });
  }

  private handleConnectionError() {
    this.reconnectAttempts++;
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('❌ RealTimeTableSync: Max reconnection attempts reached');
      this.emit('connectionFailed', { attempts: this.reconnectAttempts });
    }
  }

  // Room Management
  private joinTableRoom() {
    if (this.socket) {
      this.socket.emit('join:tables');
      console.log('🏠 RealTimeTableSync: Joined tables room');
    }
  }

  private leaveTableRoom() {
    if (this.socket) {
      this.socket.emit('leave:tables');
      console.log('🚪 RealTimeTableSync: Left tables room');
    }
  }

  // Table Status Synchronization
  syncTableStatus(tableId: string, status: TableStatus): void {
    if (this.socket?.connected) {
      this.socket.emit('table:status:update', {
        tableId,
        status,
        timestamp: new Date()
      });
      console.log(`📊 RealTimeTableSync: Synced table ${tableId} status:`, status);
    } else {
      console.warn('⚠️ RealTimeTableSync: Cannot sync - socket not connected');
    }
  }

  // Concurrent Access Prevention
  async preventConcurrentOrders(tableId: string, employeeId: string, sessionId: string): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.socket?.connected) {
        resolve(false);
        return;
      }

      // Check if table is already locked
      const existingLock = this.activeLocks.get(tableId);
      if (existingLock && existingLock.sessionId !== sessionId) {
        console.log(`🔒 RealTimeTableSync: Table ${tableId} is locked by another session`);
        resolve(false);
        return;
      }

      // Request lock
      this.socket.emit('table:lock:request', {
        tableId,
        employeeId,
        sessionId,
        duration: 30000 // 30 seconds
      });

      // Listen for lock response
      const lockResponseHandler = (data: { success: boolean; tableId: string; sessionId: string }) => {
        if (data.tableId === tableId && data.sessionId === sessionId) {
          this.socket?.off('table:lock:response', lockResponseHandler);
          resolve(data.success);
        }
      };

      this.socket.on('table:lock:response', lockResponseHandler);

      // Timeout after 5 seconds
      setTimeout(() => {
        this.socket?.off('table:lock:response', lockResponseHandler);
        resolve(false);
      }, 5000);
    });
  }

  // Release table lock
  releaseLock(tableId: string, sessionId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('table:lock:release', {
        tableId,
        sessionId,
        timestamp: new Date()
      });
      this.activeLocks.delete(tableId);
      console.log(`🔓 RealTimeTableSync: Released lock for table ${tableId}`);
    }
  }

  // Order Updates Broadcasting
  broadcastOrderUpdates(orderId: string, updates: OrderUpdate): void {
    if (this.socket?.connected) {
      this.socket.emit('order:update:broadcast', {
        orderId,
        updates,
        timestamp: new Date()
      });
      console.log(`📢 RealTimeTableSync: Broadcasted order ${orderId} updates`);
    }
  }

  // Employee Assignment Management
  handleEmployeeAssignment(assignment: EmployeeTableAssignment): void {
    if (this.socket?.connected) {
      this.socket.emit('table:assignment:create', {
        assignment,
        timestamp: new Date()
      });
      console.log(`👤 RealTimeTableSync: Created assignment for table ${assignment.tableId}`);
    }
  }

  releaseEmployeeAssignment(tableId: string, employeeId: string, sessionId: string): void {
    if (this.socket?.connected) {
      this.socket.emit('table:assignment:release', {
        tableId,
        employeeId,
        sessionId,
        timestamp: new Date()
      });
      console.log(`👤 RealTimeTableSync: Released assignment for table ${tableId}`);
    }
  }

  // Event Management
  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback?: Function): void {
    if (!this.eventListeners.has(event)) return;

    if (callback) {
      const listeners = this.eventListeners.get(event)!;
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    } else {
      this.eventListeners.delete(event);
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.error(`❌ RealTimeTableSync: Error in event listener for ${event}:`, error);
        }
      });
    }
  }

  // Utility Methods
  isTableLocked(tableId: string): boolean {
    return this.activeLocks.has(tableId);
  }

  getTableLock(tableId: string): ConcurrencyLock | null {
    return this.activeLocks.get(tableId) || null;
  }

  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  // Cleanup
  disconnect(): void {
    if (this.socket) {
      this.leaveTableRoom();
      this.socket.disconnect();
      this.socket = null;
      this.activeLocks.clear();
      this.eventListeners.clear();
      console.log('🔌 RealTimeTableSync: Disconnected and cleaned up');
    }
  }
}

// Singleton instance
export const realTimeTableSync = new RealTimeTableSync();
export default RealTimeTableSync;
