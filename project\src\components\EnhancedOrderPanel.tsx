import React, { useState } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { OrderItem } from '../types';
import { X, Plus, Minus, CreditCard, DollarSign, ShoppingBag, Tag } from 'lucide-react';

const EnhancedOrderPanel: React.FC = () => {
  const { state, dispatch, apiCall } = useEnhancedAppContext();
  const [showPayment, setShowPayment] = useState(false);
  const [tip, setTip] = useState(0);
  const [tabName, setTabName] = useState('');
  const [isEditingTab, setIsEditingTab] = useState(false);
  
  const currentOrder = state.currentOrder;
  
  const handleRemoveItem = (itemId: string) => {
    dispatch({ type: 'REMOVE_ITEM_FROM_ORDER', payload: itemId });
  };
  
  const handleUpdateQuantity = (item: OrderItem, newQuantity: number) => {
    if (newQuantity <= 0) {
      handleRemoveItem(item.id);
    } else {
      dispatch({
        type: 'UPDATE_ITEM_QUANTITY',
        payload: { id: item.id, quantity: newQuantity }
      });
    }
  };
  
  const handleClearOrder = () => {
    dispatch({ type: 'CLEAR_CURRENT_ORDER' });
  };
  
  const handlePayment = async (method: 'cash' | 'card' | 'mobile') => {
    if (!currentOrder) return;
    
    try {
      // Create order in backend
      const orderData = {
        items: currentOrder.items,
        subtotal: currentOrder.subtotal,
        tax: currentOrder.tax,
        total: currentOrder.total + tip,
        payment_method: method,
        tip: tip,
        tab_name: currentOrder.tabName || null,
        tenant_id: state.currentTenant?.id,
        location_id: state.currentLocation?.id,
        employee_id: state.currentEmployee?.id
      };

      const response = await apiCall('/api/orders', {
        method: 'POST',
        body: JSON.stringify(orderData)
      });

      if (response.ok) {
        // Clear the current order
        dispatch({ type: 'CLEAR_CURRENT_ORDER' });
        setShowPayment(false);
        setTip(0);
        
        // Show success message
        alert('Order completed successfully!');
      } else {
        alert('Failed to process order. Please try again.');
      }
    } catch (error) {
      console.error('Error processing order:', error);
      alert('Error processing order. Please try again.');
    }
  };
  
  const handleSaveTab = () => {
    if (tabName.trim()) {
      dispatch({ type: 'SET_TAB_NAME', payload: tabName.trim() });
    }
    setIsEditingTab(false);
  };
  
  const calculateTipAmount = (percentage: number) => {
    if (!currentOrder) return 0;
    return Math.round(currentOrder.subtotal * percentage * 100) / 100;
  };
  
  // If there's no current order, show empty state
  if (!currentOrder || currentOrder.items.length === 0) {
    return (
      <div className="bg-white rounded-lg p-6 flex flex-col items-center justify-center h-full text-center shadow-sm">
        <ShoppingBag className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-xl font-semibold text-gray-900 mb-2">No Items in Order</h3>
        <p className="text-gray-500">
          Add items from the menu to create a new order
        </p>
      </div>
    );
  }
  
  // Payment screen
  if (showPayment) {
    return (
      <div className="bg-white rounded-lg p-6 flex flex-col h-full shadow-sm">
        <h3 className="text-xl font-semibold text-gray-900 mb-6">Payment</h3>
        
        {/* Order Summary */}
        <div className="bg-gray-50 rounded-lg p-4 mb-6">
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Subtotal</span>
            <span className="text-gray-900">${currentOrder.subtotal.toFixed(2)}</span>
          </div>
          <div className="flex justify-between mb-2">
            <span className="text-gray-600">Tax</span>
            <span className="text-gray-900">${currentOrder.tax.toFixed(2)}</span>
          </div>
          <div className="flex justify-between font-semibold">
            <span className="text-gray-600">Tip</span>
            <span className="text-blue-600">${tip.toFixed(2)}</span>
          </div>
          <div className="border-t border-gray-200 my-2"></div>
          <div className="flex justify-between font-bold">
            <span className="text-gray-900">Total</span>
            <span className="text-gray-900">${(currentOrder.subtotal + currentOrder.tax + tip).toFixed(2)}</span>
          </div>
        </div>
        
        {/* Tip options */}
        <div className="mb-6">
          <h4 className="text-gray-900 mb-3">Add Tip</h4>
          <div className="grid grid-cols-4 gap-2">
            {[0, 0.15, 0.18, 0.20].map((percentage, index) => (
              <button
                key={index}
                className={`py-2 rounded-md font-semibold ${
                  tip === calculateTipAmount(percentage)
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => setTip(calculateTipAmount(percentage))}
              >
                {percentage === 0 ? 'None' : `${percentage * 100}%`}
              </button>
            ))}
          </div>
          <div className="mt-3 flex items-center">
            <span className="text-gray-600 mr-2">Custom:</span>
            <input
              type="number"
              min="0"
              step="0.01"
              value={tip}
              onChange={(e) => setTip(Number(e.target.value))}
              className="bg-gray-50 text-gray-900 px-3 py-2 rounded-md w-full border border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        
        {/* Payment methods */}
        <div className="grid grid-cols-3 gap-3 mb-6">
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 rounded-lg p-4 transition-colors border border-gray-200"
            onClick={() => handlePayment('cash')}
          >
            <DollarSign className="h-8 w-8 text-green-500 mb-2" />
            <span className="text-gray-900">Cash</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 rounded-lg p-4 transition-colors border border-gray-200"
            onClick={() => handlePayment('card')}
          >
            <CreditCard className="h-8 w-8 text-blue-500 mb-2" />
            <span className="text-gray-900">Card</span>
          </button>
          <button
            className="flex flex-col items-center justify-center bg-gray-50 hover:bg-gray-100 rounded-lg p-4 transition-colors border border-gray-200"
            onClick={() => handlePayment('mobile')}
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-purple-500 mb-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <rect x="5" y="2" width="14" height="20" rx="2" ry="2" />
              <path d="M12 18h.01" />
            </svg>
            <span className="text-gray-900">Mobile</span>
          </button>
        </div>
        
        {/* Back button */}
        <button
          className="bg-gray-100 hover:bg-gray-200 text-gray-900 py-2 rounded-lg mt-auto transition-colors"
          onClick={() => setShowPayment(false)}
        >
          Back to Order
        </button>
      </div>
    );
  }
  
  // Order view
  return (
    <div className="bg-white rounded-lg p-4 flex flex-col h-full shadow-sm">
      {/* Order header */}
      <div className="flex justify-between items-center mb-4">
        <div className="flex items-center">
          <h3 className="text-xl font-semibold text-gray-900">Current Order</h3>
          {currentOrder.tabName && !isEditingTab && (
            <div className="ml-2 bg-blue-600 text-white text-xs px-2 py-1 rounded-full flex items-center">
              <Tag className="h-3 w-3 mr-1" />
              {currentOrder.tabName}
            </div>
          )}
        </div>
        <button
          className="text-red-500 hover:text-red-600 transition-colors"
          onClick={handleClearOrder}
        >
          <X className="h-5 w-5" />
        </button>
      </div>
      
      {/* Tab name editor */}
      {isEditingTab ? (
        <div className="flex mb-4">
          <input
            type="text"
            value={tabName}
            onChange={(e) => setTabName(e.target.value)}
            placeholder="Enter tab name"
            className="flex-grow bg-gray-50 text-gray-900 px-3 py-2 rounded-l-md focus:outline-none border border-gray-200"
            autoFocus
          />
          <button
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 rounded-r-md transition-colors"
            onClick={handleSaveTab}
          >
            Save
          </button>
        </div>
      ) : (
        <button
          className="mb-4 bg-gray-50 hover:bg-gray-100 text-gray-600 px-3 py-2 rounded-md text-sm flex items-center transition-colors border border-gray-200"
          onClick={() => {
            setTabName(currentOrder.tabName || '');
            setIsEditingTab(true);
          }}
        >
          <Tag className="h-4 w-4 mr-2" />
          {currentOrder.tabName ? 'Edit Tab Name' : 'Add Tab Name'}
        </button>
      )}
      
      {/* Items list */}
      <div className="flex-grow overflow-y-auto mb-4">
        {currentOrder.items.map(item => (
          <div key={item.id} className="bg-gray-50 rounded-lg p-3 mb-2 flex items-center border border-gray-200">
            <div className="flex-grow">
              <div className="flex justify-between">
                <h4 className="font-medium text-gray-900">{item.name}</h4>
                <button
                  className="text-gray-400 hover:text-red-500 transition-colors"
                  onClick={() => handleRemoveItem(item.id)}
                >
                  <X className="h-4 w-4" />
                </button>
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center bg-white rounded-md border border-gray-200">
                  <button
                    className="px-2 py-1 text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => handleUpdateQuantity(item, item.quantity - 1)}
                  >
                    <Minus className="h-4 w-4" />
                  </button>
                  <span className="px-2 text-gray-900">{item.quantity}</span>
                  <button
                    className="px-2 py-1 text-gray-400 hover:text-gray-600 transition-colors"
                    onClick={() => handleUpdateQuantity(item, item.quantity + 1)}
                  >
                    <Plus className="h-4 w-4" />
                  </button>
                </div>
                <span className="text-blue-600 font-semibold">
                  ${(item.price * item.quantity).toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        ))}
      </div>
      
      {/* Order totals */}
      <div className="bg-gray-50 rounded-lg p-3 mb-4 border border-gray-200">
        <div className="flex justify-between mb-1">
          <span className="text-gray-600">Subtotal</span>
          <span className="text-gray-900">${currentOrder.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between mb-1">
          <span className="text-gray-600">Tax ({(state.systemConfig.tax_rate * 100).toFixed(2)}%)</span>
          <span className="text-gray-900">${currentOrder.tax.toFixed(2)}</span>
        </div>
        <div className="border-t border-gray-200 my-2"></div>
        <div className="flex justify-between font-bold">
          <span className="text-gray-900">Total</span>
          <span className="text-gray-900">${currentOrder.total.toFixed(2)}</span>
        </div>
      </div>
      
      {/* Checkout button */}
      <button
        className="bg-green-600 hover:bg-green-700 text-white py-3 rounded-lg font-semibold flex items-center justify-center transition-colors"
        onClick={() => setShowPayment(true)}
      >
        <CreditCard className="h-5 w-5 mr-2" />
        Process Payment
      </button>
    </div>
  );
};

export default EnhancedOrderPanel;
