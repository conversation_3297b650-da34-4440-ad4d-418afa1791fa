import React, { useState, useEffect } from 'react';
import {
  CreditCard,
  Smartphone,
  DollarSign,
  Percent,
  Calculator,
  Receipt,
  CheckCircle,
  AlertCircle,
  X,
  ArrowLeft,
  Printer,
  Mail,
  MessageSquare,
  Users,
  Split,
  Clock,
  Shield,
  Zap,
  TrendingUp
} from 'lucide-react';

interface PaymentMethod {
  id: string;
  name: string;
  type: 'card' | 'cash' | 'digital_wallet' | 'gift_card' | 'check';
  icon: React.ReactNode;
  processingFee: number;
  isEnabled: boolean;
  requiresSignature?: boolean;
  supportsTip?: boolean;
}

interface SplitPayment {
  id: string;
  method: PaymentMethod;
  amount: number;
  status: 'pending' | 'processing' | 'completed' | 'failed';
}

interface ModernPaymentProcessorProps {
  order: any;
  isOpen: boolean;
  onClose: () => void;
  onPaymentComplete: (paymentData: any) => void;
  isDarkMode?: boolean;
}

const ModernPaymentProcessor: React.FC<ModernPaymentProcessorProps> = ({
  order,
  isOpen,
  onClose,
  onPaymentComplete,
  isDarkMode = false
}) => {
  const [currentStep, setCurrentStep] = useState<'method' | 'details' | 'processing' | 'complete'>('method');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<PaymentMethod | null>(null);
  const [tipAmount, setTipAmount] = useState(0);
  const [tipPercentage, setTipPercentage] = useState(0);
  const [customTip, setCustomTip] = useState('');
  const [isSplitPayment, setIsSplitPayment] = useState(false);
  const [splitPayments, setSplitPayments] = useState<SplitPayment[]>([]);
  const [customerInfo, setCustomerInfo] = useState({
    name: '',
    email: '',
    phone: ''
  });
  const [receiptOptions, setReceiptOptions] = useState({
    print: true,
    email: false,
    sms: false
  });
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const paymentMethods: PaymentMethod[] = [
    {
      id: 'card',
      name: 'Credit/Debit Card',
      type: 'card',
      icon: <CreditCard className="w-6 h-6" />,
      processingFee: 0.029,
      isEnabled: true,
      requiresSignature: true,
      supportsTip: true
    },
    {
      id: 'cash',
      name: 'Cash',
      type: 'cash',
      icon: <DollarSign className="w-6 h-6" />,
      processingFee: 0,
      isEnabled: true,
      supportsTip: true
    },
    {
      id: 'apple_pay',
      name: 'Apple Pay',
      type: 'digital_wallet',
      icon: <Smartphone className="w-6 h-6" />,
      processingFee: 0.025,
      isEnabled: true,
      supportsTip: true
    },
    {
      id: 'google_pay',
      name: 'Google Pay',
      type: 'digital_wallet',
      icon: <Smartphone className="w-6 h-6" />,
      processingFee: 0.025,
      isEnabled: true,
      supportsTip: true
    },
    {
      id: 'gift_card',
      name: 'Gift Card',
      type: 'gift_card',
      icon: <Receipt className="w-6 h-6" />,
      processingFee: 0,
      isEnabled: true,
      supportsTip: false
    }
  ];

  const tipPresets = [15, 18, 20, 25];

  useEffect(() => {
    if (isOpen) {
      setCurrentStep('method');
      setSelectedPaymentMethod(null);
      setTipAmount(0);
      setTipPercentage(0);
      setCustomTip('');
      setIsSplitPayment(false);
      setSplitPayments([]);
      setError(null);
    }
  }, [isOpen]);

  const calculateTipAmount = (percentage: number) => {
    return (order.subtotal * percentage) / 100;
  };

  const handleTipSelection = (percentage: number) => {
    setTipPercentage(percentage);
    setTipAmount(calculateTipAmount(percentage));
    setCustomTip('');
  };

  const handleCustomTip = (value: string) => {
    setCustomTip(value);
    const amount = parseFloat(value) || 0;
    setTipAmount(amount);
    setTipPercentage(0);
  };

  const calculateFinalTotal = () => {
    const processingFee = selectedPaymentMethod 
      ? order.total * selectedPaymentMethod.processingFee 
      : 0;
    return order.total + tipAmount + processingFee;
  };

  const handleSplitPayment = () => {
    setIsSplitPayment(true);
    const remainingAmount = calculateFinalTotal();
    setSplitPayments([
      {
        id: '1',
        method: paymentMethods[0],
        amount: remainingAmount / 2,
        status: 'pending'
      },
      {
        id: '2',
        method: paymentMethods[1],
        amount: remainingAmount / 2,
        status: 'pending'
      }
    ]);
  };

  const processPayment = async () => {
    if (!selectedPaymentMethod) return;

    setProcessing(true);
    setError(null);
    setCurrentStep('processing');

    try {
      // Simulate payment processing
      await new Promise(resolve => setTimeout(resolve, 2000));

      const paymentData = {
        orderId: order.id,
        paymentMethod: selectedPaymentMethod,
        amount: order.total,
        tip: tipAmount,
        total: calculateFinalTotal(),
        customerInfo,
        receiptOptions,
        splitPayments: isSplitPayment ? splitPayments : null,
        timestamp: new Date().toISOString(),
        transactionId: `txn_${Date.now()}`
      };

      setCurrentStep('complete');
      setTimeout(() => {
        onPaymentComplete(paymentData);
        onClose();
      }, 3000);

    } catch (err) {
      setError('Payment processing failed. Please try again.');
      setCurrentStep('details');
    } finally {
      setProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className={`w-full max-w-2xl mx-4 rounded-2xl shadow-2xl transition-colors duration-300 ${
        isDarkMode ? 'bg-gray-800 text-white' : 'bg-white text-gray-900'
      }`}>
        {/* Header */}
        <div className={`flex items-center justify-between p-6 border-b transition-colors duration-300 ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center space-x-3">
            <div className={`p-2 rounded-lg ${
              isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
            }`}>
              <CreditCard className="w-6 h-6 text-white" />
            </div>
            <div>
              <h2 className="text-xl font-bold">Payment Processing</h2>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Order Total: ${order.total?.toFixed(2)}
              </p>
            </div>
          </div>
          
          <button
            onClick={onClose}
            className={`p-2 rounded-lg transition-colors duration-200 ${
              isDarkMode 
                ? 'hover:bg-gray-700 text-gray-400' 
                : 'hover:bg-gray-100 text-gray-600'
            }`}
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className={`px-6 py-4 border-b transition-colors duration-300 ${
          isDarkMode ? 'border-gray-700' : 'border-gray-200'
        }`}>
          <div className="flex items-center space-x-4">
            {['method', 'details', 'processing', 'complete'].map((step, index) => (
              <div key={step} className="flex items-center">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                  currentStep === step || (index < ['method', 'details', 'processing', 'complete'].indexOf(currentStep))
                    ? isDarkMode ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
                    : isDarkMode ? 'bg-gray-700 text-gray-400' : 'bg-gray-200 text-gray-600'
                }`}>
                  {index + 1}
                </div>
                {index < 3 && (
                  <div className={`w-12 h-1 mx-2 ${
                    index < ['method', 'details', 'processing', 'complete'].indexOf(currentStep)
                      ? isDarkMode ? 'bg-blue-600' : 'bg-blue-500'
                      : isDarkMode ? 'bg-gray-700' : 'bg-gray-200'
                  }`} />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          {currentStep === 'method' && (
            <div>
              <h3 className="text-lg font-semibold mb-4">Select Payment Method</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {paymentMethods.filter(method => method.isEnabled).map((method) => (
                  <button
                    key={method.id}
                    onClick={() => {
                      setSelectedPaymentMethod(method);
                      setCurrentStep('details');
                    }}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                      isDarkMode 
                        ? 'border-gray-600 hover:border-blue-500 bg-gray-700 hover:bg-gray-600' 
                        : 'border-gray-200 hover:border-blue-500 bg-gray-50 hover:bg-gray-100'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <div className={`p-2 rounded ${
                        isDarkMode ? 'bg-gray-600' : 'bg-white'
                      }`}>
                        {method.icon}
                      </div>
                      <div className="text-left">
                        <p className="font-medium">{method.name}</p>
                        <p className={`text-sm ${
                          isDarkMode ? 'text-gray-400' : 'text-gray-600'
                        }`}>
                          {method.processingFee > 0 
                            ? `${(method.processingFee * 100).toFixed(1)}% fee`
                            : 'No fees'
                          }
                        </p>
                      </div>
                    </div>
                  </button>
                ))}
              </div>
            </div>
          )}

          {currentStep === 'details' && selectedPaymentMethod && (
            <div className="space-y-6">
              <div className="flex items-center space-x-3">
                <button
                  onClick={() => setCurrentStep('method')}
                  className={`p-2 rounded-lg transition-colors duration-200 ${
                    isDarkMode 
                      ? 'hover:bg-gray-700 text-gray-400' 
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}
                >
                  <ArrowLeft className="w-5 h-5" />
                </button>
                <h3 className="text-lg font-semibold">Payment Details</h3>
              </div>

              {/* Tip Selection */}
              {selectedPaymentMethod.supportsTip && (
                <div>
                  <h4 className="font-medium mb-3">Add Tip</h4>
                  <div className="grid grid-cols-4 gap-2 mb-3">
                    {tipPresets.map((percentage) => (
                      <button
                        key={percentage}
                        onClick={() => handleTipSelection(percentage)}
                        className={`py-2 px-3 rounded-lg font-medium transition-all duration-200 ${
                          tipPercentage === percentage
                            ? isDarkMode ? 'bg-blue-600 text-white' : 'bg-blue-500 text-white'
                            : isDarkMode 
                              ? 'bg-gray-700 text-gray-300 hover:bg-gray-600' 
                              : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {percentage}%
                      </button>
                    ))}
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">Custom:</span>
                    <input
                      type="number"
                      placeholder="0.00"
                      value={customTip}
                      onChange={(e) => handleCustomTip(e.target.value)}
                      className={`flex-1 px-3 py-2 rounded-lg border transition-colors duration-300 ${
                        isDarkMode 
                          ? 'bg-gray-700 border-gray-600 text-white' 
                          : 'bg-white border-gray-300 text-gray-900'
                      } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                    />
                  </div>
                </div>
              )}

              {/* Order Summary */}
              <div className={`p-4 rounded-lg ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
              }`}>
                <h4 className="font-medium mb-3">Order Summary</h4>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Subtotal:</span>
                    <span>${order.subtotal?.toFixed(2)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Tax:</span>
                    <span>${order.tax?.toFixed(2)}</span>
                  </div>
                  {tipAmount > 0 && (
                    <div className="flex justify-between">
                      <span>Tip:</span>
                      <span>${tipAmount.toFixed(2)}</span>
                    </div>
                  )}
                  {selectedPaymentMethod.processingFee > 0 && (
                    <div className="flex justify-between">
                      <span>Processing Fee:</span>
                      <span>${(order.total * selectedPaymentMethod.processingFee).toFixed(2)}</span>
                    </div>
                  )}
                  <div className={`flex justify-between font-bold text-lg pt-2 border-t ${
                    isDarkMode ? 'border-gray-600' : 'border-gray-200'
                  }`}>
                    <span>Total:</span>
                    <span>${calculateFinalTotal().toFixed(2)}</span>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-3">
                <button
                  onClick={handleSplitPayment}
                  className={`flex-1 py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-gray-700 hover:bg-gray-600 text-gray-300' 
                      : 'bg-gray-100 hover:bg-gray-200 text-gray-700'
                  }`}
                >
                  <Split className="w-4 h-4 inline mr-2" />
                  Split Payment
                </button>
                
                <button
                  onClick={processPayment}
                  className={`flex-1 py-3 px-4 rounded-lg font-bold transition-all duration-200 ${
                    isDarkMode 
                      ? 'bg-green-600 hover:bg-green-700 text-white' 
                      : 'bg-green-500 hover:bg-green-600 text-white'
                  }`}
                >
                  <Zap className="w-4 h-4 inline mr-2" />
                  Process Payment
                </button>
              </div>
            </div>
          )}

          {currentStep === 'processing' && (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <h3 className="text-lg font-semibold mb-2">Processing Payment</h3>
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'}`}>
                Please wait while we process your payment...
              </p>
            </div>
          )}

          {currentStep === 'complete' && (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">Payment Successful!</h3>
              <p className={`${isDarkMode ? 'text-gray-400' : 'text-gray-600'} mb-4`}>
                Your payment has been processed successfully.
              </p>
              <div className={`p-4 rounded-lg ${
                isDarkMode ? 'bg-gray-700' : 'bg-gray-50'
              }`}>
                <p className="font-medium">Transaction ID: txn_{Date.now()}</p>
                <p className="text-sm">Amount: ${calculateFinalTotal().toFixed(2)}</p>
              </div>
            </div>
          )}

          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <div className="flex items-center space-x-2">
                <AlertCircle className="w-5 h-5 text-red-600" />
                <span className="text-red-800 font-medium">{error}</span>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ModernPaymentProcessor;
