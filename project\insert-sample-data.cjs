// Insert Sample Data for Super Admin Dashboard
// Uses correct user and tenant IDs from existing database

const { Pool } = require('pg');

const dbConfig = {
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
};

async function insertSampleData() {
  console.log('📝 INSERTING SAMPLE DATA FOR SUPER ADMIN DASHBOARD');
  console.log('================================================');
  
  const pool = new Pool(dbConfig);
  
  try {
    const client = await pool.connect();
    console.log('✅ Connected to PostgreSQL database');
    
    // Sample transactions (using correct user IDs: 7-12)
    console.log('\n💰 Inserting sample transactions...');
    await client.query(`
      INSERT INTO transactions (tenant_id, user_id, amount, type, status) VALUES
      (1, 8, 15.50, 'sale', 'completed'),
      (1, 9, 8.75, 'sale', 'completed'),
      (1, 8, 22.00, 'sale', 'completed'),
      (4, 10, 45.25, 'sale', 'completed'),
      (4, 11, 67.80, 'sale', 'completed'),
      (4, 10, 33.50, 'sale', 'completed'),
      (5, 12, 12.25, 'sale', 'completed'),
      (5, 12, 18.90, 'sale', 'completed'),
      (1, 8, 35.75, 'sale', 'completed'),
      (4, 11, 28.50, 'sale', 'completed')
    `);
    console.log('✅ Sample transactions inserted');
    
    // Sample security audits (using correct user IDs: 7-12)
    console.log('\n🔒 Inserting sample security audits...');
    await client.query(`
      INSERT INTO security_audits (tenant_id, user_id, event, severity, ip_address, details, status) VALUES
      (NULL, 7, 'Super admin login', 'low', '*************', 'Successful super admin authentication', 'resolved'),
      (1, 8, 'Failed login attempt', 'medium', '*************', 'Multiple failed login attempts detected', 'investigating'),
      (4, 10, 'Password changed', 'low', '*************', 'User password successfully updated', 'resolved'),
      (NULL, NULL, 'Suspicious API access', 'high', '*********', 'Unusual API access pattern detected', 'open'),
      (5, 12, 'Account locked', 'medium', '************', 'Account locked due to failed attempts', 'resolved'),
      (1, 9, 'Successful login', 'low', '*************', 'Employee login successful', 'resolved'),
      (4, 11, 'Menu access', 'low', '*************', 'Manager accessed menu configuration', 'resolved')
    `);
    console.log('✅ Sample security audits inserted');
    
    // Sample system activity (using correct user IDs: 7-12)
    console.log('\n📊 Inserting sample system activity...');
    await client.query(`
      INSERT INTO system_activity (tenant_id, user_id, action, type, details) VALUES
      (NULL, 7, 'System backup completed', 'success', 'Daily automated backup completed successfully'),
      (1, 8, 'New user created', 'info', 'New employee account created for Demo Restaurant'),
      (4, 10, 'Menu updated', 'info', 'Restaurant menu items updated'),
      (NULL, NULL, 'Database maintenance', 'info', 'Routine database optimization completed'),
      (5, 12, 'Payment processed', 'success', 'Customer payment processed successfully'),
      (1, 9, 'Shift started', 'info', 'Employee shift started'),
      (4, 11, 'Report generated', 'info', 'Daily sales report generated'),
      (NULL, 7, 'Security scan completed', 'success', 'Automated security scan completed')
    `);
    console.log('✅ Sample system activity inserted');
    
    // Sample system metrics
    console.log('\n📈 Inserting sample system metrics...');
    await client.query(`
      INSERT INTO system_metrics (metric_name, metric_value, metric_unit, tenant_id) VALUES
      ('cpu_usage', 45.2, 'percent', NULL),
      ('memory_usage', 67.8, 'percent', NULL),
      ('disk_usage', 78.5, 'percent', NULL),
      ('response_time', 125.3, 'milliseconds', NULL),
      ('active_connections', 15, 'count', NULL),
      ('daily_transactions', 156, 'count', 1),
      ('daily_revenue', 1250.75, 'dollars', 1),
      ('daily_transactions', 89, 'count', 4),
      ('daily_revenue', 2150.50, 'dollars', 4),
      ('daily_transactions', 45, 'count', 5),
      ('daily_revenue', 675.25, 'dollars', 5),
      ('error_rate', 0.2, 'percent', NULL),
      ('uptime', 99.8, 'percent', NULL)
    `);
    console.log('✅ Sample system metrics inserted');
    
    // Sample AI analytics
    console.log('\n🤖 Inserting sample AI analytics...');
    await client.query(`
      INSERT INTO ai_analytics (tenant_id, analysis_type, predictions, confidence_score, recommendations) VALUES
      (1, 'revenue_forecast', '{"next_month": 15000, "growth_rate": 12.5}', 87.5, '{"pricing": "increase_coffee_by_5_percent", "inventory": "stock_more_pastries"}'),
      (4, 'customer_behavior', '{"churn_risk": 8.2, "lifetime_value": 1250}', 92.1, '{"retention": "loyalty_program", "upsell": "premium_menu_items"}'),
      (5, 'inventory_optimization', '{"overstock": 12, "understock": 5}', 89.3, '{"reduce": ["sauce_packets"], "increase": ["pizza_dough"]}'),
      (1, 'staff_optimization', '{"optimal_hours": 35, "efficiency": 92}', 85.7, '{"scheduling": "reduce_overlap", "training": "focus_on_speed"}'),
      (4, 'pricing_optimization', '{"recommended_changes": 8, "potential_increase": 12}', 91.2, '{"items": ["premium_dishes"], "strategy": "dynamic_pricing"}')
    `);
    console.log('✅ Sample AI analytics inserted');
    
    // Update user timestamps
    console.log('\n⏰ Updating user timestamps...');
    await client.query(`UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '1 hour' WHERE id IN (7, 8, 10, 12)`);
    await client.query(`UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '3 hours' WHERE id IN (9, 11)`);
    console.log('✅ User timestamps updated');
    
    client.release();
    
    console.log('\n🎉 SAMPLE DATA INSERTION COMPLETED SUCCESSFULLY!');
    console.log('✅ All sample data inserted with correct foreign key references');
    console.log('✅ Super Admin Dashboard ready for PostgreSQL integration');
    console.log('✅ Real data available for all dashboard features');
    
  } catch (error) {
    console.error('\n💥 SAMPLE DATA INSERTION FAILED!');
    console.error('❌ Error:', error.message);
    throw error;
  } finally {
    await pool.end();
  }
}

// Run the insertion
insertSampleData()
  .then(() => {
    console.log('\n🚀 Database is fully ready for Super Admin Dashboard!');
    process.exit(0);
  })
  .catch(error => {
    console.error('💥 Insertion failed:', error);
    process.exit(1);
  });
