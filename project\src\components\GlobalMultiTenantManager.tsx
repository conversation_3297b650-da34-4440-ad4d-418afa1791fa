import React, { useState, useEffect } from 'react';
import {
  Globe,
  Building,
  Users,
  MapPin,
  TrendingUp,
  DollarSign,
  Activity,
  Shield,
  Settings,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Eye,
  Download,
  Upload,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  BarChart3,
  PieChart,
  LineChart,
  Zap,
  Database,
  Server,
  Wifi,
  Lock
} from 'lucide-react';

interface GlobalTenant {
  id: string;
  name: string;
  businessName: string;
  slug: string;
  region: string;
  country: string;
  timezone: string;
  currency: string;
  status: 'active' | 'inactive' | 'suspended' | 'trial';
  plan: 'starter' | 'professional' | 'enterprise' | 'custom';
  createdAt: string;
  lastActivity: string;
  metrics: {
    totalRevenue: number;
    monthlyRevenue: number;
    activeUsers: number;
    totalOrders: number;
    averageOrderValue: number;
    customerSatisfaction: number;
  };
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    storageUsed: number;
    bandwidthUsed: number;
    apiCalls: number;
  };
  billing: {
    currentPeriodStart: string;
    currentPeriodEnd: string;
    amountDue: number;
    paymentStatus: 'paid' | 'pending' | 'overdue' | 'failed';
    nextBillingDate: string;
  };
  compliance: {
    gdprCompliant: boolean;
    ccpaCompliant: boolean;
    pciCompliant: boolean;
    lastAudit: string;
    complianceScore: number;
  };
}

interface RegionalStats {
  region: string;
  tenantCount: number;
  totalRevenue: number;
  averagePerformance: number;
  resourceUtilization: number;
  complianceScore: number;
}

interface GlobalAlert {
  id: string;
  type: 'performance' | 'security' | 'billing' | 'compliance' | 'capacity';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  tenantId?: string;
  region?: string;
  timestamp: string;
  acknowledged: boolean;
}

const GlobalMultiTenantManager: React.FC = () => {
  const [tenants, setTenants] = useState<GlobalTenant[]>([]);
  const [regionalStats, setRegionalStats] = useState<RegionalStats[]>([]);
  const [globalAlerts, setGlobalAlerts] = useState<GlobalAlert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRegion, setSelectedRegion] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [selectedPlan, setSelectedPlan] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadGlobalData();
  }, []);

  const loadGlobalData = async () => {
    try {
      setIsLoading(true);

      // Load global tenants
      const tenantsResponse = await fetch('http://localhost:4000/api/admin/global/tenants', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (tenantsResponse.ok) {
        const tenantsData = await tenantsResponse.json();
        setTenants(tenantsData);
      }

      // Load regional statistics
      const statsResponse = await fetch('http://localhost:4000/api/admin/global/regional-stats', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        setRegionalStats(statsData);
      }

      // Load global alerts
      const alertsResponse = await fetch('http://localhost:4000/api/admin/global/alerts', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      if (alertsResponse.ok) {
        const alertsData = await alertsResponse.json();
        setGlobalAlerts(alertsData);
      }

    } catch (error) {
      console.error('Error loading global data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const suspendTenant = async (tenantId: string, reason: string) => {
    try {
      await fetch(`http://localhost:4000/api/admin/global/tenants/${tenantId}/suspend`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ reason })
      });

      setTenants(tenants.map(tenant =>
        tenant.id === tenantId ? { ...tenant, status: 'suspended' } : tenant
      ));
    } catch (error) {
      console.error('Error suspending tenant:', error);
    }
  };

  const activateTenant = async (tenantId: string) => {
    try {
      await fetch(`http://localhost:4000/api/admin/global/tenants/${tenantId}/activate`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`,
          'Content-Type': 'application/json'
        }
      });

      setTenants(tenants.map(tenant =>
        tenant.id === tenantId ? { ...tenant, status: 'active' } : tenant
      ));
    } catch (error) {
      console.error('Error activating tenant:', error);
    }
  };

  const exportGlobalReport = async (format: 'csv' | 'pdf' | 'excel') => {
    try {
      const response = await fetch(`http://localhost:4000/api/admin/global/export?format=${format}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('authToken')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `global-tenant-report-${new Date().toISOString().split('T')[0]}.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Error exporting report:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'trial':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'inactive':
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
      case 'suspended':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'starter':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'professional':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'enterprise':
        return 'text-purple-600 bg-purple-100 dark:bg-purple-900 dark:text-purple-300';
      case 'custom':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const formatCurrency = (amount: number, currency: string = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const filteredTenants = tenants.filter(tenant => {
    const matchesSearch = tenant.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.businessName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         tenant.slug.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRegion = selectedRegion === 'all' || tenant.region === selectedRegion;
    const matchesStatus = selectedStatus === 'all' || tenant.status === selectedStatus;
    const matchesPlan = selectedPlan === 'all' || tenant.plan === selectedPlan;

    return matchesSearch && matchesRegion && matchesStatus && matchesPlan;
  });

  const regions = [...new Set(tenants.map(t => t.region))];
  const statuses = ['active', 'inactive', 'suspended', 'trial'];
  const plans = ['starter', 'professional', 'enterprise', 'custom'];

  if (isLoading) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Globe className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading global tenant data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>

      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                Global Multi-Tenant Management
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Manage tenants across all regions with real-time analytics and controls
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={() => exportGlobalReport('pdf')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export Report</span>
              </button>

              <button
                onClick={loadGlobalData}
                className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'bg-gray-700 hover:bg-gray-600 text-gray-300'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-600'
                }`}
              >
                <RefreshCw className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Global Alerts */}
        {globalAlerts.filter(alert => !alert.acknowledged && alert.severity === 'critical').length > 0 && (
          <div className="mb-8">
            <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-3">
                <AlertTriangle className="w-5 h-5 text-red-600" />
                <h3 className="text-lg font-semibold text-red-800 dark:text-red-200">
                  Critical Global Alerts
                </h3>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {globalAlerts.filter(alert => !alert.acknowledged && alert.severity === 'critical').map((alert) => (
                  <div key={alert.id} className="bg-white dark:bg-gray-800 p-3 rounded border">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="font-medium text-gray-900 dark:text-white">{alert.title}</p>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{alert.message}</p>
                        {alert.region && (
                          <span className="text-xs text-gray-500">Region: {alert.region}</span>
                        )}
                      </div>
                      <button className="px-2 py-1 text-xs bg-red-600 text-white rounded hover:bg-red-700">
                        View
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Regional Overview */}
        <div className="mb-8">
          <h2 className={`text-2xl font-bold mb-6 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            Regional Overview
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {regionalStats.map((region) => (
              <div key={region.region} className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {region.region}
                  </h3>
                  <MapPin className="w-5 h-5 text-blue-600" />
                </div>

                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Tenants
                    </span>
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {region.tenantCount}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Revenue
                    </span>
                    <span className={`font-medium ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {formatCurrency(region.totalRevenue)}
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Performance
                    </span>
                    <span className={`font-medium ${
                      region.averagePerformance >= 90 ? 'text-green-600' :
                      region.averagePerformance >= 70 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {region.averagePerformance.toFixed(1)}%
                    </span>
                  </div>

                  <div className="flex justify-between">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Compliance
                    </span>
                    <span className={`font-medium ${
                      region.complianceScore >= 95 ? 'text-green-600' :
                      region.complianceScore >= 80 ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {region.complianceScore.toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Filters and Search */}
        <div className={`p-6 rounded-lg ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        } shadow-lg mb-8`}>
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search tenants..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                    isDarkMode
                      ? 'bg-gray-700 border-gray-600 text-white'
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>
            </div>

            <div className="flex gap-4">
              <select
                value={selectedRegion}
                onChange={(e) => setSelectedRegion(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="all">All Regions</option>
                {regions.map(region => (
                  <option key={region} value={region}>{region}</option>
                ))}
              </select>

              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="all">All Status</option>
                {statuses.map(status => (
                  <option key={status} value={status}>
                    {status.charAt(0).toUpperCase() + status.slice(1)}
                  </option>
                ))}
              </select>

              <select
                value={selectedPlan}
                onChange={(e) => setSelectedPlan(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode
                    ? 'bg-gray-700 border-gray-600 text-white'
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                <option value="all">All Plans</option>
                {plans.map(plan => (
                  <option key={plan} value={plan}>
                    {plan.charAt(0).toUpperCase() + plan.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Tenants Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {filteredTenants.map((tenant) => (
            <div key={tenant.id} className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg hover:shadow-xl transition-shadow`}>

              {/* Tenant Header */}
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenant.name}
                  </h3>
                  <p className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    {tenant.businessName}
                  </p>
                </div>
                <button className={`p-2 rounded-lg transition-colors ${
                  isDarkMode
                    ? 'hover:bg-gray-700 text-gray-400'
                    : 'hover:bg-gray-100 text-gray-600'
                }`}>
                  <MoreVertical className="w-4 h-4" />
                </button>
              </div>

              {/* Status and Plan */}
              <div className="flex items-center space-x-2 mb-4">
                <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(tenant.status)}`}>
                  {tenant.status}
                </span>
                <span className={`px-2 py-1 text-xs rounded-full ${getPlanColor(tenant.plan)}`}>
                  {tenant.plan}
                </span>
              </div>

              {/* Metrics */}
              <div className="space-y-3 mb-4">
                <div className="flex justify-between">
                  <span className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Monthly Revenue
                  </span>
                  <span className={`font-medium ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {formatCurrency(tenant.metrics.monthlyRevenue, tenant.currency)}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Active Users
                  </span>
                  <span className={`font-medium ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenant.metrics.activeUsers}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Orders
                  </span>
                  <span className={`font-medium ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {tenant.metrics.totalOrders.toLocaleString()}
                  </span>
                </div>

                <div className="flex justify-between">
                  <span className={`text-sm ${
                    isDarkMode ? 'text-gray-400' : 'text-gray-600'
                  }`}>
                    Satisfaction
                  </span>
                  <span className={`font-medium ${
                    tenant.metrics.customerSatisfaction >= 4.5 ? 'text-green-600' :
                    tenant.metrics.customerSatisfaction >= 4.0 ? 'text-yellow-600' : 'text-red-600'
                  }`}>
                    {tenant.metrics.customerSatisfaction.toFixed(1)}/5.0
                  </span>
                </div>
              </div>

              {/* Resource Usage */}
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
                    Resource Usage
                  </span>
                  <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>
                    {Math.max(tenant.resources.cpuUsage, tenant.resources.memoryUsage).toFixed(0)}%
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700">
                  <div
                    className={`h-2 rounded-full ${
                      Math.max(tenant.resources.cpuUsage, tenant.resources.memoryUsage) >= 90 ? 'bg-red-600' :
                      Math.max(tenant.resources.cpuUsage, tenant.resources.memoryUsage) >= 70 ? 'bg-yellow-600' : 'bg-green-600'
                    }`}
                    style={{ width: `${Math.max(tenant.resources.cpuUsage, tenant.resources.memoryUsage)}%` }}
                  ></div>
                </div>
              </div>

              {/* Actions */}
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <button className="p-2 text-blue-600 hover:bg-blue-100 dark:hover:bg-blue-900 rounded">
                    <Eye className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                    <Edit className="w-4 h-4" />
                  </button>
                  <button className="p-2 text-gray-600 hover:bg-gray-100 dark:hover:bg-gray-700 rounded">
                    <Settings className="w-4 h-4" />
                  </button>
                </div>

                <div className="text-xs text-gray-500">
                  {tenant.region} • {tenant.country}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredTenants.length === 0 && (
          <div className="text-center py-12">
            <Building className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className={`text-lg font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-900'
            }`}>
              No tenants found
            </h3>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-500' : 'text-gray-600'
            }`}>
              Try adjusting your search criteria or filters
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GlobalMultiTenantManager;