import React, { useState, useEffect } from 'react';
import { Table, Employee } from '../types';
import { 
  Users, 
  MapPin, 
  Clock, 
  CheckCircle, 
  AlertCircle, 
  X,
  RefreshCw,
  Search,
  Filter
} from 'lucide-react';

interface TableSelectionModalProps {
  availableTables: Table[];
  currentEmployee: Employee;
  onTableSelect: (table: Table) => void;
  onCancel: () => void;
  loading?: boolean;
  error?: string | null;
}

const TableSelectionModal: React.FC<TableSelectionModalProps> = ({
  availableTables,
  currentEmployee,
  onTableSelect,
  onCancel,
  loading = false,
  error = null
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSection, setSelectedSection] = useState<string>('all');
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [showConfirmation, setShowConfirmation] = useState(false);

  // Get unique sections from available tables
  const sections = ['all', ...new Set(availableTables.map(table => table.section).filter(Boolean))];

  // Filter tables based on search and section
  const filteredTables = availableTables.filter(table => {
    const matchesSearch = searchTerm === '' || 
      table.number.toString().includes(searchTerm) ||
      table.section?.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSection = selectedSection === 'all' || table.section === selectedSection;
    
    return matchesSearch && matchesSection;
  });

  // Group tables by section for better visualization
  const tablesBySection = filteredTables.reduce((acc, table) => {
    const section = table.section || 'General';
    if (!acc[section]) {
      acc[section] = [];
    }
    acc[section].push(table);
    return acc;
  }, {} as Record<string, Table[]>);

  const handleTableClick = (table: Table) => {
    setSelectedTable(table);
    setShowConfirmation(true);
  };

  const handleConfirmSelection = () => {
    if (selectedTable) {
      onTableSelect(selectedTable);
    }
  };

  const getTableStatusColor = (table: Table) => {
    switch (table.status) {
      case 'available':
        return 'bg-green-100 border-green-300 hover:bg-green-200';
      case 'occupied':
        return 'bg-red-100 border-red-300 cursor-not-allowed';
      case 'reserved':
        return 'bg-yellow-100 border-yellow-300 cursor-not-allowed';
      case 'needs-cleaning':
        return 'bg-orange-100 border-orange-300 cursor-not-allowed';
      case 'out-of-order':
        return 'bg-gray-100 border-gray-300 cursor-not-allowed';
      default:
        return 'bg-gray-100 border-gray-300';
    }
  };

  const getTableIcon = (table: Table) => {
    switch (table.shape) {
      case 'circle':
        return '⭕';
      case 'oval':
        return '🔵';
      default:
        return '⬜';
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-2xl w-full max-w-6xl h-5/6 flex flex-col">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-t-xl">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">Select Table for Dine-In Order</h2>
              <p className="text-blue-100">
                Employee: {currentEmployee.name} | Available Tables: {availableTables.length}
              </p>
            </div>
            <button
              onClick={onCancel}
              className="text-white hover:text-gray-200 transition-colors"
              disabled={loading}
            >
              <X className="w-6 h-6" />
            </button>
          </div>
        </div>

        {/* Search and Filter Bar */}
        <div className="p-4 border-b border-gray-200 bg-gray-50">
          <div className="flex items-center space-x-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search by table number or section..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-500" />
              <select
                value={selectedSection}
                onChange={(e) => setSelectedSection(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {sections.map(section => (
                  <option key={section} value={section}>
                    {section === 'all' ? 'All Sections' : section}
                  </option>
                ))}
              </select>
            </div>

            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              disabled={loading}
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mx-4 mt-4 bg-red-50 border-l-4 border-red-400 p-4">
            <div className="flex">
              <AlertCircle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {/* Tables Grid */}
        <div className="flex-1 overflow-y-auto p-6">
          {loading ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="w-16 h-16 border-4 border-gray-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4"></div>
                <p className="text-gray-600">Loading available tables...</p>
              </div>
            </div>
          ) : filteredTables.length === 0 ? (
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <MapPin className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Available Tables</h3>
                <p className="text-gray-600">
                  {searchTerm || selectedSection !== 'all' 
                    ? 'No tables match your search criteria.' 
                    : 'All tables are currently occupied or unavailable.'}
                </p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {Object.entries(tablesBySection).map(([section, tables]) => (
                <div key={section}>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <MapPin className="w-5 h-5 mr-2 text-blue-600" />
                    {section} ({tables.length} tables)
                  </h3>
                  
                  <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4">
                    {tables.map(table => (
                      <button
                        key={table.id}
                        onClick={() => handleTableClick(table)}
                        disabled={table.status !== 'available' || loading}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 ${getTableStatusColor(table)} ${
                          table.status === 'available' ? 'hover:shadow-md transform hover:scale-105' : ''
                        }`}
                      >
                        <div className="text-center">
                          <div className="text-2xl mb-2">{getTableIcon(table)}</div>
                          <div className="font-semibold text-gray-900">Table {table.number}</div>
                          <div className="text-sm text-gray-600 flex items-center justify-center mt-1">
                            <Users className="w-3 h-3 mr-1" />
                            {table.seats} seats
                          </div>
                          {table.tableType && (
                            <div className="text-xs text-gray-500 mt-1 capitalize">
                              {table.tableType.replace('_', ' ')}
                            </div>
                          )}
                          <div className={`text-xs font-medium mt-2 px-2 py-1 rounded-full ${
                            table.status === 'available' 
                              ? 'bg-green-200 text-green-800' 
                              : 'bg-red-200 text-red-800'
                          }`}>
                            {table.status.replace('_', ' ').replace('-', ' ')}
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="bg-gray-50 px-6 py-4 rounded-b-xl border-t border-gray-200">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              Select an available table to continue with the dine-in order process
            </div>
            <button
              onClick={onCancel}
              className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              disabled={loading}
            >
              Cancel
            </button>
          </div>
        </div>
      </div>

      {/* Confirmation Modal */}
      {showConfirmation && selectedTable && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-60">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">Confirm Table Selection</h3>
            
            <div className="bg-blue-50 rounded-lg p-4 mb-4">
              <div className="flex items-center space-x-3">
                <div className="text-3xl">{getTableIcon(selectedTable)}</div>
                <div>
                  <div className="font-semibold text-gray-900">Table {selectedTable.number}</div>
                  <div className="text-sm text-gray-600">
                    {selectedTable.seats} seats • {selectedTable.section || 'General'}
                  </div>
                  {selectedTable.tableType && (
                    <div className="text-xs text-gray-500 capitalize">
                      {selectedTable.tableType.replace('_', ' ')}
                    </div>
                  )}
                </div>
              </div>
            </div>

            <p className="text-gray-600 mb-6">
              You are about to assign Table {selectedTable.number} to your session. 
              This will require employee PIN confirmation in the next step.
            </p>

            <div className="flex justify-end space-x-3">
              <button
                onClick={() => {
                  setShowConfirmation(false);
                  setSelectedTable(null);
                }}
                className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
              >
                Back
              </button>
              <button
                onClick={handleConfirmSelection}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center space-x-2"
              >
                <CheckCircle className="w-4 h-4" />
                <span>Confirm Selection</span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default TableSelectionModal;
