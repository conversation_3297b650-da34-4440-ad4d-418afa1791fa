/**
 * Apply Database Schema Fixes and Populate Real Data
 */

const { Pool } = require('pg');
const fs = require('fs');

// Database connection
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function applyDatabaseFixes() {
  console.log('🔧 APPLYING DATABASE SCHEMA FIXES & REAL DATA');
  console.log('==============================================');

  try {
    // Test connection
    console.log('\n📡 Testing database connection...');
    const client = await pool.connect();
    console.log('✅ Database connected successfully');

    // Apply schema fixes
    console.log('\n🔧 Applying schema fixes...');
    
    // Fix missing amount column
    try {
      await client.query(`
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name='orders' AND column_name='amount') THEN
                ALTER TABLE orders ADD COLUMN amount DECIMAL(10,2) DEFAULT 0.00;
                RAISE NOTICE 'Added amount column to orders table';
            ELSE
                RAISE NOTICE 'Amount column already exists';
            END IF;
        END $$;
      `);
      console.log('✅ Orders table schema updated');
    } catch (error) {
      console.log('⚠️ Orders table update:', error.message);
    }

    // Fix exchange rate precision
    try {
      await client.query(`
        DO $$ 
        BEGIN
            IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name='exchange_rates') THEN
                ALTER TABLE exchange_rates ALTER COLUMN rate TYPE DECIMAL(15,8);
                RAISE NOTICE 'Updated exchange_rates precision';
            END IF;
        END $$;
      `);
      console.log('✅ Exchange rates precision updated');
    } catch (error) {
      console.log('⚠️ Exchange rates update:', error.message);
    }

    // Create categories first
    console.log('\n📂 Creating product categories...');
    const categories = [
      { name: 'Beverages', description: 'Hot and cold drinks', color: '#3B82F6', sort_order: 1 },
      { name: 'Main Courses', description: 'Full meals and entrees', color: '#10B981', sort_order: 2 },
      { name: 'Appetizers', description: 'Starters and small plates', color: '#F59E0B', sort_order: 3 },
      { name: 'Desserts', description: 'Sweet treats and desserts', color: '#EF4444', sort_order: 4 }
    ];

    for (const category of categories) {
      try {
        await client.query(`
          INSERT INTO categories (tenant_id, name, description, color, sort_order, is_active, created_at, updated_at) 
          VALUES (1, $1, $2, $3, $4, true, NOW(), NOW())
          ON CONFLICT (tenant_id, name) DO UPDATE SET
            description = EXCLUDED.description,
            color = EXCLUDED.color,
            updated_at = NOW()
        `, [category.name, category.description, category.color, category.sort_order]);
        console.log(`✅ Category: ${category.name}`);
      } catch (error) {
        console.log(`⚠️ Category ${category.name}:`, error.message);
      }
    }

    // Get category IDs
    const categoryResult = await client.query('SELECT id, name FROM categories WHERE tenant_id = 1');
    const categoryMap = {};
    categoryResult.rows.forEach(row => {
      categoryMap[row.name] = row.id;
    });

    // Create comprehensive product data
    console.log('\n📦 Creating comprehensive product data...');
    const products = [
      // Beverages
      { name: 'Espresso', description: 'Rich, bold espresso shot made from premium coffee beans', price: 3.50, cost: 0.80, category: 'Beverages', sku: 'BEV-ESP-001' },
      { name: 'Cappuccino', description: 'Classic Italian coffee with steamed milk and foam', price: 4.50, cost: 1.20, category: 'Beverages', sku: 'BEV-CAP-002' },
      { name: 'Latte', description: 'Smooth espresso with steamed milk and light foam', price: 4.75, cost: 1.30, category: 'Beverages', sku: 'BEV-LAT-003' },
      { name: 'Americano', description: 'Espresso shots with hot water for a clean taste', price: 3.75, cost: 0.90, category: 'Beverages', sku: 'BEV-AME-004' },
      { name: 'Mocha', description: 'Espresso with chocolate syrup and steamed milk', price: 5.25, cost: 1.50, category: 'Beverages', sku: 'BEV-MOC-005' },
      { name: 'Green Tea', description: 'Premium organic green tea leaves', price: 3.25, cost: 0.70, category: 'Beverages', sku: 'BEV-GTE-006' },
      { name: 'Fresh Orange Juice', description: 'Freshly squeezed orange juice', price: 4.95, cost: 1.80, category: 'Beverages', sku: 'BEV-ORA-007' },
      { name: 'Sparkling Water', description: 'Premium sparkling mineral water', price: 2.95, cost: 0.60, category: 'Beverages', sku: 'BEV-SPA-008' },

      // Main Courses
      { name: 'Grilled Chicken Breast', description: 'Herb-seasoned grilled chicken with vegetables', price: 18.99, cost: 8.50, category: 'Main Courses', sku: 'MAIN-CHI-001' },
      { name: 'Beef Burger', description: 'Angus beef patty with lettuce, tomato, and fries', price: 16.99, cost: 7.20, category: 'Main Courses', sku: 'MAIN-BUR-002' },
      { name: 'Salmon Fillet', description: 'Atlantic salmon with lemon butter sauce', price: 22.99, cost: 12.00, category: 'Main Courses', sku: 'MAIN-SAL-003' },
      { name: 'Vegetarian Pasta', description: 'Penne pasta with seasonal vegetables and herbs', price: 14.99, cost: 5.50, category: 'Main Courses', sku: 'MAIN-PAS-004' },
      { name: 'Ribeye Steak', description: 'Premium ribeye steak cooked to perfection', price: 28.99, cost: 15.50, category: 'Main Courses', sku: 'MAIN-RIB-005' },
      { name: 'Fish and Chips', description: 'Beer-battered fish with crispy fries', price: 17.99, cost: 7.80, category: 'Main Courses', sku: 'MAIN-FIS-006' },

      // Appetizers
      { name: 'Caesar Salad', description: 'Crisp romaine lettuce with Caesar dressing', price: 11.99, cost: 4.20, category: 'Appetizers', sku: 'APP-CAE-001' },
      { name: 'Chicken Wings', description: 'Spicy buffalo wings with blue cheese dip', price: 13.99, cost: 5.80, category: 'Appetizers', sku: 'APP-WIN-002' },
      { name: 'Mozzarella Sticks', description: 'Golden fried mozzarella with marinara sauce', price: 9.99, cost: 3.50, category: 'Appetizers', sku: 'APP-MOZ-003' },
      { name: 'Garlic Bread', description: 'Toasted bread with garlic butter and herbs', price: 7.99, cost: 2.20, category: 'Appetizers', sku: 'APP-GAR-004' },
      { name: 'Soup of the Day', description: 'Chef\'s special soup made fresh daily', price: 8.99, cost: 2.80, category: 'Appetizers', sku: 'APP-SOU-005' },

      // Desserts
      { name: 'Chocolate Cake', description: 'Rich chocolate layer cake with ganache', price: 8.99, cost: 3.20, category: 'Desserts', sku: 'DES-CHO-001' },
      { name: 'Cheesecake', description: 'New York style cheesecake with berry compote', price: 7.99, cost: 2.80, category: 'Desserts', sku: 'DES-CHE-002' },
      { name: 'Ice Cream Sundae', description: 'Vanilla ice cream with chocolate sauce', price: 6.99, cost: 2.10, category: 'Desserts', sku: 'DES-ICE-003' },
      { name: 'Apple Pie', description: 'Homemade apple pie with cinnamon', price: 7.49, cost: 2.50, category: 'Desserts', sku: 'DES-APP-004' }
    ];

    for (const product of products) {
      try {
        const categoryId = categoryMap[product.category];
        await client.query(`
          INSERT INTO products (tenant_id, name, description, price, cost, category_id, sku, is_active, created_at, updated_at) 
          VALUES (1, $1, $2, $3, $4, $5, $6, true, NOW(), NOW())
          ON CONFLICT (sku) DO UPDATE SET
            name = EXCLUDED.name,
            description = EXCLUDED.description,
            price = EXCLUDED.price,
            cost = EXCLUDED.cost,
            updated_at = NOW()
        `, [product.name, product.description, product.price, product.cost, categoryId, product.sku]);
        console.log(`✅ Product: ${product.name} ($${product.price})`);
      } catch (error) {
        console.log(`⚠️ Product ${product.name}:`, error.message);
      }
    }

    // Create real users
    console.log('\n👥 Creating user accounts...');
    const users = [
      { name: 'Super Administrator', email: '<EMAIL>', pin: '123456', role: 'super_admin' },
      { name: 'Restaurant Manager', email: '<EMAIL>', pin: '567890', role: 'manager' },
      { name: 'Senior Cashier', email: '<EMAIL>', pin: '111222', role: 'employee' },
      { name: 'Kitchen Staff', email: '<EMAIL>', pin: '333444', role: 'employee' },
      { name: 'Tenant Admin', email: '<EMAIL>', pin: '555666', role: 'tenant_admin' }
    ];

    for (const user of users) {
      try {
        await client.query(`
          INSERT INTO users (tenant_id, name, email, pin, role, is_active, created_at, updated_at) 
          VALUES (1, $1, $2, $3, $4, true, NOW(), NOW())
          ON CONFLICT (pin) DO UPDATE SET
            name = EXCLUDED.name,
            email = EXCLUDED.email,
            role = EXCLUDED.role,
            updated_at = NOW()
        `, [user.name, user.email, user.pin, user.role]);
        console.log(`✅ User: ${user.name} (${user.role}) - PIN: ${user.pin}`);
      } catch (error) {
        console.log(`⚠️ User ${user.name}:`, error.message);
      }
    }

    // Create tenant
    console.log('\n🏢 Creating tenant information...');
    try {
      await client.query(`
        INSERT INTO tenants (name, slug, contact_email, contact_phone, address, city, state, country, timezone, currency, is_active, created_at, updated_at) 
        VALUES ('Demo Restaurant', 'demo-restaurant', '<EMAIL>', '******-0123', '123 Main Street', 'New York', 'NY', 'USA', 'America/New_York', 'USD', true, NOW(), NOW())
        ON CONFLICT (slug) DO UPDATE SET
          name = EXCLUDED.name,
          contact_email = EXCLUDED.contact_email,
          updated_at = NOW()
      `);
      console.log('✅ Tenant: Demo Restaurant');
    } catch (error) {
      console.log('⚠️ Tenant creation:', error.message);
    }

    client.release();

    console.log('\n🎉 DATABASE FIXES & REAL DATA APPLIED SUCCESSFULLY');
    console.log('==================================================');
    console.log('✅ Schema fixes applied');
    console.log('✅ 4 product categories created');
    console.log('✅ 22 real products added');
    console.log('✅ 5 user accounts created');
    console.log('✅ Tenant information added');
    console.log('\n🔑 User Access Credentials:');
    console.log('👑 Super Admin: PIN 123456');
    console.log('👨‍💼 Manager: PIN 567890');
    console.log('👤 Cashier: PIN 111222');
    console.log('👨‍🍳 Kitchen: PIN 333444');
    console.log('🏢 Tenant Admin: PIN 555666');

  } catch (error) {
    console.error('💥 Error applying database fixes:', error);
  } finally {
    await pool.end();
  }
}

applyDatabaseFixes();
