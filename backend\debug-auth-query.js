const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

async function debugAuthQuery() {
  try {
    // First, get the tenant
    console.log('1. Getting tenant with slug "barpos-system":');
    const tenantResult = await pool.query(`
      SELECT * FROM tenants WHERE slug = $1
    `, ['barpos-system']);
    
    console.table(tenantResult.rows);
    
    if (tenantResult.rows.length === 0) {
      console.log('❌ No tenant found!');
      return;
    }
    
    const tenant = tenantResult.rows[0];
    console.log(`✅ Found tenant: ${tenant.name} (ID: ${tenant.id})`);
    
    // Now run the exact same query as authentication
    console.log('\n2. Running authentication employee query:');
    const employeeResult = await pool.query(`
      SELECT e.*
      FROM employees e
      WHERE e.tenant_id = $1 AND (e.is_active = true OR e.is_active IS NULL)
    `, [tenant.id]);
    
    console.log(`Found ${employeeResult.rows.length} employees:`);
    console.table(employeeResult.rows.map(emp => ({
      id: emp.id,
      name: emp.name,
      pin: emp.pin,
      pin_hash: emp.pin_hash ? emp.pin_hash.substring(0, 20) + '...' : null,
      role: emp.role,
      is_active: emp.is_active,
      tenant_id: emp.tenant_id
    })));
    
    // Test PIN comparison for each employee
    console.log('\n3. Testing PIN comparisons:');
    const testPins = ['567890', '111111', '222222'];
    
    for (const testPin of testPins) {
      console.log(`\nTesting PIN: ${testPin}`);
      for (const employee of employeeResult.rows) {
        if (employee.pin === testPin) {
          console.log(`✅ Match found: ${employee.name} (${employee.role}) - PIN: ${employee.pin}`);
        }
      }
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    await pool.end();
  }
}

debugAuthQuery();
