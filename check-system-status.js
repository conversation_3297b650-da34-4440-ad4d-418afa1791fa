const http = require('http');
const fs = require('fs');
const path = require('path');

async function checkSystemStatus() {
    console.log('🔍 RESTROFLOW System Status Check');
    console.log('=' .repeat(50));
    
    let results = {
        backend: false,
        frontend: false,
        authentication: false,
        posInterface: false,
        superAdminInterface: false
    };
    
    // Check Backend Server (Port 4000)
    console.log('\n🖥️  Checking Backend Server...');
    try {
        const backendResponse = await makeRequest('localhost', 4000, '/api/health/public');
        if (backendResponse.includes('status') || backendResponse.includes('healthy')) {
            console.log('✅ Backend server is running on port 4000');
            results.backend = true;
        } else {
            console.log('⚠️  Backend server responded but may have issues');
        }
    } catch (error) {
        console.log('❌ Backend server is not responding on port 4000');
        console.log(`   Error: ${error.message}`);
    }
    
    // Check Frontend Server (Port 3000)
    console.log('\n🌐 Checking Frontend Server...');
    try {
        const frontendResponse = await makeRequest('localhost', 3000, '/');
        if (frontendResponse.includes('html') || frontendResponse.includes('DOCTYPE')) {
            console.log('✅ Frontend server is running on port 3000');
            results.frontend = true;
        } else {
            console.log('⚠️  Frontend server responded but may have issues');
        }
    } catch (error) {
        console.log('❌ Frontend server is not responding on port 3000');
        console.log(`   Error: ${error.message}`);
    }
    
    // Check Authentication System
    console.log('\n🔐 Checking Authentication System...');
    try {
        const authPayload = JSON.stringify({
            pin: '999999',
            tenant_slug: 'barpos-system'
        });
        
        const authResponse = await makePostRequest('localhost', 4000, '/api/auth/login', authPayload);
        const authData = JSON.parse(authResponse);
        
        if (authData.token || authData.success) {
            console.log('✅ Authentication system is working');
            results.authentication = true;
        } else {
            console.log('⚠️  Authentication system may have issues');
        }
    } catch (error) {
        console.log('❌ Authentication system is not working');
        console.log(`   Error: ${error.message}`);
    }
    
    // Check File Existence
    console.log('\n📁 Checking Interface Files...');
    
    // Check POS Interface Files
    const posFiles = [
        'standalone-pos.html',
        'working-pos.html',
        'project/unified-pos.html'
    ];
    
    let posFileExists = false;
    for (const file of posFiles) {
        if (fs.existsSync(file)) {
            console.log(`✅ POS interface found: ${file}`);
            results.posInterface = true;
            posFileExists = true;
            break;
        }
    }
    
    if (!posFileExists) {
        console.log('❌ No POS interface files found');
    }
    
    // Check Super Admin Interface Files
    const adminFiles = [
        'project/super-admin.html',
        'super-admin.html'
    ];
    
    let adminFileExists = false;
    for (const file of adminFiles) {
        if (fs.existsSync(file)) {
            console.log(`✅ Super Admin interface found: ${file}`);
            results.superAdminInterface = true;
            adminFileExists = true;
            break;
        }
    }
    
    if (!adminFileExists) {
        console.log('❌ No Super Admin interface files found');
    }
    
    // Summary
    console.log('\n' + '='.repeat(50));
    console.log('📊 SYSTEM STATUS SUMMARY');
    console.log('='.repeat(50));
    
    const totalChecks = Object.keys(results).length;
    const passedChecks = Object.values(results).filter(result => result).length;
    const systemHealth = ((passedChecks / totalChecks) * 100).toFixed(1);
    
    console.log(`System Health: ${systemHealth}% (${passedChecks}/${totalChecks})`);
    console.log('');
    
    Object.entries(results).forEach(([check, result]) => {
        const status = result ? '✅ ONLINE' : '❌ OFFLINE';
        const checkName = check.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase());
        console.log(`${status} - ${checkName}`);
    });
    
    // Recommendations
    console.log('\n🔧 RECOMMENDATIONS:');
    if (!results.backend) {
        console.log('• Start backend server: cd backend && npm run dev');
    }
    if (!results.frontend) {
        console.log('• Frontend server is running on port 3000');
    }
    if (!results.authentication) {
        console.log('• Check database connection and authentication endpoints');
    }
    if (!results.posInterface) {
        console.log('• Verify POS interface files exist and are accessible');
    }
    if (!results.superAdminInterface) {
        console.log('• Verify Super Admin interface files exist and are accessible');
    }
    
    if (systemHealth >= 80) {
        console.log('\n🎉 System is healthy and ready for use!');
        console.log('   • POS System: http://localhost:3000/../standalone-pos.html');
        console.log('   • Super Admin: http://localhost:3000/super-admin.html');
        console.log('   • Login: http://localhost:3000/../login.html');
    } else {
        console.log('\n⚠️  System needs attention before use.');
    }
}

function makeRequest(hostname, port, path) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname,
            port,
            path,
            method: 'GET',
            timeout: 5000
        };
        
        const req = http.request(options, (res) => {
            let data = '';
            res.on('data', (chunk) => data += chunk);
            res.on('end', () => resolve(data));
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Request timeout')));
        req.end();
    });
}

function makePostRequest(hostname, port, path, data) {
    return new Promise((resolve, reject) => {
        const options = {
            hostname,
            port,
            path,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(data)
            },
            timeout: 5000
        };
        
        const req = http.request(options, (res) => {
            let responseData = '';
            res.on('data', (chunk) => responseData += chunk);
            res.on('end', () => resolve(responseData));
        });
        
        req.on('error', reject);
        req.on('timeout', () => reject(new Error('Request timeout')));
        req.write(data);
        req.end();
    });
}

// Run the check
checkSystemStatus().catch(console.error);
