// Database Migration Runner for RESTROFLOW
const fs = require('fs');
const path = require('path');
const { pool } = require('../config/connection');

class MigrationRunner {
  constructor() {
    this.migrationsPath = __dirname;
    this.migrationTable = 'schema_migrations';
  }

  async init() {
    try {
      // Create migrations table if it doesn't exist
      await pool.query(`
        CREATE TABLE IF NOT EXISTS ${this.migrationTable} (
          id SERIAL PRIMARY KEY,
          filename VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP DEFAULT NOW(),
          checksum VARCHAR(64)
        )
      `);
      console.log('✅ Migration tracking table initialized');
    } catch (error) {
      console.error('💥 Error initializing migration table:', error);
      throw error;
    }
  }

  async getExecutedMigrations() {
    try {
      const result = await pool.query(`
        SELECT filename FROM ${this.migrationTable} 
        ORDER BY executed_at ASC
      `);
      return result.rows.map(row => row.filename);
    } catch (error) {
      console.error('💥 Error fetching executed migrations:', error);
      return [];
    }
  }

  async getPendingMigrations() {
    try {
      const executed = await this.getExecutedMigrations();
      const allFiles = fs.readdirSync(this.migrationsPath)
        .filter(file => file.endsWith('.sql'))
        .sort();
      
      return allFiles.filter(file => !executed.includes(file));
    } catch (error) {
      console.error('💥 Error getting pending migrations:', error);
      return [];
    }
  }

  async executeMigration(filename) {
    try {
      const filePath = path.join(this.migrationsPath, filename);
      const sql = fs.readFileSync(filePath, 'utf8');
      
      console.log(`🔄 Executing migration: ${filename}`);
      
      // Start transaction
      await pool.query('BEGIN');
      
      try {
        // Execute migration SQL
        await pool.query(sql);
        
        // Record migration as executed
        await pool.query(`
          INSERT INTO ${this.migrationTable} (filename) 
          VALUES ($1)
        `, [filename]);
        
        // Commit transaction
        await pool.query('COMMIT');
        
        console.log(`✅ Migration completed: ${filename}`);
        return true;
        
      } catch (migrationError) {
        // Rollback on error
        await pool.query('ROLLBACK');
        throw migrationError;
      }
      
    } catch (error) {
      console.error(`💥 Error executing migration ${filename}:`, error);
      throw error;
    }
  }

  async runPendingMigrations() {
    try {
      await this.init();
      
      const pending = await this.getPendingMigrations();
      
      if (pending.length === 0) {
        console.log('✅ No pending migrations');
        return;
      }
      
      console.log(`📋 Found ${pending.length} pending migrations:`);
      pending.forEach(file => console.log(`  - ${file}`));
      
      for (const migration of pending) {
        await this.executeMigration(migration);
      }
      
      console.log('🎉 All migrations completed successfully');
      
    } catch (error) {
      console.error('💥 Migration failed:', error);
      throw error;
    }
  }

  async rollbackLastMigration() {
    try {
      const result = await pool.query(`
        SELECT filename FROM ${this.migrationTable} 
        ORDER BY executed_at DESC 
        LIMIT 1
      `);
      
      if (result.rows.length === 0) {
        console.log('ℹ️ No migrations to rollback');
        return;
      }
      
      const lastMigration = result.rows[0].filename;
      console.log(`🔄 Rolling back migration: ${lastMigration}`);
      
      // Remove from tracking table
      await pool.query(`
        DELETE FROM ${this.migrationTable} 
        WHERE filename = $1
      `, [lastMigration]);
      
      console.log(`✅ Rollback completed: ${lastMigration}`);
      console.log('⚠️ Note: SQL rollback must be done manually');
      
    } catch (error) {
      console.error('💥 Error during rollback:', error);
      throw error;
    }
  }

  async getStatus() {
    try {
      await this.init();
      
      const executed = await this.getExecutedMigrations();
      const pending = await this.getPendingMigrations();
      
      console.log('\n📊 Migration Status:');
      console.log(`  Executed: ${executed.length}`);
      console.log(`  Pending: ${pending.length}`);
      
      if (executed.length > 0) {
        console.log('\n✅ Executed Migrations:');
        executed.forEach(file => console.log(`  - ${file}`));
      }
      
      if (pending.length > 0) {
        console.log('\n⏳ Pending Migrations:');
        pending.forEach(file => console.log(`  - ${file}`));
      }
      
      return { executed, pending };
      
    } catch (error) {
      console.error('💥 Error getting migration status:', error);
      throw error;
    }
  }
}

// CLI interface
async function main() {
  const runner = new MigrationRunner();
  const command = process.argv[2];
  
  try {
    switch (command) {
      case 'run':
        await runner.runPendingMigrations();
        break;
        
      case 'status':
        await runner.getStatus();
        break;
        
      case 'rollback':
        await runner.rollbackLastMigration();
        break;
        
      default:
        console.log('Usage: node migrate.js [run|status|rollback]');
        console.log('  run     - Execute all pending migrations');
        console.log('  status  - Show migration status');
        console.log('  rollback - Rollback last migration');
        process.exit(1);
    }
    
  } catch (error) {
    console.error('💥 Migration command failed:', error);
    process.exit(1);
  } finally {
    await pool.end();
  }
}

// Export for programmatic use
module.exports = MigrationRunner;

// Run CLI if called directly
if (require.main === module) {
  main();
}
