<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTROFLOW - Performance Monitoring Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .metric-card {
            transition: all 0.3s ease;
        }
        .metric-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }
        .status-healthy { background-color: #10b981; }
        .status-warning { background-color: #f59e0b; }
        .status-critical { background-color: #ef4444; }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .chart-container {
            position: relative;
            height: 300px;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-gray-50 to-blue-50 min-h-screen">
    <div class="container mx-auto p-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900 mb-2">📊 Performance Monitoring Dashboard</h1>
                    <p class="text-gray-600">Real-time system health, performance metrics, and monitoring alerts</p>
                </div>
                <div class="flex items-center gap-4">
                    <div class="text-right">
                        <div class="text-sm text-gray-500">RESTROFLOW v2.0.0</div>
                        <div class="text-sm text-gray-500">Last Updated: <span id="last-updated">--:--:--</span></div>
                    </div>
                    <button id="refresh-data" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                        🔄 Refresh
                    </button>
                </div>
            </div>
        </div>

        <!-- System Status Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="metric-card bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">System Status</p>
                        <p class="text-2xl font-bold text-gray-900" id="system-status">Healthy</p>
                    </div>
                    <div class="status-indicator status-healthy pulse" id="status-indicator"></div>
                </div>
            </div>
            
            <div class="metric-card bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Uptime</p>
                        <p class="text-2xl font-bold text-gray-900" id="uptime">--</p>
                    </div>
                    <div class="text-green-500 text-3xl">⏱️</div>
                </div>
            </div>
            
            <div class="metric-card bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Active Users</p>
                        <p class="text-2xl font-bold text-gray-900" id="active-users">--</p>
                    </div>
                    <div class="text-blue-500 text-3xl">👥</div>
                </div>
            </div>
            
            <div class="metric-card bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600">Requests/Min</p>
                        <p class="text-2xl font-bold text-gray-900" id="requests-per-min">--</p>
                    </div>
                    <div class="text-purple-500 text-3xl">📈</div>
                </div>
            </div>
        </div>

        <!-- Performance Metrics -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- CPU & Memory Usage -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">🖥️ System Resources</h3>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <canvas id="resourceChart"></canvas>
                    </div>
                </div>
            </div>

            <!-- Response Time -->
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">⚡ Response Time</h3>
                </div>
                <div class="p-6">
                    <div class="chart-container">
                        <canvas id="responseTimeChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Database Performance -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">🗄️ Database Metrics</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Connection Pool</span>
                        <span class="font-semibold" id="db-connections">--/--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Query Time (avg)</span>
                        <span class="font-semibold" id="db-query-time">-- ms</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Slow Queries</span>
                        <span class="font-semibold text-red-600" id="db-slow-queries">--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Cache Hit Rate</span>
                        <span class="font-semibold text-green-600" id="db-cache-hit">--%</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">🔄 API Performance</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Total Requests</span>
                        <span class="font-semibold" id="api-total-requests">--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Success Rate</span>
                        <span class="font-semibold text-green-600" id="api-success-rate">--%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Error Rate</span>
                        <span class="font-semibold text-red-600" id="api-error-rate">--%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Avg Response</span>
                        <span class="font-semibold" id="api-avg-response">-- ms</span>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">💾 Cache Performance</h3>
                </div>
                <div class="p-6 space-y-4">
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Memory Usage</span>
                        <span class="font-semibold" id="cache-memory">-- MB</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Hit Rate</span>
                        <span class="font-semibold text-green-600" id="cache-hit-rate">--%</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Keys Stored</span>
                        <span class="font-semibold" id="cache-keys">--</span>
                    </div>
                    <div class="flex justify-between items-center">
                        <span class="text-gray-600">Evictions</span>
                        <span class="font-semibold text-orange-600" id="cache-evictions">--</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Alerts and Notifications -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">🚨 Active Alerts</h3>
                </div>
                <div class="p-6">
                    <div id="active-alerts" class="space-y-3">
                        <!-- Alerts will be populated here -->
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md">
                <div class="px-6 py-4 border-b border-gray-200">
                    <h3 class="text-lg font-semibold text-gray-900">📋 Recent Events</h3>
                </div>
                <div class="p-6">
                    <div id="recent-events" class="space-y-3 max-h-64 overflow-y-auto">
                        <!-- Events will be populated here -->
                    </div>
                </div>
            </div>
        </div>

        <!-- Error Logs -->
        <div class="bg-white rounded-lg shadow-md">
            <div class="px-6 py-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h3 class="text-lg font-semibold text-gray-900">🐛 Error Logs</h3>
                    <button id="clear-logs" class="text-sm bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700">
                        Clear Logs
                    </button>
                </div>
            </div>
            <div class="p-6">
                <div id="error-logs" class="bg-gray-50 rounded-lg p-4 h-64 overflow-y-auto font-mono text-sm">
                    <div class="text-gray-500">Error logs will appear here...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        const API_BASE = 'http://localhost:4000/api';
        let charts = {};
        let monitoringData = {
            systemStatus: 'healthy',
            uptime: 0,
            activeUsers: 0,
            requestsPerMin: 0,
            cpuUsage: [],
            memoryUsage: [],
            responseTime: [],
            dbMetrics: {},
            apiMetrics: {},
            cacheMetrics: {},
            alerts: [],
            events: [],
            errorLogs: []
        };

        // Initialize dashboard
        function initializeDashboard() {
            setupCharts();
            loadMonitoringData();
            startAutoRefresh();
            updateLastUpdated();
        }

        function setupCharts() {
            // Resource Usage Chart
            const resourceCtx = document.getElementById('resourceChart').getContext('2d');
            charts.resource = new Chart(resourceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'CPU Usage (%)',
                        data: [],
                        borderColor: '#3b82f6',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.4
                    }, {
                        label: 'Memory Usage (%)',
                        data: [],
                        borderColor: '#10b981',
                        backgroundColor: 'rgba(16, 185, 129, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true,
                            max: 100
                        }
                    }
                }
            });

            // Response Time Chart
            const responseCtx = document.getElementById('responseTimeChart').getContext('2d');
            charts.responseTime = new Chart(responseCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Response Time (ms)',
                        data: [],
                        borderColor: '#f59e0b',
                        backgroundColor: 'rgba(245, 158, 11, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        async function loadMonitoringData() {
            try {
                // Try to fetch real monitoring data
                const response = await fetch(`${API_BASE}/monitoring/metrics`);
                if (response.ok) {
                    const data = await response.json();
                    updateDashboardWithRealData(data);
                } else {
                    // Use mock data if API is not available
                    updateDashboardWithMockData();
                }
            } catch (error) {
                console.warn('Failed to load monitoring data, using mock data:', error);
                updateDashboardWithMockData();
            }
        }

        function updateDashboardWithRealData(data) {
            // Update system status
            document.getElementById('system-status').textContent = data.systemStatus || 'Healthy';
            document.getElementById('uptime').textContent = formatUptime(data.uptime || 0);
            document.getElementById('active-users').textContent = data.activeUsers || 0;
            document.getElementById('requests-per-min').textContent = data.requestsPerMin || 0;

            // Update database metrics
            document.getElementById('db-connections').textContent = `${data.dbMetrics?.activeConnections || 0}/${data.dbMetrics?.maxConnections || 10}`;
            document.getElementById('db-query-time').textContent = `${data.dbMetrics?.avgQueryTime || 0} ms`;
            document.getElementById('db-slow-queries').textContent = data.dbMetrics?.slowQueries || 0;
            document.getElementById('db-cache-hit').textContent = `${data.dbMetrics?.cacheHitRate || 0}%`;

            // Update API metrics
            document.getElementById('api-total-requests').textContent = data.apiMetrics?.totalRequests || 0;
            document.getElementById('api-success-rate').textContent = `${data.apiMetrics?.successRate || 0}%`;
            document.getElementById('api-error-rate').textContent = `${data.apiMetrics?.errorRate || 0}%`;
            document.getElementById('api-avg-response').textContent = `${data.apiMetrics?.avgResponse || 0} ms`;

            // Update cache metrics
            document.getElementById('cache-memory').textContent = `${data.cacheMetrics?.memoryUsage || 0} MB`;
            document.getElementById('cache-hit-rate').textContent = `${data.cacheMetrics?.hitRate || 0}%`;
            document.getElementById('cache-keys').textContent = data.cacheMetrics?.keysStored || 0;
            document.getElementById('cache-evictions').textContent = data.cacheMetrics?.evictions || 0;

            // Update charts
            updateCharts(data);
            
            // Update alerts and events
            updateAlerts(data.alerts || []);
            updateEvents(data.events || []);
        }

        function updateDashboardWithMockData() {
            const now = new Date();
            
            // Generate mock data
            const mockData = {
                systemStatus: 'Healthy',
                uptime: Date.now() - (24 * 60 * 60 * 1000), // 24 hours ago
                activeUsers: Math.floor(Math.random() * 50) + 10,
                requestsPerMin: Math.floor(Math.random() * 100) + 50,
                cpuUsage: Math.floor(Math.random() * 30) + 20,
                memoryUsage: Math.floor(Math.random() * 40) + 30,
                responseTime: Math.floor(Math.random() * 100) + 50,
                dbMetrics: {
                    activeConnections: Math.floor(Math.random() * 8) + 2,
                    maxConnections: 10,
                    avgQueryTime: Math.floor(Math.random() * 50) + 10,
                    slowQueries: Math.floor(Math.random() * 3),
                    cacheHitRate: Math.floor(Math.random() * 20) + 80
                },
                apiMetrics: {
                    totalRequests: Math.floor(Math.random() * 10000) + 5000,
                    successRate: Math.floor(Math.random() * 5) + 95,
                    errorRate: Math.floor(Math.random() * 3) + 1,
                    avgResponse: Math.floor(Math.random() * 100) + 50
                },
                cacheMetrics: {
                    memoryUsage: Math.floor(Math.random() * 100) + 50,
                    hitRate: Math.floor(Math.random() * 10) + 85,
                    keysStored: Math.floor(Math.random() * 1000) + 500,
                    evictions: Math.floor(Math.random() * 10)
                }
            };

            // Update UI with mock data
            document.getElementById('system-status').textContent = mockData.systemStatus;
            document.getElementById('uptime').textContent = formatUptime(mockData.uptime);
            document.getElementById('active-users').textContent = mockData.activeUsers;
            document.getElementById('requests-per-min').textContent = mockData.requestsPerMin;

            // Database metrics
            document.getElementById('db-connections').textContent = `${mockData.dbMetrics.activeConnections}/${mockData.dbMetrics.maxConnections}`;
            document.getElementById('db-query-time').textContent = `${mockData.dbMetrics.avgQueryTime} ms`;
            document.getElementById('db-slow-queries').textContent = mockData.dbMetrics.slowQueries;
            document.getElementById('db-cache-hit').textContent = `${mockData.dbMetrics.cacheHitRate}%`;

            // API metrics
            document.getElementById('api-total-requests').textContent = mockData.apiMetrics.totalRequests.toLocaleString();
            document.getElementById('api-success-rate').textContent = `${mockData.apiMetrics.successRate}%`;
            document.getElementById('api-error-rate').textContent = `${mockData.apiMetrics.errorRate}%`;
            document.getElementById('api-avg-response').textContent = `${mockData.apiMetrics.avgResponse} ms`;

            // Cache metrics
            document.getElementById('cache-memory').textContent = `${mockData.cacheMetrics.memoryUsage} MB`;
            document.getElementById('cache-hit-rate').textContent = `${mockData.cacheMetrics.hitRate}%`;
            document.getElementById('cache-keys').textContent = mockData.cacheMetrics.keysStored.toLocaleString();
            document.getElementById('cache-evictions').textContent = mockData.cacheMetrics.evictions;

            // Update charts with mock data
            updateChartsWithMockData(mockData);
            
            // Update alerts and events with mock data
            updateMockAlerts();
            updateMockEvents();
        }

        function updateCharts(data) {
            const now = new Date().toLocaleTimeString();
            
            // Update resource chart
            if (charts.resource) {
                charts.resource.data.labels.push(now);
                charts.resource.data.datasets[0].data.push(data.cpuUsage || 0);
                charts.resource.data.datasets[1].data.push(data.memoryUsage || 0);
                
                // Keep only last 20 data points
                if (charts.resource.data.labels.length > 20) {
                    charts.resource.data.labels.shift();
                    charts.resource.data.datasets[0].data.shift();
                    charts.resource.data.datasets[1].data.shift();
                }
                
                charts.resource.update();
            }
            
            // Update response time chart
            if (charts.responseTime) {
                charts.responseTime.data.labels.push(now);
                charts.responseTime.data.datasets[0].data.push(data.responseTime || 0);
                
                // Keep only last 20 data points
                if (charts.responseTime.data.labels.length > 20) {
                    charts.responseTime.data.labels.shift();
                    charts.responseTime.data.datasets[0].data.shift();
                }
                
                charts.responseTime.update();
            }
        }

        function updateChartsWithMockData(mockData) {
            const now = new Date().toLocaleTimeString();
            
            // Update resource chart
            if (charts.resource) {
                charts.resource.data.labels.push(now);
                charts.resource.data.datasets[0].data.push(mockData.cpuUsage);
                charts.resource.data.datasets[1].data.push(mockData.memoryUsage);
                
                // Keep only last 20 data points
                if (charts.resource.data.labels.length > 20) {
                    charts.resource.data.labels.shift();
                    charts.resource.data.datasets[0].data.shift();
                    charts.resource.data.datasets[1].data.shift();
                }
                
                charts.resource.update();
            }
            
            // Update response time chart
            if (charts.responseTime) {
                charts.responseTime.data.labels.push(now);
                charts.responseTime.data.datasets[0].data.push(mockData.responseTime);
                
                // Keep only last 20 data points
                if (charts.responseTime.data.labels.length > 20) {
                    charts.responseTime.data.labels.shift();
                    charts.responseTime.data.datasets[0].data.shift();
                }
                
                charts.responseTime.update();
            }
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('active-alerts');
            
            if (alerts.length === 0) {
                container.innerHTML = '<div class="text-green-600 text-center py-4">✅ No active alerts</div>';
                return;
            }
            
            container.innerHTML = alerts.map(alert => `
                <div class="flex items-center p-3 bg-${alert.severity === 'critical' ? 'red' : alert.severity === 'warning' ? 'yellow' : 'blue'}-50 border border-${alert.severity === 'critical' ? 'red' : alert.severity === 'warning' ? 'yellow' : 'blue'}-200 rounded-lg">
                    <div class="status-indicator status-${alert.severity === 'critical' ? 'critical' : alert.severity === 'warning' ? 'warning' : 'healthy'}"></div>
                    <div class="flex-1">
                        <div class="font-medium">${alert.title}</div>
                        <div class="text-sm text-gray-600">${alert.description}</div>
                    </div>
                    <div class="text-xs text-gray-500">${new Date(alert.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('');
        }

        function updateMockAlerts() {
            const mockAlerts = [
                {
                    severity: 'warning',
                    title: 'High Memory Usage',
                    description: 'Memory usage is above 80%',
                    timestamp: new Date()
                }
            ];
            updateAlerts(mockAlerts);
        }

        function updateEvents(events) {
            const container = document.getElementById('recent-events');
            
            if (events.length === 0) {
                container.innerHTML = '<div class="text-gray-500 text-center py-4">No recent events</div>';
                return;
            }
            
            container.innerHTML = events.map(event => `
                <div class="flex items-center justify-between p-2 border-b border-gray-100 last:border-b-0">
                    <div class="flex-1">
                        <div class="text-sm font-medium">${event.title}</div>
                        <div class="text-xs text-gray-500">${event.description}</div>
                    </div>
                    <div class="text-xs text-gray-400">${new Date(event.timestamp).toLocaleTimeString()}</div>
                </div>
            `).join('');
        }

        function updateMockEvents() {
            const mockEvents = [
                {
                    title: 'System Started',
                    description: 'RESTROFLOW system initialized successfully',
                    timestamp: new Date(Date.now() - 60000)
                },
                {
                    title: 'Database Connected',
                    description: 'PostgreSQL connection established',
                    timestamp: new Date(Date.now() - 30000)
                },
                {
                    title: 'Cache Warmed',
                    description: 'Application cache preloaded',
                    timestamp: new Date()
                }
            ];
            updateEvents(mockEvents);
        }

        function formatUptime(timestamp) {
            const uptime = Date.now() - timestamp;
            const days = Math.floor(uptime / (24 * 60 * 60 * 1000));
            const hours = Math.floor((uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000));
            const minutes = Math.floor((uptime % (60 * 60 * 1000)) / (60 * 1000));
            
            return `${days}d ${hours}h ${minutes}m`;
        }

        function updateLastUpdated() {
            document.getElementById('last-updated').textContent = new Date().toLocaleTimeString();
        }

        function startAutoRefresh() {
            // Refresh data every 30 seconds
            setInterval(() => {
                loadMonitoringData();
                updateLastUpdated();
            }, 30000);
        }

        // Event listeners
        document.getElementById('refresh-data').addEventListener('click', () => {
            loadMonitoringData();
            updateLastUpdated();
        });

        document.getElementById('clear-logs').addEventListener('click', () => {
            document.getElementById('error-logs').innerHTML = '<div class="text-gray-500">Error logs cleared...</div>';
        });

        // Initialize dashboard when page loads
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    </script>
</body>
</html>
