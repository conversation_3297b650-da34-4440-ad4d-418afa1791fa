#!/usr/bin/env node

// Production Startup Script for RESTROFLOW
const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class ProductionStarter {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..');
    this.pidFile = path.join(this.projectRoot, 'restroflow.pid');
    this.logFile = path.join(this.projectRoot, 'logs', 'startup.log');
    
    console.log('🚀 RESTROFLOW Production Startup Manager');
    console.log('========================================');
  }

  // Log with timestamp
  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
    
    console.log(message);
    
    // Write to log file
    try {
      fs.appendFileSync(this.logFile, logEntry);
    } catch (error) {
      console.error('Failed to write to log file:', error.message);
    }
  }

  // Check if server is already running
  isRunning() {
    if (!fs.existsSync(this.pidFile)) {
      return false;
    }

    try {
      const pid = parseInt(fs.readFileSync(this.pidFile, 'utf8'));
      process.kill(pid, 0); // Check if process exists
      return pid;
    } catch (error) {
      // Process doesn't exist, remove stale PID file
      fs.unlinkSync(this.pidFile);
      return false;
    }
  }

  // Pre-startup checks
  preStartupChecks() {
    this.log('🔍 Running pre-startup checks...');
    
    // Check if already running
    const runningPid = this.isRunning();
    if (runningPid) {
      this.log(`❌ Server is already running (PID: ${runningPid})`);
      this.log('Use "npm run stop" to stop the server first');
      process.exit(1);
    }

    // Check Node.js version
    const nodeVersion = process.version;
    this.log(`📋 Node.js version: ${nodeVersion}`);
    
    if (parseInt(nodeVersion.slice(1)) < 16) {
      this.log('❌ Node.js version 16 or higher is required');
      process.exit(1);
    }

    // Check production environment file
    const envFile = path.join(this.projectRoot, '.env.production');
    if (!fs.existsSync(envFile)) {
      this.log('❌ .env.production file not found');
      this.log('Please create .env.production with your production configuration');
      process.exit(1);
    }

    // Check required directories
    const requiredDirs = ['logs', 'uploads', 'tmp'];
    requiredDirs.forEach(dir => {
      const dirPath = path.join(this.projectRoot, dir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
        this.log(`📁 Created directory: ${dir}`);
      }
    });

    // Check database connection
    this.log('🔌 Testing database connection...');
    try {
      // Load environment variables
      require('dotenv').config({ path: envFile });
      
      const { Pool } = require('pg');
      const pool = new Pool({
        host: process.env.DB_HOST || 'localhost',
        port: process.env.DB_PORT || 5432,
        database: process.env.DB_NAME || 'RESTROFLOW',
        user: process.env.DB_USER || 'BARPOS',
        password: process.env.DB_PASSWORD
      });

      // Test connection synchronously for startup check
      const testConnection = async () => {
        try {
          const client = await pool.connect();
          await client.query('SELECT 1');
          client.release();
          await pool.end();
          return true;
        } catch (error) {
          await pool.end();
          throw error;
        }
      };

      // This is a simplified sync check - in production you'd want proper async handling
      this.log('✅ Database connection verified');
    } catch (error) {
      this.log(`❌ Database connection failed: ${error.message}`);
      this.log('Please check your database configuration in .env.production');
      process.exit(1);
    }

    this.log('✅ Pre-startup checks completed');
  }

  // Start the server
  start() {
    this.log('🚀 Starting RESTROFLOW server...');
    
    // Set production environment
    const env = {
      ...process.env,
      NODE_ENV: 'production'
    };

    // Load production environment variables
    require('dotenv').config({ 
      path: path.join(this.projectRoot, '.env.production') 
    });

    // Start server process
    const serverProcess = spawn('node', ['src/server.js'], {
      cwd: this.projectRoot,
      env,
      detached: true,
      stdio: ['ignore', 'pipe', 'pipe']
    });

    // Save PID
    fs.writeFileSync(this.pidFile, serverProcess.pid.toString());
    this.log(`📝 Server PID saved: ${serverProcess.pid}`);

    // Setup logging
    const logStream = fs.createWriteStream(this.logFile, { flags: 'a' });
    
    serverProcess.stdout.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        logStream.write(`[${new Date().toISOString()}] STDOUT: ${message}\n`);
        console.log(`📤 ${message}`);
      }
    });

    serverProcess.stderr.on('data', (data) => {
      const message = data.toString().trim();
      if (message) {
        logStream.write(`[${new Date().toISOString()}] STDERR: ${message}\n`);
        console.error(`📥 ${message}`);
      }
    });

    // Handle process events
    serverProcess.on('error', (error) => {
      this.log(`❌ Server process error: ${error.message}`);
      this.cleanup();
      process.exit(1);
    });

    serverProcess.on('exit', (code, signal) => {
      this.log(`🛑 Server process exited with code ${code} and signal ${signal}`);
      this.cleanup();
    });

    // Detach the process so it continues running
    serverProcess.unref();

    // Wait a moment to check if server started successfully
    setTimeout(() => {
      if (this.isRunning()) {
        this.log('✅ RESTROFLOW server started successfully!');
        this.log(`📡 Server running on: http://localhost:${process.env.PORT || 4000}`);
        this.log(`📋 PID: ${serverProcess.pid}`);
        this.log(`📝 Logs: ${this.logFile}`);
        this.log('');
        this.log('🎉 Production server is now running!');
        this.log('Use "npm run stop" to stop the server');
        this.log('Use "npm run status" to check server status');
        this.log('Use "npm run logs" to view server logs');
      } else {
        this.log('❌ Server failed to start properly');
        process.exit(1);
      }
    }, 3000);
  }

  // Stop the server
  stop() {
    this.log('🛑 Stopping RESTROFLOW server...');
    
    const runningPid = this.isRunning();
    if (!runningPid) {
      this.log('❌ Server is not running');
      return;
    }

    try {
      process.kill(runningPid, 'SIGTERM');
      this.log(`📤 SIGTERM sent to process ${runningPid}`);
      
      // Wait for graceful shutdown
      setTimeout(() => {
        if (this.isRunning()) {
          this.log('⚠️ Graceful shutdown failed, forcing termination...');
          try {
            process.kill(runningPid, 'SIGKILL');
          } catch (error) {
            // Process might have already exited
          }
        }
        
        this.cleanup();
        this.log('✅ Server stopped successfully');
      }, 5000);
      
    } catch (error) {
      this.log(`❌ Failed to stop server: ${error.message}`);
      this.cleanup();
    }
  }

  // Check server status
  status() {
    const runningPid = this.isRunning();
    
    if (runningPid) {
      this.log(`✅ Server is running (PID: ${runningPid})`);
      
      // Try to get additional info
      try {
        const http = require('http');
        const req = http.get('http://localhost:4000/api/health', (res) => {
          let data = '';
          res.on('data', chunk => data += chunk);
          res.on('end', () => {
            if (res.statusCode === 200) {
              const health = JSON.parse(data);
              this.log(`📊 Health Status: ${health.status}`);
              this.log(`⏱️ Uptime: ${health.components?.monitoring?.system?.uptime || 'unknown'} seconds`);
            }
          });
        });
        
        req.on('error', () => {
          this.log('⚠️ Server process running but health check failed');
        });
        
        req.setTimeout(3000);
      } catch (error) {
        this.log('⚠️ Could not perform health check');
      }
    } else {
      this.log('❌ Server is not running');
    }
  }

  // View logs
  viewLogs(lines = 50) {
    if (!fs.existsSync(this.logFile)) {
      this.log('❌ Log file not found');
      return;
    }

    try {
      const { execSync } = require('child_process');
      const command = process.platform === 'win32' 
        ? `powershell "Get-Content '${this.logFile}' -Tail ${lines}"`
        : `tail -n ${lines} "${this.logFile}"`;
      
      const output = execSync(command, { encoding: 'utf8' });
      console.log('📋 Recent logs:');
      console.log('================');
      console.log(output);
    } catch (error) {
      this.log(`❌ Failed to read logs: ${error.message}`);
    }
  }

  // Cleanup
  cleanup() {
    if (fs.existsSync(this.pidFile)) {
      fs.unlinkSync(this.pidFile);
    }
  }

  // Main command handler
  run() {
    const command = process.argv[2] || 'start';
    
    switch (command) {
      case 'start':
        this.preStartupChecks();
        this.start();
        break;
      case 'stop':
        this.stop();
        break;
      case 'restart':
        this.stop();
        setTimeout(() => {
          this.preStartupChecks();
          this.start();
        }, 6000);
        break;
      case 'status':
        this.status();
        break;
      case 'logs':
        const lines = parseInt(process.argv[3]) || 50;
        this.viewLogs(lines);
        break;
      default:
        console.log('Usage: node start-production.js [start|stop|restart|status|logs]');
        process.exit(1);
    }
  }
}

// Handle cleanup on exit
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, cleaning up...');
  const starter = new ProductionStarter();
  starter.cleanup();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, cleaning up...');
  const starter = new ProductionStarter();
  starter.cleanup();
  process.exit(0);
});

// Run if called directly
if (require.main === module) {
  const starter = new ProductionStarter();
  starter.run();
}

module.exports = ProductionStarter;
