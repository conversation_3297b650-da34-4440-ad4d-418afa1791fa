import React, { useState, useEffect } from 'react';
import { 
  Settings, 
  Globe, 
  Database, 
  Server, 
  Shield,
  Code,
  GitBranch,
  History,
  Download,
  Upload,
  Save,
  RefreshCw,
  Eye,
  Edit,
  Trash2,
  Plus,
  Search,
  Filter,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Users,
  Lock,
  Unlock,
  Copy,
  FileText,
  Zap,
  Activity
} from 'lucide-react';

interface ConfigurationItem {
  id: string;
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  category: 'system' | 'security' | 'database' | 'api' | 'ui' | 'integration' | 'performance';
  environment: 'development' | 'staging' | 'production' | 'all';
  description: string;
  sensitive: boolean;
  required: boolean;
  lastModified: string;
  modifiedBy: string;
  version: string;
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    options?: string[];
  };
}

interface ConfigurationVersion {
  id: string;
  version: string;
  timestamp: string;
  author: string;
  description: string;
  changes: ConfigurationChange[];
  status: 'draft' | 'active' | 'archived';
  environment: string;
}

interface ConfigurationChange {
  key: string;
  oldValue: any;
  newValue: any;
  action: 'create' | 'update' | 'delete';
  reason: string;
}

interface DeploymentStatus {
  environment: string;
  version: string;
  status: 'pending' | 'deploying' | 'success' | 'failed' | 'rollback';
  timestamp: string;
  deployedBy: string;
  healthCheck: boolean;
  rollbackAvailable: boolean;
}

const GlobalConfigurationManager: React.FC = () => {
  const [configurations, setConfigurations] = useState<ConfigurationItem[]>([]);
  const [versions, setVersions] = useState<ConfigurationVersion[]>([]);
  const [deploymentStatus, setDeploymentStatus] = useState<DeploymentStatus[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedEnvironment, setSelectedEnvironment] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [editingConfig, setEditingConfig] = useState<ConfigurationItem | null>(null);
  const [showSensitive, setShowSensitive] = useState(false);
  const [isDarkMode, setIsDarkMode] = useState(false);

  useEffect(() => {
    const darkMode = document.documentElement.classList.contains('dark') ||
                    localStorage.getItem('theme') === 'dark';
    setIsDarkMode(darkMode);
  }, []);

  useEffect(() => {
    loadConfigurationData();
  }, []);

  const loadConfigurationData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data for demonstration
      setConfigurations([
        {
          id: '1',
          key: 'DATABASE_URL',
          value: 'postgresql://localhost:5432/restroflow',
          type: 'string',
          category: 'database',
          environment: 'production',
          description: 'Primary database connection string',
          sensitive: true,
          required: true,
          lastModified: new Date().toISOString(),
          modifiedBy: '<EMAIL>',
          version: '1.2.0'
        },
        {
          id: '2',
          key: 'API_RATE_LIMIT',
          value: 1000,
          type: 'number',
          category: 'api',
          environment: 'all',
          description: 'API requests per minute limit',
          sensitive: false,
          required: true,
          lastModified: new Date(Date.now() - 86400000).toISOString(),
          modifiedBy: '<EMAIL>',
          version: '1.2.0',
          validation: {
            min: 100,
            max: 10000
          }
        },
        {
          id: '3',
          key: 'ENABLE_ANALYTICS',
          value: true,
          type: 'boolean',
          category: 'system',
          environment: 'production',
          description: 'Enable advanced analytics features',
          sensitive: false,
          required: false,
          lastModified: new Date(Date.now() - 172800000).toISOString(),
          modifiedBy: '<EMAIL>',
          version: '1.1.5'
        },
        {
          id: '4',
          key: 'SECURITY_HEADERS',
          value: {
            'X-Frame-Options': 'DENY',
            'X-Content-Type-Options': 'nosniff',
            'X-XSS-Protection': '1; mode=block'
          },
          type: 'object',
          category: 'security',
          environment: 'all',
          description: 'HTTP security headers configuration',
          sensitive: false,
          required: true,
          lastModified: new Date(Date.now() - 259200000).toISOString(),
          modifiedBy: '<EMAIL>',
          version: '1.2.0'
        }
      ]);

      setVersions([
        {
          id: '1',
          version: '1.2.0',
          timestamp: new Date().toISOString(),
          author: '<EMAIL>',
          description: 'Updated database configuration and security headers',
          changes: [
            {
              key: 'DATABASE_URL',
              oldValue: 'postgresql://localhost:5432/restroflow_old',
              newValue: 'postgresql://localhost:5432/restroflow',
              action: 'update',
              reason: 'Database migration completed'
            }
          ],
          status: 'active',
          environment: 'production'
        },
        {
          id: '2',
          version: '1.1.5',
          timestamp: new Date(Date.now() - 86400000).toISOString(),
          author: '<EMAIL>',
          description: 'Enabled analytics features',
          changes: [
            {
              key: 'ENABLE_ANALYTICS',
              oldValue: false,
              newValue: true,
              action: 'update',
              reason: 'Business requirement for analytics'
            }
          ],
          status: 'archived',
          environment: 'production'
        }
      ]);

      setDeploymentStatus([
        {
          environment: 'production',
          version: '1.2.0',
          status: 'success',
          timestamp: new Date().toISOString(),
          deployedBy: '<EMAIL>',
          healthCheck: true,
          rollbackAvailable: true
        },
        {
          environment: 'staging',
          version: '1.2.1-beta',
          status: 'deploying',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          deployedBy: '<EMAIL>',
          healthCheck: false,
          rollbackAvailable: true
        },
        {
          environment: 'development',
          version: '1.3.0-dev',
          status: 'success',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          deployedBy: '<EMAIL>',
          healthCheck: true,
          rollbackAvailable: false
        }
      ]);

    } catch (error) {
      console.error('Error loading configuration data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const saveConfiguration = async (config: ConfigurationItem) => {
    try {
      // API call would go here
      console.log('Saving configuration:', config);
      
      setConfigurations(configs => 
        configs.map(c => c.id === config.id ? config : c)
      );
      
      setEditingConfig(null);
    } catch (error) {
      console.error('Error saving configuration:', error);
    }
  };

  const deployConfiguration = async (environment: string, version: string) => {
    try {
      // API call would go here
      console.log(`Deploying version ${version} to ${environment}`);
      
      setDeploymentStatus(statuses => 
        statuses.map(s => 
          s.environment === environment 
            ? { ...s, status: 'deploying', version, timestamp: new Date().toISOString() }
            : s
        )
      );
      
      // Simulate deployment
      setTimeout(() => {
        setDeploymentStatus(statuses => 
          statuses.map(s => 
            s.environment === environment 
              ? { ...s, status: 'success', healthCheck: true }
              : s
          )
        );
      }, 3000);
      
    } catch (error) {
      console.error('Error deploying configuration:', error);
    }
  };

  const rollbackConfiguration = async (environment: string) => {
    try {
      // API call would go here
      console.log(`Rolling back configuration in ${environment}`);
      
      setDeploymentStatus(statuses => 
        statuses.map(s => 
          s.environment === environment 
            ? { ...s, status: 'rollback', timestamp: new Date().toISOString() }
            : s
        )
      );
      
    } catch (error) {
      console.error('Error rolling back configuration:', error);
    }
  };

  const exportConfiguration = async (format: 'json' | 'yaml' | 'env') => {
    try {
      const data = configurations.reduce((acc, config) => {
        if (!config.sensitive || showSensitive) {
          acc[config.key] = config.value;
        }
        return acc;
      }, {} as Record<string, any>);

      let content = '';
      let filename = '';

      switch (format) {
        case 'json':
          content = JSON.stringify(data, null, 2);
          filename = 'configuration.json';
          break;
        case 'yaml':
          content = Object.entries(data)
            .map(([key, value]) => `${key}: ${JSON.stringify(value)}`)
            .join('\n');
          filename = 'configuration.yaml';
          break;
        case 'env':
          content = Object.entries(data)
            .map(([key, value]) => `${key}=${value}`)
            .join('\n');
          filename = 'configuration.env';
          break;
      }

      const blob = new Blob([content], { type: 'text/plain' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = filename;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);
    } catch (error) {
      console.error('Error exporting configuration:', error);
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'system':
        return <Settings className="w-5 h-5" />;
      case 'security':
        return <Shield className="w-5 h-5" />;
      case 'database':
        return <Database className="w-5 h-5" />;
      case 'api':
        return <Code className="w-5 h-5" />;
      case 'ui':
        return <Eye className="w-5 h-5" />;
      case 'integration':
        return <Globe className="w-5 h-5" />;
      case 'performance':
        return <Zap className="w-5 h-5" />;
      default:
        return <FileText className="w-5 h-5" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success':
        return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';
      case 'deploying':
        return 'text-blue-600 bg-blue-100 dark:bg-blue-900 dark:text-blue-300';
      case 'failed':
        return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';
      case 'pending':
        return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';
      case 'rollback':
        return 'text-orange-600 bg-orange-100 dark:bg-orange-900 dark:text-orange-300';
      default:
        return 'text-gray-600 bg-gray-100 dark:bg-gray-900 dark:text-gray-300';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="w-4 h-4" />;
      case 'deploying':
        return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'failed':
        return <XCircle className="w-4 h-4" />;
      case 'pending':
        return <Clock className="w-4 h-4" />;
      case 'rollback':
        return <History className="w-4 h-4" />;
      default:
        return <AlertTriangle className="w-4 h-4" />;
    }
  };

  const filteredConfigurations = configurations.filter(config => {
    const matchesSearch = config.key.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         config.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || config.category === selectedCategory;
    const matchesEnvironment = selectedEnvironment === 'all' || 
                               config.environment === selectedEnvironment || 
                               config.environment === 'all';
    
    return matchesSearch && matchesCategory && matchesEnvironment;
  });

  const categories = ['all', 'system', 'security', 'database', 'api', 'ui', 'integration', 'performance'];
  const environments = ['all', 'development', 'staging', 'production'];

  if (isLoading) {
    return (
      <div className={`min-h-screen p-6 ${
        isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
      }`}>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Settings className="w-8 h-8 animate-spin mx-auto mb-4 text-blue-600" />
            <p className={isDarkMode ? 'text-gray-300' : 'text-gray-600'}>
              Loading configuration data...
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen ${
      isDarkMode ? 'bg-gray-900' : 'bg-gray-100'
    }`}>
      
      {/* Header */}
      <div className={`${
        isDarkMode ? 'bg-gray-800' : 'bg-white'
      } shadow-lg`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between py-6">
            <div>
              <h1 className={`text-3xl font-bold ${
                isDarkMode ? 'text-white' : 'text-gray-900'
              }`}>
                🌐 Global Configuration Management
              </h1>
              <p className={`text-sm ${
                isDarkMode ? 'text-gray-400' : 'text-gray-600'
              }`}>
                Centralized configuration with version control and deployment management
              </p>
            </div>
            
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setShowSensitive(!showSensitive)}
                className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                  showSensitive
                    ? 'bg-red-600 text-white hover:bg-red-700'
                    : isDarkMode
                    ? 'bg-gray-700 text-gray-300 hover:bg-gray-600'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                {showSensitive ? <Unlock className="w-4 h-4" /> : <Lock className="w-4 h-4" />}
                <span>{showSensitive ? 'Hide' : 'Show'} Sensitive</span>
              </button>
              
              <button
                onClick={() => exportConfiguration('json')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Deployment Status */}
        <div className="mb-8">
          <h2 className={`text-2xl font-bold mb-6 ${
            isDarkMode ? 'text-white' : 'text-gray-900'
          }`}>
            🚀 Deployment Status
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {deploymentStatus.map((deployment) => (
              <div key={deployment.environment} className={`p-6 rounded-lg ${
                isDarkMode ? 'bg-gray-800' : 'bg-white'
              } shadow-lg`}>
                <div className="flex items-center justify-between mb-4">
                  <h3 className={`text-lg font-semibold ${
                    isDarkMode ? 'text-white' : 'text-gray-900'
                  }`}>
                    {deployment.environment.charAt(0).toUpperCase() + deployment.environment.slice(1)}
                  </h3>
                  <span className={`px-2 py-1 text-xs rounded-full flex items-center space-x-1 ${getStatusColor(deployment.status)}`}>
                    {getStatusIcon(deployment.status)}
                    <span>{deployment.status}</span>
                  </span>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Version:</span>
                    <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>{deployment.version}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Deployed by:</span>
                    <span className={isDarkMode ? 'text-white' : 'text-gray-900'}>{deployment.deployedBy}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className={isDarkMode ? 'text-gray-400' : 'text-gray-600'}>Health Check:</span>
                    <span className={deployment.healthCheck ? 'text-green-600' : 'text-red-600'}>
                      {deployment.healthCheck ? 'Passed' : 'Failed'}
                    </span>
                  </div>
                </div>
                
                <div className="mt-4 flex space-x-2">
                  <button
                    onClick={() => deployConfiguration(deployment.environment, '1.2.1')}
                    disabled={deployment.status === 'deploying'}
                    className="flex-1 px-3 py-1 text-sm bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50 transition-colors"
                  >
                    Deploy
                  </button>
                  {deployment.rollbackAvailable && (
                    <button
                      onClick={() => rollbackConfiguration(deployment.environment)}
                      className="flex-1 px-3 py-1 text-sm bg-orange-600 text-white rounded hover:bg-orange-700 transition-colors"
                    >
                      Rollback
                    </button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Configuration Filters */}
        <div className={`p-6 rounded-lg ${
          isDarkMode ? 'bg-gray-800' : 'bg-white'
        } shadow-lg mb-8`}>
          <div className="flex flex-col lg:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search configurations..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                    isDarkMode 
                      ? 'bg-gray-700 border-gray-600 text-white' 
                      : 'bg-white border-gray-300 text-gray-900'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                />
              </div>
            </div>
            
            <div className="flex gap-4">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category === 'all' ? 'All Categories' : category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
              
              <select
                value={selectedEnvironment}
                onChange={(e) => setSelectedEnvironment(e.target.value)}
                className={`px-3 py-2 border rounded-lg ${
                  isDarkMode 
                    ? 'bg-gray-700 border-gray-600 text-white' 
                    : 'bg-white border-gray-300 text-gray-900'
                } focus:outline-none focus:ring-2 focus:ring-blue-500`}
              >
                {environments.map(env => (
                  <option key={env} value={env}>
                    {env === 'all' ? 'All Environments' : env.charAt(0).toUpperCase() + env.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Configuration Items */}
        <div className="space-y-4">
          {filteredConfigurations.map((config) => (
            <div key={config.id} className={`p-6 rounded-lg ${
              isDarkMode ? 'bg-gray-800' : 'bg-white'
            } shadow-lg`}>
              <div className="flex items-center justify-between">
                <div className="flex-1">
                  <div className="flex items-center space-x-3 mb-2">
                    <div className={`p-2 rounded-lg ${
                      config.category === 'security' ? 'bg-red-100 text-red-600 dark:bg-red-900 dark:text-red-300' :
                      config.category === 'database' ? 'bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300' :
                      config.category === 'api' ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-300' :
                      'bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      {getCategoryIcon(config.category)}
                    </div>
                    <div>
                      <h3 className={`text-lg font-semibold ${
                        isDarkMode ? 'text-white' : 'text-gray-900'
                      }`}>
                        {config.key}
                      </h3>
                      <p className={`text-sm ${
                        isDarkMode ? 'text-gray-400' : 'text-gray-600'
                      }`}>
                        {config.description}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-4 text-sm">
                    <span className={`px-2 py-1 rounded-full ${
                      config.category === 'security' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      config.category === 'database' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200'
                    }`}>
                      {config.category}
                    </span>
                    <span className={`px-2 py-1 rounded-full ${
                      config.environment === 'production' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                      config.environment === 'staging' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                      'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      {config.environment}
                    </span>
                    {config.sensitive && (
                      <span className="px-2 py-1 rounded-full bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200">
                        sensitive
                      </span>
                    )}
                    {config.required && (
                      <span className="px-2 py-1 rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200">
                        required
                      </span>
                    )}
                  </div>
                  
                  <div className="mt-3">
                    <span className={`text-sm ${
                      isDarkMode ? 'text-gray-400' : 'text-gray-600'
                    }`}>
                      Value: 
                    </span>
                    <span className={`ml-2 font-mono text-sm ${
                      isDarkMode ? 'text-white' : 'text-gray-900'
                    }`}>
                      {config.sensitive && !showSensitive 
                        ? '••••••••' 
                        : typeof config.value === 'object' 
                        ? JSON.stringify(config.value, null, 2) 
                        : String(config.value)
                      }
                    </span>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setEditingConfig(config)}
                    className={`p-2 rounded-lg transition-colors ${
                      isDarkMode 
                        ? 'hover:bg-gray-700 text-gray-400' 
                        : 'hover:bg-gray-100 text-gray-600'
                    }`}
                  >
                    <Edit className="w-4 h-4" />
                  </button>
                  
                  <button className={`p-2 rounded-lg transition-colors ${
                    isDarkMode 
                      ? 'hover:bg-gray-700 text-gray-400' 
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}>
                    <Copy className="w-4 h-4" />
                  </button>
                  
                  <button className={`p-2 rounded-lg transition-colors ${
                    isDarkMode 
                      ? 'hover:bg-gray-700 text-gray-400' 
                      : 'hover:bg-gray-100 text-gray-600'
                  }`}>
                    <History className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Empty State */}
        {filteredConfigurations.length === 0 && (
          <div className="text-center py-12">
            <Settings className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className={`text-lg font-medium ${
              isDarkMode ? 'text-gray-300' : 'text-gray-900'
            }`}>
              No configurations found
            </h3>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-500' : 'text-gray-600'
            }`}>
              Try adjusting your search criteria or filters
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default GlobalConfigurationManager;
