import React, { useState, useEffect } from 'react';
import { 
  Users, 
  UserPlus, 
  Search, 
  Filter, 
  Mail, 
  Phone, 
  Calendar, 
  Gift, 
  Star, 
  TrendingUp,
  Heart,
  DollarSign,
  ShoppingBag,
  Clock,
  MapPin,
  Edit,
  Trash2,
  Send,
  Target,
  Award,
  AlertCircle,
  CheckCircle,
  Eye,
  MessageSquare
} from 'lucide-react';

interface Customer {
  id: string;
  name: string;
  email: string;
  phone: string;
  dateJoined: string;
  lastVisit: string;
  totalSpent: number;
  visitCount: number;
  averageSpend: number;
  loyaltyPoints: number;
  loyaltyTier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  preferences: {
    favoriteItems: string[];
    dietaryRestrictions: string[];
    preferredSeating: string;
    communicationPreference: 'email' | 'sms' | 'both';
  };
  demographics: {
    ageGroup: string;
    location: string;
    occupation?: string;
  };
  behavior: {
    visitFrequency: 'Weekly' | 'Monthly' | 'Occasional' | 'Rare';
    averagePartySize: number;
    preferredTimes: string[];
    churnRisk: number;
    satisfaction: number;
  };
  campaigns: Array<{
    id: string;
    name: string;
    type: string;
    sentDate: string;
    opened: boolean;
    clicked: boolean;
    converted: boolean;
  }>;
}

interface LoyaltyProgram {
  id: string;
  name: string;
  type: 'points' | 'visits' | 'spend';
  rules: {
    pointsPerDollar?: number;
    bonusMultiplier?: number;
    tierThresholds?: { [key: string]: number };
  };
  rewards: Array<{
    id: string;
    name: string;
    pointsCost: number;
    description: string;
    type: 'discount' | 'freeItem' | 'experience';
  }>;
  active: boolean;
}

interface Campaign {
  id: string;
  name: string;
  type: 'email' | 'sms' | 'push';
  status: 'draft' | 'scheduled' | 'active' | 'completed';
  targetSegment: string;
  content: {
    subject: string;
    message: string;
    offer?: string;
    expiryDate?: string;
  };
  metrics: {
    sent: number;
    opened: number;
    clicked: number;
    converted: number;
    revenue: number;
  };
  scheduledDate?: string;
  createdDate: string;
}

const Phase3CRMSystem: React.FC = () => {
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [loyaltyPrograms, setLoyaltyPrograms] = useState<LoyaltyProgram[]>([]);
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [activeTab, setActiveTab] = useState<'customers' | 'loyalty' | 'campaigns' | 'analytics'>('customers');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterTier, setFilterTier] = useState<string>('all');
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const [showCustomerModal, setShowCustomerModal] = useState(false);
  const [showCampaignModal, setShowCampaignModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    loadCRMData();
  }, []);

  const loadCRMData = async () => {
    try {
      setIsLoading(true);
      
      // Mock data - in production this would come from API
      const mockCustomers: Customer[] = [
        {
          id: '1',
          name: 'John Smith',
          email: '<EMAIL>',
          phone: '(*************',
          dateJoined: '2024-01-15',
          lastVisit: '2024-06-01',
          totalSpent: 1250.50,
          visitCount: 18,
          averageSpend: 69.47,
          loyaltyPoints: 1250,
          loyaltyTier: 'Gold',
          preferences: {
            favoriteItems: ['Margherita Pizza', 'Caesar Salad'],
            dietaryRestrictions: ['Vegetarian'],
            preferredSeating: 'Window',
            communicationPreference: 'email'
          },
          demographics: {
            ageGroup: '26-35',
            location: 'Downtown',
            occupation: 'Software Engineer'
          },
          behavior: {
            visitFrequency: 'Weekly',
            averagePartySize: 2,
            preferredTimes: ['7:00 PM', '8:00 PM'],
            churnRisk: 15,
            satisfaction: 4.8
          },
          campaigns: [
            {
              id: 'c1',
              name: 'Summer Special',
              type: 'email',
              sentDate: '2024-05-15',
              opened: true,
              clicked: true,
              converted: true
            }
          ]
        },
        {
          id: '2',
          name: 'Sarah Johnson',
          email: '<EMAIL>',
          phone: '(*************',
          dateJoined: '2024-03-20',
          lastVisit: '2024-05-28',
          totalSpent: 890.25,
          visitCount: 12,
          averageSpend: 74.19,
          loyaltyPoints: 890,
          loyaltyTier: 'Silver',
          preferences: {
            favoriteItems: ['Grilled Chicken', 'Fish & Chips'],
            dietaryRestrictions: ['Gluten-Free'],
            preferredSeating: 'Patio',
            communicationPreference: 'sms'
          },
          demographics: {
            ageGroup: '36-45',
            location: 'Suburbs',
            occupation: 'Marketing Manager'
          },
          behavior: {
            visitFrequency: 'Monthly',
            averagePartySize: 4,
            preferredTimes: ['6:30 PM', '7:30 PM'],
            churnRisk: 35,
            satisfaction: 4.5
          },
          campaigns: [
            {
              id: 'c2',
              name: 'Birthday Offer',
              type: 'sms',
              sentDate: '2024-05-20',
              opened: true,
              clicked: false,
              converted: false
            }
          ]
        }
      ];

      const mockLoyaltyPrograms: LoyaltyProgram[] = [
        {
          id: '1',
          name: 'Taste Rewards',
          type: 'points',
          rules: {
            pointsPerDollar: 1,
            bonusMultiplier: 2,
            tierThresholds: {
              Bronze: 0,
              Silver: 500,
              Gold: 1000,
              Platinum: 2500
            }
          },
          rewards: [
            {
              id: 'r1',
              name: 'Free Appetizer',
              pointsCost: 250,
              description: 'Choose any appetizer from our menu',
              type: 'freeItem'
            },
            {
              id: 'r2',
              name: '10% Off Next Visit',
              pointsCost: 500,
              description: 'Get 10% off your entire order',
              type: 'discount'
            },
            {
              id: 'r3',
              name: 'Chef\'s Table Experience',
              pointsCost: 2000,
              description: 'Exclusive dining experience with the chef',
              type: 'experience'
            }
          ],
          active: true
        }
      ];

      const mockCampaigns: Campaign[] = [
        {
          id: '1',
          name: 'Summer Menu Launch',
          type: 'email',
          status: 'completed',
          targetSegment: 'All Active Customers',
          content: {
            subject: 'Taste Summer with Our New Menu!',
            message: 'Discover fresh flavors and seasonal ingredients in our new summer menu.',
            offer: '15% off new menu items',
            expiryDate: '2024-07-31'
          },
          metrics: {
            sent: 1250,
            opened: 687,
            clicked: 234,
            converted: 89,
            revenue: 4567.80
          },
          createdDate: '2024-05-01'
        },
        {
          id: '2',
          name: 'Weekend Brunch Promotion',
          type: 'sms',
          status: 'active',
          targetSegment: 'Weekend Visitors',
          content: {
            subject: '',
            message: 'Join us for weekend brunch! Special menu available Sat-Sun 10am-3pm.',
            offer: 'Free mimosa with brunch order',
            expiryDate: '2024-06-30'
          },
          metrics: {
            sent: 456,
            opened: 398,
            clicked: 156,
            converted: 67,
            revenue: 2134.50
          },
          createdDate: '2024-05-15'
        }
      ];

      setCustomers(mockCustomers);
      setLoyaltyPrograms(mockLoyaltyPrograms);
      setCampaigns(mockCampaigns);
    } catch (error) {
      console.error('Error loading CRM data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.phone.includes(searchTerm);
    const matchesTier = filterTier === 'all' || customer.loyaltyTier === filterTier;
    return matchesSearch && matchesTier;
  });

  const getTierColor = (tier: string) => {
    switch (tier) {
      case 'Bronze': return 'bg-orange-100 text-orange-800';
      case 'Silver': return 'bg-gray-100 text-gray-800';
      case 'Gold': return 'bg-yellow-100 text-yellow-800';
      case 'Platinum': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getChurnRiskColor = (risk: number) => {
    if (risk < 30) return 'text-green-600';
    if (risk < 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const formatCurrency = (amount: number) => `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2 })}`;

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading CRM system...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center">
              <Users className="h-6 w-6 mr-2 text-blue-600" />
              Customer Relationship Management
            </h1>
            <p className="text-gray-600 mt-1">
              Manage customers, loyalty programs, and marketing campaigns
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowCustomerModal(true)}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-2"
            >
              <UserPlus className="h-4 w-4" />
              <span>Add Customer</span>
            </button>
            
            <button
              onClick={() => setShowCampaignModal(true)}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-2"
            >
              <Send className="h-4 w-4" />
              <span>New Campaign</span>
            </button>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="bg-white border-b border-gray-200">
        <nav className="flex space-x-8 px-4">
          {[
            { id: 'customers', label: 'Customers', icon: Users },
            { id: 'loyalty', label: 'Loyalty Programs', icon: Gift },
            { id: 'campaigns', label: 'Campaigns', icon: Send },
            { id: 'analytics', label: 'CRM Analytics', icon: TrendingUp }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                activeTab === tab.id
                  ? 'border-blue-500 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </nav>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-6">
        {activeTab === 'customers' && (
          <div className="space-y-6">
            {/* Search and Filters */}
            <div className="bg-white p-4 rounded-lg shadow-sm border">
              <div className="flex flex-col md:flex-row gap-4">
                <div className="flex-1 relative">
                  <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search customers by name, email, or phone..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
                
                <select
                  value={filterTier}
                  onChange={(e) => setFilterTier(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="all">All Tiers</option>
                  <option value="Bronze">Bronze</option>
                  <option value="Silver">Silver</option>
                  <option value="Gold">Gold</option>
                  <option value="Platinum">Platinum</option>
                </select>
              </div>
            </div>

            {/* Customer Stats */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Customers</p>
                    <p className="text-2xl font-bold text-gray-900">{customers.length}</p>
                  </div>
                  <Users className="h-8 w-8 text-blue-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Lifetime Value</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {formatCurrency(customers.reduce((sum, c) => sum + c.totalSpent, 0) / customers.length)}
                    </p>
                  </div>
                  <DollarSign className="h-8 w-8 text-green-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Avg Satisfaction</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {(customers.reduce((sum, c) => sum + c.behavior.satisfaction, 0) / customers.length).toFixed(1)}
                    </p>
                  </div>
                  <Star className="h-8 w-8 text-yellow-500" />
                </div>
              </div>
              
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">At-Risk Customers</p>
                    <p className="text-2xl font-bold text-gray-900">
                      {customers.filter(c => c.behavior.churnRisk > 60).length}
                    </p>
                  </div>
                  <AlertCircle className="h-8 w-8 text-red-500" />
                </div>
              </div>
            </div>

            {/* Customer List */}
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="px-6 py-4 border-b border-gray-200">
                <h3 className="text-lg font-semibold text-gray-900">Customer Directory</h3>
              </div>
              
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Customer
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Tier
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Total Spent
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Visits
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Last Visit
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Churn Risk
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        Actions
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {filteredCustomers.map((customer) => (
                      <tr key={customer.id} className="hover:bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
                                <span className="text-sm font-medium text-blue-600">
                                  {customer.name.split(' ').map(n => n[0]).join('')}
                                </span>
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{customer.name}</div>
                              <div className="text-sm text-gray-500">{customer.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getTierColor(customer.loyaltyTier)}`}>
                            {customer.loyaltyTier}
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {formatCurrency(customer.totalSpent)}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          {customer.visitCount}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                          {new Date(customer.lastVisit).toLocaleDateString()}
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap">
                          <span className={`text-sm font-medium ${getChurnRiskColor(customer.behavior.churnRisk)}`}>
                            {customer.behavior.churnRisk}%
                          </span>
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => {
                                setSelectedCustomer(customer);
                                setShowCustomerModal(true);
                              }}
                              className="text-blue-600 hover:text-blue-900"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button className="text-green-600 hover:text-green-900">
                              <Mail className="h-4 w-4" />
                            </button>
                            <button className="text-purple-600 hover:text-purple-900">
                              <MessageSquare className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {/* Other tabs content would be implemented here */}
        {activeTab !== 'customers' && (
          <div className="bg-white p-8 rounded-lg shadow-sm border text-center">
            <div className="max-w-md mx-auto">
              <div className="bg-blue-100 rounded-full p-3 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <Users className="h-8 w-8 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {activeTab.charAt(0).toUpperCase() + activeTab.slice(1)} Management
              </h3>
              <p className="text-gray-600 mb-4">
                Advanced {activeTab} management system is being implemented with comprehensive features and automation.
              </p>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-500">
                  This section will include full {activeTab} management capabilities with real-time tracking and analytics.
                </p>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Phase3CRMSystem;
