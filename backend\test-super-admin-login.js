const fetch = require('node-fetch');

async function testSuperAdminLogin() {
  console.log('🔐 Testing Super Admin Login Interface...\n');

  const testCases = [
    {
      name: 'Super Admin Login (PIN: 888888)',
      pin: '888888',
      tenant_slug: 'barpos-system',
      expectedRole: 'super_admin'
    },
    {
      name: 'Super Admin Login (PIN: 999999)',
      pin: '999999',
      tenant_slug: 'barpos-system',
      expectedRole: 'super_admin'
    },
    {
      name: 'Invalid PIN Test',
      pin: '000000',
      tenant_slug: 'barpos-system',
      expectedRole: null,
      shouldFail: true
    }
  ];

  let passedTests = 0;
  let totalTests = testCases.length;

  for (const testCase of testCases) {
    console.log(`🧪 Testing: ${testCase.name}`);
    console.log(`   PIN: ${testCase.pin}`);
    console.log(`   Tenant: ${testCase.tenant_slug}`);

    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: testCase.pin,
          tenant_slug: testCase.tenant_slug
        }),
      });

      const data = await response.json();

      if (testCase.shouldFail) {
        if (!response.ok) {
          console.log(`   ✅ PASS - Authentication correctly failed`);
          console.log(`   📝 Error: ${data.error || 'Authentication failed'}`);
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Authentication should have failed but succeeded`);
        }
      } else {
        if (response.ok && data.employee && data.employee.role === testCase.expectedRole) {
          console.log(`   ✅ PASS - Authentication successful`);
          console.log(`   👤 User: ${data.employee.name} (${data.employee.role})`);
          console.log(`   🏢 Tenant: ${data.tenant.name}`);
          console.log(`   🔑 Token: ${data.token ? 'Generated' : 'Missing'}`);
          passedTests++;
        } else {
          console.log(`   ❌ FAIL - Authentication failed or wrong role`);
          console.log(`   📝 Response: ${JSON.stringify(data, null, 2)}`);
        }
      }
    } catch (error) {
      console.log(`   ❌ FAIL - Network/Server Error`);
      console.log(`   📝 Error: ${error.message}`);
    }

    console.log('');
  }

  console.log('='.repeat(50));
  console.log(`📊 TEST RESULTS: ${passedTests}/${totalTests} tests passed`);
  console.log(`📈 Success Rate: ${Math.round((passedTests / totalTests) * 100)}%`);
  
  if (passedTests === totalTests) {
    console.log('🎉 All Super Admin login tests PASSED!');
    console.log('✅ The restored Super Admin login interface is working correctly.');
  } else {
    console.log('⚠️  Some tests failed. Please check the authentication system.');
  }
}

testSuperAdminLogin().catch(console.error);
