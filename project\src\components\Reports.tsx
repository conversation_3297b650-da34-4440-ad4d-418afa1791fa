import React, { useMemo } from 'react';
import { useAppContext } from '../context/AppContext';
import { Bar<PERSON><PERSON>, DollarSign, TrendingUp, Users } from 'lucide-react';

const Reports: React.FC = () => {
  const { state } = useAppContext();
  
  // Calculate summary statistics
  const stats = useMemo(() => {
    const paidOrders = state.orders.filter(order => order.status === 'paid');
    
    // Calculate total revenue
    const totalRevenue = paidOrders.reduce((sum, order) => sum + order.total, 0);
    
    // Calculate revenue by category
    const revenueByCategory: Record<string, number> = {};
    paidOrders.forEach(order => {
      order.items.forEach(item => {
        const product = state.products.find(p => p.id === item.productId);
        if (product) {
          const category = product.category;
          revenueByCategory[category] = (revenueByCategory[category] || 0) + (item.price * item.quantity);
        }
      });
    });
    
    // Calculate top selling items
    const itemSales: Record<string, { name: string; quantity: number; revenue: number }> = {};
    paidOrders.forEach(order => {
      order.items.forEach(item => {
        if (!itemSales[item.productId]) {
          itemSales[item.productId] = {
            name: item.name,
            quantity: 0,
            revenue: 0
          };
        }
        
        itemSales[item.productId].quantity += item.quantity;
        itemSales[item.productId].revenue += item.price * item.quantity;
      });
    });
    
    const topItems = Object.values(itemSales)
      .sort((a, b) => b.quantity - a.quantity)
      .slice(0, 5);
    
    return {
      totalOrders: paidOrders.length,
      totalRevenue,
      averageOrderValue: paidOrders.length > 0 ? totalRevenue / paidOrders.length : 0,
      revenueByCategory,
      topItems
    };
  }, [state.orders, state.products]);
  
  // Helper function to format currency
  const formatCurrency = (amount: number): string => {
    return `$${amount.toFixed(2)}`;
  };
  
  // Format category name for display
  const formatCategoryName = (category: string): string => {
    return category.charAt(0).toUpperCase() + category.slice(1).replace('-', ' ');
  };
  
  // Calculate percentage of total for category chart
  const getCategoryPercentage = (value: number): number => {
    return stats.totalRevenue > 0 ? (value / stats.totalRevenue) * 100 : 0;
  };
  
  // If no orders, show empty state
  if (state.orders.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-6">
        <BarChart className="h-12 w-12 text-gray-500 mb-4" />
        <h3 className="text-xl font-semibold text-white mb-2">No Reports Available</h3>
        <p className="text-gray-400">
          Complete some orders to see sales reports
        </p>
      </div>
    );
  }
  
  return (
    <div className="p-4 h-full overflow-y-auto scrollbar-thin scrollbar-thumb-gray-700">
      <h2 className="text-xl font-semibold text-white mb-4">Sales Reports</h2>
      
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <div className="bg-purple-900 p-2 rounded-md mr-3">
              <DollarSign className="h-5 w-5 text-purple-400" />
            </div>
            <span className="text-gray-300">Total Revenue</span>
          </div>
          <div className="text-2xl font-bold text-white">{formatCurrency(stats.totalRevenue)}</div>
          <div className="text-sm text-gray-400 mt-1">{stats.totalOrders} orders</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <div className="bg-amber-900 p-2 rounded-md mr-3">
              <TrendingUp className="h-5 w-5 text-amber-400" />
            </div>
            <span className="text-gray-300">Average Order</span>
          </div>
          <div className="text-2xl font-bold text-white">{formatCurrency(stats.averageOrderValue)}</div>
          <div className="text-sm text-gray-400 mt-1">per transaction</div>
        </div>
        
        <div className="bg-gray-800 rounded-lg p-4">
          <div className="flex items-center mb-2">
            <div className="bg-blue-900 p-2 rounded-md mr-3">
              <Users className="h-5 w-5 text-blue-400" />
            </div>
            <span className="text-gray-300">Customer Tabs</span>
          </div>
          <div className="text-2xl font-bold text-white">
            {state.orders.filter(order => order.tabName && order.tabName.trim() !== '').length}
          </div>
          <div className="text-sm text-gray-400 mt-1">named tabs</div>
        </div>
      </div>
      
      {/* Revenue by Category */}
      <div className="bg-gray-800 rounded-lg p-4 mb-6">
        <h3 className="text-lg font-semibold text-white mb-4">Revenue by Category</h3>
        
        {Object.entries(stats.revenueByCategory)
          .sort((a, b) => b[1] - a[1])
          .map(([category, revenue]) => (
            <div key={category} className="mb-3 last:mb-0">
              <div className="flex justify-between mb-1">
                <span className="text-gray-300">{formatCategoryName(category)}</span>
                <span className="text-white">{formatCurrency(revenue)}</span>
              </div>
              <div className="bg-gray-700 rounded-full h-2 w-full">
                <div 
                  className="bg-purple-600 rounded-full h-2" 
                  style={{ width: `${getCategoryPercentage(revenue)}%` }}
                />
              </div>
            </div>
          ))}
      </div>
      
      {/* Top Selling Items */}
      <div className="bg-gray-800 rounded-lg p-4">
        <h3 className="text-lg font-semibold text-white mb-4">Top Selling Items</h3>
        
        <table className="w-full">
          <thead>
            <tr className="text-left text-gray-400 border-b border-gray-700">
              <th className="pb-2">Item</th>
              <th className="pb-2 text-right">Quantity</th>
              <th className="pb-2 text-right">Revenue</th>
            </tr>
          </thead>
          <tbody>
            {stats.topItems.map((item, index) => (
              <tr key={index} className="border-b border-gray-700 last:border-0">
                <td className="py-3 text-white">{item.name}</td>
                <td className="py-3 text-gray-300 text-right">{item.quantity}</td>
                <td className="py-3 text-amber-400 text-right font-medium">{formatCurrency(item.revenue)}</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default Reports;