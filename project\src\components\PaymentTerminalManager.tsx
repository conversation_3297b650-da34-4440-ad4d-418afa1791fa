import React, { useState, useEffect } from 'react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import { CreditCard, Smartphone, Wifi, WifiOff, Settings, Plus, CheckCircle, AlertTriangle, RefreshCw } from 'lucide-react';

interface PaymentTerminal {
  id: string;
  name: string;
  type: 'clover' | 'moneris' | 'stripe_terminal' | 'paypal_zettle' | 'square';
  model: string;
  serial_number: string;
  location_id: string;
  location_name: string;
  status: 'online' | 'offline' | 'error' | 'maintenance';
  connection_type: 'wifi' | 'ethernet' | 'cellular';
  battery_level?: number;
  last_transaction: string;
  daily_transactions: number;
  daily_volume: number;
  firmware_version: string;
  supported_payments: string[];
  configuration: {
    tip_enabled: boolean;
    receipt_options: string[];
    currency: string;
    tax_rate: number;
  };
}

interface PaymentMethod {
  id: string;
  name: string;
  type: 'card' | 'digital_wallet' | 'nfc' | 'qr_code';
  provider: string;
  icon: string;
  enabled: boolean;
  processing_fee: number;
  supported_terminals: string[];
}

const PaymentTerminalManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [terminals, setTerminals] = useState<PaymentTerminal[]>([]);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [selectedTerminal, setSelectedTerminal] = useState<PaymentTerminal | null>(null);
  const [showAddTerminalModal, setShowAddTerminalModal] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [activeView, setActiveView] = useState<'terminals' | 'methods' | 'settings'>('terminals');
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [newTerminalData, setNewTerminalData] = useState({
    name: '',
    type: 'card_reader',
    location: '',
    ip_address: '',
    supported_payments: ['credit_card', 'debit_card']
  });

  // Handler functions
  const handleAddTerminal = async () => {
    setActionLoading('add-terminal');
    try {
      const response = await apiCall('/api/payments/terminals', {
        method: 'POST',
        body: JSON.stringify(newTerminalData)
      });

      if (response.ok) {
        const newTerminal = await response.json();
        setTerminals(prev => [...prev, newTerminal]);
        setShowAddTerminalModal(false);
        setNewTerminalData({
          name: '',
          type: 'card_reader',
          location: '',
          ip_address: '',
          supported_payments: ['credit_card', 'debit_card']
        });
      } else {
        throw new Error('Failed to add terminal');
      }
    } catch (error) {
      console.error('Error adding terminal:', error);
      setError('Failed to add terminal. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleTerminalAction = async (terminalId: string, action: string) => {
    setActionLoading(`${action}-${terminalId}`);
    try {
      const response = await apiCall(`/api/payments/terminals/${terminalId}/${action}`, {
        method: 'POST'
      });

      if (response.ok) {
        // Update terminal status
        setTerminals(prev => prev.map(terminal =>
          terminal.id === terminalId
            ? { ...terminal, status: action === 'restart' ? 'online' : action }
            : terminal
        ));
      } else {
        throw new Error(`Failed to ${action} terminal`);
      }
    } catch (error) {
      console.error(`Error ${action} terminal:`, error);
      setError(`Failed to ${action} terminal. Please try again.`);
    } finally {
      setActionLoading(null);
    }
  };

  const handlePaymentMethodToggle = async (methodId: string, enabled: boolean) => {
    setActionLoading(`toggle-${methodId}`);
    try {
      const response = await apiCall(`/api/payments/methods/${methodId}`, {
        method: 'PATCH',
        body: JSON.stringify({ enabled })
      });

      if (response.ok) {
        setPaymentMethods(prev => prev.map(method =>
          method.id === methodId ? { ...method, enabled } : method
        ));
      } else {
        throw new Error('Failed to update payment method');
      }
    } catch (error) {
      console.error('Error updating payment method:', error);
      setError('Failed to update payment method. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  // Load payment terminals and methods
  useEffect(() => {
    const loadPaymentData = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        console.log('💳 Loading payment terminals and methods...');
        
        const [terminalsResponse, methodsResponse] = await Promise.all([
          apiCall('/api/payments/terminals'),
          apiCall('/api/payments/methods')
        ]);
        
        if (terminalsResponse.ok && methodsResponse.ok) {
          const terminalsData = await terminalsResponse.json();
          const methodsData = await methodsResponse.json();
          setTerminals(terminalsData);
          setPaymentMethods(methodsData);
          console.log('✅ Payment data loaded successfully');
        }
      } catch (error) {
        console.error('❌ Error loading payment data:', error);
        setError('Failed to load payment data. Using mock data.');
        
        // Fallback to mock data
        const mockTerminals: PaymentTerminal[] = [
          {
            id: 'term_1',
            name: 'Main Counter Terminal',
            type: 'clover',
            model: 'Clover Flex',
            serial_number: 'CLV-001-2024',
            location_id: 'loc_1',
            location_name: 'Downtown Restaurant',
            status: 'online',
            connection_type: 'wifi',
            battery_level: 85,
            last_transaction: new Date(Date.now() - 15 * 60 * 1000).toISOString(),
            daily_transactions: 127,
            daily_volume: 3450.75,
            firmware_version: '2.1.4',
            supported_payments: ['visa', 'mastercard', 'amex', 'apple_pay', 'google_pay', 'tap_to_pay'],
            configuration: {
              tip_enabled: true,
              receipt_options: ['print', 'email', 'sms'],
              currency: 'USD',
              tax_rate: 8.25
            }
          },
          {
            id: 'term_2',
            name: 'Drive-Thru Terminal',
            type: 'stripe_terminal',
            model: 'Stripe Reader S700',
            serial_number: 'STR-002-2024',
            location_id: 'loc_2',
            location_name: 'Airport Branch',
            status: 'online',
            connection_type: 'ethernet',
            last_transaction: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
            daily_transactions: 89,
            daily_volume: 2180.50,
            firmware_version: '1.8.2',
            supported_payments: ['visa', 'mastercard', 'amex', 'apple_pay', 'google_pay'],
            configuration: {
              tip_enabled: false,
              receipt_options: ['print'],
              currency: 'USD',
              tax_rate: 8.25
            }
          },
          {
            id: 'term_3',
            name: 'Mobile Terminal',
            type: 'paypal_zettle',
            model: 'Zettle Reader 2',
            serial_number: 'ZET-003-2024',
            location_id: 'loc_3',
            location_name: 'Mall Food Court',
            status: 'offline',
            connection_type: 'cellular',
            battery_level: 15,
            last_transaction: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
            daily_transactions: 0,
            daily_volume: 0,
            firmware_version: '3.2.1',
            supported_payments: ['visa', 'mastercard', 'paypal'],
            configuration: {
              tip_enabled: true,
              receipt_options: ['email'],
              currency: 'USD',
              tax_rate: 8.25
            }
          }
        ];

        const mockMethods: PaymentMethod[] = [
          {
            id: 'visa',
            name: 'Visa',
            type: 'card',
            provider: 'Visa Inc.',
            icon: '💳',
            enabled: true,
            processing_fee: 2.9,
            supported_terminals: ['term_1', 'term_2', 'term_3']
          },
          {
            id: 'mastercard',
            name: 'Mastercard',
            type: 'card',
            provider: 'Mastercard Inc.',
            icon: '💳',
            enabled: true,
            processing_fee: 2.9,
            supported_terminals: ['term_1', 'term_2', 'term_3']
          },
          {
            id: 'apple_pay',
            name: 'Apple Pay',
            type: 'digital_wallet',
            provider: 'Apple Inc.',
            icon: '📱',
            enabled: true,
            processing_fee: 2.6,
            supported_terminals: ['term_1', 'term_2']
          },
          {
            id: 'google_pay',
            name: 'Google Pay',
            type: 'digital_wallet',
            provider: 'Google LLC',
            icon: '📱',
            enabled: true,
            processing_fee: 2.6,
            supported_terminals: ['term_1', 'term_2']
          },
          {
            id: 'tap_to_pay',
            name: 'Tap to Pay',
            type: 'nfc',
            provider: 'NFC Consortium',
            icon: '📡',
            enabled: true,
            processing_fee: 2.4,
            supported_terminals: ['term_1']
          },
          {
            id: 'qr_payment',
            name: 'QR Code Payment',
            type: 'qr_code',
            provider: 'Various',
            icon: '📱',
            enabled: false,
            processing_fee: 1.9,
            supported_terminals: ['term_1', 'term_2', 'term_3']
          }
        ];

        setTerminals(mockTerminals);
        setPaymentMethods(mockMethods);
      } finally {
        setIsLoading(false);
      }
    };
    
    loadPaymentData();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadPaymentData, 30000);
    return () => clearInterval(interval);
  }, [apiCall]);

  const getStatusColor = (status: PaymentTerminal['status']) => {
    switch (status) {
      case 'online': return 'bg-green-100 text-green-800';
      case 'offline': return 'bg-red-100 text-red-800';
      case 'error': return 'bg-red-100 text-red-800';
      case 'maintenance': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: PaymentTerminal['status']) => {
    switch (status) {
      case 'online': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'offline': return <WifiOff className="h-4 w-4 text-red-500" />;
      case 'error': return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'maintenance': return <Settings className="h-4 w-4 text-yellow-500" />;
      default: return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getConnectionIcon = (type: string) => {
    switch (type) {
      case 'wifi': return <Wifi className="h-4 w-4 text-blue-500" />;
      case 'ethernet': return <Wifi className="h-4 w-4 text-green-500" />;
      case 'cellular': return <Smartphone className="h-4 w-4 text-purple-500" />;
      default: return <Wifi className="h-4 w-4 text-gray-500" />;
    }
  };

  const getBatteryColor = (level?: number) => {
    if (!level) return 'text-gray-400';
    if (level > 50) return 'text-green-500';
    if (level > 20) return 'text-yellow-500';
    return 'text-red-500';
  };

  const getTerminalStats = () => {
    const onlineTerminals = terminals.filter(t => t.status === 'online').length;
    const totalTransactions = terminals.reduce((sum, t) => sum + t.daily_transactions, 0);
    const totalVolume = terminals.reduce((sum, t) => sum + t.daily_volume, 0);
    const enabledMethods = paymentMethods.filter(m => m.enabled).length;
    
    return { onlineTerminals, totalTransactions, totalVolume, enabledMethods };
  };

  const stats = getTerminalStats();

  if (isLoading) {
    return (
      <div className="h-full flex items-center justify-center">
        <div className="flex flex-col items-center space-y-4">
          <RefreshCw className="h-8 w-8 text-blue-500 animate-spin" />
          <p className="text-gray-600">Loading payment terminals...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-white">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Payment Terminal Manager</h2>
            <p className="text-sm text-gray-500">Manage payment devices and methods</p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={() => setShowAddTerminalModal(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md flex items-center space-x-2 transition-colors"
            >
              <Plus className="h-4 w-4" />
              <span>Add Terminal</span>
            </button>
            <button
              onClick={() => window.location.reload()}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Refresh Data"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mx-4 mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <p className="text-yellow-800 text-sm">⚠️ {error}</p>
        </div>
      )}

      {/* Stats Cards */}
      <div className="p-4 bg-gray-50 border-b border-gray-200">
        <div className="grid grid-cols-4 gap-4">
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Online Terminals</p>
                <p className="text-2xl font-bold text-gray-900">{stats.onlineTerminals}/{terminals.length}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Daily Transactions</p>
                <p className="text-2xl font-bold text-gray-900">{stats.totalTransactions}</p>
              </div>
              <CreditCard className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Daily Volume</p>
                <p className="text-2xl font-bold text-gray-900">${stats.totalVolume.toFixed(2)}</p>
              </div>
              <CreditCard className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-gray-500 text-sm">Payment Methods</p>
                <p className="text-2xl font-bold text-gray-900">{stats.enabledMethods}/{paymentMethods.length}</p>
              </div>
              <Smartphone className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-gray-50 border-b border-gray-200 p-4">
        <div className="flex space-x-1">
          {[
            { id: 'terminals', label: 'Terminals', icon: CreditCard },
            { id: 'methods', label: 'Payment Methods', icon: Smartphone },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveView(tab.id as any)}
              className={`flex items-center space-x-2 px-4 py-2 rounded-md transition-colors ${
                activeView === tab.id
                  ? 'bg-white text-blue-600 shadow-sm border border-gray-200'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto p-4">
        {activeView === 'terminals' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {terminals.map((terminal) => (
              <div
                key={terminal.id}
                onClick={() => setSelectedTerminal(terminal)}
                className="bg-white border border-gray-200 rounded-lg p-4 cursor-pointer hover:shadow-md transition-shadow"
              >
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h3 className="font-semibold text-gray-900">{terminal.name}</h3>
                    <p className="text-sm text-gray-600">{terminal.model}</p>
                    <p className="text-xs text-gray-500">{terminal.location_name}</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(terminal.status)}
                    <span className={`text-xs px-2 py-1 rounded-full font-medium ${getStatusColor(terminal.status)}`}>
                      {terminal.status.toUpperCase()}
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3 mb-3">
                  <div>
                    <p className="text-xs text-gray-500">Daily Transactions</p>
                    <p className="font-semibold text-gray-900">{terminal.daily_transactions}</p>
                  </div>
                  <div>
                    <p className="text-xs text-gray-500">Daily Volume</p>
                    <p className="font-semibold text-gray-900">${terminal.daily_volume.toFixed(0)}</p>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <div className="flex items-center space-x-1">
                    {getConnectionIcon(terminal.connection_type)}
                    <span>{terminal.connection_type}</span>
                  </div>
                  {terminal.battery_level && (
                    <div className="flex items-center space-x-1">
                      <span className={getBatteryColor(terminal.battery_level)}>
                        🔋 {terminal.battery_level}%
                      </span>
                    </div>
                  )}
                </div>

                <div className="mt-3 flex flex-wrap gap-1">
                  {terminal.supported_payments.slice(0, 3).map((payment) => (
                    <span key={payment} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                      {payment.replace('_', ' ')}
                    </span>
                  ))}
                  {terminal.supported_payments.length > 3 && (
                    <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                      +{terminal.supported_payments.length - 3} more
                    </span>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'methods' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {paymentMethods.map((method) => (
              <div
                key={method.id}
                className="bg-white border border-gray-200 rounded-lg p-4"
              >
                <div className="flex justify-between items-start mb-3">
                  <div className="flex items-center space-x-3">
                    <span className="text-2xl">{method.icon}</span>
                    <div>
                      <h3 className="font-semibold text-gray-900">{method.name}</h3>
                      <p className="text-sm text-gray-600">{method.provider}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={method.enabled}
                        onChange={() => {
                          setPaymentMethods(prev => prev.map(m => 
                            m.id === method.id ? { ...m, enabled: !m.enabled } : m
                          ));
                        }}
                        className="sr-only peer"
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Processing Fee</span>
                    <span className="font-medium text-gray-900">{method.processing_fee}%</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Type</span>
                    <span className="font-medium text-gray-900 capitalize">{method.type.replace('_', ' ')}</span>
                  </div>
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-500">Supported Terminals</span>
                    <span className="font-medium text-gray-900">{method.supported_terminals.length}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {activeView === 'settings' && (
          <div className="bg-white border border-gray-200 rounded-lg p-6 text-center">
            <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Payment Settings</h3>
            <p className="text-gray-600">Advanced payment configuration coming soon...</p>
          </div>
        )}
      </div>

      {/* Terminal Details Modal */}
      {selectedTerminal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[80vh] overflow-y-auto">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-lg font-semibold text-gray-900">{selectedTerminal.name}</h3>
                <p className="text-gray-600">{selectedTerminal.model} • {selectedTerminal.serial_number}</p>
              </div>
              <button
                onClick={() => setSelectedTerminal(null)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>
            
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-sm text-gray-500">Status</p>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedTerminal.status)}
                  <span className={`text-sm px-2 py-1 rounded-full font-medium ${getStatusColor(selectedTerminal.status)}`}>
                    {selectedTerminal.status.toUpperCase()}
                  </span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Connection</p>
                <div className="flex items-center space-x-2">
                  {getConnectionIcon(selectedTerminal.connection_type)}
                  <span className="text-sm font-medium">{selectedTerminal.connection_type}</span>
                </div>
              </div>
              <div>
                <p className="text-sm text-gray-500">Firmware</p>
                <p className="text-sm font-medium">{selectedTerminal.firmware_version}</p>
              </div>
              <div>
                <p className="text-sm text-gray-500">Last Transaction</p>
                <p className="text-sm font-medium">
                  {new Date(selectedTerminal.last_transaction).toLocaleTimeString()}
                </p>
              </div>
            </div>

            <div className="mb-4">
              <p className="text-sm text-gray-500 mb-2">Supported Payment Methods</p>
              <div className="flex flex-wrap gap-2">
                {selectedTerminal.supported_payments.map((payment) => (
                  <span key={payment} className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">
                    {payment.replace('_', ' ')}
                  </span>
                ))}
              </div>
            </div>

            <button
              onClick={() => setSelectedTerminal(null)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}

      {/* Add Terminal Modal */}
      {showAddTerminalModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold mb-4">Add New Terminal</h3>
            <p className="text-gray-600 mb-4">Terminal provisioning interface coming soon...</p>
            <button
              onClick={() => setShowAddTerminalModal(false)}
              className="w-full bg-gray-500 text-white py-2 px-4 rounded-md hover:bg-gray-600 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      )}
    </div>
  );
};

export default PaymentTerminalManager;
