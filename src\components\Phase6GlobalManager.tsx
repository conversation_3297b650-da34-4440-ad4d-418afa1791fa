import React, { useState, useEffect } from 'react';
import { 
  Globe, 
  DollarSign, 
  CreditCard, 
  Shield, 
  TrendingUp,
  MapPin,
  Settings,
  RefreshCw,
  Plus,
  Edit,
  CheckCircle,
  AlertTriangle,
  Activity,
  BarChart3
} from 'lucide-react';

interface Currency {
  code: string;
  name: string;
  symbol: string;
  rate: number;
  isActive: boolean;
  lastUpdated: string;
}

interface PaymentGateway {
  id: string;
  name: string;
  region: string;
  status: 'active' | 'inactive' | 'maintenance';
  supportedCurrencies: string[];
  transactionFee: number;
  volume: number;
}

interface ComplianceStatus {
  region: string;
  regulation: string;
  status: 'compliant' | 'pending' | 'non-compliant';
  lastAudit: string;
  nextAudit: string;
  score: number;
}

const Phase6GlobalManager: React.FC = () => {
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [paymentGateways, setPaymentGateways] = useState<PaymentGateway[]>([]);
  const [complianceStatus, setComplianceStatus] = useState<ComplianceStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('overview');
  const [globalStats, setGlobalStats] = useState({
    totalRegions: 0,
    activeCurrencies: 0,
    totalVolume: 0,
    complianceScore: 0
  });

  useEffect(() => {
    loadGlobalData();
  }, []);

  const loadGlobalData = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('authToken');
      
      // Try to load real global data
      const response = await fetch('/api/global/dashboard', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const data = await response.json();
        setCurrencies(data.currencies || []);
        setPaymentGateways(data.gateways || []);
        setComplianceStatus(data.compliance || []);
        setGlobalStats(data.stats || {});
      } else {
        // Fallback to mock data
        setCurrencies([
          { code: 'USD', name: 'US Dollar', symbol: '$', rate: 1.0, isActive: true, lastUpdated: '2024-01-15T10:30:00Z' },
          { code: 'EUR', name: 'Euro', symbol: '€', rate: 0.85, isActive: true, lastUpdated: '2024-01-15T10:30:00Z' },
          { code: 'GBP', name: 'British Pound', symbol: '£', rate: 0.73, isActive: true, lastUpdated: '2024-01-15T10:30:00Z' },
          { code: 'CAD', name: 'Canadian Dollar', symbol: 'C$', rate: 1.35, isActive: true, lastUpdated: '2024-01-15T10:30:00Z' },
          { code: 'JPY', name: 'Japanese Yen', symbol: '¥', rate: 110.0, isActive: false, lastUpdated: '2024-01-15T10:30:00Z' },
          { code: 'AUD', name: 'Australian Dollar', symbol: 'A$', rate: 1.45, isActive: true, lastUpdated: '2024-01-15T10:30:00Z' }
        ]);

        setPaymentGateways([
          { id: '1', name: 'Stripe Global', region: 'North America', status: 'active', supportedCurrencies: ['USD', 'CAD'], transactionFee: 2.9, volume: 125430 },
          { id: '2', name: 'Adyen Europe', region: 'Europe', status: 'active', supportedCurrencies: ['EUR', 'GBP'], transactionFee: 2.5, volume: 89650 },
          { id: '3', name: 'PayPal Global', region: 'Global', status: 'active', supportedCurrencies: ['USD', 'EUR', 'GBP', 'CAD'], transactionFee: 3.4, volume: 67890 },
          { id: '4', name: 'Square Asia', region: 'Asia Pacific', status: 'maintenance', supportedCurrencies: ['AUD', 'JPY'], transactionFee: 2.8, volume: 23450 }
        ]);

        setComplianceStatus([
          { region: 'EU', regulation: 'GDPR', status: 'compliant', lastAudit: '2024-01-01', nextAudit: '2024-07-01', score: 96.5 },
          { region: 'US', regulation: 'CCPA', status: 'compliant', lastAudit: '2023-12-15', nextAudit: '2024-06-15', score: 94.2 },
          { region: 'Canada', regulation: 'PIPEDA', status: 'compliant', lastAudit: '2024-01-10', nextAudit: '2024-07-10', score: 92.8 },
          { region: 'UK', regulation: 'UK GDPR', status: 'pending', lastAudit: '2023-11-20', nextAudit: '2024-05-20', score: 88.5 }
        ]);

        setGlobalStats({
          totalRegions: 4,
          activeCurrencies: 5,
          totalVolume: 306420,
          complianceScore: 93.0
        });
      }
    } catch (error) {
      console.error('Failed to load global data:', error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number, currency = 'USD') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': case 'compliant': return 'text-green-600 bg-green-100';
      case 'inactive': case 'non-compliant': return 'text-red-600 bg-red-100';
      case 'maintenance': case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Global Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Globe className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Regions</p>
              <p className="text-2xl font-semibold text-gray-900">{globalStats.totalRegions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Currencies</p>
              <p className="text-2xl font-semibold text-gray-900">{globalStats.activeCurrencies}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <TrendingUp className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Global Volume</p>
              <p className="text-2xl font-semibold text-gray-900">{formatCurrency(globalStats.totalVolume)}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Shield className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Compliance Score</p>
              <p className="text-2xl font-semibold text-gray-900">{globalStats.complianceScore}%</p>
            </div>
          </div>
        </div>
      </div>

      {/* Regional Performance */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Regional Performance</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {paymentGateways.map((gateway) => (
              <div key={gateway.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium text-gray-900">{gateway.name}</h4>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(gateway.status)}`}>
                    {gateway.status}
                  </span>
                </div>
                <div className="space-y-1 text-sm text-gray-600">
                  <div className="flex justify-between">
                    <span>Region:</span>
                    <span className="font-medium">{gateway.region}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Volume:</span>
                    <span className="font-medium">{formatCurrency(gateway.volume)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Fee:</span>
                    <span className="font-medium">{gateway.transactionFee}%</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Compliance Overview */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Compliance Status</h3>
        </div>
        <div className="p-6">
          <div className="space-y-4">
            {complianceStatus.map((compliance, index) => (
              <div key={index} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center">
                  <Shield className="h-5 w-5 text-blue-500 mr-3" />
                  <div>
                    <p className="font-medium text-gray-900">{compliance.regulation}</p>
                    <p className="text-sm text-gray-500">{compliance.region}</p>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <span className="text-sm font-medium text-gray-900">{compliance.score}%</span>
                  <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(compliance.status)}`}>
                    {compliance.status}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );

  const renderCurrencies = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Currency Management</h3>
        <div className="flex space-x-3">
          <button 
            onClick={loadGlobalData}
            className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2"
          >
            <RefreshCw className="h-4 w-4" />
            <span>Update Rates</span>
          </button>
          <button className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
            <Plus className="h-4 w-4" />
            <span>Add Currency</span>
          </button>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow overflow-hidden">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Currency</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Symbol</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Exchange Rate</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Updated</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {currencies.map((currency) => (
              <tr key={currency.code}>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="flex items-center">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-semibold text-sm mr-3">
                      {currency.code}
                    </div>
                    <span className="text-sm font-medium text-gray-900">{currency.name}</span>
                  </div>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 font-medium">
                  {currency.symbol}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {currency.rate.toFixed(4)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {new Date(currency.lastUpdated).toLocaleString()}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                    currency.isActive 
                      ? 'bg-green-100 text-green-800' 
                      : 'bg-gray-100 text-gray-800'
                  }`}>
                    {currency.isActive ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <button className="text-blue-600 hover:text-blue-900 mr-3">
                    <Edit className="h-4 w-4" />
                  </button>
                  <button className="text-green-600 hover:text-green-900">
                    <RefreshCw className="h-4 w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  const renderPaymentGateways = () => (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-900">Payment Gateways</h3>
        <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Add Gateway</span>
        </button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {paymentGateways.map((gateway) => (
          <div key={gateway.id} className="bg-white rounded-lg shadow p-6">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <CreditCard className="h-6 w-6 text-blue-600 mr-3" />
                <div>
                  <h4 className="text-lg font-medium text-gray-900">{gateway.name}</h4>
                  <p className="text-sm text-gray-500">{gateway.region}</p>
                </div>
              </div>
              <span className={`px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(gateway.status)}`}>
                {gateway.status}
              </span>
            </div>
            
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Transaction Volume:</span>
                <span className="text-gray-900 font-medium">{formatCurrency(gateway.volume)}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Transaction Fee:</span>
                <span className="text-gray-900 font-medium">{gateway.transactionFee}%</span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-500">Supported Currencies:</span>
                <span className="text-gray-900 font-medium">{gateway.supportedCurrencies.length}</span>
              </div>
            </div>

            <div className="flex space-x-2">
              <button className="flex-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Configure
              </button>
              <button className="flex-1 bg-gray-600 hover:bg-gray-700 text-white px-3 py-2 rounded-md text-sm font-medium">
                Test
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="flex items-center space-x-2">
          <RefreshCw className="h-6 w-6 animate-spin text-cyan-600" />
          <span className="text-gray-600">Loading global systems...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-bold text-gray-900">Phase 6: Global Expansion</h2>
        <p className="text-gray-600">Multi-currency and international payment management</p>
      </div>

      {/* Sub Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'overview', name: 'Overview', icon: Globe },
            { id: 'currencies', name: 'Currencies', icon: DollarSign },
            { id: 'gateways', name: 'Payment Gateways', icon: CreditCard }
          ].map((tab) => {
            const Icon = tab.icon;
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`
                  flex items-center space-x-2 py-2 px-1 border-b-2 font-medium text-sm
                  ${activeTab === tab.id
                    ? 'border-cyan-500 text-cyan-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }
                `}
              >
                <Icon className="h-4 w-4" />
                <span>{tab.name}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && renderOverview()}
      {activeTab === 'currencies' && renderCurrencies()}
      {activeTab === 'gateways' && renderPaymentGateways()}
    </div>
  );
};

export default Phase6GlobalManager;
