import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  Search,
  Filter,
  Grid,
  List,
  Clock,
  Star,
  Plus,
  Minus,
  ShoppingCart,
  Package,
  Coffee,
  Utensils,
  Pizza,
  Sandwich,
  IceCream,
  Wine
} from 'lucide-react';
import { Button, Card, Input, Badge, Grid as GridLayout, Flex, LoadingSpinner } from '../ui/DesignSystem';

interface Product {
  id: string;
  name: string;
  price: number;
  category: string;
  image?: string;
  description?: string;
  isAvailable: boolean;
  preparationTime?: number;
  allergens?: string[];
  nutritionalInfo?: any;
  variants?: ProductVariant[];
  modifiers?: ProductModifier[];
  popularity?: number;
  isNew?: boolean;
  isRecommended?: boolean;
}

interface ProductVariant {
  id: string;
  name: string;
  priceAdjustment: number;
}

interface ProductModifier {
  id: string;
  name: string;
  price: number;
  category: string;
  required: boolean;
  maxSelections?: number;
}

interface OptimizedProductGridProps {
  products: Product[];
  categories: string[];
  onAddToOrder: (product: Product, quantity?: number) => void;
  loading?: boolean;
  isDarkMode?: boolean;
}

const OptimizedProductGrid: React.FC<OptimizedProductGridProps> = ({
  products,
  categories,
  onAddToOrder,
  loading = false,
  isDarkMode = false
}) => {
  // State management
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'name' | 'price' | 'popularity'>('name');
  const [showFilters, setShowFilters] = useState(false);
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [showOnlyAvailable, setShowOnlyAvailable] = useState(true);
  const [showOnlyRecommended, setShowOnlyRecommended] = useState(false);

  // Category icons mapping
  const getCategoryIcon = useCallback((category: string) => {
    const icons: { [key: string]: React.ReactNode } = {
      'all': <Grid className="w-5 h-5" />,
      'Beverages': <Coffee className="w-5 h-5" />,
      'Food': <Utensils className="w-5 h-5" />,
      'Pizza': <Pizza className="w-5 h-5" />,
      'Sandwiches': <Sandwich className="w-5 h-5" />,
      'Desserts': <IceCream className="w-5 h-5" />,
      'Alcohol': <Wine className="w-5 h-5" />
    };
    return icons[category] || <Package className="w-5 h-5" />;
  }, []);

  // Memoized filtered and sorted products
  const filteredProducts = useMemo(() => {
    let filtered = products.filter(product => {
      const matchesCategory = selectedCategory === 'all' || product.category === selectedCategory;
      const matchesSearch = product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           product.description?.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesPrice = product.price >= priceRange[0] && product.price <= priceRange[1];
      const matchesAvailability = !showOnlyAvailable || product.isAvailable;
      const matchesRecommended = !showOnlyRecommended || product.isRecommended;
      
      return matchesCategory && matchesSearch && matchesPrice && matchesAvailability && matchesRecommended;
    });

    // Sort products
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'price':
          return a.price - b.price;
        case 'popularity':
          return (b.popularity || 0) - (a.popularity || 0);
        case 'name':
        default:
          return a.name.localeCompare(b.name);
      }
    });

    return filtered;
  }, [products, selectedCategory, searchTerm, priceRange, showOnlyAvailable, showOnlyRecommended, sortBy]);

  // Handle add to order with quantity
  const handleAddToOrder = useCallback((product: Product, quantity: number = 1) => {
    onAddToOrder(product, quantity);
  }, [onAddToOrder]);

  // Quick add functionality
  const handleQuickAdd = useCallback((product: Product, event: React.MouseEvent) => {
    event.stopPropagation();
    handleAddToOrder(product, 1);
  }, [handleAddToOrder]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* Search and Filter Header */}
      <div className={`p-4 border-b transition-colors duration-300 ${
        isDarkMode ? 'border-gray-700 bg-gray-800' : 'border-gray-200 bg-white'
      }`}>
        {/* Search Bar */}
        <div className="mb-4">
          <Input
            type="search"
            placeholder="Search products..."
            value={searchTerm}
            onChange={setSearchTerm}
            icon={Search}
            fullWidth
          />
        </div>

        {/* Category Tabs */}
        <div className="flex space-x-2 overflow-x-auto pb-2 mb-4">
          {['all', ...categories].map((category) => (
            <Button
              key={category}
              variant={selectedCategory === category ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setSelectedCategory(category)}
              icon={() => getCategoryIcon(category)}
              className="whitespace-nowrap"
            >
              {category === 'all' ? 'All Items' : category}
            </Button>
          ))}
        </div>

        {/* Controls Row */}
        <Flex justify="between" align="center" className="mb-2">
          <Flex gap="sm">
            {/* View Mode Toggle */}
            <div className="flex rounded-lg border border-gray-300 dark:border-gray-600 overflow-hidden">
              <Button
                variant={viewMode === 'grid' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('grid')}
                icon={Grid}
                className="rounded-none border-0"
              />
              <Button
                variant={viewMode === 'list' ? 'primary' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('list')}
                icon={List}
                className="rounded-none border-0"
              />
            </div>

            {/* Sort Dropdown */}
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'name' | 'price' | 'popularity')}
              className={`px-3 py-1.5 text-sm rounded-lg border transition-colors ${
                isDarkMode 
                  ? 'bg-gray-700 border-gray-600 text-white' 
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="name">Sort by Name</option>
              <option value="price">Sort by Price</option>
              <option value="popularity">Sort by Popularity</option>
            </select>
          </Flex>

          <Flex gap="sm" align="center">
            {/* Filter Toggle */}
            <Button
              variant={showFilters ? 'primary' : 'ghost'}
              size="sm"
              onClick={() => setShowFilters(!showFilters)}
              icon={Filter}
            >
              Filters
            </Button>

            {/* Results Count */}
            <Badge variant="secondary">
              {filteredProducts.length} items
            </Badge>
          </Flex>
        </Flex>

        {/* Advanced Filters */}
        {showFilters && (
          <Card variant="outlined" padding="sm" className="mt-3">
            <Flex direction="col" gap="sm">
              <Flex gap="lg" wrap>
                {/* Price Range */}
                <div className="flex-1 min-w-48">
                  <label className="block text-sm font-medium mb-1">Price Range</label>
                  <Flex gap="sm" align="center">
                    <input
                      type="range"
                      min="0"
                      max="100"
                      value={priceRange[0]}
                      onChange={(e) => setPriceRange([Number(e.target.value), priceRange[1]])}
                      className="flex-1"
                    />
                    <span className="text-sm">${priceRange[0]} - ${priceRange[1]}</span>
                  </Flex>
                </div>

                {/* Filter Checkboxes */}
                <Flex direction="col" gap="sm">
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={showOnlyAvailable}
                      onChange={(e) => setShowOnlyAvailable(e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Available only</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={showOnlyRecommended}
                      onChange={(e) => setShowOnlyRecommended(e.target.checked)}
                      className="rounded"
                    />
                    <span className="text-sm">Recommended only</span>
                  </label>
                </Flex>
              </Flex>
            </Flex>
          </Card>
        )}
      </div>

      {/* Products Grid/List */}
      <div className="flex-1 p-4 overflow-y-auto">
        {filteredProducts.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-64 text-center">
            <Package className={`w-16 h-16 mb-4 ${
              isDarkMode ? 'text-gray-600' : 'text-gray-400'
            }`} />
            <h3 className={`text-lg font-medium mb-2 ${
              isDarkMode ? 'text-gray-400' : 'text-gray-600'
            }`}>
              No products found
            </h3>
            <p className={`text-sm ${
              isDarkMode ? 'text-gray-500' : 'text-gray-500'
            }`}>
              Try adjusting your search or filters
            </p>
          </div>
        ) : (
          <GridLayout 
            cols={viewMode === 'grid' ? 4 : 1} 
            gap="md"
            className={viewMode === 'list' ? 'max-w-none' : ''}
          >
            {filteredProducts.map((product) => (
              <ProductCard
                key={product.id}
                product={product}
                viewMode={viewMode}
                onAddToOrder={handleAddToOrder}
                onQuickAdd={handleQuickAdd}
                isDarkMode={isDarkMode}
                getCategoryIcon={getCategoryIcon}
              />
            ))}
          </GridLayout>
        )}
      </div>
    </div>
  );
};

// Optimized Product Card Component
const ProductCard: React.FC<{
  product: Product;
  viewMode: 'grid' | 'list';
  onAddToOrder: (product: Product, quantity: number) => void;
  onQuickAdd: (product: Product, event: React.MouseEvent) => void;
  isDarkMode: boolean;
  getCategoryIcon: (category: string) => React.ReactNode;
}> = ({ product, viewMode, onAddToOrder, onQuickAdd, isDarkMode, getCategoryIcon }) => {
  const [quantity, setQuantity] = useState(1);

  const handleAddWithQuantity = useCallback(() => {
    onAddToOrder(product, quantity);
    setQuantity(1);
  }, [product, quantity, onAddToOrder]);

  if (viewMode === 'list') {
    return (
      <Card 
        hoverable 
        padding="md"
        className={`transition-all duration-200 ${
          !product.isAvailable ? 'opacity-50' : ''
        }`}
      >
        <Flex align="center" gap="md">
          {/* Product Icon */}
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
          }`}>
            {getCategoryIcon(product.category)}
          </div>

          {/* Product Info */}
          <div className="flex-1">
            <Flex justify="between" align="start">
              <div>
                <Flex align="center" gap="sm">
                  <h3 className="font-semibold">{product.name}</h3>
                  {product.isNew && <Badge variant="success" size="sm">New</Badge>}
                  {product.isRecommended && <Badge variant="warning" size="sm">⭐</Badge>}
                </Flex>
                <p className={`text-sm mt-1 ${
                  isDarkMode ? 'text-gray-400' : 'text-gray-600'
                }`}>
                  {product.description || product.category}
                </p>
                {product.preparationTime && (
                  <Flex align="center" gap="sm" className="mt-1">
                    <Clock className="w-3 h-3" />
                    <span className="text-xs">{product.preparationTime}min</span>
                  </Flex>
                )}
              </div>

              <div className="text-right">
                <p className="text-lg font-bold text-blue-600 dark:text-blue-400">
                  ${product.price.toFixed(2)}
                </p>
                <Flex gap="sm" align="center" className="mt-2">
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    icon={Minus}
                  />
                  <span className="w-8 text-center font-medium">{quantity}</span>
                  <Button
                    variant="ghost"
                    size="xs"
                    onClick={() => setQuantity(quantity + 1)}
                    icon={Plus}
                  />
                  <Button
                    variant="primary"
                    size="sm"
                    onClick={handleAddWithQuantity}
                    disabled={!product.isAvailable}
                    icon={ShoppingCart}
                  >
                    Add
                  </Button>
                </Flex>
              </div>
            </Flex>
          </div>
        </Flex>
      </Card>
    );
  }

  return (
    <Card 
      hoverable 
      padding="md"
      className={`h-full transition-all duration-200 ${
        !product.isAvailable ? 'opacity-50' : ''
      }`}
    >
      <div className="h-full flex flex-col">
        {/* Product Icon */}
        <div className="flex-1 flex items-center justify-center mb-3">
          <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
            isDarkMode ? 'bg-gray-700' : 'bg-gray-100'
          } group-hover:scale-110 transition-transform duration-200`}>
            {getCategoryIcon(product.category)}
          </div>
        </div>

        {/* Product Info */}
        <div className="text-center">
          <Flex justify="center" align="center" gap="sm" className="mb-1">
            <h3 className="font-semibold text-sm line-clamp-2">{product.name}</h3>
            {product.isNew && <Badge variant="success" size="sm">New</Badge>}
          </Flex>
          
          <p className="text-lg font-bold text-blue-600 dark:text-blue-400 mb-2">
            ${product.price.toFixed(2)}
          </p>

          {product.preparationTime && (
            <Flex justify="center" align="center" gap="sm" className="mb-3">
              <Clock className="w-3 h-3" />
              <span className="text-xs">{product.preparationTime}min</span>
            </Flex>
          )}

          {/* Quick Add Button */}
          <Button
            variant="primary"
            size="sm"
            onClick={(e) => onQuickAdd(product, e)}
            disabled={!product.isAvailable}
            icon={Plus}
            fullWidth
          >
            Add to Order
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default OptimizedProductGrid;
