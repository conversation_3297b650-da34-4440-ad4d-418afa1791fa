<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - POS System</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f9fafb;
            color: #111827;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 1rem;
        }
        
        .login-card {
            background: white;
            border-radius: 1rem;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            padding: 2rem;
            width: 100%;
            max-width: 400px;
        }
        
        .login-title {
            text-align: center;
            font-size: 2rem;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 0.5rem;
        }
        
        .login-subtitle {
            text-align: center;
            color: #6b7280;
            margin-bottom: 2rem;
        }
        
        .form-group {
            margin-bottom: 1.5rem;
        }
        
        .form-label {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }
        
        .form-input {
            width: 100%;
            padding: 0.75rem 1rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            font-size: 1rem;
            transition: all 0.2s;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 0.75rem 1rem;
            border: none;
            border-radius: 0.5rem;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .btn-primary {
            background-color: #3b82f6;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #2563eb;
        }
        
        .btn-danger {
            background-color: #ef4444;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #dc2626;
        }
        
        .btn-success {
            background-color: #10b981;
            color: white;
        }
        
        .btn-success:hover {
            background-color: #059669;
        }
        
        .error-message {
            background-color: #fef2f2;
            border: 1px solid #fecaca;
            color: #b91c1c;
            padding: 0.75rem 1rem;
            border-radius: 0.5rem;
            margin-bottom: 1rem;
        }
        
        .demo-info {
            text-align: center;
            color: #6b7280;
            font-size: 0.875rem;
            margin-top: 1.5rem;
        }
        
        .pos-system {
            min-height: 100vh;
            background-color: #f9fafb;
        }
        
        .header {
            background: white;
            border-bottom: 1px solid #e5e7eb;
            padding: 1rem 2rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .header-title {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .header-subtitle {
            color: #6b7280;
            margin-left: 1rem;
        }
        
        .header-user {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        
        .main-content {
            display: flex;
            height: calc(100vh - 80px);
        }
        
        .products-section {
            flex: 1;
            padding: 1.5rem;
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 1rem;
        }
        
        .product-card {
            background: white;
            border-radius: 0.5rem;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            padding: 1rem;
            cursor: pointer;
            transition: all 0.2s;
        }
        
        .product-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }
        
        .product-name {
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 0.25rem;
        }
        
        .product-category {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }
        
        .product-price {
            font-size: 1.125rem;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .cart-section {
            width: 320px;
            background: white;
            border-left: 1px solid #e5e7eb;
            padding: 1.5rem;
        }
        
        .cart-empty {
            text-align: center;
            color: #6b7280;
            padding: 2rem 0;
        }
        
        .cart-items {
            margin-bottom: 1.5rem;
        }
        
        .cart-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: #f9fafb;
            padding: 0.75rem;
            border-radius: 0.5rem;
            margin-bottom: 0.75rem;
        }
        
        .cart-item-info {
            flex: 1;
        }
        
        .cart-item-name {
            font-weight: 500;
            color: #1f2937;
        }
        
        .cart-item-price {
            font-size: 0.875rem;
            color: #6b7280;
        }
        
        .cart-item-controls {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }
        
        .qty-btn {
            width: 32px;
            height: 32px;
            border: none;
            border-radius: 50%;
            background: #e5e7eb;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
        }
        
        .qty-btn:hover {
            background: #d1d5db;
        }
        
        .cart-total {
            border-top: 1px solid #e5e7eb;
            padding-top: 1rem;
        }
        
        .total-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
        }
        
        .total-label {
            font-size: 1.125rem;
            font-weight: 600;
            color: #1f2937;
        }
        
        .total-amount {
            font-size: 1.5rem;
            font-weight: bold;
            color: #3b82f6;
        }
        
        .theme-toggle {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1000;
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.9);
            cursor: pointer;
            font-size: 20px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .hidden { display: none !important; }
        
        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }
            .cart-section {
                width: 100%;
            }
            .products-grid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
        }
    </style>
</head>
<body>
    <!-- Theme Toggle -->
    <button onclick="toggleTheme()" class="theme-toggle" title="Toggle Theme">🌙</button>

    <!-- Login Screen -->
    <div id="loginScreen" class="gradient-bg">
        <div class="login-card">
            <h1 class="login-title">RestroFlow</h1>
            <p class="login-subtitle">Enter your PIN to access the system</p>
            
            <div class="form-group">
                <label class="form-label">PIN Code</label>
                <input
                    type="password"
                    id="pinInput"
                    class="form-input"
                    placeholder="Enter your PIN"
                    onkeypress="handleEnterKey(event)"
                />
            </div>
            
            <div id="errorMessage" class="error-message hidden">
                Invalid PIN. Please try again.
            </div>
            
            <button onclick="login()" class="btn btn-primary">
                Access System
            </button>
            
            <div class="demo-info">
                <p>Demo PIN: 1234</p>
            </div>
        </div>
    </div>

    <!-- POS System -->
    <div id="posSystem" class="pos-system hidden">
        <!-- Header -->
        <div class="header">
            <div style="display: flex; align-items: center;">
                <h1 class="header-title">RestroFlow</h1>
                <span class="header-subtitle">Point of Sale System</span>
            </div>
            <div class="header-user">
                <span style="color: #6b7280; font-size: 0.875rem;">Welcome, Demo User</span>
                <button onclick="logout()" class="btn btn-danger" style="width: auto; padding: 0.5rem 1rem;">
                    Logout
                </button>
            </div>
        </div>

        <div class="main-content">
            <!-- Products Section -->
            <div class="products-section">
                <h2 class="section-title">Products</h2>
                <div class="products-grid">
                    <div onclick="addToCart('Burger', 12.99)" class="product-card">
                        <div class="product-name">Burger</div>
                        <div class="product-category">Main Course</div>
                        <div class="product-price">$12.99</div>
                    </div>
                    <div onclick="addToCart('Pizza', 18.99)" class="product-card">
                        <div class="product-name">Pizza</div>
                        <div class="product-category">Main Course</div>
                        <div class="product-price">$18.99</div>
                    </div>
                    <div onclick="addToCart('Salad', 8.99)" class="product-card">
                        <div class="product-name">Salad</div>
                        <div class="product-category">Appetizer</div>
                        <div class="product-price">$8.99</div>
                    </div>
                    <div onclick="addToCart('Soda', 2.99)" class="product-card">
                        <div class="product-name">Soda</div>
                        <div class="product-category">Beverage</div>
                        <div class="product-price">$2.99</div>
                    </div>
                    <div onclick="addToCart('Coffee', 3.99)" class="product-card">
                        <div class="product-name">Coffee</div>
                        <div class="product-category">Beverage</div>
                        <div class="product-price">$3.99</div>
                    </div>
                    <div onclick="addToCart('Dessert', 6.99)" class="product-card">
                        <div class="product-name">Dessert</div>
                        <div class="product-category">Sweet</div>
                        <div class="product-price">$6.99</div>
                    </div>
                </div>
            </div>

            <!-- Cart Section -->
            <div class="cart-section">
                <h2 class="section-title">Current Order</h2>
                
                <div id="cartEmpty" class="cart-empty">
                    No items in cart
                </div>
                
                <div id="cartItems" class="hidden">
                    <div id="cartList" class="cart-items">
                        <!-- Cart items will be loaded here -->
                    </div>
                    
                    <div class="cart-total">
                        <div class="total-row">
                            <span class="total-label">Total:</span>
                            <span id="totalAmount" class="total-amount">$0.00</span>
                        </div>
                        
                        <button onclick="processPayment()" class="btn btn-success">
                            Process Payment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Global variables
        let cart = [];
        let darkMode = false;

        // Login function
        function login() {
            const pin = document.getElementById('pinInput').value;
            const errorMessage = document.getElementById('errorMessage');
            
            if (pin === '1234') {
                document.getElementById('loginScreen').classList.add('hidden');
                document.getElementById('posSystem').classList.remove('hidden');
                errorMessage.classList.add('hidden');
                console.log('Login successful!');
            } else {
                errorMessage.classList.remove('hidden');
                console.log('Invalid PIN entered');
            }
        }

        // Handle Enter key press
        function handleEnterKey(event) {
            if (event.key === 'Enter') {
                login();
            }
        }

        // Logout function
        function logout() {
            document.getElementById('posSystem').classList.add('hidden');
            document.getElementById('loginScreen').classList.remove('hidden');
            document.getElementById('pinInput').value = '';
            cart = [];
            updateCart();
            console.log('Logged out successfully');
        }

        // Add to cart function
        function addToCart(name, price) {
            const existingItem = cart.find(item => item.name === name);
            if (existingItem) {
                existingItem.quantity += 1;
            } else {
                cart.push({ name: name, price: price, quantity: 1 });
            }
            updateCart();
            console.log('Added to cart:', name, price);
        }

        // Update cart display
        function updateCart() {
            const cartEmpty = document.getElementById('cartEmpty');
            const cartItems = document.getElementById('cartItems');
            const cartList = document.getElementById('cartList');
            const totalAmount = document.getElementById('totalAmount');

            if (cart.length === 0) {
                cartEmpty.classList.remove('hidden');
                cartItems.classList.add('hidden');
            } else {
                cartEmpty.classList.add('hidden');
                cartItems.classList.remove('hidden');
                
                cartList.innerHTML = '';
                cart.forEach((item, index) => {
                    const cartItem = document.createElement('div');
                    cartItem.className = 'cart-item';
                    cartItem.innerHTML = `
                        <div class="cart-item-info">
                            <div class="cart-item-name">${item.name}</div>
                            <div class="cart-item-price">$${item.price.toFixed(2)} each</div>
                        </div>
                        <div class="cart-item-controls">
                            <button onclick="updateQuantity(${index}, ${item.quantity - 1})" class="qty-btn">-</button>
                            <span style="width: 32px; text-align: center;">${item.quantity}</span>
                            <button onclick="updateQuantity(${index}, ${item.quantity + 1})" class="qty-btn">+</button>
                        </div>
                    `;
                    cartList.appendChild(cartItem);
                });
                
                const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                totalAmount.textContent = `$${total.toFixed(2)}`;
            }
        }

        // Update quantity
        function updateQuantity(index, quantity) {
            if (quantity <= 0) {
                cart.splice(index, 1);
            } else {
                cart[index].quantity = quantity;
            }
            updateCart();
        }

        // Process payment
        function processPayment() {
            const total = cart.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            alert(`Payment processed successfully! Total: $${total.toFixed(2)}`);
            cart = [];
            updateCart();
        }

        // Toggle theme
        function toggleTheme() {
            darkMode = !darkMode;
            const themeToggle = document.querySelector('.theme-toggle');
            
            if (darkMode) {
                document.body.style.backgroundColor = '#1f2937';
                document.body.style.color = '#f9fafb';
                themeToggle.textContent = '☀️';
            } else {
                document.body.style.backgroundColor = '#f9fafb';
                document.body.style.color = '#111827';
                themeToggle.textContent = '🌙';
            }
        }

        // Initialize
        console.log('RestroFlow POS System Loaded Successfully!');
        console.log('Use PIN: 1234 to login');
    </script>
</body>
</html>
