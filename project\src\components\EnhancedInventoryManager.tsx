import React, { useState, useEffect } from 'react';
import { 
  Package, 
  Plus, 
  Minus, 
  Edit3, 
  AlertTriangle, 
  TrendingUp, 
  TrendingDown,
  RefreshCw,
  Search,
  Filter,
  Download,
  Upload,
  ArrowUpDown,
  Bell,
  Settings,
  BarChart3,
  Truck,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface InventoryItem {
  id: string;
  name: string;
  sku: string;
  category: string;
  current_stock: number;
  minimum_stock: number;
  maximum_stock: number;
  unit_cost: number;
  selling_price: number;
  supplier: string;
  last_updated: string;
  status: 'in_stock' | 'low_stock' | 'out_of_stock' | 'overstocked';
}

interface InventoryAction {
  id: string;
  type: 'adjust' | 'transfer' | 'order' | 'alert' | 'audit';
  label: string;
  icon: React.ComponentType<any>;
  color: string;
  requiresInput?: boolean;
}

export const EnhancedInventoryManager: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [inventory, setInventory] = useState<InventoryItem[]>([]);
  const [filteredInventory, setFilteredInventory] = useState<InventoryItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showBulkActions, setShowBulkActions] = useState(false);
  const [showAdjustModal, setShowAdjustModal] = useState<string | null>(null);
  const [showTransferModal, setShowTransferModal] = useState(false);
  const [showOrderModal, setShowOrderModal] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Inventory actions configuration
  const inventoryActions: InventoryAction[] = [
    {
      id: 'adjust_stock',
      type: 'adjust',
      label: 'Adjust Stock',
      icon: Edit3,
      color: 'blue',
      requiresInput: true
    },
    {
      id: 'transfer_stock',
      type: 'transfer',
      label: 'Transfer Stock',
      icon: Truck,
      color: 'purple',
      requiresInput: true
    },
    {
      id: 'reorder_stock',
      type: 'order',
      label: 'Reorder Items',
      icon: Plus,
      color: 'green',
      requiresInput: true
    },
    {
      id: 'set_alerts',
      type: 'alert',
      label: 'Set Alerts',
      icon: Bell,
      color: 'orange',
      requiresInput: true
    },
    {
      id: 'audit_inventory',
      type: 'audit',
      label: 'Start Audit',
      icon: BarChart3,
      color: 'gray'
    },
    {
      id: 'export_data',
      type: 'audit',
      label: 'Export Data',
      icon: Download,
      color: 'indigo'
    }
  ];

  // Load inventory data
  useEffect(() => {
    loadInventoryData();
  }, []);

  // Filter and sort inventory
  useEffect(() => {
    let filtered = inventory.filter(item => {
      const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           item.category.toLowerCase().includes(searchTerm.toLowerCase());
      
      const matchesFilter = filterStatus === 'all' || item.status === filterStatus;
      
      return matchesSearch && matchesFilter;
    });

    // Sort inventory
    filtered.sort((a, b) => {
      let aValue: any = a[sortBy as keyof InventoryItem];
      let bValue: any = b[sortBy as keyof InventoryItem];
      
      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    setFilteredInventory(filtered);
  }, [inventory, searchTerm, filterStatus, sortBy, sortOrder]);

  const loadInventoryData = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const response = await apiCall('/api/inventory');
      if (response.ok) {
        const data = await response.json();
        setInventory(data);
      } else {
        throw new Error('Failed to load inventory');
      }
    } catch (error) {
      console.error('Error loading inventory:', error);
      setError('Failed to load inventory data');
      
      // Mock data fallback
      setInventory([
        {
          id: 'inv_001',
          name: 'Chicken Breast',
          sku: 'CHK-001',
          category: 'Meat',
          current_stock: 25,
          minimum_stock: 10,
          maximum_stock: 100,
          unit_cost: 8.50,
          selling_price: 15.99,
          supplier: 'Fresh Foods Inc',
          last_updated: new Date().toISOString(),
          status: 'in_stock'
        },
        {
          id: 'inv_002',
          name: 'Tomatoes',
          sku: 'VEG-002',
          category: 'Vegetables',
          current_stock: 5,
          minimum_stock: 15,
          maximum_stock: 50,
          unit_cost: 2.25,
          selling_price: 4.99,
          supplier: 'Garden Fresh',
          last_updated: new Date().toISOString(),
          status: 'low_stock'
        },
        {
          id: 'inv_003',
          name: 'Olive Oil',
          sku: 'OIL-003',
          category: 'Condiments',
          current_stock: 0,
          minimum_stock: 5,
          maximum_stock: 20,
          unit_cost: 12.00,
          selling_price: 18.99,
          supplier: 'Mediterranean Imports',
          last_updated: new Date().toISOString(),
          status: 'out_of_stock'
        }
      ]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleStockAdjustment = async (itemId: string, adjustment: number, reason: string) => {
    setActionLoading(`adjust_${itemId}`);
    try {
      const response = await apiCall(`/api/inventory/${itemId}/adjust`, {
        method: 'POST',
        body: JSON.stringify({
          adjustment,
          reason,
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        const updatedItem = await response.json();
        setInventory(prev => prev.map(item => 
          item.id === itemId ? { ...item, ...updatedItem } : item
        ));
        setShowAdjustModal(null);
      } else {
        throw new Error('Failed to adjust stock');
      }
    } catch (error) {
      console.error('Error adjusting stock:', error);
      setError('Failed to adjust stock. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const handleBulkAction = async (action: string) => {
    if (selectedItems.length === 0) {
      setError('Please select items to perform bulk action');
      return;
    }

    setActionLoading(action);
    try {
      const response = await apiCall('/api/inventory/bulk-action', {
        method: 'POST',
        body: JSON.stringify({
          action,
          item_ids: selectedItems,
          timestamp: new Date().toISOString()
        })
      });

      if (response.ok) {
        await loadInventoryData();
        setSelectedItems([]);
        setShowBulkActions(false);
      } else {
        throw new Error(`Failed to perform ${action}`);
      }
    } catch (error) {
      console.error('Error performing bulk action:', error);
      setError(`Failed to perform ${action}. Please try again.`);
    } finally {
      setActionLoading(null);
    }
  };

  const handleExportData = async () => {
    setActionLoading('export');
    try {
      const response = await apiCall('/api/inventory/export', {
        method: 'POST',
        body: JSON.stringify({
          format: 'csv',
          include_history: true,
          filters: { status: filterStatus, search: searchTerm }
        })
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `inventory-export-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        throw new Error('Failed to export data');
      }
    } catch (error) {
      console.error('Error exporting data:', error);
      setError('Failed to export data. Please try again.');
    } finally {
      setActionLoading(null);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'in_stock': return 'bg-green-100 text-green-800';
      case 'low_stock': return 'bg-yellow-100 text-yellow-800';
      case 'out_of_stock': return 'bg-red-100 text-red-800';
      case 'overstocked': return 'bg-blue-100 text-blue-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStockIcon = (item: InventoryItem) => {
    if (item.current_stock === 0) return <XCircle className="h-4 w-4 text-red-500" />;
    if (item.current_stock <= item.minimum_stock) return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
    if (item.current_stock >= item.maximum_stock) return <TrendingUp className="h-4 w-4 text-blue-500" />;
    return <CheckCircle className="h-4 w-4 text-green-500" />;
  };

  const getActionButtonClass = (action: InventoryAction) => {
    const baseClass = "flex items-center space-x-2 px-3 py-2 rounded-lg font-medium transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm";
    
    switch (action.color) {
      case 'blue': return `${baseClass} bg-blue-600 hover:bg-blue-700 text-white`;
      case 'green': return `${baseClass} bg-green-600 hover:bg-green-700 text-white`;
      case 'purple': return `${baseClass} bg-purple-600 hover:bg-purple-700 text-white`;
      case 'orange': return `${baseClass} bg-orange-600 hover:bg-orange-700 text-white`;
      case 'gray': return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
      case 'indigo': return `${baseClass} bg-indigo-600 hover:bg-indigo-700 text-white`;
      default: return `${baseClass} bg-gray-600 hover:bg-gray-700 text-white`;
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-8 w-8 animate-spin text-blue-600" />
        <span className="ml-2 text-gray-600">Loading inventory...</span>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Inventory Management</h2>
          <p className="text-gray-600">Manage stock levels, transfers, and reorders</p>
        </div>
        <button
          onClick={loadInventoryData}
          disabled={isLoading}
          className="flex items-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
        >
          <RefreshCw className={`h-4 w-4 ${isLoading ? 'animate-spin' : ''}`} />
          <span>Refresh</span>
        </button>
      </div>

      {/* Controls */}
      <div className="flex flex-col md:flex-row gap-4 mb-6">
        {/* Search */}
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="Search items, SKU, or category..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        {/* Filter */}
        <select
          value={filterStatus}
          onChange={(e) => setFilterStatus(e.target.value)}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="all">All Status</option>
          <option value="in_stock">In Stock</option>
          <option value="low_stock">Low Stock</option>
          <option value="out_of_stock">Out of Stock</option>
          <option value="overstocked">Overstocked</option>
        </select>

        {/* Sort */}
        <select
          value={`${sortBy}_${sortOrder}`}
          onChange={(e) => {
            const [field, order] = e.target.value.split('_');
            setSortBy(field);
            setSortOrder(order as 'asc' | 'desc');
          }}
          className="px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          <option value="name_asc">Name A-Z</option>
          <option value="name_desc">Name Z-A</option>
          <option value="current_stock_asc">Stock Low-High</option>
          <option value="current_stock_desc">Stock High-Low</option>
          <option value="category_asc">Category A-Z</option>
        </select>
      </div>

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3 mb-6">
        {inventoryActions.map((action) => (
          <button
            key={action.id}
            onClick={() => {
              if (action.id === 'export_data') {
                handleExportData();
              } else if (action.id === 'audit_inventory') {
                handleBulkAction('audit');
              } else {
                // Handle other actions
                console.log(`Action: ${action.id}`);
              }
            }}
            disabled={actionLoading === action.id}
            className={getActionButtonClass(action)}
          >
            {actionLoading === action.id ? (
              <RefreshCw className="h-4 w-4 animate-spin" />
            ) : (
              <action.icon className="h-4 w-4" />
            )}
            <span>{action.label}</span>
          </button>
        ))}
      </div>

      {/* Bulk Actions */}
      {selectedItems.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <span className="text-blue-800 font-medium">
              {selectedItems.length} item(s) selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkAction('adjust_stock')}
                className="px-3 py-1 bg-blue-600 text-white rounded text-sm hover:bg-blue-700"
              >
                Bulk Adjust
              </button>
              <button
                onClick={() => handleBulkAction('set_alerts')}
                className="px-3 py-1 bg-orange-600 text-white rounded text-sm hover:bg-orange-700"
              >
                Set Alerts
              </button>
              <button
                onClick={() => setSelectedItems([])}
                className="px-3 py-1 bg-gray-600 text-white rounded text-sm hover:bg-gray-700"
              >
                Clear Selection
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center">
            <AlertTriangle className="h-5 w-5 text-red-600 mr-2" />
            <span className="text-red-800">{error}</span>
          </div>
          <button
            onClick={() => setError(null)}
            className="mt-2 text-red-600 hover:text-red-800 font-medium text-sm"
          >
            Dismiss
          </button>
        </div>
      )}

      {/* Inventory Table */}
      <div className="overflow-x-auto">
        <table className="w-full border-collapse border border-gray-200">
          <thead>
            <tr className="bg-gray-50">
              <th className="border border-gray-200 px-4 py-3 text-left">
                <input
                  type="checkbox"
                  checked={selectedItems.length === filteredInventory.length && filteredInventory.length > 0}
                  onChange={(e) => {
                    if (e.target.checked) {
                      setSelectedItems(filteredInventory.map(item => item.id));
                    } else {
                      setSelectedItems([]);
                    }
                  }}
                  className="rounded"
                />
              </th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Item</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">SKU</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Category</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Stock</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Status</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Cost</th>
              <th className="border border-gray-200 px-4 py-3 text-left font-medium text-gray-900">Actions</th>
            </tr>
          </thead>
          <tbody>
            {filteredInventory.map((item) => (
              <tr key={item.id} className="hover:bg-gray-50">
                <td className="border border-gray-200 px-4 py-3">
                  <input
                    type="checkbox"
                    checked={selectedItems.includes(item.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedItems(prev => [...prev, item.id]);
                      } else {
                        setSelectedItems(prev => prev.filter(id => id !== item.id));
                      }
                    }}
                    className="rounded"
                  />
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="flex items-center space-x-3">
                    {getStockIcon(item)}
                    <div>
                      <div className="font-medium text-gray-900">{item.name}</div>
                      <div className="text-sm text-gray-500">{item.supplier}</div>
                    </div>
                  </div>
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">{item.sku}</td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">{item.category}</td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="text-sm">
                    <div className="font-medium">{item.current_stock}</div>
                    <div className="text-gray-500">Min: {item.minimum_stock}</div>
                  </div>
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                    {item.status.replace('_', ' ').toUpperCase()}
                  </span>
                </td>
                <td className="border border-gray-200 px-4 py-3 text-sm text-gray-900">
                  ${item.unit_cost.toFixed(2)}
                </td>
                <td className="border border-gray-200 px-4 py-3">
                  <div className="flex space-x-2">
                    <button
                      onClick={() => setShowAdjustModal(item.id)}
                      className="text-blue-600 hover:text-blue-800"
                      title="Adjust Stock"
                    >
                      <Edit3 className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => console.log('Transfer', item.id)}
                      className="text-purple-600 hover:text-purple-800"
                      title="Transfer Stock"
                    >
                      <Truck className="h-4 w-4" />
                    </button>
                    <button
                      onClick={() => console.log('Reorder', item.id)}
                      className="text-green-600 hover:text-green-800"
                      title="Reorder"
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {filteredInventory.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <Package className="h-12 w-12 mx-auto mb-3 text-gray-400" />
          <p>No inventory items found</p>
        </div>
      )}
    </div>
  );
};
