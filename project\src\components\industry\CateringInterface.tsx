import React, { useState, useEffect } from 'react';
import { 
  Calendar, 
  Users, 
  MapPin, 
  Clock, 
  DollarSign,
  Truck,
  CheckCircle,
  AlertCircle,
  FileText,
  Phone,
  Mail,
  Settings,
  Package,
  Utensils,
  Star,
  Timer,
  ClipboardList
} from 'lucide-react';

interface CateringEvent {
  id: string;
  eventName: string;
  clientName: string;
  clientEmail: string;
  clientPhone: string;
  eventDate: Date;
  eventTime: string;
  location: string;
  guestCount: number;
  eventType: 'corporate' | 'wedding' | 'birthday' | 'conference' | 'other';
  status: 'inquiry' | 'quoted' | 'confirmed' | 'in-progress' | 'completed' | 'cancelled';
  totalAmount: number;
  depositPaid: number;
  menu: CateringMenuItem[];
  specialRequests: string;
  equipmentNeeded: string[];
  staffRequired: number;
  setupTime: string;
  deliveryAddress: string;
}

interface CateringMenuItem {
  id: string;
  name: string;
  description: string;
  category: 'appetizers' | 'mains' | 'sides' | 'desserts' | 'beverages' | 'packages';
  pricePerPerson: number;
  minimumOrder: number;
  servingSize: string;
  dietaryInfo: string[];
  preparationTime: number;
  isPackage: boolean;
  packageItems?: string[];
}

interface Equipment {
  id: string;
  name: string;
  category: 'serving' | 'cooking' | 'setup' | 'transport';
  quantity: number;
  available: number;
  dailyRate: number;
}

interface Staff {
  id: string;
  name: string;
  role: 'chef' | 'server' | 'coordinator' | 'driver';
  hourlyRate: number;
  availability: Date[];
  skills: string[];
}

const CateringInterface: React.FC = () => {
  const [activeEvents, setActiveEvents] = useState<CateringEvent[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<CateringEvent | null>(null);
  const [currentView, setCurrentView] = useState<'events' | 'menu' | 'equipment' | 'timeline'>('events');
  const [selectedCategory, setSelectedCategory] = useState<string>('packages');

  // Mock catering menu
  const cateringMenu: Record<string, CateringMenuItem[]> = {
    packages: [
      {
        id: '1',
        name: 'Executive Lunch Package',
        description: 'Complete lunch solution for corporate events',
        category: 'packages',
        pricePerPerson: 28.50,
        minimumOrder: 20,
        servingSize: 'Per person',
        dietaryInfo: ['Vegetarian options', 'Gluten-free available'],
        preparationTime: 120,
        isPackage: true,
        packageItems: ['Choice of 3 entrees', 'Garden salad', 'Rolls & butter', 'Dessert', 'Beverages']
      },
      {
        id: '2',
        name: 'Wedding Reception Package',
        description: 'Elegant dining experience for your special day',
        category: 'packages',
        pricePerPerson: 65.00,
        minimumOrder: 50,
        servingSize: 'Per person',
        dietaryInfo: ['Customizable', 'Dietary restrictions accommodated'],
        preparationTime: 240,
        isPackage: true,
        packageItems: ['Cocktail hour appetizers', 'Choice of 2 entrees', 'Sides', 'Wedding cake', 'Open bar']
      }
    ],
    appetizers: [
      {
        id: '3',
        name: 'Artisan Cheese Board',
        description: 'Selection of local and imported cheeses',
        category: 'appetizers',
        pricePerPerson: 8.50,
        minimumOrder: 10,
        servingSize: '4 oz per person',
        dietaryInfo: ['Vegetarian', 'Gluten-free crackers available'],
        preparationTime: 30,
        isPackage: false
      },
      {
        id: '4',
        name: 'Shrimp Cocktail',
        description: 'Jumbo shrimp with cocktail sauce',
        category: 'appetizers',
        pricePerPerson: 12.00,
        minimumOrder: 15,
        servingSize: '3 pieces per person',
        dietaryInfo: ['Gluten-free'],
        preparationTime: 45,
        isPackage: false
      }
    ],
    mains: [
      {
        id: '5',
        name: 'Grilled Salmon',
        description: 'Atlantic salmon with lemon herb butter',
        category: 'mains',
        pricePerPerson: 24.00,
        minimumOrder: 20,
        servingSize: '6 oz portion',
        dietaryInfo: ['Gluten-free', 'Dairy-free option'],
        preparationTime: 90,
        isPackage: false
      },
      {
        id: '6',
        name: 'Beef Tenderloin',
        description: 'Prime beef with red wine reduction',
        category: 'mains',
        pricePerPerson: 32.00,
        minimumOrder: 25,
        servingSize: '8 oz portion',
        dietaryInfo: ['Gluten-free'],
        preparationTime: 120,
        isPackage: false
      }
    ]
  };

  // Mock events
  const mockEvents: CateringEvent[] = [
    {
      id: '1',
      eventName: 'Tech Corp Annual Meeting',
      clientName: 'Sarah Johnson',
      clientEmail: '<EMAIL>',
      clientPhone: '(*************',
      eventDate: new Date('2024-02-15'),
      eventTime: '12:00 PM',
      location: 'Downtown Conference Center',
      guestCount: 85,
      eventType: 'corporate',
      status: 'confirmed',
      totalAmount: 2422.50,
      depositPaid: 1211.25,
      menu: [cateringMenu.packages[0]],
      specialRequests: 'Vegetarian options for 15 guests, no nuts',
      equipmentNeeded: ['Chafing dishes', 'Serving utensils', 'Linens'],
      staffRequired: 4,
      setupTime: '10:00 AM',
      deliveryAddress: '123 Business Plaza, Suite 500'
    },
    {
      id: '2',
      eventName: 'Smith-Wilson Wedding',
      clientName: 'Emily Smith',
      clientEmail: '<EMAIL>',
      clientPhone: '(*************',
      eventDate: new Date('2024-03-22'),
      eventTime: '6:00 PM',
      location: 'Riverside Gardens',
      guestCount: 120,
      eventType: 'wedding',
      status: 'in-progress',
      totalAmount: 7800.00,
      depositPaid: 3900.00,
      menu: [cateringMenu.packages[1]],
      specialRequests: 'Gluten-free cake, kosher meal options',
      equipmentNeeded: ['Tables', 'Chairs', 'Sound system', 'Lighting'],
      staffRequired: 8,
      setupTime: '2:00 PM',
      deliveryAddress: '456 Garden Lane, Riverside'
    }
  ];

  useEffect(() => {
    setActiveEvents(mockEvents);
    setSelectedEvent(mockEvents[0]);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'inquiry': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'quoted': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'confirmed': return 'bg-green-100 text-green-800 border-green-200';
      case 'in-progress': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'completed': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'cancelled': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getEventTypeIcon = (type: string) => {
    switch (type) {
      case 'corporate': return '🏢';
      case 'wedding': return '💒';
      case 'birthday': return '🎂';
      case 'conference': return '📊';
      default: return '🎉';
    }
  };

  const renderEventsView = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Events List */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-6">
            <div className="flex items-center justify-between mb-4">
              <h2 className="text-xl font-bold text-gray-900">Upcoming Events</h2>
              <button className="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg font-medium transition-all duration-200">
                New Event
              </button>
            </div>
            
            <div className="space-y-4">
              {activeEvents.map((event) => (
                <div
                  key={event.id}
                  onClick={() => setSelectedEvent(event)}
                  className={`p-4 border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                    selectedEvent?.id === event.id
                      ? 'border-indigo-500 bg-indigo-50'
                      : 'border-gray-200 hover:border-indigo-300 hover:shadow-md'
                  }`}
                >
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{getEventTypeIcon(event.eventType)}</span>
                      <div>
                        <h3 className="font-semibold text-gray-900">{event.eventName}</h3>
                        <p className="text-sm text-gray-600">{event.clientName}</p>
                      </div>
                    </div>
                    <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getStatusColor(event.status)}`}>
                      {event.status.replace('-', ' ')}
                    </span>
                  </div>
                  
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                    <div className="flex items-center space-x-2">
                      <Calendar className="w-4 h-4 text-gray-400" />
                      <span>{event.eventDate.toLocaleDateString()}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Users className="w-4 h-4 text-gray-400" />
                      <span>{event.guestCount} guests</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <MapPin className="w-4 h-4 text-gray-400" />
                      <span>{event.location}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <DollarSign className="w-4 h-4 text-gray-400" />
                      <span>${event.totalAmount.toLocaleString()}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Event Details */}
        <div className="lg:col-span-1">
          {selectedEvent && (
            <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Event Details</h3>
              
              <div className="space-y-4">
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">{selectedEvent.eventName}</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center space-x-2">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <span>{selectedEvent.clientPhone}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Mail className="w-4 h-4 text-gray-400" />
                      <span>{selectedEvent.clientEmail}</span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Event Info</h4>
                  <div className="space-y-2 text-sm">
                    <div>Date: {selectedEvent.eventDate.toLocaleDateString()}</div>
                    <div>Time: {selectedEvent.eventTime}</div>
                    <div>Guests: {selectedEvent.guestCount}</div>
                    <div>Staff: {selectedEvent.staffRequired}</div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Financial</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total:</span>
                      <span className="font-medium">${selectedEvent.totalAmount.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Deposit:</span>
                      <span className="font-medium text-green-600">${selectedEvent.depositPaid.toLocaleString()}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Balance:</span>
                      <span className="font-medium text-orange-600">
                        ${(selectedEvent.totalAmount - selectedEvent.depositPaid).toLocaleString()}
                      </span>
                    </div>
                  </div>
                </div>
                
                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Special Requests</h4>
                  <p className="text-sm text-gray-600">{selectedEvent.specialRequests}</p>
                </div>
                
                <div className="space-y-2">
                  <button className="w-full bg-indigo-600 hover:bg-indigo-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200">
                    Edit Event
                  </button>
                  <button className="w-full bg-green-600 hover:bg-green-700 text-white py-2 px-4 rounded-lg font-medium transition-all duration-200">
                    Generate Invoice
                  </button>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );

  const renderMenuView = () => (
    <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-6">
      <div className="flex items-center space-x-3 mb-4">
        <Utensils className="w-6 h-6 text-indigo-600" />
        <h2 className="text-xl font-bold text-gray-900">Catering Menu</h2>
      </div>
      
      {/* Category Tabs */}
      <div className="flex space-x-2 mb-6 overflow-x-auto">
        {Object.keys(cateringMenu).map((category) => (
          <button
            key={category}
            onClick={() => setSelectedCategory(category)}
            className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 whitespace-nowrap ${
              selectedCategory === category
                ? 'bg-indigo-600 text-white shadow-lg'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
            }`}
          >
            {category.charAt(0).toUpperCase() + category.slice(1)}
          </button>
        ))}
      </div>
      
      {/* Menu Items */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {cateringMenu[selectedCategory]?.map((item) => (
          <div key={item.id} className="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 hover:shadow-md transition-all duration-200">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900">{item.name}</h3>
                <p className="text-sm text-gray-600 mt-1">{item.description}</p>
              </div>
              {item.isPackage && (
                <span className="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">
                  Package
                </span>
              )}
            </div>
            
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span>Price per person:</span>
                <span className="font-bold text-indigo-600">${item.pricePerPerson}</span>
              </div>
              <div className="flex justify-between">
                <span>Minimum order:</span>
                <span>{item.minimumOrder} people</span>
              </div>
              <div className="flex justify-between">
                <span>Prep time:</span>
                <span>{item.preparationTime} min</span>
              </div>
            </div>
            
            <div className="mt-3">
              <div className="text-xs text-gray-500 mb-2">Dietary Info:</div>
              <div className="flex flex-wrap gap-1">
                {item.dietaryInfo.map((info, index) => (
                  <span key={index} className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">
                    {info}
                  </span>
                ))}
              </div>
            </div>
            
            {item.packageItems && (
              <div className="mt-3">
                <div className="text-xs text-gray-500 mb-2">Package includes:</div>
                <ul className="text-xs text-gray-600 space-y-1">
                  {item.packageItems.map((packageItem, index) => (
                    <li key={index} className="flex items-center space-x-1">
                      <CheckCircle className="w-3 h-3 text-green-500" />
                      <span>{packageItem}</span>
                    </li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );

  const renderTimelineView = () => (
    <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-6">
      <div className="flex items-center space-x-3 mb-4">
        <Timer className="w-6 h-6 text-indigo-600" />
        <h2 className="text-xl font-bold text-gray-900">Event Timeline</h2>
      </div>
      
      {selectedEvent && (
        <div className="space-y-6">
          <div className="bg-indigo-50 rounded-lg p-4">
            <h3 className="font-semibold text-indigo-900 mb-2">{selectedEvent.eventName}</h3>
            <p className="text-indigo-700">{selectedEvent.eventDate.toLocaleDateString()} at {selectedEvent.eventTime}</p>
          </div>
          
          <div className="space-y-4">
            {[
              { time: '2 days before', task: 'Final headcount confirmation', status: 'pending' },
              { time: '1 day before', task: 'Prep ingredients and equipment', status: 'pending' },
              { time: selectedEvent.setupTime, task: 'Setup and preparation', status: 'pending' },
              { time: selectedEvent.eventTime, task: 'Event service begins', status: 'pending' },
              { time: '2 hours after', task: 'Cleanup and breakdown', status: 'pending' }
            ].map((item, index) => (
              <div key={index} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg">
                <div className="flex-shrink-0">
                  <div className={`w-4 h-4 rounded-full ${
                    item.status === 'completed' ? 'bg-green-500' :
                    item.status === 'in-progress' ? 'bg-yellow-500' :
                    'bg-gray-300'
                  }`}></div>
                </div>
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{item.task}</div>
                  <div className="text-sm text-gray-600">{item.time}</div>
                </div>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  item.status === 'completed' ? 'bg-green-100 text-green-800' :
                  item.status === 'in-progress' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-gray-100 text-gray-800'
                }`}>
                  {item.status}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );

  const views = [
    { id: 'events', label: 'Events', icon: Calendar },
    { id: 'menu', label: 'Menu', icon: Utensils },
    { id: 'equipment', label: 'Equipment', icon: Package },
    { id: 'timeline', label: 'Timeline', icon: Timer }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-indigo-50 via-blue-50 to-purple-50 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-indigo-700 to-blue-600 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Calendar className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Premier Catering</h1>
              <p className="text-indigo-100">Event planning & culinary excellence</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{activeEvents.length}</div>
              <div className="text-indigo-200">Active Events</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">
                ${activeEvents.reduce((sum, event) => sum + event.totalAmount, 0).toLocaleString()}
              </div>
              <div className="text-indigo-200">Total Revenue</div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-2 mb-6">
        <div className="flex space-x-2">
          {views.map((view) => {
            const Icon = view.icon;
            return (
              <button
                key={view.id}
                onClick={() => setCurrentView(view.id as any)}
                className={`flex items-center space-x-2 px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                  currentView === view.id
                    ? 'bg-indigo-600 text-white shadow-lg'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{view.label}</span>
              </button>
            );
          })}
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1">
        {currentView === 'events' && renderEventsView()}
        {currentView === 'menu' && renderMenuView()}
        {currentView === 'timeline' && renderTimelineView()}
        {currentView === 'equipment' && (
          <div className="bg-white rounded-2xl shadow-lg border border-indigo-200 p-6">
            <h2 className="text-xl font-bold text-gray-900 mb-4">Equipment Management</h2>
            <p className="text-gray-600">Equipment tracking and rental management coming soon...</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default CateringInterface;
