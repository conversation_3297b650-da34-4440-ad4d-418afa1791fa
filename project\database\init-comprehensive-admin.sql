-- Comprehensive Super Admin Database Schema
-- Database: BARPOS, User: <PERSON><PERSON><PERSON>, Password: Chaand@0319

-- Create database if not exists (run as superuser)
-- CREATE DATABASE "BARPOS" OWNER "BARPOS";

-- Connect to BARPOS database
\c BARPOS;

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Tenants table (Phase 1)
CREATE TABLE IF NOT EXISTS tenants (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    plan VARCHAR(20) DEFAULT 'basic' CHECK (plan IN ('basic', 'pro', 'enterprise')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    locations INTEGER DEFAULT 1,
    settings JSONB DEFAULT '{}',
    features TEXT[] DEFAULT ARRAY['basic_pos']
);

-- Users table (Phase 1)
CREATE TABLE IF NOT EXISTS users (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255),
    role VARCHAR(50) DEFAULT 'employee' CHECK (role IN ('super_admin', 'tenant_admin', 'manager', 'employee')),
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login TIMESTAMP,
    permissions TEXT[] DEFAULT ARRAY['pos_access'],
    settings JSONB DEFAULT '{}'
);

-- Transactions table (Phase 1)
CREATE TABLE IF NOT EXISTS transactions (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id),
    amount DECIMAL(10,2) NOT NULL,
    type VARCHAR(50) DEFAULT 'sale',
    status VARCHAR(20) DEFAULT 'completed',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metadata JSONB DEFAULT '{}'
);

-- Security audits table (Phase 2)
CREATE TABLE IF NOT EXISTS security_audits (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    event VARCHAR(255) NOT NULL,
    severity VARCHAR(20) DEFAULT 'low' CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    ip_address INET,
    user_agent TEXT,
    details TEXT,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'investigating', 'resolved')),
    metadata JSONB DEFAULT '{}'
);

-- System activity table (All Phases)
CREATE TABLE IF NOT EXISTS system_activity (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    user_id INTEGER REFERENCES users(id),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    action VARCHAR(255) NOT NULL,
    type VARCHAR(20) DEFAULT 'info' CHECK (type IN ('success', 'warning', 'error', 'info')),
    details TEXT,
    metadata JSONB DEFAULT '{}'
);

-- System metrics table (Phase 2)
CREATE TABLE IF NOT EXISTS system_metrics (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    metric_name VARCHAR(100) NOT NULL,
    metric_value DECIMAL(15,4),
    metric_unit VARCHAR(20),
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    metadata JSONB DEFAULT '{}'
);

-- AI analytics table (Phase 3)
CREATE TABLE IF NOT EXISTS ai_analytics (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    analysis_type VARCHAR(100) NOT NULL,
    predictions JSONB DEFAULT '{}',
    confidence_score DECIMAL(5,2),
    recommendations JSONB DEFAULT '{}',
    metadata JSONB DEFAULT '{}'
);

-- Backup logs table (Phase 2)
CREATE TABLE IF NOT EXISTS backup_logs (
    id SERIAL PRIMARY KEY,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    backup_type VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'running', 'completed', 'failed')),
    file_path TEXT,
    file_size BIGINT,
    duration_seconds INTEGER,
    error_message TEXT,
    metadata JSONB DEFAULT '{}'
);

-- API usage logs table (Phase 2)
CREATE TABLE IF NOT EXISTS api_usage_logs (
    id SERIAL PRIMARY KEY,
    tenant_id INTEGER REFERENCES tenants(id) ON DELETE CASCADE,
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    endpoint VARCHAR(255) NOT NULL,
    method VARCHAR(10) NOT NULL,
    status_code INTEGER,
    response_time_ms INTEGER,
    ip_address INET,
    user_agent TEXT,
    api_key_id VARCHAR(100),
    metadata JSONB DEFAULT '{}'
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_tenants_status ON tenants(status);
CREATE INDEX IF NOT EXISTS idx_tenants_plan ON tenants(plan);
CREATE INDEX IF NOT EXISTS idx_users_tenant_id ON users(tenant_id);
CREATE INDEX IF NOT EXISTS idx_users_role ON users(role);
CREATE INDEX IF NOT EXISTS idx_users_status ON users(status);
CREATE INDEX IF NOT EXISTS idx_transactions_tenant_id ON transactions(tenant_id);
CREATE INDEX IF NOT EXISTS idx_transactions_created_at ON transactions(created_at);
CREATE INDEX IF NOT EXISTS idx_security_audits_timestamp ON security_audits(timestamp);
CREATE INDEX IF NOT EXISTS idx_security_audits_severity ON security_audits(severity);
CREATE INDEX IF NOT EXISTS idx_system_activity_timestamp ON system_activity(timestamp);
CREATE INDEX IF NOT EXISTS idx_system_metrics_timestamp ON system_metrics(timestamp);
CREATE INDEX IF NOT EXISTS idx_api_usage_timestamp ON api_usage_logs(timestamp);

-- Insert sample data for testing

-- Sample tenants
INSERT INTO tenants (name, slug, status, plan, locations, features) VALUES
('Coffee Shop Pro', 'coffee-shop-pro', 'active', 'pro', 2, ARRAY['basic_pos', 'advanced_analytics', 'api_access']),
('Restaurant Chain', 'restaurant-chain', 'active', 'enterprise', 5, ARRAY['basic_pos', 'advanced_analytics', 'api_access', 'ai_features', 'multi_location']),
('Bakery Delights', 'bakery-delights', 'active', 'basic', 1, ARRAY['basic_pos']),
('Fast Food Corner', 'fast-food-corner', 'inactive', 'pro', 3, ARRAY['basic_pos', 'advanced_analytics']),
('Pizza Palace', 'pizza-palace', 'active', 'enterprise', 4, ARRAY['basic_pos', 'advanced_analytics', 'api_access', 'ai_features'])
ON CONFLICT (slug) DO NOTHING;

-- Sample users
INSERT INTO users (tenant_id, name, email, role, status, permissions) VALUES
(NULL, 'Super Administrator', '<EMAIL>', 'super_admin', 'active', ARRAY['all']),
(1, 'John Manager', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'user_management', 'pos_access']),
(1, 'Sarah Barista', '<EMAIL>', 'employee', 'active', ARRAY['pos_access']),
(2, 'Mike Owner', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'user_management', 'pos_access']),
(2, 'Lisa Manager', '<EMAIL>', 'manager', 'active', ARRAY['user_management', 'pos_access']),
(3, 'Tom Baker', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'pos_access']),
(4, 'Anna Fast', '<EMAIL>', 'tenant_admin', 'inactive', ARRAY['tenant_management', 'pos_access']),
(5, 'Carlos Pizza', '<EMAIL>', 'tenant_admin', 'active', ARRAY['tenant_management', 'user_management', 'pos_access'])
ON CONFLICT (email) DO NOTHING;

-- Sample transactions
INSERT INTO transactions (tenant_id, user_id, amount, type, status) VALUES
(1, 2, 15.50, 'sale', 'completed'),
(1, 3, 8.75, 'sale', 'completed'),
(1, 2, 22.00, 'sale', 'completed'),
(2, 4, 45.25, 'sale', 'completed'),
(2, 5, 67.80, 'sale', 'completed'),
(2, 4, 33.50, 'sale', 'completed'),
(3, 6, 12.25, 'sale', 'completed'),
(3, 6, 18.90, 'sale', 'completed'),
(5, 8, 28.75, 'sale', 'completed'),
(5, 8, 41.20, 'sale', 'completed');

-- Sample security audits
INSERT INTO security_audits (tenant_id, user_id, event, severity, ip_address, details, status) VALUES
(NULL, 1, 'Super admin login', 'low', '*************', 'Successful super admin authentication', 'resolved'),
(1, 2, 'Failed login attempt', 'medium', '*************', 'Multiple failed login attempts detected', 'investigating'),
(2, 4, 'Password changed', 'low', '*************', 'User password successfully updated', 'resolved'),
(NULL, NULL, 'Suspicious API access', 'high', '*********', 'Unusual API access pattern detected', 'open'),
(3, 6, 'Account locked', 'medium', '************', 'Account locked due to failed attempts', 'resolved');

-- Sample system activity
INSERT INTO system_activity (tenant_id, user_id, action, type, details) VALUES
(NULL, 1, 'System backup completed', 'success', 'Daily automated backup completed successfully'),
(1, 2, 'New user created', 'info', 'New employee account created for Coffee Shop Pro'),
(2, 4, 'Menu updated', 'info', 'Restaurant menu items updated'),
(NULL, NULL, 'Database maintenance', 'info', 'Routine database optimization completed'),
(5, 8, 'Payment processed', 'success', 'Customer payment processed successfully');

-- Sample system metrics
INSERT INTO system_metrics (metric_name, metric_value, metric_unit, tenant_id) VALUES
('cpu_usage', 45.2, 'percent', NULL),
('memory_usage', 67.8, 'percent', NULL),
('disk_usage', 78.5, 'percent', NULL),
('response_time', 125.3, 'milliseconds', NULL),
('active_connections', 15, 'count', NULL),
('daily_transactions', 156, 'count', 1),
('daily_revenue', 1250.75, 'dollars', 1),
('daily_transactions', 89, 'count', 2),
('daily_revenue', 2150.50, 'dollars', 2);

-- Sample AI analytics
INSERT INTO ai_analytics (tenant_id, analysis_type, predictions, confidence_score, recommendations) VALUES
(1, 'revenue_forecast', '{"next_month": 15000, "growth_rate": 12.5}', 87.5, '{"pricing": "increase_coffee_by_5_percent", "inventory": "stock_more_pastries"}'),
(2, 'customer_behavior', '{"churn_risk": 8.2, "lifetime_value": 1250}', 92.1, '{"retention": "loyalty_program", "upsell": "premium_menu_items"}'),
(5, 'inventory_optimization', '{"overstock": 12, "understock": 5}', 89.3, '{"reduce": ["sauce_packets"], "increase": ["pizza_dough"]}');

-- Update timestamps
UPDATE tenants SET last_login = CURRENT_TIMESTAMP - INTERVAL '2 hours' WHERE id IN (1, 2, 5);
UPDATE tenants SET last_login = CURRENT_TIMESTAMP - INTERVAL '1 day' WHERE id = 3;
UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '1 hour' WHERE id IN (1, 2, 4, 8);
UPDATE users SET last_login = CURRENT_TIMESTAMP - INTERVAL '3 hours' WHERE id IN (3, 5, 6);

-- Create views for easier querying
CREATE OR REPLACE VIEW tenant_summary AS
SELECT 
    t.id,
    t.name,
    t.slug,
    t.status,
    t.plan,
    t.locations,
    COUNT(u.id) as user_count,
    COALESCE(SUM(tr.amount), 0) as total_revenue,
    COUNT(tr.id) as transaction_count,
    t.created_at,
    t.last_login
FROM tenants t
LEFT JOIN users u ON t.id = u.tenant_id
LEFT JOIN transactions tr ON t.id = tr.tenant_id
GROUP BY t.id, t.name, t.slug, t.status, t.plan, t.locations, t.created_at, t.last_login;

CREATE OR REPLACE VIEW system_health AS
SELECT 
    COUNT(DISTINCT t.id) as total_tenants,
    COUNT(DISTINCT t.id) FILTER (WHERE t.status = 'active') as active_tenants,
    COUNT(DISTINCT u.id) as total_users,
    COUNT(DISTINCT u.id) FILTER (WHERE u.status = 'active') as active_users,
    COUNT(tr.id) as total_transactions,
    COALESCE(SUM(tr.amount), 0) as total_revenue,
    COUNT(sa.id) FILTER (WHERE sa.severity IN ('high', 'critical')) as critical_security_events
FROM tenants t
CROSS JOIN users u
CROSS JOIN transactions tr
CROSS JOIN security_audits sa;

-- Grant permissions
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO "BARPOS";
GRANT ALL PRIVILEGES ON ALL FUNCTIONS IN SCHEMA public TO "BARPOS";

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transactions_updated_at BEFORE UPDATE ON transactions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;

-- Display summary
SELECT 'Database initialization completed successfully!' as status;
SELECT * FROM tenant_summary;
SELECT * FROM system_health;
