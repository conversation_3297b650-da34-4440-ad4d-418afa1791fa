// Performance Metrics System Testing Script
// Tests comprehensive performance monitoring with real-time metrics collection and alerting

const fetch = require('node-fetch');

const SERVER_URL = 'http://localhost:4000';

// Test configuration
const TEST_CONFIG = {
  server_url: SERVER_URL,
  test_duration: 30000, // 30 seconds
  concurrent_requests: 5,
  request_interval: 1000, // 1 second between requests
  performance_thresholds: {
    max_response_time: 2000, // 2 seconds
    max_error_rate: 5, // 5%
    max_memory_usage: 90 // 90%
  }
};

// Test results storage
const testResults = {
  performance_stats: null,
  cache_stats: null,
  database_performance: null,
  monitoring_stats: null,
  alerts: null,
  health_check: null,
  load_test_results: null
};

// Authentication token for API calls
let authToken = null;

console.log('🚀 Starting Performance Metrics System Testing...\n');

// Helper function to authenticate
async function authenticate() {
  try {
    const response = await fetch(`${SERVER_URL}/api/auth/login`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        pin: '123456',
        tenant_slug: 'barpos-system'
      })
    });

    if (response.ok) {
      const result = await response.json();
      authToken = result.token;
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.error('❌ Authentication failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    return false;
  }
}

// Test 1: Performance Statistics Endpoint
async function testPerformanceStats() {
  console.log('\n📊 Testing Performance Statistics Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/performance/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.performance_stats = result;
      
      console.log('✅ Performance stats retrieved successfully');
      console.log(`   📈 Total Requests: ${result.performance_stats?.total_requests || 'N/A'}`);
      console.log(`   ⏱️  Average Response Time: ${result.performance_stats?.avg_response_time || 'N/A'}ms`);
      console.log(`   🐌 Slow Requests: ${result.performance_stats?.slow_request_percent || 'N/A'}`);
      console.log(`   ❌ Error Rate: ${result.performance_stats?.error_rate || 'N/A'}`);
      console.log(`   🔄 Requests/Second: ${result.performance_stats?.requests_per_second?.toFixed(2) || 'N/A'}`);
      
      return true;
    } else {
      console.error('❌ Performance stats test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Performance stats error:', error.message);
    return false;
  }
}

// Test 2: Cache Performance Endpoint
async function testCachePerformance() {
  console.log('\n💾 Testing Cache Performance Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/performance/cache`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.cache_stats = result;
      
      console.log('✅ Cache performance retrieved successfully');
      console.log(`   📊 Cache Hit Rate: ${result.cache_stats?.hit_rate || 'N/A'}`);
      console.log(`   🔢 Total Operations: ${result.cache_stats?.total_operations || 'N/A'}`);
      console.log(`   💾 Memory Usage: ${result.cache_stats?.memory_usage || 'N/A'}`);
      
      return true;
    } else {
      console.error('❌ Cache performance test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Cache performance error:', error.message);
    return false;
  }
}

// Test 3: Database Performance Endpoint
async function testDatabasePerformance() {
  console.log('\n🗄️  Testing Database Performance Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/performance/database`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.database_performance = result;
      
      console.log('✅ Database performance retrieved successfully');
      console.log(`   🔌 Connection Pool: ${result.database_performance?.connection_pool?.total_connections || 'N/A'}/${result.database_performance?.connection_pool?.max_connections || 'N/A'}`);
      console.log(`   ⏱️  Average Query Time: ${result.database_performance?.query_analysis?.avg_query_time || 'N/A'}ms`);
      console.log(`   🐌 Slow Queries: ${result.database_performance?.query_analysis?.slow_queries || 'N/A'}`);
      
      return true;
    } else {
      console.error('❌ Database performance test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Database performance error:', error.message);
    return false;
  }
}

// Test 4: Monitoring Statistics Endpoint
async function testMonitoringStats() {
  console.log('\n📈 Testing Monitoring Statistics Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/monitoring/stats`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.monitoring_stats = result;
      
      console.log('✅ Monitoring stats retrieved successfully');
      console.log(`   ⏰ Uptime: ${result.monitoring?.system?.uptime || 'N/A'} seconds`);
      console.log(`   📊 Requests (Last Hour): ${result.monitoring?.system?.requests_last_hour || 'N/A'}`);
      console.log(`   ❌ Error Rate: ${result.monitoring?.system?.error_rate || 'N/A'}`);
      console.log(`   ⚠️  Active Alerts: ${result.monitoring?.system?.active_alerts || 'N/A'}`);
      
      return true;
    } else {
      console.error('❌ Monitoring stats test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Monitoring stats error:', error.message);
    return false;
  }
}

// Test 5: Alerts Endpoint
async function testAlertsEndpoint() {
  console.log('\n🚨 Testing Alerts Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/monitoring/alerts`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.alerts = result;
      
      console.log('✅ Alerts retrieved successfully');
      console.log(`   📊 Total Alerts: ${result.alerts?.length || 0}`);
      
      if (result.alerts && result.alerts.length > 0) {
        console.log('   🔍 Recent Alerts:');
        result.alerts.slice(0, 3).forEach((alert, index) => {
          console.log(`      ${index + 1}. ${alert.type} - ${alert.timestamp}`);
        });
      } else {
        console.log('   ✅ No active alerts');
      }
      
      return true;
    } else {
      console.error('❌ Alerts test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Alerts error:', error.message);
    return false;
  }
}

// Test 6: Health Check Endpoint
async function testHealthCheck() {
  console.log('\n🏥 Testing Health Check Endpoint...');
  
  try {
    const response = await fetch(`${SERVER_URL}/api/health`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Content-Type': 'application/json'
      }
    });

    if (response.ok) {
      const result = await response.json();
      testResults.health_check = result;
      
      console.log('✅ Health check retrieved successfully');
      console.log(`   🏥 Overall Status: ${result.status || 'N/A'}`);
      console.log(`   📊 Performance Status: ${result.components?.performance?.status || 'N/A'}`);
      console.log(`   🗄️  Database Status: ${result.components?.database?.status || 'N/A'}`);
      console.log(`   📈 Monitoring Status: ${result.components?.monitoring?.status || 'N/A'}`);
      console.log(`   🔒 Security Status: ${result.components?.security?.cors_enabled ? 'Enabled' : 'Disabled'}`);
      
      return true;
    } else {
      console.error('❌ Health check test failed:', response.status);
      return false;
    }
  } catch (error) {
    console.error('❌ Health check error:', error.message);
    return false;
  }
}

// Test 7: Load Testing with Performance Monitoring
async function performLoadTest() {
  console.log('\n🔥 Performing Load Test with Performance Monitoring...');

  const loadTestResults = {
    total_requests: 0,
    successful_requests: 0,
    failed_requests: 0,
    response_times: [],
    start_time: Date.now(),
    end_time: null
  };

  try {
    const promises = [];
    const testEndpoints = [
      '/api/health/public',
      '/api/products',
      '/api/categories',
      '/api/employees'
    ];

    // Generate concurrent requests
    for (let i = 0; i < TEST_CONFIG.concurrent_requests; i++) {
      const promise = (async () => {
        const endTime = Date.now() + TEST_CONFIG.test_duration;

        while (Date.now() < endTime) {
          const endpoint = testEndpoints[Math.floor(Math.random() * testEndpoints.length)];
          const requestStart = Date.now();

          try {
            const headers = {
              'Content-Type': 'application/json'
            };

            // Add auth header for protected endpoints
            if (endpoint !== '/api/health/public') {
              headers['Authorization'] = `Bearer ${authToken}`;
            }

            const response = await fetch(`${SERVER_URL}${endpoint}`, {
              method: 'GET',
              headers
            });

            const responseTime = Date.now() - requestStart;
            loadTestResults.response_times.push(responseTime);
            loadTestResults.total_requests++;

            if (response.ok) {
              loadTestResults.successful_requests++;
            } else {
              loadTestResults.failed_requests++;
            }

          } catch (error) {
            loadTestResults.failed_requests++;
            loadTestResults.total_requests++;
          }

          // Wait before next request
          await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.request_interval));
        }
      })();

      promises.push(promise);
    }

    // Wait for all concurrent requests to complete
    await Promise.all(promises);

    loadTestResults.end_time = Date.now();
    testResults.load_test_results = loadTestResults;

    // Calculate statistics
    const avgResponseTime = loadTestResults.response_times.length > 0
      ? loadTestResults.response_times.reduce((a, b) => a + b, 0) / loadTestResults.response_times.length
      : 0;

    const maxResponseTime = Math.max(...loadTestResults.response_times);
    const minResponseTime = Math.min(...loadTestResults.response_times);
    const errorRate = (loadTestResults.failed_requests / loadTestResults.total_requests) * 100;

    console.log('✅ Load test completed');
    console.log(`   📊 Total Requests: ${loadTestResults.total_requests}`);
    console.log(`   ✅ Successful: ${loadTestResults.successful_requests}`);
    console.log(`   ❌ Failed: ${loadTestResults.failed_requests}`);
    console.log(`   📈 Error Rate: ${errorRate.toFixed(2)}%`);
    console.log(`   ⏱️  Avg Response Time: ${avgResponseTime.toFixed(2)}ms`);
    console.log(`   🚀 Min Response Time: ${minResponseTime}ms`);
    console.log(`   🐌 Max Response Time: ${maxResponseTime}ms`);

    return true;
  } catch (error) {
    console.error('❌ Load test error:', error.message);
    return false;
  }
}

// Main test execution
async function runPerformanceMetricsTests() {
  const testFunctions = [
    { name: 'Authentication', func: authenticate },
    { name: 'Performance Statistics', func: testPerformanceStats },
    { name: 'Cache Performance', func: testCachePerformance },
    { name: 'Database Performance', func: testDatabasePerformance },
    { name: 'Monitoring Statistics', func: testMonitoringStats },
    { name: 'Alerts Endpoint', func: testAlertsEndpoint },
    { name: 'Health Check', func: testHealthCheck },
    { name: 'Load Testing', func: performLoadTest }
  ];

  let passedTests = 0;
  const totalTests = testFunctions.length;

  for (const test of testFunctions) {
    try {
      const result = await test.func();
      if (result) {
        passedTests++;
      }
    } catch (error) {
      console.error(`❌ ${test.name} test failed:`, error.message);
    }
  }

  // Final results
  console.log('\n' + '='.repeat(60));
  console.log('📊 PERFORMANCE METRICS SYSTEM TEST RESULTS');
  console.log('='.repeat(60));
  console.log(`Tests Passed: ${passedTests}/${totalTests}`);
  console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`);

  if (passedTests === totalTests) {
    console.log('🎉 All performance monitoring systems are working correctly!');
  } else {
    console.log('⚠️  Some performance monitoring systems need attention.');
  }

  // Performance analysis
  if (testResults.load_test_results) {
    const loadTest = testResults.load_test_results;
    const avgResponseTime = loadTest.response_times.reduce((a, b) => a + b, 0) / loadTest.response_times.length;
    const errorRate = (loadTest.failed_requests / loadTest.total_requests) * 100;

    console.log('\n📈 PERFORMANCE ANALYSIS:');
    console.log(`   Response Time: ${avgResponseTime < TEST_CONFIG.performance_thresholds.max_response_time ? '✅' : '❌'} ${avgResponseTime.toFixed(2)}ms (threshold: ${TEST_CONFIG.performance_thresholds.max_response_time}ms)`);
    console.log(`   Error Rate: ${errorRate < TEST_CONFIG.performance_thresholds.max_error_rate ? '✅' : '❌'} ${errorRate.toFixed(2)}% (threshold: ${TEST_CONFIG.performance_thresholds.max_error_rate}%)`);
  }

  console.log('\n✅ Performance Metrics System testing completed!');
}

// Run the tests
runPerformanceMetricsTests().catch(console.error);
