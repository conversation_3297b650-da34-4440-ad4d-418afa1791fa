import React, { useState, useEffect } from 'react';
import { 
  Bell, 
  Mail, 
  MessageSquare, 
  Smartphone, 
  Send, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  Settings,
  Plus,
  Filter,
  Search,
  Eye,
  Edit,
  Trash2,
  Users,
  Zap
} from 'lucide-react';
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';

interface NotificationTemplate {
  id: number;
  template_name: string;
  template_type: 'email' | 'sms' | 'push' | 'webhook';
  category: string;
  subject_template: string;
  body_template: string;
  variables: string[];
  is_active: boolean;
  usage_count: number;
  created_at: string;
}

interface NotificationQueue {
  id: number;
  tenant_name: string;
  template_name: string;
  recipient_email: string;
  notification_type: string;
  priority: number;
  subject: string;
  status: 'pending' | 'sent' | 'failed' | 'cancelled';
  scheduled_at: string;
  sent_at?: string;
  error_message?: string;
  retry_count: number;
}

interface NotificationMetrics {
  total_sent: number;
  delivery_rate: number;
  open_rate: number;
  click_rate: number;
  failed_notifications: number;
  pending_notifications: number;
}

export function NotificationCenter() {
  const [templates, setTemplates] = useState<NotificationTemplate[]>([]);
  const [queue, setQueue] = useState<NotificationQueue[]>([]);
  const [metrics, setMetrics] = useState<NotificationMetrics | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<'overview' | 'templates' | 'queue' | 'analytics'>('overview');
  const [filterStatus, setFilterStatus] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    fetchNotificationData();
  }, []);

  const fetchNotificationData = async () => {
    try {
      setLoading(true);
      await new Promise(resolve => setTimeout(resolve, 800));

      // Mock notification templates
      const mockTemplates: NotificationTemplate[] = [
        {
          id: 1,
          template_name: 'Welcome Email',
          template_type: 'email',
          category: 'onboarding',
          subject_template: 'Welcome to {{tenant_name}}!',
          body_template: 'Welcome {{user_name}} to {{tenant_name}}. Your account is now active.',
          variables: ['tenant_name', 'user_name'],
          is_active: true,
          usage_count: 245,
          created_at: '2024-01-15T00:00:00Z'
        },
        {
          id: 2,
          template_name: 'Invoice Due Reminder',
          template_type: 'email',
          category: 'billing',
          subject_template: 'Invoice {{invoice_number}} Due Soon',
          body_template: 'Your invoice {{invoice_number}} for {{amount}} is due on {{due_date}}.',
          variables: ['invoice_number', 'amount', 'due_date'],
          is_active: true,
          usage_count: 89,
          created_at: '2024-01-20T00:00:00Z'
        },
        {
          id: 3,
          template_name: 'Security Alert',
          template_type: 'email',
          category: 'security',
          subject_template: 'Security Alert - {{alert_type}}',
          body_template: 'A security event has been detected: {{description}}',
          variables: ['alert_type', 'description', 'timestamp'],
          is_active: true,
          usage_count: 12,
          created_at: '2024-02-01T00:00:00Z'
        },
        {
          id: 4,
          template_name: 'Order Confirmation SMS',
          template_type: 'sms',
          category: 'orders',
          subject_template: '',
          body_template: 'Order #{{order_id}} confirmed. Total: {{total}}. ETA: {{eta}}',
          variables: ['order_id', 'total', 'eta'],
          is_active: true,
          usage_count: 1567,
          created_at: '2024-01-10T00:00:00Z'
        }
      ];

      // Mock notification queue
      const mockQueue: NotificationQueue[] = [
        {
          id: 1,
          tenant_name: 'Demo Restaurant',
          template_name: 'Invoice Due Reminder',
          recipient_email: '<EMAIL>',
          notification_type: 'billing',
          priority: 3,
          subject: 'Invoice INV-2024-001 Due Soon',
          status: 'sent',
          scheduled_at: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
          sent_at: new Date(Date.now() - 2 * 60 * 60 * 1000 + 5 * 60 * 1000).toISOString(),
          retry_count: 0
        },
        {
          id: 2,
          tenant_name: 'Pizza Palace',
          template_name: 'Welcome Email',
          recipient_email: '<EMAIL>',
          notification_type: 'onboarding',
          priority: 5,
          subject: 'Welcome to Pizza Palace!',
          status: 'pending',
          scheduled_at: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
          retry_count: 0
        },
        {
          id: 3,
          tenant_name: 'Burger Chain Inc',
          template_name: 'Security Alert',
          recipient_email: '<EMAIL>',
          notification_type: 'security',
          priority: 1,
          subject: 'Security Alert - Failed Login Attempts',
          status: 'failed',
          scheduled_at: new Date(Date.now() - 1 * 60 * 60 * 1000).toISOString(),
          error_message: 'SMTP server timeout',
          retry_count: 2
        }
      ];

      // Mock metrics
      const mockMetrics: NotificationMetrics = {
        total_sent: 15420,
        delivery_rate: 98.5,
        open_rate: 24.3,
        click_rate: 3.7,
        failed_notifications: 23,
        pending_notifications: 8
      };

      setTemplates(mockTemplates);
      setQueue(mockQueue);
      setMetrics(mockMetrics);
    } catch (error) {
      console.error('Error fetching notification data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent': return 'text-green-700 bg-green-100';
      case 'pending': return 'text-yellow-700 bg-yellow-100';
      case 'failed': return 'text-red-700 bg-red-100';
      case 'cancelled': return 'text-gray-700 bg-gray-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent': return <CheckCircle className="h-4 w-4" />;
      case 'pending': return <Clock className="h-4 w-4" />;
      case 'failed': return <XCircle className="h-4 w-4" />;
      case 'cancelled': return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'email': return <Mail className="h-5 w-5" />;
      case 'sms': return <MessageSquare className="h-5 w-5" />;
      case 'push': return <Smartphone className="h-5 w-5" />;
      case 'webhook': return <Zap className="h-5 w-5" />;
      default: return <Bell className="h-5 w-5" />;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'billing': return 'text-blue-700 bg-blue-100';
      case 'security': return 'text-red-700 bg-red-100';
      case 'onboarding': return 'text-green-700 bg-green-100';
      case 'orders': return 'text-purple-700 bg-purple-100';
      case 'marketing': return 'text-orange-700 bg-orange-100';
      default: return 'text-gray-700 bg-gray-100';
    }
  };

  const getPriorityColor = (priority: number) => {
    if (priority <= 2) return 'text-red-700 bg-red-100';
    if (priority <= 4) return 'text-yellow-700 bg-yellow-100';
    return 'text-green-700 bg-green-100';
  };

  const getPriorityLabel = (priority: number) => {
    if (priority <= 2) return 'High';
    if (priority <= 4) return 'Medium';
    return 'Low';
  };

  const filteredQueue = queue.filter(notification => {
    const matchesStatus = filterStatus === 'all' || notification.status === filterStatus;
    const matchesSearch = searchTerm === '' || 
      notification.tenant_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.template_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.recipient_email.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesStatus && matchesSearch;
  });

  // Chart data
  const notificationTrend = [
    { date: '2024-12-01', sent: 450, failed: 12 },
    { date: '2024-12-02', sent: 520, failed: 8 },
    { date: '2024-12-03', sent: 380, failed: 15 },
    { date: '2024-12-04', sent: 610, failed: 5 },
    { date: '2024-12-05', sent: 490, failed: 9 }
  ];

  const typeDistribution = [
    { name: 'Email', value: 65, color: '#3B82F6' },
    { name: 'SMS', value: 25, color: '#10B981' },
    { name: 'Push', value: 8, color: '#F59E0B' },
    { name: 'Webhook', value: 2, color: '#EF4444' }
  ];

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-6"></div>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="bg-gray-200 h-24 rounded-lg"></div>
            ))}
          </div>
          <div className="bg-gray-200 h-96 rounded-lg"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Notification Center</h2>
          <p className="text-gray-600 mt-1">
            Manage notification templates, monitor delivery, and track engagement
          </p>
        </div>
        <div className="flex space-x-3">
          <button className="flex items-center px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 transition-colors">
            <Send className="h-4 w-4 mr-2" />
            Send Test
          </button>
          <button className="flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
            <Plus className="h-4 w-4 mr-2" />
            New Template
          </button>
        </div>
      </div>

      {/* Metrics Cards */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Send className="h-8 w-8 text-blue-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Total Sent</p>
                <p className="text-2xl font-semibold text-gray-900">
                  {metrics.total_sent.toLocaleString()}
                </p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <CheckCircle className="h-8 w-8 text-green-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Delivery Rate</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.delivery_rate}%</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Eye className="h-8 w-8 text-purple-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Open Rate</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.open_rate}%</p>
              </div>
            </div>
          </div>
          
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-600" />
              <div className="ml-3">
                <p className="text-sm font-medium text-gray-500">Pending</p>
                <p className="text-2xl font-semibold text-gray-900">{metrics.pending_notifications}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Tabs */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('overview')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'overview'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Bell className="h-4 w-4 inline mr-2" />
            Overview
          </button>
          <button
            onClick={() => setActiveTab('templates')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'templates'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Mail className="h-4 w-4 inline mr-2" />
            Templates ({templates.length})
          </button>
          <button
            onClick={() => setActiveTab('queue')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'queue'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <Send className="h-4 w-4 inline mr-2" />
            Queue ({queue.length})
          </button>
          <button
            onClick={() => setActiveTab('analytics')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'analytics'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            <BarChart className="h-4 w-4 inline mr-2" />
            Analytics
          </button>
        </nav>
      </div>

      {/* Content */}
      {activeTab === 'overview' && (
        <div className="space-y-6">
          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Notification Trend */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Trends (5 days)</h3>
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={notificationTrend}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis
                    dataKey="date"
                    tickFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <YAxis />
                  <Tooltip
                    labelFormatter={(value) => new Date(value).toLocaleDateString()}
                  />
                  <Line
                    type="monotone"
                    dataKey="sent"
                    stroke="#10B981"
                    strokeWidth={2}
                    name="Sent"
                  />
                  <Line
                    type="monotone"
                    dataKey="failed"
                    stroke="#EF4444"
                    strokeWidth={2}
                    name="Failed"
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Type Distribution */}
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Types</h3>
              <ResponsiveContainer width="100%" height={300}>
                <PieChart>
                  <Pie
                    data={typeDistribution}
                    cx="50%"
                    cy="50%"
                    labelLine={false}
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                    outerRadius={80}
                    fill="#8884d8"
                    dataKey="value"
                  >
                    {typeDistribution.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="bg-white rounded-lg border border-gray-200">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Notifications</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {queue.slice(0, 5).map(notification => (
                <div key={notification.id} className="px-6 py-4 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}>
                        {getStatusIcon(notification.status)}
                        <span className="ml-1 capitalize">{notification.status}</span>
                      </span>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{notification.template_name}</div>
                        <div className="text-sm text-gray-500">{notification.tenant_name}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500">
                      {new Date(notification.scheduled_at).toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {activeTab === 'templates' && (
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {templates.map(template => (
            <div key={template.id} className="bg-white p-6 rounded-lg border border-gray-200 hover:shadow-md transition-shadow">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="text-blue-600">
                    {getTypeIcon(template.template_type)}
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900">{template.template_name}</h3>
                    <p className="text-sm text-gray-500 capitalize">{template.template_type} template</p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`px-2 py-1 text-xs rounded-full font-medium ${getCategoryColor(template.category)}`}>
                    {template.category}
                  </span>
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    template.is_active ? 'bg-green-100 text-green-700' : 'bg-gray-100 text-gray-700'
                  }`}>
                    {template.is_active ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>

              <div className="space-y-3">
                {template.subject_template && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                    <div className="text-sm text-gray-900 bg-gray-50 p-2 rounded border">
                      {template.subject_template}
                    </div>
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Body</label>
                  <div className="text-sm text-gray-900 bg-gray-50 p-2 rounded border">
                    {template.body_template.length > 100
                      ? template.body_template.substring(0, 100) + '...'
                      : template.body_template
                    }
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Variables</span>
                  <div className="flex flex-wrap gap-1">
                    {template.variables.slice(0, 3).map((variable, index) => (
                      <span key={index} className="px-2 py-1 text-xs bg-blue-100 text-blue-700 rounded">
                        {variable}
                      </span>
                    ))}
                    {template.variables.length > 3 && (
                      <span className="text-xs text-gray-500">+{template.variables.length - 3} more</span>
                    )}
                  </div>
                </div>

                <div className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">Usage Count</span>
                  <span className="font-medium">{template.usage_count.toLocaleString()}</span>
                </div>
              </div>

              <div className="mt-4 pt-4 border-t border-gray-200">
                <div className="flex space-x-2">
                  <button className="flex-1 px-3 py-2 text-sm bg-blue-50 text-blue-700 rounded-md hover:bg-blue-100 transition-colors">
                    <Edit className="h-4 w-4 inline mr-1" />
                    Edit
                  </button>
                  <button className="flex-1 px-3 py-2 text-sm bg-gray-50 text-gray-700 rounded-md hover:bg-gray-100 transition-colors">
                    <Send className="h-4 w-4 inline mr-1" />
                    Test
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {activeTab === 'queue' && (
        <div className="space-y-6">
          {/* Filters */}
          <div className="bg-white p-4 rounded-lg border border-gray-200">
            <div className="flex flex-wrap items-center gap-4">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-gray-500" />
                <span className="text-sm font-medium text-gray-700">Filters:</span>
              </div>

              <select
                value={filterStatus}
                onChange={(e) => setFilterStatus(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="all">All Status</option>
                <option value="pending">Pending</option>
                <option value="sent">Sent</option>
                <option value="failed">Failed</option>
                <option value="cancelled">Cancelled</option>
              </select>

              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search notifications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          {/* Queue Table */}
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Notification
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Recipient
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Scheduled
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredQueue.map(notification => (
                  <tr key={notification.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{notification.template_name}</div>
                        <div className="text-sm text-gray-500">{notification.tenant_name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {notification.recipient_email}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(notification.priority)}`}>
                        {getPriorityLabel(notification.priority)}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(notification.status)}`}>
                        {getStatusIcon(notification.status)}
                        <span className="ml-1 capitalize">{notification.status}</span>
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {new Date(notification.scheduled_at).toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button className="text-blue-600 hover:text-blue-900 mr-3">
                        <Eye className="h-4 w-4" />
                      </button>
                      {notification.status === 'failed' && (
                        <button className="text-green-600 hover:text-green-900 mr-3">
                          <Send className="h-4 w-4" />
                        </button>
                      )}
                      <button className="text-red-600 hover:text-red-900">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'analytics' && (
        <div className="space-y-6">
          {/* Performance Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Click Rate</p>
                  <p className="text-2xl font-semibold text-gray-900">{metrics?.click_rate}%</p>
                </div>
                <div className="text-blue-500">
                  <svg className="h-8 w-8" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                    <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Bounce Rate</p>
                  <p className="text-2xl font-semibold text-gray-900">2.1%</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-orange-500" />
              </div>
            </div>

            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-500">Unsubscribe Rate</p>
                  <p className="text-2xl font-semibold text-gray-900">0.8%</p>
                </div>
                <Users className="h-8 w-8 text-purple-500" />
              </div>
            </div>
          </div>

          {/* Delivery Performance */}
          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium text-gray-900 mb-4">Delivery Performance</h3>
            <ResponsiveContainer width="100%" height={300}>
              <BarChart data={notificationTrend}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="date"
                  tickFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <YAxis />
                <Tooltip
                  labelFormatter={(value) => new Date(value).toLocaleDateString()}
                />
                <Bar dataKey="sent" fill="#10B981" name="Delivered" />
                <Bar dataKey="failed" fill="#EF4444" name="Failed" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </div>
      )}
    </div>
  );
}
