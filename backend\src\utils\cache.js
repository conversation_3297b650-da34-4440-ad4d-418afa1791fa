// Redis-like In-Memory Cache for RESTROFLOW Performance Optimization
const NodeCache = require('node-cache');

class CacheManager {
  constructor() {
    // Initialize cache with different TTL for different data types
    this.cache = new NodeCache({
      stdTTL: 300, // 5 minutes default
      checkperiod: 60, // Check for expired keys every minute
      useClones: false // Better performance, but be careful with object mutations
    });

    // Cache statistics
    this.stats = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0
    };

    // Cache key prefixes for different data types
    this.prefixes = {
      PRODUCTS: 'products:',
      CATEGORIES: 'categories:',
      ORDERS: 'orders:',
      TENANTS: 'tenants:',
      EMPLOYEES: 'employees:',
      ANALYTICS: 'analytics:',
      TABLES: 'tables:',
      PAYMENTS: 'payments:'
    };

    // Different TTL for different data types (in seconds)
    this.ttlConfig = {
      PRODUCTS: 600, // 10 minutes - products don't change often
      CATEGORIES: 900, // 15 minutes - categories change rarely
      ORDERS: 60, // 1 minute - orders change frequently
      TENANTS: 1800, // 30 minutes - tenant info changes rarely
      EMPLOYEES: 300, // 5 minutes - employee data moderate changes
      ANALYTICS: 120, // 2 minutes - analytics need to be relatively fresh
      TABLES: 30, // 30 seconds - table status changes frequently
      PAYMENTS: 180 // 3 minutes - payment data moderate changes
    };

    console.log('🚀 Cache Manager initialized with performance optimizations');
  }

  // Generate cache key with tenant isolation
  generateKey(prefix, tenantId, identifier = '') {
    return `${prefix}${tenantId}:${identifier}`;
  }

  // Get data from cache
  get(key) {
    const value = this.cache.get(key);
    if (value !== undefined) {
      this.stats.hits++;
      console.log(`📈 Cache HIT: ${key}`);
      return value;
    } else {
      this.stats.misses++;
      console.log(`📉 Cache MISS: ${key}`);
      return null;
    }
  }

  // Set data in cache with appropriate TTL
  set(key, value, dataType = 'DEFAULT') {
    const ttl = this.ttlConfig[dataType] || this.cache.options.stdTTL;
    const success = this.cache.set(key, value, ttl);
    if (success) {
      this.stats.sets++;
      console.log(`💾 Cache SET: ${key} (TTL: ${ttl}s)`);
    }
    return success;
  }

  // Delete specific key
  delete(key) {
    const success = this.cache.del(key);
    if (success) {
      this.stats.deletes++;
      console.log(`🗑️ Cache DELETE: ${key}`);
    }
    return success;
  }

  // Clear all cache for a tenant
  clearTenant(tenantId) {
    const keys = this.cache.keys();
    const tenantKeys = keys.filter(key => key.includes(`:${tenantId}:`));
    const deleted = this.cache.del(tenantKeys);
    console.log(`🧹 Cleared ${deleted} cache entries for tenant ${tenantId}`);
    return deleted;
  }

  // Clear cache by data type
  clearByType(dataType, tenantId = null) {
    const prefix = this.prefixes[dataType];
    if (!prefix) return 0;

    const keys = this.cache.keys();
    const typeKeys = keys.filter(key => {
      if (tenantId) {
        return key.startsWith(prefix) && key.includes(`:${tenantId}:`);
      }
      return key.startsWith(prefix);
    });
    
    const deleted = this.cache.del(typeKeys);
    console.log(`🧹 Cleared ${deleted} ${dataType} cache entries`);
    return deleted;
  }

  // Get cache statistics
  getStats() {
    const cacheStats = this.cache.getStats();
    return {
      ...this.stats,
      keys: cacheStats.keys,
      hits_ratio: this.stats.hits / (this.stats.hits + this.stats.misses) || 0,
      memory_usage: process.memoryUsage()
    };
  }

  // Warm up cache with frequently accessed data
  async warmUp(pool, tenantId) {
    try {
      console.log(`🔥 Warming up cache for tenant ${tenantId}...`);

      // Cache products
      const productsResult = await pool.query(
        'SELECT * FROM products WHERE tenant_id = $1 AND is_active = true',
        [tenantId]
      );
      const productsKey = this.generateKey(this.prefixes.PRODUCTS, tenantId, 'all');
      this.set(productsKey, productsResult.rows, 'PRODUCTS');

      // Cache categories
      const categoriesResult = await pool.query(
        'SELECT * FROM categories WHERE tenant_id = $1 AND is_active = true',
        [tenantId]
      );
      const categoriesKey = this.generateKey(this.prefixes.CATEGORIES, tenantId, 'all');
      this.set(categoriesKey, categoriesResult.rows, 'CATEGORIES');

      // Cache active tables
      const tablesResult = await pool.query(
        'SELECT * FROM tables WHERE tenant_id = $1 AND is_active = true',
        [tenantId]
      );
      const tablesKey = this.generateKey(this.prefixes.TABLES, tenantId, 'all');
      this.set(tablesKey, tablesResult.rows, 'TABLES');

      console.log(`✅ Cache warmed up for tenant ${tenantId}`);
    } catch (error) {
      console.error('❌ Cache warm-up failed:', error);
    }
  }

  // Cache middleware for Express routes
  middleware(dataType, keyGenerator) {
    return (req, res, next) => {
      if (req.method !== 'GET') {
        return next(); // Only cache GET requests
      }

      const cacheKey = keyGenerator(req);
      const cachedData = this.get(cacheKey);

      if (cachedData) {
        return res.json({
          ...cachedData,
          _cached: true,
          _cache_timestamp: new Date().toISOString()
        });
      }

      // Store original res.json
      const originalJson = res.json;
      
      // Override res.json to cache the response
      res.json = (data) => {
        if (res.statusCode === 200 && data.success !== false) {
          this.set(cacheKey, data, dataType);
        }
        return originalJson.call(res, data);
      };

      next();
    };
  }

  // Invalidate cache when data changes
  invalidateOnChange(dataType, tenantId, identifier = '') {
    const patterns = [
      this.generateKey(this.prefixes[dataType], tenantId, 'all'),
      this.generateKey(this.prefixes[dataType], tenantId, identifier)
    ];

    patterns.forEach(pattern => {
      this.delete(pattern);
    });

    // Also clear related analytics cache
    if (['PRODUCTS', 'ORDERS', 'PAYMENTS'].includes(dataType)) {
      this.clearByType('ANALYTICS', tenantId);
    }
  }

  // Health check
  healthCheck() {
    const stats = this.getStats();
    return {
      status: 'healthy',
      cache_enabled: true,
      total_keys: stats.keys,
      hit_ratio: (stats.hits_ratio * 100).toFixed(2) + '%',
      memory_usage_mb: (stats.memory_usage.heapUsed / 1024 / 1024).toFixed(2)
    };
  }
}

// Create singleton instance
const cacheManager = new CacheManager();

module.exports = {
  CacheManager,
  cache: cacheManager
};
