import React, { useState, useEffect } from 'react';
import { 
  Wine, 
  Clock, 
  Users, 
  Star, 
  ChefHat, 
  Calendar, 
  CreditCard,
  Bell,
  Heart,
  Award,
  Utensils,
  MapPin,
  Phone,
  Mail,
  Gift
} from 'lucide-react';

interface Guest {
  id: string;
  name: string;
  preferences: string[];
  allergies: string[];
  visitCount: number;
  lastVisit: Date;
  favoriteWines: string[];
  specialOccasions: string[];
}

interface WineItem {
  id: string;
  name: string;
  vintage: string;
  region: string;
  price: number;
  pairingNotes: string[];
  inStock: number;
  rating: number;
}

interface Course {
  id: string;
  name: string;
  description: string;
  price: number;
  preparationTime: number;
  allergens: string[];
  wineParings: string[];
  status: 'pending' | 'preparing' | 'ready' | 'served';
}

const FineDiningInterface: React.FC = () => {
  const [selectedTable, setSelectedTable] = useState<string>('Table 12');
  const [currentGuest, setCurrentGuest] = useState<Guest | null>(null);
  const [selectedCourses, setSelectedCourses] = useState<Course[]>([]);
  const [wineSelection, setWineSelection] = useState<WineItem[]>([]);
  const [serviceNotes, setServiceNotes] = useState<string>('');
  const [courseProgress, setCourseProgress] = useState<number>(0);

  // Mock data
  const guestProfiles: Guest[] = [
    {
      id: '1',
      name: 'Mr. & Mrs. Anderson',
      preferences: ['Vegetarian options', 'Wine pairings', 'Quiet seating'],
      allergies: ['Shellfish', 'Nuts'],
      visitCount: 12,
      lastVisit: new Date('2024-01-15'),
      favoriteWines: ['Château Margaux 2015', 'Opus One 2018'],
      specialOccasions: ['Anniversary dinner']
    }
  ];

  const wineCollection: WineItem[] = [
    {
      id: '1',
      name: 'Château Margaux',
      vintage: '2015',
      region: 'Bordeaux, France',
      price: 450,
      pairingNotes: ['Red meat', 'Aged cheese', 'Dark chocolate'],
      inStock: 6,
      rating: 4.9
    },
    {
      id: '2',
      name: 'Dom Pérignon',
      vintage: '2012',
      region: 'Champagne, France',
      price: 320,
      pairingNotes: ['Oysters', 'Caviar', 'Light appetizers'],
      inStock: 12,
      rating: 4.8
    }
  ];

  const coursesMenu: Course[] = [
    {
      id: '1',
      name: 'Amuse-Bouche',
      description: 'Truffle foam with micro herbs',
      price: 0,
      preparationTime: 5,
      allergens: [],
      wineParings: ['Champagne', 'Sauvignon Blanc'],
      status: 'pending'
    },
    {
      id: '2',
      name: 'Foie Gras Terrine',
      description: 'Pan-seared foie gras with fig compote and brioche',
      price: 65,
      preparationTime: 15,
      allergens: ['Gluten'],
      wineParings: ['Sauternes', 'Riesling'],
      status: 'pending'
    },
    {
      id: '3',
      name: 'Wagyu Beef Tenderloin',
      description: 'A5 Wagyu with seasonal vegetables and red wine reduction',
      price: 125,
      preparationTime: 25,
      allergens: [],
      wineParings: ['Cabernet Sauvignon', 'Bordeaux'],
      status: 'pending'
    }
  ];

  useEffect(() => {
    if (guestProfiles.length > 0) {
      setCurrentGuest(guestProfiles[0]);
    }
  }, []);

  const addCourse = (course: Course) => {
    setSelectedCourses([...selectedCourses, { ...course, status: 'pending' }]);
  };

  const addWine = (wine: WineItem) => {
    setWineSelection([...wineSelection, wine]);
  };

  const getOrderTotal = () => {
    const courseTotal = selectedCourses.reduce((sum, course) => sum + course.price, 0);
    const wineTotal = wineSelection.reduce((sum, wine) => sum + wine.price, 0);
    return courseTotal + wineTotal;
  };

  return (
    <div className="h-full bg-gradient-to-br from-red-50 via-amber-50 to-orange-50 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-red-900 to-red-800 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Utensils className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Fine Dining Experience</h1>
              <p className="text-red-100">Curated culinary excellence with personalized service</p>
            </div>
          </div>
          <div className="text-right">
            <div className="text-2xl font-bold">{selectedTable}</div>
            <div className="text-red-200">Premium Service</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Guest Profile & Service Notes */}
        <div className="space-y-6">
          {/* Guest Profile */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Users className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Guest Profile</h2>
            </div>
            
            {currentGuest && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">{currentGuest.name}</h3>
                  <div className="flex items-center space-x-1">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <span className="text-sm font-medium text-gray-600">VIP Guest</span>
                  </div>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Visits:</span>
                    <span className="ml-2 font-semibold">{currentGuest.visitCount}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Last Visit:</span>
                    <span className="ml-2 font-semibold">{currentGuest.lastVisit.toLocaleDateString()}</span>
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Preferences</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentGuest.preferences.map((pref, index) => (
                      <span key={index} className="px-2 py-1 bg-green-100 text-green-800 rounded-full text-xs">
                        {pref}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Allergies</h4>
                  <div className="flex flex-wrap gap-2">
                    {currentGuest.allergies.map((allergy, index) => (
                      <span key={index} className="px-2 py-1 bg-red-100 text-red-800 rounded-full text-xs">
                        ⚠️ {allergy}
                      </span>
                    ))}
                  </div>
                </div>

                <div>
                  <h4 className="font-semibold text-gray-900 mb-2">Favorite Wines</h4>
                  <div className="space-y-1">
                    {currentGuest.favoriteWines.map((wine, index) => (
                      <div key={index} className="text-sm text-gray-600 flex items-center space-x-2">
                        <Wine className="w-3 h-3" />
                        <span>{wine}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Service Notes */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Bell className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Service Notes</h2>
            </div>
            
            <textarea
              value={serviceNotes}
              onChange={(e) => setServiceNotes(e.target.value)}
              placeholder="Special requests, dietary notes, service preferences..."
              className="w-full h-32 p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-red-500 focus:border-transparent resize-none"
            />
            
            <div className="mt-4 space-y-2">
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Heart className="w-4 h-4 text-red-500" />
                <span>Anniversary celebration - complimentary dessert</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-600">
                <Gift className="w-4 h-4 text-purple-500" />
                <span>Preferred seating: Window table</span>
              </div>
            </div>
          </div>

          {/* Course Progress */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Clock className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Course Progress</h2>
            </div>
            
            <div className="space-y-3">
              {selectedCourses.map((course, index) => (
                <div key={course.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div>
                    <div className="font-semibold text-gray-900">{course.name}</div>
                    <div className="text-sm text-gray-600">{course.preparationTime} min prep</div>
                  </div>
                  <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                    course.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                    course.status === 'preparing' ? 'bg-blue-100 text-blue-800' :
                    course.status === 'ready' ? 'bg-green-100 text-green-800' :
                    'bg-gray-100 text-gray-800'
                  }`}>
                    {course.status}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Menu Selection */}
        <div className="space-y-6">
          {/* Course Menu */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <ChefHat className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Tasting Menu</h2>
            </div>
            
            <div className="space-y-4">
              {coursesMenu.map((course) => (
                <div key={course.id} className="border border-gray-200 rounded-lg p-4 hover:border-red-300 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{course.name}</h3>
                    <span className="text-lg font-bold text-red-600">
                      {course.price === 0 ? 'Complimentary' : `$${course.price}`}
                    </span>
                  </div>
                  
                  <p className="text-gray-600 text-sm mb-3">{course.description}</p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <span>⏱️ {course.preparationTime} min</span>
                      {course.allergens.length > 0 && (
                        <span className="text-red-500">⚠️ {course.allergens.join(', ')}</span>
                      )}
                    </div>
                    
                    <button
                      onClick={() => addCourse(course)}
                      className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                    >
                      Add Course
                    </button>
                  </div>
                  
                  {course.wineParings.length > 0 && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="text-xs text-gray-600">
                        <Wine className="w-3 h-3 inline mr-1" />
                        Recommended pairings: {course.wineParings.join(', ')}
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Wine Selection & Order Summary */}
        <div className="space-y-6">
          {/* Wine Collection */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <Wine className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Wine Collection</h2>
            </div>
            
            <div className="space-y-4">
              {wineCollection.map((wine) => (
                <div key={wine.id} className="border border-gray-200 rounded-lg p-4 hover:border-red-300 transition-colors">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="font-semibold text-gray-900">{wine.name}</h3>
                      <p className="text-sm text-gray-600">{wine.vintage} • {wine.region}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-red-600">${wine.price}</div>
                      <div className="flex items-center space-x-1">
                        <Star className="w-3 h-3 text-yellow-500 fill-current" />
                        <span className="text-xs text-gray-600">{wine.rating}</span>
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-xs text-gray-600 mb-3">
                    Stock: {wine.inStock} bottles
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div className="text-xs text-gray-500">
                      Pairs with: {wine.pairingNotes.join(', ')}
                    </div>
                    
                    <button
                      onClick={() => addWine(wine)}
                      className="bg-gradient-to-r from-amber-600 to-amber-700 hover:from-amber-700 hover:to-amber-800 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200"
                    >
                      Add Wine
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Order Summary */}
          <div className="bg-white rounded-2xl shadow-lg border border-red-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <CreditCard className="w-6 h-6 text-red-600" />
              <h2 className="text-xl font-bold text-gray-900">Order Summary</h2>
            </div>
            
            <div className="space-y-3">
              {selectedCourses.map((course, index) => (
                <div key={`course-${index}`} className="flex items-center justify-between">
                  <span className="text-gray-900">{course.name}</span>
                  <span className="font-semibold">${course.price}</span>
                </div>
              ))}
              
              {wineSelection.map((wine, index) => (
                <div key={`wine-${index}`} className="flex items-center justify-between">
                  <span className="text-gray-900">{wine.name} {wine.vintage}</span>
                  <span className="font-semibold">${wine.price}</span>
                </div>
              ))}
              
              {(selectedCourses.length > 0 || wineSelection.length > 0) && (
                <>
                  <div className="border-t border-gray-200 pt-3">
                    <div className="flex items-center justify-between text-lg font-bold">
                      <span>Total</span>
                      <span className="text-red-600">${getOrderTotal()}</span>
                    </div>
                  </div>
                  
                  <button className="w-full bg-gradient-to-r from-red-600 to-red-700 hover:from-red-700 hover:to-red-800 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 mt-4">
                    Send to Kitchen
                  </button>
                </>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FineDiningInterface;
