// Phase 3I: Multi-Language Localization System
// Language Selector Component

import React, { useState, useRef, useEffect } from 'react';
import { useTranslation } from '../hooks/useTranslation';
import { ChevronDownIcon, GlobeAltIcon, CheckIcon } from '@heroicons/react/24/outline';

const LanguageSelector = ({ 
  className = '',
  showFlag = true,
  showNativeName = true,
  compact = false,
  position = 'bottom-right'
}) => {
  const {
    currentLanguage,
    supportedLanguages,
    changeLanguage,
    getCurrentLanguageInfo,
    loading,
    t
  } = useTranslation();

  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const dropdownRef = useRef(null);
  const searchRef = useRef(null);

  const currentLang = getCurrentLanguageInfo();

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
        setSearchTerm('');
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchRef.current) {
      searchRef.current.focus();
    }
  }, [isOpen]);

  // Filter languages based on search term
  const filteredLanguages = supportedLanguages.filter(lang =>
    lang.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.nativeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    lang.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Group languages by tier
  const groupedLanguages = filteredLanguages.reduce((acc, lang) => {
    const tier = `tier${lang.tier}`;
    if (!acc[tier]) acc[tier] = [];
    acc[tier].push(lang);
    return acc;
  }, {});

  const handleLanguageSelect = async (languageCode) => {
    if (languageCode !== currentLanguage) {
      await changeLanguage(languageCode);
      console.log(`🌍 Language changed to: ${languageCode}`);
    }
    setIsOpen(false);
    setSearchTerm('');
  };

  const getTierLabel = (tier) => {
    switch (tier) {
      case 'tier1': return t('language.tier1', 'Primary Markets');
      case 'tier2': return t('language.tier2', 'Asian Markets');
      case 'tier3': return t('language.tier3', 'Emerging Markets');
      default: return '';
    }
  };

  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-left':
        return 'left-0 top-full mt-2';
      case 'bottom-right':
        return 'right-0 top-full mt-2';
      case 'top-left':
        return 'left-0 bottom-full mb-2';
      case 'top-right':
        return 'right-0 bottom-full mb-2';
      default:
        return 'right-0 top-full mt-2';
    }
  };

  if (compact) {
    return (
      <div className={`relative ${className}`} ref={dropdownRef}>
        <button
          onClick={() => setIsOpen(!isOpen)}
          className="flex items-center space-x-1 px-2 py-1 text-sm bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
          disabled={loading}
        >
          {showFlag && <span className="text-lg">{currentLang.flag}</span>}
          <span className="text-xs font-medium">{currentLang.code}</span>
          <ChevronDownIcon className={`w-3 h-3 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
        </button>

        {isOpen && (
          <div className={`absolute ${getPositionClasses()} w-48 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-50 max-h-64 overflow-hidden`}>
            <div className="p-2 border-b border-gray-200 dark:border-gray-700">
              <input
                ref={searchRef}
                type="text"
                placeholder={t('language.search', 'Search languages...')}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <div className="max-h-48 overflow-y-auto">
              {Object.entries(groupedLanguages).map(([tier, languages]) => (
                <div key={tier}>
                  <div className="px-3 py-1 text-xs font-semibold text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                    {getTierLabel(tier)}
                  </div>
                  {languages.map((lang) => (
                    <button
                      key={lang.code}
                      onClick={() => handleLanguageSelect(lang.code)}
                      className={`w-full flex items-center justify-between px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                        lang.code === currentLanguage ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                      }`}
                    >
                      <div className="flex items-center space-x-2">
                        <span className="text-base">{lang.flag}</span>
                        <div className="text-left">
                          <div className="font-medium">{showNativeName ? lang.nativeName : lang.name}</div>
                          <div className="text-xs text-gray-500 dark:text-gray-400">{lang.code}</div>
                        </div>
                      </div>
                      {lang.code === currentLanguage && (
                        <CheckIcon className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                      )}
                    </button>
                  ))}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  }

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center space-x-2 px-4 py-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors shadow-sm"
        disabled={loading}
      >
        <GlobeAltIcon className="w-5 h-5 text-gray-500 dark:text-gray-400" />
        {showFlag && <span className="text-xl">{currentLang.flag}</span>}
        <div className="text-left">
          <div className="text-sm font-medium text-gray-900 dark:text-white">
            {showNativeName ? currentLang.nativeName : currentLang.name}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400">{currentLang.code}</div>
        </div>
        <ChevronDownIcon className={`w-4 h-4 text-gray-500 dark:text-gray-400 transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </button>

      {isOpen && (
        <div className={`absolute ${getPositionClasses()} w-80 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-xl z-50 max-h-96 overflow-hidden`}>
          <div className="p-3 border-b border-gray-200 dark:border-gray-700">
            <input
              ref={searchRef}
              type="text"
              placeholder={t('language.search', 'Search languages...')}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
          
          <div className="max-h-64 overflow-y-auto">
            {Object.entries(groupedLanguages).map(([tier, languages]) => (
              <div key={tier}>
                <div className="px-4 py-2 text-sm font-semibold text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
                  {getTierLabel(tier)} ({languages.length})
                </div>
                {languages.map((lang) => (
                  <button
                    key={lang.code}
                    onClick={() => handleLanguageSelect(lang.code)}
                    className={`w-full flex items-center justify-between px-4 py-3 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors ${
                      lang.code === currentLanguage ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <span className="text-2xl">{lang.flag}</span>
                      <div className="text-left">
                        <div className="font-medium">{lang.nativeName}</div>
                        <div className="text-sm text-gray-500 dark:text-gray-400">{lang.name}</div>
                        <div className="text-xs text-gray-400 dark:text-gray-500">{lang.code}</div>
                      </div>
                      {lang.rtl && (
                        <span className="px-2 py-1 text-xs bg-orange-100 dark:bg-orange-900/20 text-orange-600 dark:text-orange-400 rounded">
                          RTL
                        </span>
                      )}
                    </div>
                    {lang.code === currentLanguage && (
                      <CheckIcon className="w-5 h-5 text-blue-600 dark:text-blue-400" />
                    )}
                  </button>
                ))}
              </div>
            ))}
          </div>
          
          {filteredLanguages.length === 0 && (
            <div className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
              <GlobeAltIcon className="w-8 h-8 mx-auto mb-2 opacity-50" />
              <div className="text-sm">{t('language.no_results', 'No languages found')}</div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
