#!/usr/bin/env node

/**
 * Master Test Suite Runner
 * Executes all comprehensive tests and generates final report
 */

import { spawn } from 'child_process';
import colors from 'colors';
import fs from 'fs';

const TEST_SUITES = [
  { name: 'API Endpoint Testing', script: 'comprehensive-api-test.js', timeout: 60000 },
  { name: 'Database Integration Testing', script: 'database-integration-test.js', timeout: 60000 },
  { name: 'Authentication & Security Testing', script: 'auth-security-test.js', timeout: 60000 },
  { name: 'Performance & Load Testing', script: 'performance-load-test.js', timeout: 120000 }
];

const RESULTS = {
  totalSuites: TEST_SUITES.length,
  completedSuites: 0,
  failedSuites: 0,
  suiteResults: [],
  startTime: Date.now()
};

const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  switch(type) {
    case 'success': console.log(`[${timestamp}] ✅ ${message}`.green); break;
    case 'error': console.log(`[${timestamp}] ❌ ${message}`.red); break;
    case 'warning': console.log(`[${timestamp}] ⚠️  ${message}`.yellow); break;
    case 'info': console.log(`[${timestamp}] ℹ️  ${message}`.blue); break;
    default: console.log(`[${timestamp}] ${message}`);
  }
};

const runTestSuite = (suite) => {
  return new Promise((resolve, reject) => {
    log(`Starting ${suite.name}...`, 'info');
    
    const startTime = Date.now();
    const child = spawn('node', [suite.script], {
      stdio: 'pipe',
      cwd: process.cwd()
    });
    
    let output = '';
    let errorOutput = '';
    
    child.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    child.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    child.on('close', (code) => {
      const duration = Date.now() - startTime;
      
      if (code === 0) {
        log(`${suite.name} completed successfully in ${duration}ms`, 'success');
        resolve({
          name: suite.name,
          success: true,
          duration,
          output,
          code
        });
      } else {
        log(`${suite.name} failed with code ${code}`, 'error');
        resolve({
          name: suite.name,
          success: false,
          duration,
          output,
          errorOutput,
          code
        });
      }
    });
    
    child.on('error', (error) => {
      log(`${suite.name} error: ${error.message}`, 'error');
      resolve({
        name: suite.name,
        success: false,
        duration: Date.now() - startTime,
        error: error.message
      });
    });
    
    // Set timeout
    setTimeout(() => {
      child.kill('SIGTERM');
      log(`${suite.name} timed out after ${suite.timeout}ms`, 'warning');
      resolve({
        name: suite.name,
        success: false,
        duration: suite.timeout,
        error: 'Timeout'
      });
    }, suite.timeout);
  });
};

const extractTestResults = (output) => {
  const lines = output.split('\n');
  let totalTests = 0;
  let passedTests = 0;
  let failedTests = 0;
  let successRate = 0;
  
  for (const line of lines) {
    if (line.includes('Total Tests:')) {
      totalTests = parseInt(line.match(/\d+/)[0]);
    }
    if (line.includes('Passed:')) {
      passedTests = parseInt(line.match(/\d+/)[0]);
    }
    if (line.includes('Failed:')) {
      failedTests = parseInt(line.match(/\d+/)[0]);
    }
    if (line.includes('Success Rate:')) {
      successRate = parseFloat(line.match(/[\d.]+%/)[0]);
    }
  }
  
  return { totalTests, passedTests, failedTests, successRate };
};

const generateFinalReport = () => {
  const totalDuration = Date.now() - RESULTS.startTime;
  
  console.log('\n' + '='.repeat(80).cyan);
  console.log('🏆 COMPREHENSIVE TESTING SUITE - FINAL REPORT'.cyan.bold);
  console.log('='.repeat(80).cyan);
  
  console.log(`\n📊 EXECUTION SUMMARY:`.white.bold);
  console.log(`Total Test Suites: ${RESULTS.totalSuites}`.white);
  console.log(`Completed Suites: ${RESULTS.completedSuites}`.green);
  console.log(`Failed Suites: ${RESULTS.failedSuites}`.red);
  console.log(`Total Execution Time: ${(totalDuration / 1000).toFixed(1)}s`.yellow);
  
  console.log(`\n📋 SUITE RESULTS:`.white.bold);
  
  let totalTests = 0;
  let totalPassed = 0;
  let totalFailed = 0;
  
  RESULTS.suiteResults.forEach((result, index) => {
    const status = result.success ? '✅ PASS'.green : '❌ FAIL'.red;
    const duration = `${(result.duration / 1000).toFixed(1)}s`;
    
    console.log(`${index + 1}. ${result.name}: ${status} (${duration})`);
    
    if (result.output) {
      const testResults = extractTestResults(result.output);
      if (testResults.totalTests > 0) {
        console.log(`   Tests: ${testResults.passedTests}/${testResults.totalTests} passed (${testResults.successRate}%)`.gray);
        totalTests += testResults.totalTests;
        totalPassed += testResults.passedTests;
        totalFailed += testResults.failedTests;
      }
    }
    
    if (!result.success && result.error) {
      console.log(`   Error: ${result.error}`.red);
    }
  });
  
  if (totalTests > 0) {
    const overallSuccessRate = ((totalPassed / totalTests) * 100).toFixed(1);
    console.log(`\n🎯 OVERALL TEST RESULTS:`.white.bold);
    console.log(`Total Tests Executed: ${totalTests}`.white);
    console.log(`Total Passed: ${totalPassed}`.green);
    console.log(`Total Failed: ${totalFailed}`.red);
    console.log(`Overall Success Rate: ${overallSuccessRate}%`.yellow.bold);
    
    // Determine overall status
    if (overallSuccessRate >= 90) {
      console.log(`\n🏆 SYSTEM STATUS: EXCELLENT - Ready for Production`.green.bold);
    } else if (overallSuccessRate >= 80) {
      console.log(`\n⚠️ SYSTEM STATUS: GOOD - Minor issues to address`.yellow.bold);
    } else if (overallSuccessRate >= 70) {
      console.log(`\n🔧 SYSTEM STATUS: NEEDS IMPROVEMENT - Several issues to fix`.orange.bold);
    } else {
      console.log(`\n🚨 SYSTEM STATUS: CRITICAL - Major issues require attention`.red.bold);
    }
  }
  
  console.log(`\n📄 Detailed test report available in: COMPREHENSIVE_TEST_REPORT.md`.blue);
  console.log('='.repeat(80).cyan);
};

const runAllTests = async () => {
  console.log('🚀 Starting Comprehensive Testing Suite...'.cyan.bold);
  console.log('This will run all test suites and generate a final report.\n'.gray);
  
  // Check if backend is running
  try {
    const response = await fetch('http://localhost:4000/api/health');
    if (!response.ok) {
      throw new Error('Backend not responding');
    }
    log('Backend server is running and healthy', 'success');
  } catch (error) {
    log('Backend server is not running! Please start the backend first.', 'error');
    log('Run: node working-server.js', 'info');
    process.exit(1);
  }
  
  // Run each test suite
  for (const suite of TEST_SUITES) {
    try {
      const result = await runTestSuite(suite);
      RESULTS.suiteResults.push(result);
      RESULTS.completedSuites++;
      
      if (!result.success) {
        RESULTS.failedSuites++;
      }
    } catch (error) {
      log(`Failed to run ${suite.name}: ${error.message}`, 'error');
      RESULTS.failedSuites++;
      RESULTS.suiteResults.push({
        name: suite.name,
        success: false,
        error: error.message,
        duration: 0
      });
    }
  }
  
  // Generate final report
  generateFinalReport();
  
  // Exit with appropriate code
  process.exit(RESULTS.failedSuites > 0 ? 1 : 0);
};

// Handle process termination
process.on('SIGINT', () => {
  log('Test suite interrupted by user', 'warning');
  generateFinalReport();
  process.exit(1);
});

process.on('SIGTERM', () => {
  log('Test suite terminated', 'warning');
  generateFinalReport();
  process.exit(1);
});

// Run the tests
runAllTests().catch((error) => {
  log(`Test suite failed: ${error.message}`, 'error');
  process.exit(1);
});
