import React, { useState, useEffect, useRef } from 'react';
import { 
  Plus, 
  Edit3, 
  Trash2, 
  Move, 
  RotateCw, 
  Users, 
  Clock, 
  DollarSign,
  CheckCircle,
  AlertCircle,
  Coffee,
  Utensils,
  CreditCard,
  Save,
  X,
  Grid,
  Settings,
  RefreshCw,
  Search,
  Filter,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface Table {
  id: string;
  number: number;
  name?: string;
  seats: number;
  x: number;
  y: number;
  width: number;
  height: number;
  shape: 'rectangle' | 'circle' | 'oval';
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  section: string;
  tableType: 'regular' | 'bar' | 'private' | 'outdoor' | 'booth';
  guestCount?: number;
  maxCapacity?: number;
  serverAssigned?: string;
  serverName?: string;
  seatedTime?: Date;
  currentOrderId?: string;
  orderTotal?: number;
  orderItems?: number;
  notes?: string;
  rotation?: number;
}

interface FloorSection {
  id: string;
  name: string;
  color: string;
  x: number;
  y: number;
  width: number;
  height: number;
}

interface FloorLayoutData {
  id: string;
  name: string;
  width: number;
  height: number;
  tables: Table[];
  sections: FloorSection[];
}

const EnhancedFloorLayoutManager: React.FC = () => {
  const { apiCall, state, dispatch } = useEnhancedAppContext();
  const [floorData, setFloorData] = useState<FloorLayoutData | null>(null);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [selectedSection, setSelectedSection] = useState<string>('all');
  const [isEditMode, setIsEditMode] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 });
  const [showTableModal, setShowTableModal] = useState(false);
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [showLegend, setShowLegend] = useState(true);
  const [zoomLevel, setZoomLevel] = useState(1);
  const floorRef = useRef<HTMLDivElement>(null);

  // Table creation state
  const [newTable, setNewTable] = useState<Partial<Table>>({
    number: 1,
    name: '',
    seats: 4,
    width: 80,
    height: 80,
    shape: 'rectangle',
    tableType: 'regular',
    section: '',
    x: 100,
    y: 100,
    rotation: 0
  });

  useEffect(() => {
    fetchFloorLayout();
  }, []);

  const fetchFloorLayout = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall('/api/floor/layout');
      if (response.ok) {
        const data = await response.json();
        setFloorData(data);
      } else {
        // Create default floor layout if none exists
        const defaultLayout: FloorLayoutData = {
          id: 'default',
          name: 'Main Dining Room',
          width: 800,
          height: 600,
          tables: [],
          sections: [
            {
              id: 'main',
              name: 'Main Dining',
              color: '#3B82F6',
              x: 50,
              y: 50,
              width: 400,
              height: 300
            },
            {
              id: 'bar',
              name: 'Bar Area',
              color: '#10B981',
              x: 500,
              y: 50,
              width: 250,
              height: 150
            }
          ]
        };
        setFloorData(defaultLayout);
      }
    } catch (error) {
      console.error('Error fetching floor layout:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getTableStatusColor = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return 'bg-green-500 border-green-600 text-white';
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return 'bg-yellow-500 border-yellow-600 text-white';
          case 'eating':
            return 'bg-blue-500 border-blue-600 text-white';
          case 'waiting-for-check':
            return 'bg-purple-500 border-purple-600 text-white';
          case 'paying':
            return 'bg-orange-500 border-orange-600 text-white';
          default:
            return 'bg-red-500 border-red-600 text-white';
        }
      case 'reserved':
        return 'bg-indigo-500 border-indigo-600 text-white';
      case 'needs-cleaning':
        return 'bg-gray-500 border-gray-600 text-white';
      case 'out-of-order':
        return 'bg-red-800 border-red-900 text-white';
      default:
        return 'bg-gray-400 border-gray-500 text-white';
    }
  };

  const getTableStatusIcon = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return <Utensils className="h-4 w-4" />;
          case 'eating':
            return <Coffee className="h-4 w-4" />;
          case 'waiting-for-check':
            return <Clock className="h-4 w-4" />;
          case 'paying':
            return <CreditCard className="h-4 w-4" />;
          default:
            return <Users className="h-4 w-4" />;
        }
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      case 'out-of-order':
        return <X className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleTableClick = (table: Table) => {
    if (isEditMode) {
      setSelectedTable(table);
      setNewTable(table);
      setShowTableModal(true);
      return;
    }

    // Handle table selection for orders
    if (state.currentOrder && state.currentOrder.items.length > 0) {
      // If there's an active order, assign it to this table
      assignOrderToTable(table);
    } else {
      // Show table details or start new order
      setSelectedTable(table);
      if (table.status === 'available') {
        startNewOrder(table);
      }
    }
  };

  const assignOrderToTable = async (table: Table) => {
    if (!state.currentOrder) return;

    try {
      // Update table status and assign order
      await updateTableStatus(table.id, 'occupied', 'ordering');
      
      // Set table in current order context
      dispatch({
        type: 'SET_CURRENT_ORDER',
        payload: {
          ...state.currentOrder,
          tableId: table.id,
          tableNumber: table.number,
          guestCount: table.guestCount || 1
        }
      });

      // Update local table state
      setFloorData(prev => prev ? {
        ...prev,
        tables: prev.tables.map(t => 
          t.id === table.id 
            ? { 
                ...t, 
                status: 'occupied', 
                substatus: 'ordering',
                currentOrderId: state.currentOrder?.id,
                orderTotal: state.currentOrder?.total,
                orderItems: state.currentOrder?.items.length
              }
            : t
        )
      } : null);

      setShowTableSelector(false);
    } catch (error) {
      console.error('Error assigning order to table:', error);
    }
  };

  const startNewOrder = (table: Table) => {
    // Clear any existing order
    dispatch({ type: 'CLEAR_CURRENT_ORDER' });
    
    // Create new order with table context
    dispatch({
      type: 'SET_CURRENT_ORDER',
      payload: {
        id: `order_${Date.now()}`,
        items: [],
        subtotal: 0,
        tax: 0,
        total: 0,
        tableId: table.id,
        tableNumber: table.number,
        guestCount: table.guestCount || 1,
        timestamp: new Date().toISOString()
      }
    });

    // Update table status
    updateTableStatus(table.id, 'occupied', 'ordering');
  };

  const updateTableStatus = async (tableId: string, status: Table['status'], substatus?: Table['substatus']) => {
    try {
      const response = await apiCall(`/api/floor/tables/${tableId}/status`, {
        method: 'PUT',
        body: JSON.stringify({ status, substatus })
      });

      if (response.ok) {
        // Update local state
        setFloorData(prev => prev ? {
          ...prev,
          tables: prev.tables.map(t => 
            t.id === tableId 
              ? { ...t, status, substatus }
              : t
          )
        } : null);
      }
    } catch (error) {
      console.error('Error updating table status:', error);
    }
  };

  const createNewTable = async () => {
    if (!floorData || !newTable.number || !newTable.section) return;

    try {
      const tableData = {
        ...newTable,
        id: `table_${Date.now()}`,
        status: 'available' as const,
        maxCapacity: newTable.seats
      };

      const response = await apiCall('/api/floor/tables', {
        method: 'POST',
        body: JSON.stringify(tableData)
      });

      if (response.ok) {
        const createdTable = await response.json();
        
        // Add to local state
        setFloorData(prev => prev ? {
          ...prev,
          tables: [...prev.tables, createdTable]
        } : null);

        // Reset form
        setNewTable({
          number: Math.max(...floorData.tables.map(t => t.number), 0) + 1,
          name: '',
          seats: 4,
          width: 80,
          height: 80,
          shape: 'rectangle',
          tableType: 'regular',
          section: '',
          x: 100,
          y: 100,
          rotation: 0
        });

        setShowTableModal(false);
      }
    } catch (error) {
      console.error('Error creating table:', error);
    }
  };

  const updateTable = async () => {
    if (!selectedTable || !newTable.number) return;

    try {
      const response = await apiCall(`/api/floor/tables/${selectedTable.id}`, {
        method: 'PUT',
        body: JSON.stringify(newTable)
      });

      if (response.ok) {
        const updatedTable = await response.json();
        
        // Update local state
        setFloorData(prev => prev ? {
          ...prev,
          tables: prev.tables.map(t => 
            t.id === selectedTable.id ? updatedTable : t
          )
        } : null);

        setShowTableModal(false);
        setSelectedTable(null);
      }
    } catch (error) {
      console.error('Error updating table:', error);
    }
  };

  const deleteTable = async (tableId: string) => {
    if (!confirm('Are you sure you want to delete this table?')) return;

    try {
      const response = await apiCall(`/api/floor/tables/${tableId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        // Remove from local state
        setFloorData(prev => prev ? {
          ...prev,
          tables: prev.tables.filter(t => t.id !== tableId)
        } : null);

        setShowTableModal(false);
        setSelectedTable(null);
      }
    } catch (error) {
      console.error('Error deleting table:', error);
    }
  };

  const handleMouseDown = (e: React.MouseEvent, table: Table) => {
    if (!isEditMode) return;
    
    e.preventDefault();
    setIsDragging(true);
    setSelectedTable(table);
    
    const rect = floorRef.current?.getBoundingClientRect();
    if (rect) {
      setDragOffset({
        x: e.clientX - rect.left - table.x * zoomLevel,
        y: e.clientY - rect.top - table.y * zoomLevel
      });
    }
  };

  const handleMouseMove = (e: React.MouseEvent) => {
    if (!isDragging || !selectedTable || !floorRef.current) return;

    const rect = floorRef.current.getBoundingClientRect();
    const newX = (e.clientX - rect.left - dragOffset.x) / zoomLevel;
    const newY = (e.clientY - rect.top - dragOffset.y) / zoomLevel;

    // Update table position
    setFloorData(prev => prev ? {
      ...prev,
      tables: prev.tables.map(t => 
        t.id === selectedTable.id 
          ? { ...t, x: Math.max(0, newX), y: Math.max(0, newY) }
          : t
      )
    } : null);
  };

  const handleMouseUp = () => {
    if (isDragging && selectedTable) {
      // Save position to backend
      const table = floorData?.tables.find(t => t.id === selectedTable.id);
      if (table) {
        updateTable();
      }
    }
    setIsDragging(false);
    setDragOffset({ x: 0, y: 0 });
  };

  const filteredTables = floorData?.tables.filter(table => {
    const matchesSection = selectedSection === 'all' || table.section === selectedSection;
    const matchesSearch = !searchTerm || 
      table.number.toString().includes(searchTerm) ||
      table.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      table.serverName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || table.status === statusFilter;
    
    return matchesSection && matchesSearch && matchesStatus;
  }) || [];

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading floor layout...</span>
      </div>
    );
  }

  return (
    <div className="h-full flex flex-col bg-gray-50">
      {/* Header Controls */}
      <div className="bg-white border-b border-gray-200 p-4">
        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-bold text-gray-900">Floor Layout</h2>
            <span className="text-sm text-gray-500">
              {filteredTables.length} tables
            </span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setShowLegend(!showLegend)}
              className={`p-2 rounded-md ${showLegend ? 'bg-blue-100 text-blue-600' : 'bg-gray-100 text-gray-600'}`}
            >
              {showLegend ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
            </button>
            
            <button
              onClick={() => setIsEditMode(!isEditMode)}
              className={`px-3 py-2 rounded-md font-medium ${
                isEditMode 
                  ? 'bg-orange-100 text-orange-700 border border-orange-300' 
                  : 'bg-gray-100 text-gray-700 border border-gray-300'
              }`}
            >
              <Edit3 className="h-4 w-4 mr-2 inline" />
              {isEditMode ? 'Exit Edit' : 'Edit Mode'}
            </button>
            
            <button
              onClick={() => {
                setSelectedTable(null);
                setNewTable({
                  number: Math.max(...(floorData?.tables.map(t => t.number) || [0]), 0) + 1,
                  name: '',
                  seats: 4,
                  width: 80,
                  height: 80,
                  shape: 'rectangle',
                  tableType: 'regular',
                  section: floorData?.sections[0]?.id || '',
                  x: 100,
                  y: 100,
                  rotation: 0
                });
                setShowTableModal(true);
              }}
              className="px-3 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              <Plus className="h-4 w-4 mr-2 inline" />
              Add Table
            </button>
            
            <button
              onClick={fetchFloorLayout}
              className="p-2 text-gray-600 hover:text-gray-900"
            >
              <RefreshCw className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Filters and Search */}
        <div className="flex items-center space-x-4">
          <div className="flex-1 max-w-md">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search tables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent w-full"
              />
            </div>
          </div>
          
          <select
            value={selectedSection}
            onChange={(e) => setSelectedSection(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Sections</option>
            {floorData?.sections.map(section => (
              <option key={section.id} value={section.id}>{section.name}</option>
            ))}
          </select>
          
          <select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="all">All Status</option>
            <option value="available">Available</option>
            <option value="occupied">Occupied</option>
            <option value="reserved">Reserved</option>
            <option value="needs-cleaning">Needs Cleaning</option>
            <option value="out-of-order">Out of Order</option>
          </select>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={() => setZoomLevel(Math.max(0.5, zoomLevel - 0.1))}
              className="p-2 text-gray-600 hover:text-gray-900"
            >
              <Minimize2 className="h-4 w-4" />
            </button>
            <span className="text-sm text-gray-600 min-w-[3rem] text-center">
              {Math.round(zoomLevel * 100)}%
            </span>
            <button
              onClick={() => setZoomLevel(Math.min(2, zoomLevel + 0.1))}
              className="p-2 text-gray-600 hover:text-gray-900"
            >
              <Maximize2 className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Floor Layout Canvas */}
        <div className="flex-1 overflow-auto bg-gray-100 p-4">
          <div
            ref={floorRef}
            className="relative bg-white border border-gray-300 rounded-lg shadow-sm"
            style={{
              width: (floorData?.width || 800) * zoomLevel,
              height: (floorData?.height || 600) * zoomLevel,
              transform: `scale(${zoomLevel})`,
              transformOrigin: 'top left'
            }}
            onMouseMove={handleMouseMove}
            onMouseUp={handleMouseUp}
            onMouseLeave={handleMouseUp}
          >
            {/* Sections */}
            {floorData?.sections.map(section => (
              <div
                key={section.id}
                className="absolute border-2 border-dashed opacity-30 rounded-lg"
                style={{
                  left: section.x,
                  top: section.y,
                  width: section.width,
                  height: section.height,
                  borderColor: section.color,
                  backgroundColor: section.color + '20'
                }}
              >
                <div 
                  className="absolute top-2 left-2 text-xs font-medium px-2 py-1 rounded"
                  style={{ backgroundColor: section.color, color: 'white' }}
                >
                  {section.name}
                </div>
              </div>
            ))}

            {/* Tables */}
            {filteredTables.map(table => (
              <div
                key={table.id}
                className={`absolute border-2 rounded-lg cursor-pointer transition-all duration-200 ${
                  getTableStatusColor(table.status, table.substatus)
                } ${selectedTable?.id === table.id ? 'ring-2 ring-blue-400' : ''} ${
                  isEditMode ? 'hover:ring-2 hover:ring-gray-400' : 'hover:shadow-lg'
                }`}
                style={{
                  left: table.x,
                  top: table.y,
                  width: table.width,
                  height: table.height,
                  borderRadius: table.shape === 'circle' ? '50%' : table.shape === 'oval' ? '50%' : '8px',
                  transform: `rotate(${table.rotation || 0}deg)`
                }}
                onClick={() => handleTableClick(table)}
                onMouseDown={(e) => handleMouseDown(e, table)}
              >
                <div className="h-full flex flex-col items-center justify-center p-2 text-center">
                  <div className="flex items-center space-x-1 mb-1">
                    {getTableStatusIcon(table.status, table.substatus)}
                    <span className="font-bold text-sm">{table.number}</span>
                  </div>
                  
                  {table.name && (
                    <div className="text-xs opacity-90 truncate w-full">{table.name}</div>
                  )}
                  
                  <div className="text-xs opacity-75">
                    {table.guestCount || 0}/{table.seats}
                  </div>
                  
                  {table.orderTotal && (
                    <div className="text-xs font-medium">
                      ${table.orderTotal.toFixed(2)}
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Legend */}
        {showLegend && (
          <div className="w-64 bg-white border-l border-gray-200 p-4">
            <h3 className="font-semibold text-gray-900 mb-4">Table Status Legend</h3>
            <div className="space-y-3">
              {[
                { status: 'available', label: 'Available', icon: <CheckCircle className="h-4 w-4" /> },
                { status: 'occupied', substatus: 'ordering', label: 'Ordering', icon: <Utensils className="h-4 w-4" /> },
                { status: 'occupied', substatus: 'eating', label: 'Eating', icon: <Coffee className="h-4 w-4" /> },
                { status: 'occupied', substatus: 'waiting-for-check', label: 'Waiting for Check', icon: <Clock className="h-4 w-4" /> },
                { status: 'occupied', substatus: 'paying', label: 'Paying', icon: <CreditCard className="h-4 w-4" /> },
                { status: 'reserved', label: 'Reserved', icon: <Clock className="h-4 w-4" /> },
                { status: 'needs-cleaning', label: 'Needs Cleaning', icon: <AlertCircle className="h-4 w-4" /> },
                { status: 'out-of-order', label: 'Out of Order', icon: <X className="h-4 w-4" /> }
              ].map((item, index) => (
                <div key={index} className="flex items-center space-x-3">
                  <div className={`w-8 h-8 rounded border-2 flex items-center justify-center ${
                    getTableStatusColor(item.status as Table['status'], item.substatus as Table['substatus'])
                  }`}>
                    {item.icon}
                  </div>
                  <span className="text-sm text-gray-700">{item.label}</span>
                </div>
              ))}
            </div>
            
            {isEditMode && (
              <div className="mt-6 pt-4 border-t border-gray-200">
                <h4 className="font-medium text-gray-900 mb-2">Edit Mode</h4>
                <p className="text-xs text-gray-600">
                  • Click tables to edit properties
                  • Drag tables to move them
                  • Use controls to add/delete tables
                </p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Table Creation/Edit Modal */}
      {showTableModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                {selectedTable ? 'Edit Table' : 'Create New Table'}
              </h3>
              <button
                onClick={() => {
                  setShowTableModal(false);
                  setSelectedTable(null);
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6 space-y-6">
              {/* Basic Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Table Number *
                  </label>
                  <input
                    type="number"
                    value={newTable.number || ''}
                    onChange={(e) => setNewTable({...newTable, number: parseInt(e.target.value) || 1})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="1"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Table Name (Optional)
                  </label>
                  <input
                    type="text"
                    value={newTable.name || ''}
                    onChange={(e) => setNewTable({...newTable, name: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="e.g., VIP Table, Window Seat"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Seats *
                  </label>
                  <input
                    type="number"
                    value={newTable.seats || ''}
                    onChange={(e) => setNewTable({...newTable, seats: parseInt(e.target.value) || 1})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="1"
                    max="20"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Section *
                  </label>
                  <select
                    value={newTable.section || ''}
                    onChange={(e) => setNewTable({...newTable, section: e.target.value})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    required
                  >
                    <option value="">Select Section</option>
                    {floorData?.sections.map(section => (
                      <option key={section.id} value={section.id}>{section.name}</option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Table Properties */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Table Type
                  </label>
                  <select
                    value={newTable.tableType || 'regular'}
                    onChange={(e) => setNewTable({...newTable, tableType: e.target.value as Table['tableType']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="regular">Regular</option>
                    <option value="bar">Bar</option>
                    <option value="booth">Booth</option>
                    <option value="private">Private</option>
                    <option value="outdoor">Outdoor</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Shape
                  </label>
                  <select
                    value={newTable.shape || 'rectangle'}
                    onChange={(e) => setNewTable({...newTable, shape: e.target.value as Table['shape']})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="rectangle">Rectangle</option>
                    <option value="circle">Circle</option>
                    <option value="oval">Oval</option>
                  </select>
                </div>
              </div>

              {/* Size and Position */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Width (px)
                  </label>
                  <input
                    type="number"
                    value={newTable.width || 80}
                    onChange={(e) => setNewTable({...newTable, width: parseInt(e.target.value) || 80})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="40"
                    max="200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Height (px)
                  </label>
                  <input
                    type="number"
                    value={newTable.height || 80}
                    onChange={(e) => setNewTable({...newTable, height: parseInt(e.target.value) || 80})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="40"
                    max="200"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    X Position
                  </label>
                  <input
                    type="number"
                    value={newTable.x || 100}
                    onChange={(e) => setNewTable({...newTable, x: parseInt(e.target.value) || 100})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Y Position
                  </label>
                  <input
                    type="number"
                    value={newTable.y || 100}
                    onChange={(e) => setNewTable({...newTable, y: parseInt(e.target.value) || 100})}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    min="0"
                  />
                </div>
              </div>

              {/* Rotation */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Rotation (degrees)
                </label>
                <input
                  type="range"
                  min="0"
                  max="360"
                  step="15"
                  value={newTable.rotation || 0}
                  onChange={(e) => setNewTable({...newTable, rotation: parseInt(e.target.value)})}
                  className="w-full"
                />
                <div className="text-center text-sm text-gray-600 mt-1">
                  {newTable.rotation || 0}°
                </div>
              </div>

              {/* Preview */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="text-sm font-medium text-gray-700 mb-3">Preview</h4>
                <div className="relative bg-white border border-gray-200 rounded-lg" style={{ height: '120px' }}>
                  <div
                    className="absolute bg-blue-500 border-2 border-blue-600 text-white rounded-lg flex items-center justify-center"
                    style={{
                      left: '50%',
                      top: '50%',
                      width: Math.min(newTable.width || 80, 80),
                      height: Math.min(newTable.height || 80, 80),
                      transform: `translate(-50%, -50%) rotate(${newTable.rotation || 0}deg)`,
                      borderRadius: newTable.shape === 'circle' ? '50%' : newTable.shape === 'oval' ? '50%' : '8px'
                    }}
                  >
                    <div className="text-center">
                      <div className="font-bold text-sm">{newTable.number}</div>
                      {newTable.name && <div className="text-xs opacity-90">{newTable.name}</div>}
                      <div className="text-xs">0/{newTable.seats}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-between items-center p-6 border-t border-gray-200">
              {selectedTable && (
                <button
                  onClick={() => deleteTable(selectedTable.id)}
                  className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 flex items-center"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Table
                </button>
              )}

              <div className="flex space-x-3 ml-auto">
                <button
                  onClick={() => {
                    setShowTableModal(false);
                    setSelectedTable(null);
                  }}
                  className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
                >
                  Cancel
                </button>
                <button
                  onClick={selectedTable ? updateTable : createNewTable}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 flex items-center"
                >
                  <Save className="h-4 w-4 mr-2" />
                  {selectedTable ? 'Update Table' : 'Create Table'}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Table Selector Modal for Dine-in Orders */}
      {showTableSelector && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center p-6 border-b border-gray-200">
              <h3 className="text-lg font-semibold text-gray-900">
                Select Table for Dine-in Order
              </h3>
              <button
                onClick={() => setShowTableSelector(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            <div className="p-6">
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {floorData?.tables
                  .filter(table => table.status === 'available')
                  .map(table => (
                    <button
                      key={table.id}
                      onClick={() => assignOrderToTable(table)}
                      className="p-4 border-2 border-gray-200 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors text-center"
                    >
                      <div className="flex items-center justify-center mb-2">
                        <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                          getTableStatusColor(table.status)
                        }`}>
                          <span className="font-bold">{table.number}</span>
                        </div>
                      </div>
                      {table.name && (
                        <div className="text-sm text-gray-600 mb-1">{table.name}</div>
                      )}
                      <div className="text-sm text-gray-500">
                        {table.seats} seats • {table.tableType}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        Section: {floorData?.sections.find(s => s.id === table.section)?.name}
                      </div>
                    </button>
                  ))}
              </div>

              {floorData?.tables.filter(table => table.status === 'available').length === 0 && (
                <div className="text-center py-8">
                  <AlertCircle className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Available Tables</h3>
                  <p className="text-gray-500">All tables are currently occupied or unavailable.</p>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default EnhancedFloorLayoutManager;
