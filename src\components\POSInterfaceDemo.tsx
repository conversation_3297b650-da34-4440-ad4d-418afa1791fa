import React, { useState } from 'react';

interface POSInterfaceDemoProps {
  onBack: () => void;
}

const POSInterfaceDemo: React.FC<POSInterfaceDemoProps> = ({ onBack }) => {
  const [selectedInterface, setSelectedInterface] = useState<string>('current');

  const interfaceOptions = [
    {
      id: 'current',
      name: 'Current Enhanced POS',
      description: 'Modern, optimized POS interface with beautiful styling',
      pin: '111222 / 567890',
      features: ['Enhanced Visual Design', 'Real Database (23 products)', 'Interactive Shopping Cart', 'Payment Processing'],
      color: 'from-blue-600 to-indigo-700',
      icon: '🍽️',
      status: 'Recommended'
    },
    {
      id: 'original',
      name: 'Original POS System',
      description: 'Full-featured POS with AI analytics and advanced capabilities',
      pin: '999999',
      features: ['AI Fraud Detection', 'Multi-Currency Support', 'Sales Predictions', 'Advanced Analytics'],
      color: 'from-purple-600 to-indigo-700',
      icon: '🤖',
      status: 'Advanced'
    },
    {
      id: 'unified',
      name: 'Unified POS System',
      description: 'Enhanced unified interface with comprehensive features',
      pin: '999999',
      features: ['Unified Interface', 'Advanced Integration', 'Modern UI', 'Comprehensive Controls'],
      color: 'from-green-600 to-teal-700',
      icon: '🔧',
      status: 'Enhanced'
    },
    {
      id: 'industry',
      name: 'Industry-Specific POS',
      description: 'Specialized interfaces for different restaurant types',
      pin: '999999',
      features: ['Bar/Pub Interface', 'Cafe/Coffee Shop', 'Fine Dining', 'Food Truck', 'Quick Service', 'Hotel Restaurant', 'Catering'],
      color: 'from-orange-600 to-red-700',
      icon: '🏭',
      status: 'Specialized'
    }
  ];

  const accessMethods = [
    { pin: '111222', role: 'Employee', interface: 'Enhanced POS', description: 'Standard POS access for daily operations' },
    { pin: '567890', role: 'Manager', interface: 'Enhanced POS', description: 'Manager access with additional features' },
    { pin: '555666', role: 'Tenant Admin', interface: 'Super Admin', description: 'Currently routes to Super Admin Dashboard' },
    { pin: '123456', role: 'Super Admin', interface: 'Admin Dashboard', description: 'Complete system administration' },
    { pin: '999999', role: 'Any', interface: 'Original Collection', description: 'Access to all original interfaces' },
    { pin: '000000', role: 'Debug', interface: 'System Debugger', description: 'System diagnostics and troubleshooting' }
  ];

  const features = {
    current: [
      'Beautiful gradient header with live status indicators',
      'Interactive category filtering with hover animations',
      'Modern product grid with card-based layout',
      'Enhanced shopping cart with real-time updates',
      'Professional checkout with gradient buttons',
      'Real database integration (23 products, 6 categories)',
      'Responsive design for all devices',
      'Fast performance with optimized React components'
    ],
    original: [
      'AI-powered fraud detection and analytics',
      'Multi-currency support with live exchange rates',
      'Advanced order management and history',
      'Sales predictions and business insights',
      'Comprehensive payment processing',
      'Real-time analytics dashboard',
      'Customer behavior analysis',
      'Advanced reporting capabilities'
    ],
    unified: [
      'Enhanced unified interface design',
      'Advanced feature integration',
      'Comprehensive system controls',
      'Modern UI components',
      'Streamlined workflow',
      'Integrated management tools',
      'Cross-platform compatibility',
      'Scalable architecture'
    ],
    industry: [
      'Bar & Pub: Tab management, drink menu, happy hour pricing',
      'Cafe & Coffee: Quick orders, loyalty program, mobile orders',
      'Fine Dining: Course management, wine pairing, service timing',
      'Food Truck: Mobile optimization, location tracking, offline mode',
      'Quick Service: Speed optimization, drive-thru, combo meals',
      'Hotel Restaurant: Room service, guest billing, multiple outlets',
      'Catering: Event management, bulk orders, delivery scheduling'
    ]
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Header */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-xl">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-8">
            <div className="flex items-center space-x-6">
              <div className="bg-white/20 p-4 rounded-xl backdrop-blur-sm">
                <span className="text-4xl">🍽️</span>
              </div>
              <div>
                <h1 className="text-4xl font-bold bg-gradient-to-r from-white to-blue-100 bg-clip-text text-transparent">
                  POS Interface Demo
                </h1>
                <p className="text-blue-100 mt-2 text-lg">Explore all available POS interfaces and features</p>
              </div>
            </div>
            <button 
              onClick={onBack}
              className="bg-white/20 hover:bg-white/30 text-white px-6 py-3 rounded-xl font-medium transition-all duration-200 backdrop-blur-sm"
            >
              ← Back to Dashboard
            </button>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Interface Selection */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {interfaceOptions.map((option) => (
            <div 
              key={option.id} 
              className={`bg-white rounded-xl shadow-lg overflow-hidden cursor-pointer transition-all duration-300 transform hover:-translate-y-2 hover:shadow-xl ${
                selectedInterface === option.id ? 'ring-4 ring-blue-500' : ''
              }`}
              onClick={() => setSelectedInterface(option.id)}
            >
              <div className={`bg-gradient-to-r ${option.color} p-6 text-white`}>
                <div className="flex items-center justify-between mb-3">
                  <div className="text-4xl">{option.icon}</div>
                  <span className="bg-white/20 text-white px-3 py-1 rounded-full text-xs font-medium">
                    {option.status}
                  </span>
                </div>
                <h3 className="text-xl font-bold mb-2">{option.name}</h3>
                <p className="text-white/90 text-sm">{option.description}</p>
              </div>
              <div className="p-6">
                <div className="mb-4">
                  <div className="text-sm font-medium text-gray-600 mb-2">Access PIN:</div>
                  <div className="bg-gray-100 px-3 py-2 rounded-lg text-sm font-mono">{option.pin}</div>
                </div>
                <div className="space-y-2">
                  {option.features.slice(0, 3).map((feature, index) => (
                    <div key={index} className="flex items-center text-sm text-gray-600">
                      <span className="text-green-500 mr-2">✓</span>
                      {feature}
                    </div>
                  ))}
                  {option.features.length > 3 && (
                    <div className="text-sm text-gray-500">+{option.features.length - 3} more features</div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Selected Interface Details */}
        <div className="bg-white rounded-xl shadow-lg p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            {interfaceOptions.find(opt => opt.id === selectedInterface)?.name} - Detailed Features
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Features</h3>
              <div className="space-y-3">
                {features[selectedInterface as keyof typeof features]?.map((feature, index) => (
                  <div key={index} className="flex items-start">
                    <span className="text-green-500 mr-3 mt-1">✓</span>
                    <span className="text-gray-700">{feature}</span>
                  </div>
                ))}
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Interface Preview</h3>
              <div className="bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg p-6 text-center">
                <div className="text-6xl mb-4">
                  {interfaceOptions.find(opt => opt.id === selectedInterface)?.icon}
                </div>
                <p className="text-gray-600 mb-4">
                  {interfaceOptions.find(opt => opt.id === selectedInterface)?.description}
                </p>
                <div className="bg-blue-100 text-blue-800 px-4 py-2 rounded-lg text-sm font-medium">
                  PIN: {interfaceOptions.find(opt => opt.id === selectedInterface)?.pin}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Access Methods */}
        <div className="bg-white rounded-xl shadow-lg p-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🔑 All Access Methods</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {accessMethods.map((method, index) => (
              <div key={index} className="bg-gradient-to-br from-gray-50 to-gray-100 rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <div className="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-bold">
                    PIN: {method.pin}
                  </div>
                  <div className="text-sm text-gray-500">{method.role}</div>
                </div>
                <h3 className="font-bold text-gray-900 mb-2">{method.interface}</h3>
                <p className="text-sm text-gray-600">{method.description}</p>
              </div>
            ))}
          </div>
        </div>

        {/* Quick Start Guide */}
        <div className="mt-8 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-8 border border-blue-200">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">🚀 Quick Start Guide</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">For Daily Operations</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="bg-green-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                  <span>Open http://localhost:5173</span>
                </div>
                <div className="flex items-center">
                  <span className="bg-green-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                  <span>Enter PIN 111222 (Employee) or 567890 (Manager)</span>
                </div>
                <div className="flex items-center">
                  <span className="bg-green-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                  <span>Access Enhanced POS with modern design</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">For Advanced Features</h3>
              <div className="space-y-3">
                <div className="flex items-center">
                  <span className="bg-purple-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">1</span>
                  <span>Open http://localhost:5173</span>
                </div>
                <div className="flex items-center">
                  <span className="bg-purple-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">2</span>
                  <span>Enter PIN 999999</span>
                </div>
                <div className="flex items-center">
                  <span className="bg-purple-500 text-white w-6 h-6 rounded-full flex items-center justify-center text-sm font-bold mr-3">3</span>
                  <span>Choose from Original or Industry-specific interfaces</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default POSInterfaceDemo;
