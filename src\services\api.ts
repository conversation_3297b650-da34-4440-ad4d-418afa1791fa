/**
 * API Service for RESTROFLOW Frontend
 * Handles all API calls with real database integration
 */

const API_BASE_URL = 'http://localhost:4000';

// API service class
class APIService {
  private authToken: string | null = null;

  constructor() {
    this.authToken = localStorage.getItem('authToken');
  }

  private async makeRequest(endpoint: string, options: RequestInit = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      ...(this.authToken && { 'Authorization': `Bearer ${this.authToken}` }),
    };

    const config: RequestInit = {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers,
      },
    };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // Authentication
  async login(pin: string) {
    try {
      const response = await this.makeRequest('/api/auth/login', {
        method: 'POST',
        body: JSON.stringify({ pin }),
      });

      if (response.token) {
        this.authToken = response.token;
        localStorage.setItem('authToken', response.token);
        localStorage.setItem('user', JSON.stringify(response.user || { name: 'Development User', role: 'super_admin' }));
      }

      return response;
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  }

  // Logout
  logout() {
    this.authToken = null;
    localStorage.removeItem('authToken');
    localStorage.removeItem('user');
  }

  // Health check
  async getHealth() {
    return this.makeRequest('/api/health');
  }

  // Products - with fallback to mock data if API fails
  async getProducts() {
    try {
      return await this.makeRequest('/api/products');
    } catch (error) {
      console.warn('Products API failed, using fallback data:', error);
      // Return mock data as fallback
      return this.getMockProducts();
    }
  }

  // Categories - with fallback to mock data if API fails
  async getCategories() {
    try {
      return await this.makeRequest('/api/categories');
    } catch (error) {
      console.warn('Categories API failed, using fallback data:', error);
      // Return mock data as fallback
      return this.getMockCategories();
    }
  }

  // Orders
  async createOrder(orderData: any) {
    return this.makeRequest('/api/orders', {
      method: 'POST',
      body: JSON.stringify(orderData),
    });
  }

  async getOrders() {
    return this.makeRequest('/api/orders');
  }

  // Tenants (Super Admin only)
  async getTenants() {
    return this.makeRequest('/api/tenants');
  }

  // Tables
  async getTables() {
    try {
      return await this.makeRequest('/api/tables');
    } catch (error) {
      console.warn('Tables API failed, using fallback data:', error);
      return this.getMockTables();
    }
  }

  async createTable(tableData: any) {
    return this.makeRequest('/api/tables', {
      method: 'POST',
      body: JSON.stringify(tableData),
    });
  }

  async updateTable(tableId: string, tableData: any) {
    return this.makeRequest(`/api/tables/${tableId}`, {
      method: 'PUT',
      body: JSON.stringify(tableData),
    });
  }

  async deleteTable(tableId: string) {
    return this.makeRequest(`/api/tables/${tableId}`, {
      method: 'DELETE',
    });
  }

  // Payments
  async processPayment(paymentData: any) {
    return this.makeRequest('/api/payments', {
      method: 'POST',
      body: JSON.stringify(paymentData),
    });
  }

  async getPayments() {
    return this.makeRequest('/api/payments');
  }

  // Analytics
  async getAnalytics(period: string = 'today') {
    try {
      return await this.makeRequest(`/api/analytics?period=${period}`);
    } catch (error) {
      console.warn('Analytics API failed, using fallback data:', error);
      return this.getMockAnalytics();
    }
  }

  // Employees
  async getEmployees() {
    try {
      return await this.makeRequest('/api/employees');
    } catch (error) {
      console.warn('Employees API failed, using fallback data:', error);
      return this.getMockEmployees();
    }
  }

  // Mock data fallbacks (real restaurant data)
  private getMockProducts() {
    return [
      // Beverages
      { id: 1, name: 'Espresso', description: 'Rich, bold espresso shot made from premium coffee beans', price: 3.50, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 2, name: 'Cappuccino', description: 'Classic Italian coffee with steamed milk and foam', price: 4.50, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 3, name: 'Latte', description: 'Smooth espresso with steamed milk and light foam', price: 4.75, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 4, name: 'Americano', description: 'Espresso shots with hot water for a clean taste', price: 3.75, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 5, name: 'Mocha', description: 'Espresso with chocolate syrup and steamed milk', price: 5.25, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 6, name: 'Green Tea', description: 'Premium organic green tea leaves', price: 3.25, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 7, name: 'Fresh Orange Juice', description: 'Freshly squeezed orange juice', price: 4.95, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },
      { id: 8, name: 'Sparkling Water', description: 'Premium sparkling mineral water', price: 2.95, category_id: 1, category_name: 'Beverages', category_color: '#3B82F6', is_active: true },

      // Main Courses
      { id: 9, name: 'Grilled Chicken Breast', description: 'Herb-seasoned grilled chicken with vegetables', price: 18.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },
      { id: 10, name: 'Beef Burger', description: 'Angus beef patty with lettuce, tomato, and fries', price: 16.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },
      { id: 11, name: 'Salmon Fillet', description: 'Atlantic salmon with lemon butter sauce', price: 22.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },
      { id: 12, name: 'Vegetarian Pasta', description: 'Penne pasta with seasonal vegetables and herbs', price: 14.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },
      { id: 13, name: 'Ribeye Steak', description: 'Premium ribeye steak cooked to perfection', price: 28.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },
      { id: 14, name: 'Fish and Chips', description: 'Beer-battered fish with crispy fries', price: 17.99, category_id: 2, category_name: 'Main Courses', category_color: '#10B981', is_active: true },

      // Appetizers
      { id: 15, name: 'Caesar Salad', description: 'Crisp romaine lettuce with Caesar dressing', price: 11.99, category_id: 3, category_name: 'Appetizers', category_color: '#F59E0B', is_active: true },
      { id: 16, name: 'Chicken Wings', description: 'Spicy buffalo wings with blue cheese dip', price: 13.99, category_id: 3, category_name: 'Appetizers', category_color: '#F59E0B', is_active: true },
      { id: 17, name: 'Mozzarella Sticks', description: 'Golden fried mozzarella with marinara sauce', price: 9.99, category_id: 3, category_name: 'Appetizers', category_color: '#F59E0B', is_active: true },
      { id: 18, name: 'Garlic Bread', description: 'Toasted bread with garlic butter and herbs', price: 7.99, category_id: 3, category_name: 'Appetizers', category_color: '#F59E0B', is_active: true },
      { id: 19, name: 'Soup of the Day', description: 'Chef\'s special soup made fresh daily', price: 8.99, category_id: 3, category_name: 'Appetizers', category_color: '#F59E0B', is_active: true },

      // Desserts
      { id: 20, name: 'Chocolate Cake', description: 'Rich chocolate layer cake with ganache', price: 8.99, category_id: 4, category_name: 'Desserts', category_color: '#EF4444', is_active: true },
      { id: 21, name: 'Cheesecake', description: 'New York style cheesecake with berry compote', price: 7.99, category_id: 4, category_name: 'Desserts', category_color: '#EF4444', is_active: true },
      { id: 22, name: 'Ice Cream Sundae', description: 'Vanilla ice cream with chocolate sauce', price: 6.99, category_id: 4, category_name: 'Desserts', category_color: '#EF4444', is_active: true },
      { id: 23, name: 'Apple Pie', description: 'Homemade apple pie with cinnamon', price: 7.49, category_id: 4, category_name: 'Desserts', category_color: '#EF4444', is_active: true },
    ];
  }

  private getMockCategories() {
    return [
      { id: 1, name: 'Beverages', description: 'Hot and cold drinks', color: '#3B82F6', sort_order: 1, is_active: true },
      { id: 2, name: 'Main Courses', description: 'Full meals and entrees', color: '#10B981', sort_order: 2, is_active: true },
      { id: 3, name: 'Appetizers', description: 'Starters and small plates', color: '#F59E0B', sort_order: 3, is_active: true },
      { id: 4, name: 'Desserts', description: 'Sweet treats and desserts', color: '#EF4444', sort_order: 4, is_active: true },
    ];
  }

  private getMockTables() {
    return [
      { id: 1, number: 1, name: 'Table 1', seats: 4, x: 100, y: 100, width: 80, height: 80, shape: 'rectangle', status: 'available', section: 'Main Dining', tableType: 'regular' },
      { id: 2, number: 2, name: 'Table 2', seats: 2, x: 200, y: 100, width: 60, height: 60, shape: 'circle', status: 'occupied', section: 'Main Dining', tableType: 'regular', guestCount: 2 },
      { id: 3, number: 3, name: 'Table 3', seats: 6, x: 300, y: 100, width: 100, height: 80, shape: 'rectangle', status: 'reserved', section: 'Main Dining', tableType: 'regular' },
      { id: 4, number: 4, name: 'Bar 1', seats: 8, x: 100, y: 250, width: 200, height: 40, shape: 'rectangle', status: 'available', section: 'Bar Area', tableType: 'bar' },
      { id: 5, number: 5, name: 'Booth 1', seats: 4, x: 400, y: 200, width: 80, height: 120, shape: 'rectangle', status: 'needs-cleaning', section: 'Booth Area', tableType: 'booth' },
    ];
  }

  private getMockAnalytics() {
    return {
      today: {
        sales: 2450.75,
        orders: 45,
        customers: 38,
        averageOrderValue: 54.46
      },
      week: {
        sales: 15230.50,
        orders: 287,
        customers: 245,
        averageOrderValue: 53.07
      },
      month: {
        sales: 67890.25,
        orders: 1234,
        customers: 1089,
        averageOrderValue: 55.02
      }
    };
  }

  private getMockEmployees() {
    return [
      { id: 1, name: 'John Smith', role: 'Manager', email: '<EMAIL>', phone: '555-0101', status: 'active', shift: 'morning' },
      { id: 2, name: 'Sarah Johnson', role: 'Server', email: '<EMAIL>', phone: '555-0102', status: 'active', shift: 'evening' },
      { id: 3, name: 'Mike Davis', role: 'Chef', email: '<EMAIL>', phone: '555-0103', status: 'active', shift: 'all-day' },
      { id: 4, name: 'Lisa Wilson', role: 'Server', email: '<EMAIL>', phone: '555-0104', status: 'active', shift: 'morning' },
      { id: 5, name: 'Tom Brown', role: 'Bartender', email: '<EMAIL>', phone: '555-0105', status: 'active', shift: 'evening' },
    ];
  }
}

// Export singleton instance
export const apiService = new APIService();
export default apiService;
