import React, { useState, useEffect } from 'react';
import { 
  Clock, 
  DollarSign, 
  Users, 
  TrendingUp, 
  TrendingDown,
  BarChart3,
  Target,
  AlertCircle,
  CheckCircle,
  RefreshCw
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface TableMetric {
  table_id: string;
  table_number: number;
  section: string;
  turns_today: number;
  avg_turn_time: number;
  revenue_today: number;
  covers_today: number;
  efficiency_score: number;
  peak_hours: string[];
  avg_party_size: number;
  wait_time_accuracy: number;
}

interface TablePerformanceData {
  summary: {
    total_tables: number;
    avg_turn_time: number;
    avg_revenue_per_table: number;
    peak_utilization: number;
    total_covers_today: number;
  };
  table_metrics: TableMetric[];
  turn_time_trends: Array<{
    hour: number;
    avg_turn_time: number;
    table_count: number;
    efficiency: number;
  }>;
  section_performance: Array<{
    section: string;
    tables: number;
    avg_turn_time: number;
    revenue_today: number;
    utilization: number;
    covers: number;
  }>;
}

const TablePerformanceAnalytics: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [performanceData, setPerformanceData] = useState<TablePerformanceData | null>(null);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'today' | '7d' | '30d'>('today');
  const [selectedSection, setSelectedSection] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'efficiency' | 'revenue' | 'turns'>('efficiency');
  const [isLoading, setIsLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    fetchTablePerformance();
    const interval = setInterval(fetchTablePerformance, 60000); // Update every minute
    return () => clearInterval(interval);
  }, [selectedTimeRange]);

  const fetchTablePerformance = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall(`/api/analytics/table-performance?range=${selectedTimeRange}`);
      
      if (response.ok) {
        const data = await response.json();
        setPerformanceData(data);
        setLastUpdated(new Date());
      }
    } catch (error) {
      console.error('Error fetching table performance:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = Math.round(minutes % 60);
    return hours > 0 ? `${hours}h ${mins}m` : `${mins}m`;
  };

  const getEfficiencyColor = (score: number) => {
    if (score >= 90) return 'text-green-600 bg-green-100';
    if (score >= 80) return 'text-yellow-600 bg-yellow-100';
    return 'text-red-600 bg-red-100';
  };

  const getEfficiencyIcon = (score: number) => {
    if (score >= 90) return <CheckCircle className="h-4 w-4" />;
    if (score >= 80) return <Target className="h-4 w-4" />;
    return <AlertCircle className="h-4 w-4" />;
  };

  const filteredTables = performanceData?.table_metrics.filter(table => 
    selectedSection === 'all' || table.section === selectedSection
  ).sort((a, b) => {
    switch (sortBy) {
      case 'efficiency':
        return b.efficiency_score - a.efficiency_score;
      case 'revenue':
        return b.revenue_today - a.revenue_today;
      case 'turns':
        return b.turns_today - a.turns_today;
      default:
        return 0;
    }
  }) || [];

  const sections = [...new Set(performanceData?.table_metrics.map(t => t.section) || [])];

  if (isLoading && !performanceData) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading table performance...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Table Performance Analytics</h2>
          <p className="text-gray-600">Monitor table efficiency, turn times, and revenue performance</p>
        </div>
        <div className="flex items-center space-x-4">
          <button
            onClick={fetchTablePerformance}
            className="flex items-center px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </button>
          {lastUpdated && (
            <span className="text-sm text-gray-500">
              Last updated: {lastUpdated.toLocaleTimeString()}
            </span>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-wrap gap-4 bg-white p-4 rounded-lg shadow-sm border">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Time Range</label>
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="today">Today</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Section</label>
          <select
            value={selectedSection}
            onChange={(e) => setSelectedSection(e.target.value)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="all">All Sections</option>
            {sections.map(section => (
              <option key={section} value={section}>{section}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Sort By</label>
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value as any)}
            className="border border-gray-300 rounded-md px-3 py-2 text-sm"
          >
            <option value="efficiency">Efficiency Score</option>
            <option value="revenue">Revenue</option>
            <option value="turns">Table Turns</option>
          </select>
        </div>
      </div>

      {/* Summary Cards */}
      {performanceData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-blue-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Tables</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.total_tables}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-green-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Turn Time</p>
                <p className="text-2xl font-bold text-gray-900">{formatTime(performanceData.summary.avg_turn_time)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <DollarSign className="h-8 w-8 text-purple-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Avg Revenue/Table</p>
                <p className="text-2xl font-bold text-gray-900">{formatCurrency(performanceData.summary.avg_revenue_per_table)}</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-orange-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Peak Utilization</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.peak_utilization.toFixed(1)}%</p>
              </div>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm border">
            <div className="flex items-center">
              <Users className="h-8 w-8 text-indigo-600" />
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Covers</p>
                <p className="text-2xl font-bold text-gray-900">{performanceData.summary.total_covers_today}</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Section Performance */}
      {performanceData && (
        <div className="bg-white rounded-lg shadow-sm border">
          <div className="p-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Section Performance</h3>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {performanceData.section_performance.map((section) => (
                <div key={section.section} className="border rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">{section.section}</h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Tables:</span>
                      <span className="font-medium">{section.tables}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Avg Turn Time:</span>
                      <span className="font-medium">{formatTime(section.avg_turn_time)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Revenue:</span>
                      <span className="font-medium">{formatCurrency(section.revenue_today)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Utilization:</span>
                      <span className="font-medium">{section.utilization}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Covers:</span>
                      <span className="font-medium">{section.covers}</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Individual Table Performance */}
      <div className="bg-white rounded-lg shadow-sm border">
        <div className="p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Individual Table Performance</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Table</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Efficiency</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Turns</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Turn Time</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Revenue</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Covers</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Peak Hours</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredTables.map((table) => (
                <tr key={table.table_id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-gray-900">Table {table.table_number}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{table.section}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getEfficiencyColor(table.efficiency_score)}`}>
                      {getEfficiencyIcon(table.efficiency_score)}
                      <span className="ml-1">{table.efficiency_score}%</span>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{table.turns_today}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatTime(table.avg_turn_time)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{formatCurrency(table.revenue_today)}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{table.covers_today}</td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {table.peak_hours.join(', ')}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default TablePerformanceAnalytics;
