#!/usr/bin/env node

/**
 * RESTROFLOW COMPLETE SYSTEM LAUNCHER
 * 
 * This script launches the complete, optimized RESTROFLOW system
 * with clean backend and frontend servers.
 */

const { spawn } = require('child_process');
const fs = require('fs');

class RestroflowLauncher {
    constructor() {
        this.processes = new Map();
        this.isShuttingDown = false;
    }

    log(message, type = 'info') {
        const timestamp = new Date().toISOString();
        const prefix = {
            info: '🚀',
            success: '✅',
            warning: '⚠️',
            error: '❌',
            system: '🔧'
        }[type] || '🚀';
        
        console.log(`${prefix} [${timestamp}] ${message}`);
    }

    async launch() {
        console.log('\n' + '='.repeat(80));
        console.log('🌟 RESTROFLOW COMPLETE SYSTEM LAUNCHER');
        console.log('='.repeat(80));
        
        try {
            await this.performSystemChecks();
            await this.startBackendServer();
            await this.startFrontendServer();
            await this.setupGracefulShutdown();
            
            this.displaySystemInfo();
            this.log('🎉 RESTROFLOW COMPLETE SYSTEM LAUNCHED SUCCESSFULLY!', 'success');
            
        } catch (error) {
            this.log(`💥 Launch failed: ${error.message}`, 'error');
            await this.shutdown();
            process.exit(1);
        }
    }

    async performSystemChecks() {
        this.log('🔍 Performing system checks...', 'system');
        
        // Check if required files exist
        const requiredFiles = [
            'clean-backend-server.js',
            'clean-pos-system.html'
        ];

        for (const file of requiredFiles) {
            if (!fs.existsSync(file)) {
                throw new Error(`Required file missing: ${file}`);
            }
        }

        this.log('✅ All required files found', 'success');
    }

    async startBackendServer() {
        this.log('🔧 Starting clean backend server...', 'system');
        
        const backendProcess = spawn('node', ['clean-backend-server.js'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                NODE_ENV: 'production',
                PORT: '4000'
            }
        });

        this.processes.set('backend', backendProcess);

        // Handle backend output
        backendProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                console.log(`[BACKEND] ${output}`);
            }
        });

        backendProcess.stderr.on('data', (data) => {
            const output = data.toString().trim();
            if (output) {
                console.error(`[BACKEND ERROR] ${output}`);
            }
        });

        backendProcess.on('exit', (code) => {
            this.log(`Backend server exited with code ${code}`, code === 0 ? 'info' : 'error');
            if (!this.isShuttingDown && code !== 0) {
                this.log('🔄 Attempting to restart backend server...', 'warning');
                setTimeout(() => this.startBackendServer(), 5000);
            }
        });

        // Wait for backend to be ready
        await this.waitForBackend();
        this.log('✅ Backend server started successfully', 'success');
    }

    async startFrontendServer() {
        this.log('🎨 Starting frontend server...', 'system');
        
        // Check if frontend server is already running
        try {
            const response = await fetch('http://localhost:5173');
            if (response.ok) {
                this.log('✅ Frontend server already running on port 5173', 'success');
                return;
            }
        } catch (error) {
            // Frontend not running, we'll start it
        }

        const frontendProcess = spawn('npx', ['serve', '.', '-p', '5173'], {
            stdio: ['pipe', 'pipe', 'pipe'],
            env: {
                ...process.env,
                NODE_ENV: 'production'
            }
        });

        this.processes.set('frontend', frontendProcess);

        // Handle frontend output
        frontendProcess.stdout.on('data', (data) => {
            const output = data.toString().trim();
            if (output && !output.includes('webpack')) {
                console.log(`[FRONTEND] ${output}`);
            }
        });

        frontendProcess.stderr.on('data', (data) => {
            const output = data.toString().trim();
            if (output && !output.includes('webpack')) {
                console.error(`[FRONTEND ERROR] ${output}`);
            }
        });

        frontendProcess.on('exit', (code) => {
            this.log(`Frontend server exited with code ${code}`, code === 0 ? 'info' : 'error');
            if (!this.isShuttingDown && code !== 0) {
                this.log('🔄 Attempting to restart frontend server...', 'warning');
                setTimeout(() => this.startFrontendServer(), 5000);
            }
        });

        // Wait for frontend to be ready
        await this.waitForFrontend();
        this.log('✅ Frontend server started successfully', 'success');
    }

    async waitForBackend(maxAttempts = 30) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const response = await fetch('http://localhost:4000/api/health');
                if (response.ok) {
                    return;
                }
            } catch (error) {
                if (attempt === maxAttempts) {
                    throw new Error('Backend server failed to start');
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    async waitForFrontend(maxAttempts = 30) {
        for (let attempt = 1; attempt <= maxAttempts; attempt++) {
            try {
                const response = await fetch('http://localhost:5173');
                if (response.ok) {
                    return;
                }
            } catch (error) {
                if (attempt === maxAttempts) {
                    throw new Error('Frontend server failed to start');
                }
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
        }
    }

    async setupGracefulShutdown() {
        const signals = ['SIGINT', 'SIGTERM', 'SIGQUIT'];
        
        signals.forEach(signal => {
            process.on(signal, async () => {
                this.log(`🛑 Received ${signal}, initiating graceful shutdown...`);
                await this.shutdown();
                process.exit(0);
            });
        });

        process.on('uncaughtException', async (error) => {
            this.log(`💥 Uncaught exception: ${error.message}`, 'error');
            await this.shutdown();
            process.exit(1);
        });

        process.on('unhandledRejection', async (reason, promise) => {
            this.log(`💥 Unhandled rejection: ${reason}`, 'error');
            await this.shutdown();
            process.exit(1);
        });
    }

    async shutdown() {
        if (this.isShuttingDown) return;
        
        this.isShuttingDown = true;
        this.log('🛑 Shutting down RESTROFLOW system...', 'system');

        // Gracefully stop all processes
        for (const [name, process] of this.processes) {
            this.log(`🛑 Stopping ${name} server...`, 'system');
            
            try {
                process.kill('SIGTERM');
                
                // Wait for graceful shutdown
                await new Promise((resolve) => {
                    const timeout = setTimeout(() => {
                        this.log(`⚠️ Force killing ${name} server`, 'warning');
                        process.kill('SIGKILL');
                        resolve();
                    }, 5000);
                    
                    process.on('exit', () => {
                        clearTimeout(timeout);
                        resolve();
                    });
                });
                
                this.log(`✅ ${name} server stopped`, 'success');
            } catch (error) {
                this.log(`❌ Error stopping ${name}: ${error.message}`, 'error');
            }
        }

        this.log('✅ RESTROFLOW system shutdown completed', 'success');
    }

    displaySystemInfo() {
        console.log('\n' + '='.repeat(80));
        console.log('🌟 RESTROFLOW COMPLETE SYSTEM - OPERATIONAL');
        console.log('='.repeat(80));
        console.log('📡 Backend Server:  http://localhost:4000');
        console.log('🎨 Frontend Server: http://localhost:5173');
        console.log('');
        console.log('🔑 Test Credentials:');
        console.log('   👑 Super Admin: PIN 123456');
        console.log('   👨‍💼 Manager:     PIN 567890');
        console.log('   👤 Employee:    PIN 111222, 555666');
        console.log('');
        console.log('🌐 Access Points:');
        console.log('   📱 Clean POS System: http://localhost:5173/clean-pos-system.html');
        console.log('   👑 Super Admin:      http://localhost:5173/project/super-admin.html');
        console.log('   🔐 Login Portal:     http://localhost:5173/login.html');
        console.log('   📊 Dashboard:        http://localhost:5173/dashboard.html');
        console.log('');
        console.log('📊 API Endpoints:');
        console.log('   🔍 Health Check:     http://localhost:4000/api/health');
        console.log('   🔐 Authentication:   http://localhost:4000/api/auth/login');
        console.log('   📦 Products:         http://localhost:4000/api/products');
        console.log('   🛒 Orders:           http://localhost:4000/api/orders');
        console.log('   📊 Analytics:        http://localhost:4000/api/analytics/sales');
        console.log('   👥 Employees:        http://localhost:4000/api/employees');
        console.log('   📦 Inventory:        http://localhost:4000/api/inventory');
        console.log('   🍳 Kitchen Orders:   http://localhost:4000/api/kitchen/orders');
        console.log('');
        console.log('✨ Features:');
        console.log('   ✅ Complete POS Interface with Menu & Cart');
        console.log('   ✅ Authentication System (PIN-based)');
        console.log('   ✅ Order Management & Processing');
        console.log('   ✅ Real-time Analytics & Reporting');
        console.log('   ✅ Inventory Management');
        console.log('   ✅ Employee Management');
        console.log('   ✅ Kitchen Display System');
        console.log('   ✅ Multi-category Product Management');
        console.log('   ✅ Responsive Design (Mobile & Desktop)');
        console.log('   ✅ Real-time System Status Monitoring');
        console.log('');
        console.log('🛑 To stop the system: Press Ctrl+C');
        console.log('='.repeat(80));
    }
}

// Global fetch polyfill for Node.js
if (typeof fetch === 'undefined') {
    global.fetch = require('node-fetch');
}

// Main execution
if (require.main === module) {
    const launcher = new RestroflowLauncher();
    launcher.launch().catch(error => {
        console.error('💥 System launch failed:', error);
        process.exit(1);
    });
}

module.exports = RestroflowLauncher;
