<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - POS System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .hidden { display: none !important; }
        .show { display: block !important; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">RestroFlow</h1>
                <p class="text-gray-600">Enter your PIN to access the system</p>
            </div>

            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">PIN Code</label>
                    <input
                        type="password"
                        id="pinInput"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your PIN"
                    />
                </div>

                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    Invalid PIN. Please try again.
                </div>

                <button
                    onclick="login()"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Access System
                </button>
            </div>

            <div class="mt-6 text-center text-sm text-gray-500">
                <p>Demo PIN: 1234</p>
            </div>
        </div>
    </div>

    <script>
        function login() {
            const pin = document.getElementById('pinInput').value;
            const errorMessage = document.getElementById('errorMessage');

            if (pin === '1234') {
                // Hide login screen and show POS system
                document.getElementById('loginScreen').style.display = 'none';
                // Redirect to POS system
                window.location.href = 'project/index.html';
            } else {
                // Show error message
                errorMessage.classList.remove('hidden');
                document.getElementById('pinInput').value = '';
            }
        }

        // Allow Enter key to login
        document.getElementById('pinInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                login();
            }
        });


    </script>
</body>
</html>
