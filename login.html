<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RestroFlow - POS System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            padding: 0;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .hidden { display: none !important; }
        .show { display: block !important; }
    </style>
</head>
<body class="bg-gray-50">
    <!-- Login Screen -->
    <div id="loginScreen" class="min-h-screen gradient-bg flex items-center justify-center p-4">
        <div class="bg-white rounded-2xl shadow-2xl p-8 w-full max-w-md">
            <div class="text-center mb-8">
                <h1 class="text-3xl font-bold text-gray-800 mb-2">RestroFlow</h1>
                <p class="text-gray-600">Enter your PIN to access the system</p>
            </div>

            <div class="space-y-6">
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-2">PIN Code</label>
                    <input
                        type="password"
                        id="pinInput"
                        class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                        placeholder="Enter your PIN"
                    />
                </div>

                <div id="errorMessage" class="hidden bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg">
                    Invalid PIN. Please try again.
                </div>

                <button
                    onclick="login()"
                    class="w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 transition-colors"
                >
                    Access System
                </button>
            </div>

            <div class="mt-6 text-center text-sm text-gray-500">
                <p>Demo PIN: 1234</p>
            </div>
        </div>
    </div>

    <script>
        // System configuration
        const API_BASE_URL = 'http://localhost:4000';
        
        // Role-based redirects
        const ROLE_REDIRECTS = {
            'super_admin': '/project/super-admin.html',
            'manager': '/project/index.html?role=manager',
            'employee': '/project/index.html?role=employee',
            'tenant_admin': '/project/index.html?role=tenant_admin'
        };

        // PIN to role mapping (for fallback)
        const PIN_ROLES = {
            '123456': 'super_admin',
            '567890': 'manager', 
            '111222': 'employee',
            '555666': 'tenant_admin',
            '999999': '/project/index.html?mode=original',
            '000000': '/project/index.html?mode=debug'
        };

        // DOM elements
        const loginForm = document.getElementById('loginForm');
        const pinInput = document.getElementById('pin');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loadingText = document.getElementById('loadingText');
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        const backendStatus = document.getElementById('backendStatus');

        // Check backend status on load
        checkBackendStatus();

        // Auto-focus PIN input
        pinInput.focus();

        // PIN input formatting
        pinInput.addEventListener('input', function(e) {
            // Only allow numbers
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
            
            // Hide error when user starts typing
            hideError();
        });

        // Handle form submission
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const pin = pinInput.value.trim();
            
            if (!pin) {
                showError('Please enter your PIN');
                return;
            }

            if (pin.length < 4) {
                showError('PIN must be at least 4 digits');
                return;
            }

            await authenticateUser(pin);
        });

        // Authentication function
        async function authenticateUser(pin) {
            setLoading(true);
            hideError();

            try {
                // Try backend authentication first
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ pin: pin })
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.token && data.user) {
                        // Store authentication data
                        localStorage.setItem('authToken', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        localStorage.setItem('loginTime', new Date().toISOString());
                        
                        // Redirect based on role
                        const redirectUrl = ROLE_REDIRECTS[data.user.role] || '/project/index.html';
                        
                        // Show success message briefly
                        showSuccess(`Welcome, ${data.user.name || data.user.role}!`);
                        
                        // Redirect after brief delay
                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 1000);
                        
                        return;
                    }
                }

                // Fallback to PIN-based routing if backend auth fails
                if (PIN_ROLES[pin]) {
                    // Store fallback auth data
                    const userData = {
                        pin: pin,
                        role: PIN_ROLES[pin].includes('/') ? 'user' : PIN_ROLES[pin],
                        name: `User ${pin}`,
                        loginTime: new Date().toISOString()
                    };
                    
                    localStorage.setItem('user', JSON.stringify(userData));
                    localStorage.setItem('loginTime', new Date().toISOString());
                    
                    const redirectUrl = PIN_ROLES[pin].includes('/') ? PIN_ROLES[pin] : ROLE_REDIRECTS[PIN_ROLES[pin]];
                    
                    showSuccess('Access granted!');
                    
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                    
                    return;
                }

                // Invalid PIN
                showError('Invalid PIN. Please check your credentials and try again.');

            } catch (error) {
                console.error('Authentication error:', error);
                
                // Fallback authentication if backend is unavailable
                if (PIN_ROLES[pin]) {
                    const userData = {
                        pin: pin,
                        role: PIN_ROLES[pin].includes('/') ? 'user' : PIN_ROLES[pin],
                        name: `User ${pin}`,
                        loginTime: new Date().toISOString()
                    };
                    
                    localStorage.setItem('user', JSON.stringify(userData));
                    localStorage.setItem('loginTime', new Date().toISOString());
                    
                    const redirectUrl = PIN_ROLES[pin].includes('/') ? PIN_ROLES[pin] : ROLE_REDIRECTS[PIN_ROLES[pin]];
                    
                    showSuccess('Access granted (offline mode)!');
                    
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                    
                    return;
                }
                
                showError('Unable to connect to authentication server. Please try again.');
                backendStatus.textContent = 'Disconnected';
                backendStatus.className = 'text-red-400';
            } finally {
                setLoading(false);
            }
        }

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    backendStatus.textContent = 'Connected';
                    backendStatus.className = 'text-green-400';
                } else {
                    backendStatus.textContent = 'Limited';
                    backendStatus.className = 'text-yellow-400';
                }
            } catch (error) {
                backendStatus.textContent = 'Offline';
                backendStatus.className = 'text-red-400';
            }
        }

        // UI helper functions
        function setLoading(loading) {
            if (loading) {
                loginBtn.disabled = true;
                loginText.classList.add('hidden');
                loadingText.classList.remove('hidden');
                pinInput.disabled = true;
            } else {
                loginBtn.disabled = false;
                loginText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                pinInput.disabled = false;
                pinInput.focus();
            }
        }

        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            pinInput.classList.add('border-red-500');
            
            // Auto-hide error after 5 seconds
            setTimeout(hideError, 5000);
        }

        function hideError() {
            errorMessage.classList.add('hidden');
            pinInput.classList.remove('border-red-500');
        }

        function showSuccess(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            errorMessage.className = errorMessage.className.replace('bg-red-500/20 border-red-500/50', 'bg-green-500/20 border-green-500/50');
            errorText.className = errorText.className.replace('text-red-100', 'text-green-100');
            errorMessage.querySelector('span').textContent = '✅';
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Enter key submits form
            if (e.key === 'Enter' && document.activeElement === pinInput) {
                loginForm.dispatchEvent(new Event('submit'));
            }
            
            // Escape key clears input
            if (e.key === 'Escape') {
                pinInput.value = '';
                hideError();
                pinInput.focus();
            }
        });
    </script>
</body>
</html>
