<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RESTROFLOW - Restaurant POS System Login</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .pin-input {
            background: rgba(255, 255, 255, 0.9);
            border: 2px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
        }
        .pin-input:focus {
            background: rgba(255, 255, 255, 1);
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        .login-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            transition: all 0.3s ease;
        }
        .login-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        .loading {
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body class="min-h-screen flex items-center justify-center p-4">
    <!-- Login Container -->
    <div class="login-container rounded-2xl p-8 w-full max-w-md shadow-2xl fade-in">
        <!-- Header -->
        <div class="text-center mb-8">
            <div class="bg-white/20 rounded-full w-20 h-20 mx-auto mb-4 flex items-center justify-center">
                <span class="text-4xl">🍽️</span>
            </div>
            <h1 class="text-3xl font-bold text-white mb-2">RESTROFLOW</h1>
            <p class="text-white/80">Restaurant POS System</p>
            <div class="mt-4 text-sm text-white/60">
                <div class="flex items-center justify-center space-x-2">
                    <div class="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>System Online</span>
                </div>
            </div>
        </div>

        <!-- Login Form -->
        <form id="loginForm" class="space-y-6">
            <div>
                <label for="pin" class="block text-white font-medium mb-2">
                    🔐 Enter Your PIN
                </label>
                <input 
                    type="password" 
                    id="pin" 
                    name="pin"
                    class="pin-input w-full px-4 py-3 rounded-lg text-gray-800 placeholder-gray-500 focus:outline-none text-center text-xl font-mono tracking-widest"
                    placeholder="••••••"
                    maxlength="6"
                    required
                    autocomplete="off"
                >
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="hidden bg-red-500/20 border border-red-500/50 rounded-lg p-3">
                <div class="flex items-center text-red-100 text-sm">
                    <span class="mr-2">⚠️</span>
                    <span id="errorText">Invalid PIN. Please try again.</span>
                </div>
            </div>

            <!-- Login Button -->
            <button 
                type="submit" 
                id="loginBtn"
                class="login-btn w-full py-3 rounded-lg text-white font-semibold text-lg focus:outline-none focus:ring-4 focus:ring-white/20"
            >
                <span id="loginText">🚀 Access System</span>
                <span id="loadingText" class="hidden">
                    <span class="loading inline-block w-5 h-5 border-2 border-white/30 border-t-white rounded-full mr-2"></span>
                    Authenticating...
                </span>
            </button>
        </form>

        <!-- Quick Access Info -->
        <div class="mt-8 pt-6 border-t border-white/20">
            <div class="text-center text-white/60 text-xs space-y-1">
                <div class="font-medium text-white/80 mb-2">Quick Access Guide:</div>
                <div>👑 Super Admin: PIN 123456</div>
                <div>👨‍💼 Manager: PIN 567890</div>
                <div>👤 Employee: PIN 111222</div>
                <div>🏢 Tenant Admin: PIN 555666</div>
            </div>
        </div>

        <!-- System Status -->
        <div class="mt-6 text-center">
            <div class="text-white/40 text-xs">
                <div>Backend: <span id="backendStatus" class="text-green-400">Connected</span></div>
                <div>Version: 2.0.0 Enterprise</div>
            </div>
        </div>
    </div>

    <script>
        // System configuration
        const API_BASE_URL = 'http://localhost:4000';
        
        // Role-based redirects
        const ROLE_REDIRECTS = {
            'super_admin': '/project/super-admin.html',
            'manager': '/project/index.html?role=manager',
            'employee': '/project/index.html?role=employee',
            'tenant_admin': '/project/index.html?role=tenant_admin'
        };

        // PIN to role mapping (for fallback)
        const PIN_ROLES = {
            '123456': 'super_admin',
            '567890': 'manager', 
            '111222': 'employee',
            '555666': 'tenant_admin',
            '999999': '/project/index.html?mode=original',
            '000000': '/project/index.html?mode=debug'
        };

        // DOM elements
        const loginForm = document.getElementById('loginForm');
        const pinInput = document.getElementById('pin');
        const loginBtn = document.getElementById('loginBtn');
        const loginText = document.getElementById('loginText');
        const loadingText = document.getElementById('loadingText');
        const errorMessage = document.getElementById('errorMessage');
        const errorText = document.getElementById('errorText');
        const backendStatus = document.getElementById('backendStatus');

        // Check backend status on load
        checkBackendStatus();

        // Auto-focus PIN input
        pinInput.focus();

        // PIN input formatting
        pinInput.addEventListener('input', function(e) {
            // Only allow numbers
            e.target.value = e.target.value.replace(/[^0-9]/g, '');
            
            // Hide error when user starts typing
            hideError();
        });

        // Handle form submission
        loginForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const pin = pinInput.value.trim();
            
            if (!pin) {
                showError('Please enter your PIN');
                return;
            }

            if (pin.length < 4) {
                showError('PIN must be at least 4 digits');
                return;
            }

            await authenticateUser(pin);
        });

        // Authentication function
        async function authenticateUser(pin) {
            setLoading(true);
            hideError();

            try {
                // Try backend authentication first
                const response = await fetch(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ pin: pin })
                });

                if (response.ok) {
                    const data = await response.json();
                    
                    if (data.token && data.user) {
                        // Store authentication data
                        localStorage.setItem('authToken', data.token);
                        localStorage.setItem('user', JSON.stringify(data.user));
                        localStorage.setItem('loginTime', new Date().toISOString());
                        
                        // Redirect based on role
                        const redirectUrl = ROLE_REDIRECTS[data.user.role] || '/project/index.html';
                        
                        // Show success message briefly
                        showSuccess(`Welcome, ${data.user.name || data.user.role}!`);
                        
                        // Redirect after brief delay
                        setTimeout(() => {
                            window.location.href = redirectUrl;
                        }, 1000);
                        
                        return;
                    }
                }

                // Fallback to PIN-based routing if backend auth fails
                if (PIN_ROLES[pin]) {
                    // Store fallback auth data
                    const userData = {
                        pin: pin,
                        role: PIN_ROLES[pin].includes('/') ? 'user' : PIN_ROLES[pin],
                        name: `User ${pin}`,
                        loginTime: new Date().toISOString()
                    };
                    
                    localStorage.setItem('user', JSON.stringify(userData));
                    localStorage.setItem('loginTime', new Date().toISOString());
                    
                    const redirectUrl = PIN_ROLES[pin].includes('/') ? PIN_ROLES[pin] : ROLE_REDIRECTS[PIN_ROLES[pin]];
                    
                    showSuccess('Access granted!');
                    
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                    
                    return;
                }

                // Invalid PIN
                showError('Invalid PIN. Please check your credentials and try again.');

            } catch (error) {
                console.error('Authentication error:', error);
                
                // Fallback authentication if backend is unavailable
                if (PIN_ROLES[pin]) {
                    const userData = {
                        pin: pin,
                        role: PIN_ROLES[pin].includes('/') ? 'user' : PIN_ROLES[pin],
                        name: `User ${pin}`,
                        loginTime: new Date().toISOString()
                    };
                    
                    localStorage.setItem('user', JSON.stringify(userData));
                    localStorage.setItem('loginTime', new Date().toISOString());
                    
                    const redirectUrl = PIN_ROLES[pin].includes('/') ? PIN_ROLES[pin] : ROLE_REDIRECTS[PIN_ROLES[pin]];
                    
                    showSuccess('Access granted (offline mode)!');
                    
                    setTimeout(() => {
                        window.location.href = redirectUrl;
                    }, 1000);
                    
                    return;
                }
                
                showError('Unable to connect to authentication server. Please try again.');
                backendStatus.textContent = 'Disconnected';
                backendStatus.className = 'text-red-400';
            } finally {
                setLoading(false);
            }
        }

        // Check backend status
        async function checkBackendStatus() {
            try {
                const response = await fetch(`${API_BASE_URL}/api/health`, {
                    method: 'GET',
                    timeout: 5000
                });
                
                if (response.ok) {
                    backendStatus.textContent = 'Connected';
                    backendStatus.className = 'text-green-400';
                } else {
                    backendStatus.textContent = 'Limited';
                    backendStatus.className = 'text-yellow-400';
                }
            } catch (error) {
                backendStatus.textContent = 'Offline';
                backendStatus.className = 'text-red-400';
            }
        }

        // UI helper functions
        function setLoading(loading) {
            if (loading) {
                loginBtn.disabled = true;
                loginText.classList.add('hidden');
                loadingText.classList.remove('hidden');
                pinInput.disabled = true;
            } else {
                loginBtn.disabled = false;
                loginText.classList.remove('hidden');
                loadingText.classList.add('hidden');
                pinInput.disabled = false;
                pinInput.focus();
            }
        }

        function showError(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            pinInput.classList.add('border-red-500');
            
            // Auto-hide error after 5 seconds
            setTimeout(hideError, 5000);
        }

        function hideError() {
            errorMessage.classList.add('hidden');
            pinInput.classList.remove('border-red-500');
        }

        function showSuccess(message) {
            errorText.textContent = message;
            errorMessage.classList.remove('hidden');
            errorMessage.className = errorMessage.className.replace('bg-red-500/20 border-red-500/50', 'bg-green-500/20 border-green-500/50');
            errorText.className = errorText.className.replace('text-red-100', 'text-green-100');
            errorMessage.querySelector('span').textContent = '✅';
        }

        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Enter key submits form
            if (e.key === 'Enter' && document.activeElement === pinInput) {
                loginForm.dispatchEvent(new Event('submit'));
            }
            
            // Escape key clears input
            if (e.key === 'Escape') {
                pinInput.value = '';
                hideError();
                pinInput.focus();
            }
        });
    </script>
</body>
</html>
