#!/usr/bin/env node

/**
 * Manual Frontend Testing Suite for POS System
 * Simplified testing approach with manual verification
 */

import puppeteer from 'puppeteer';

const BASE_URL = 'http://localhost:5173';
const TEST_CREDENTIALS = [
  { pin: '888888', role: 'Super Admin' },
  { pin: '999999', role: 'Super Admin' },
  { pin: '123456', role: 'Employee' },
  { pin: '234567', role: 'Employee' },
  { pin: '567890', role: 'Manager' }
];

async function runManualTests() {
  console.log('🧪 Starting Manual Frontend Testing...\n');
  
  let browser, page;
  const testResults = [];
  
  try {
    browser = await puppeteer.launch({ 
      headless: false, 
      defaultViewport: { width: 1920, height: 1080 }
    });
    page = await browser.newPage();
    
    // Monitor console errors
    const consoleErrors = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        consoleErrors.push(msg.text());
        console.log(`🔴 Console Error: ${msg.text()}`);
      }
    });
    
    // Monitor network failures
    const networkErrors = [];
    page.on('requestfailed', request => {
      networkErrors.push(`${request.url()} - ${request.failure().errorText}`);
      console.log(`🌐 Network Error: ${request.url()}`);
    });
    
    console.log('📱 Opening POS System...');
    await page.goto(BASE_URL, { waitUntil: 'networkidle2', timeout: 30000 });
    
    // Test 1: Check if Enhanced Login is visible
    console.log('\n🔐 Testing Login Interface...');
    
    try {
      const title = await page.title();
      console.log(`✅ Page Title: ${title}`);
      
      // Check for Enhanced Login indicator
      await page.waitForSelector('div:contains("ENHANCED LOGIN ACTIVE")', { timeout: 5000 });
      console.log('✅ Enhanced Login indicator found');
      
      // Check for number pad
      const numberButtons = await page.$$('button');
      const numberPadButtons = [];
      for (const button of numberButtons) {
        const text = await page.evaluate(el => el.textContent, button);
        if (/^[0-9]$/.test(text.trim())) {
          numberPadButtons.push(text.trim());
        }
      }
      console.log(`✅ Number pad buttons found: ${numberPadButtons.join(', ')}`);
      
      // Check for PIN dots
      const pinDots = await page.$$('div[style*="width: 48px"][style*="height: 48px"]');
      console.log(`✅ PIN dots found: ${pinDots.length} dots`);
      
      // Check for development credentials panel
      const credentialsPanel = await page.$('div:contains("Development Credentials")');
      if (credentialsPanel) {
        console.log('✅ Development credentials panel found');
      } else {
        console.log('❌ Development credentials panel not found');
      }
      
    } catch (error) {
      console.log(`❌ Login interface error: ${error.message}`);
    }
    
    // Test 2: Test Authentication
    console.log('\n🔑 Testing Authentication...');
    
    for (const credential of TEST_CREDENTIALS) {
      try {
        console.log(`\nTesting ${credential.role} PIN: ${credential.pin}`);
        
        // Clear any existing PIN
        const clearButton = await page.$('button:contains("Clear")');
        if (clearButton) await clearButton.click();
        
        // Enter PIN
        for (const digit of credential.pin) {
          const button = await page.$(`button:contains("${digit}")`);
          if (button) {
            await button.click();
            await page.waitForTimeout(100);
          }
        }
        
        // Check if Sign In button is enabled
        const signInButton = await page.$('button:contains("Sign In")');
        if (signInButton) {
          const isDisabled = await page.evaluate(el => el.disabled, signInButton);
          if (!isDisabled) {
            console.log(`✅ Sign In button enabled for PIN ${credential.pin}`);
            
            // Try to login
            await signInButton.click();
            await page.waitForTimeout(3000);
            
            // Check if we're still on login page or moved to POS
            const currentUrl = page.url();
            const stillOnLogin = await page.$('button:contains("Sign In")');
            
            if (!stillOnLogin) {
              console.log(`✅ Login successful for ${credential.role} (${credential.pin})`);
              
              // Take screenshot of logged-in state
              await page.screenshot({ 
                path: `login-success-${credential.pin}.png`,
                fullPage: true 
              });
              
              // Look for POS interface elements
              const posElements = await page.$$('div, nav, main');
              console.log(`✅ Found ${posElements.length} interface elements after login`);
              
              // Go back to login for next test
              await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
              
            } else {
              console.log(`❌ Login failed for ${credential.role} (${credential.pin})`);
            }
          } else {
            console.log(`❌ Sign In button disabled for PIN ${credential.pin}`);
          }
        } else {
          console.log(`❌ Sign In button not found for PIN ${credential.pin}`);
        }
        
      } catch (error) {
        console.log(`❌ Authentication error for ${credential.pin}: ${error.message}`);
      }
    }
    
    // Test 3: Visual Elements
    console.log('\n🎨 Testing Visual Elements...');
    
    try {
      // Go back to login page
      await page.goto(BASE_URL, { waitUntil: 'networkidle2' });
      
      // Check for gradient background
      const gradientElement = await page.$('div[style*="linear-gradient"]');
      if (gradientElement) {
        console.log('✅ Gradient background found');
      } else {
        console.log('❌ Gradient background not found');
      }
      
      // Check for animated blobs
      const animatedElements = await page.$$('div[style*="animation"]');
      console.log(`✅ Found ${animatedElements.length} animated elements`);
      
      // Take final screenshot
      await page.screenshot({ 
        path: 'login-interface-final.png',
        fullPage: true 
      });
      console.log('✅ Screenshot saved: login-interface-final.png');
      
    } catch (error) {
      console.log(`❌ Visual elements error: ${error.message}`);
    }
    
    // Test 4: Performance Check
    console.log('\n⚡ Testing Performance...');
    
    try {
      const metrics = await page.metrics();
      const memoryUsage = (metrics.JSHeapUsedSize / 1024 / 1024).toFixed(2);
      console.log(`✅ Memory usage: ${memoryUsage} MB`);
      
      // Test page load time
      const startTime = Date.now();
      await page.reload({ waitUntil: 'networkidle2' });
      const loadTime = Date.now() - startTime;
      console.log(`✅ Page reload time: ${loadTime}ms`);
      
    } catch (error) {
      console.log(`❌ Performance test error: ${error.message}`);
    }
    
    // Summary
    console.log('\n📊 TEST SUMMARY');
    console.log('================');
    console.log(`Console Errors: ${consoleErrors.length}`);
    console.log(`Network Errors: ${networkErrors.length}`);
    
    if (consoleErrors.length > 0) {
      console.log('\n🔴 Console Errors:');
      consoleErrors.forEach(error => console.log(`   ${error}`));
    }
    
    if (networkErrors.length > 0) {
      console.log('\n🌐 Network Errors:');
      networkErrors.forEach(error => console.log(`   ${error}`));
    }
    
    console.log('\n✅ Manual testing completed!');
    console.log('Check the generated screenshots for visual verification.');
    
  } catch (error) {
    console.error('💥 Critical test failure:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

// Run the manual tests
runManualTests().catch(console.error);
