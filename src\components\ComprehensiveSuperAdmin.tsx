import React, { useState, useEffect } from 'react';
import { 
  Building2, 
  Bar<PERSON>hart3, 
  CreditCard, 
  Brain, 
  Globe, 
  Shield, 
  Settings,
  Users,
  TrendingUp,
  Zap,
  AlertTriangle,
  CheckCircle,
  Activity,
  DollarSign,
  Cpu,
  Database
} from 'lucide-react';

interface TabData {
  id: string;
  name: string;
  icon: React.ComponentType<any>;
  color: string;
  description: string;
  phase?: string;
}

const ComprehensiveSuperAdmin: React.FC = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [systemStats, setSystemStats] = useState({
    totalTenants: 0,
    activeUsers: 0,
    totalRevenue: 0,
    systemHealth: 'excellent'
  });

  const tabs: TabData[] = [
    { 
      id: 'overview', 
      name: 'Overview', 
      icon: Activity, 
      color: 'red',
      description: 'System overview and key metrics'
    },
    { 
      id: 'tenants', 
      name: 'Tenants', 
      icon: Building2, 
      color: 'gray',
      description: 'Multi-tenant management'
    },
    { 
      id: 'phase3-analytics', 
      name: 'Analytics', 
      icon: BarChart3, 
      color: 'blue',
      description: 'Advanced analytics and reporting',
      phase: 'Phase 3'
    },
    { 
      id: 'phase4-payments', 
      name: 'Payments', 
      icon: CreditCard, 
      color: 'green',
      description: 'Enhanced payment processing',
      phase: 'Phase 4'
    },
    { 
      id: 'phase5-ai', 
      name: 'AI Center', 
      icon: Brain, 
      color: 'purple',
      description: 'AI and automation management',
      phase: 'Phase 5'
    },
    { 
      id: 'phase6-global', 
      name: 'Global', 
      icon: Globe, 
      color: 'cyan',
      description: 'Global expansion features',
      phase: 'Phase 6'
    },
    { 
      id: 'security', 
      name: 'Security', 
      icon: Shield, 
      color: 'gray',
      description: 'Security monitoring and controls'
    },
    { 
      id: 'system', 
      name: 'System', 
      icon: Settings, 
      color: 'gray',
      description: 'System configuration and monitoring'
    }
  ];

  useEffect(() => {
    loadSystemStats();
    const interval = setInterval(loadSystemStats, 30000);
    return () => clearInterval(interval);
  }, []);

  const loadSystemStats = async () => {
    try {
      const token = localStorage.getItem('authToken');
      const response = await fetch('/api/admin/system/stats', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const data = await response.json();
        setSystemStats(data);
      }
    } catch (error) {
      console.error('Failed to load system stats:', error);
    }
  };

  const getTabColorClasses = (color: string, isActive: boolean) => {
    const colorMap = {
      red: isActive ? 'border-red-500 text-red-600' : 'text-red-500 hover:text-red-700 hover:border-red-300',
      blue: isActive ? 'border-blue-500 text-blue-600' : 'text-blue-500 hover:text-blue-700 hover:border-blue-300',
      green: isActive ? 'border-green-500 text-green-600' : 'text-green-500 hover:text-green-700 hover:border-green-300',
      purple: isActive ? 'border-purple-500 text-purple-600' : 'text-purple-500 hover:text-purple-700 hover:border-purple-300',
      cyan: isActive ? 'border-cyan-500 text-cyan-600' : 'text-cyan-500 hover:text-cyan-700 hover:border-cyan-300',
      gray: isActive ? 'border-gray-500 text-gray-600' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300'
    };
    return colorMap[color as keyof typeof colorMap] || colorMap.gray;
  };

  const renderOverviewTab = () => (
    <div className="space-y-6">
      {/* System Health Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Tenants</p>
              <p className="text-2xl font-semibold text-gray-900">{systemStats.totalTenants}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <Users className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Active Users</p>
              <p className="text-2xl font-semibold text-gray-900">{systemStats.activeUsers}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <DollarSign className="h-8 w-8 text-purple-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">Total Revenue</p>
              <p className="text-2xl font-semibold text-gray-900">${systemStats.totalRevenue.toLocaleString()}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-500">System Health</p>
              <p className="text-2xl font-semibold text-green-600 capitalize">{systemStats.systemHealth}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Phase Status Overview */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Phase Implementation Status</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {tabs.filter(tab => tab.phase).map(tab => (
              <div key={tab.id} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <tab.icon className={`h-6 w-6 text-${tab.color}-600`} />
                    <div className="ml-3">
                      <p className="text-sm font-medium text-gray-900">{tab.phase}</p>
                      <p className="text-xs text-gray-500">{tab.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="ml-1 text-xs text-green-600 font-medium">Active</span>
                  </div>
                </div>
                <p className="mt-2 text-xs text-gray-600">{tab.description}</p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white rounded-lg shadow">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Quick Actions</h3>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <button 
              onClick={() => setActiveTab('tenants')}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Building2 className="h-8 w-8 text-blue-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Manage Tenants</span>
            </button>
            <button 
              onClick={() => setActiveTab('phase5-ai')}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Brain className="h-8 w-8 text-purple-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">AI Dashboard</span>
            </button>
            <button 
              onClick={() => setActiveTab('security')}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Shield className="h-8 w-8 text-red-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">Security Center</span>
            </button>
            <button 
              onClick={() => setActiveTab('system')}
              className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
            >
              <Settings className="h-8 w-8 text-gray-600 mb-2" />
              <span className="text-sm font-medium text-gray-900">System Config</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <Shield className="h-8 w-8 text-red-600 mr-3" />
              <div>
                <h1 className="text-2xl font-bold text-gray-900">RestroFlow Super Admin</h1>
                <p className="text-sm text-gray-500">Enterprise Security & Management Center</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                <span className="text-sm text-gray-600">System Online</span>
              </div>
              <button 
                onClick={() => {
                  localStorage.clear();
                  window.location.href = '/login.html';
                }}
                className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium"
              >
                Logout
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <nav className="flex space-x-4 overflow-x-auto" aria-label="Tabs">
            {tabs.map((tab) => {
              const isActive = activeTab === tab.id;
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`
                    whitespace-nowrap py-4 px-3 border-b-2 font-medium text-sm flex items-center space-x-2
                    ${isActive 
                      ? getTabColorClasses(tab.color, true)
                      : `border-transparent ${getTabColorClasses(tab.color, false)}`
                    }
                  `}
                >
                  <Icon className="h-4 w-4" />
                  <span>{tab.name}</span>
                  {tab.phase && (
                    <span className={`text-xs px-2 py-1 rounded-full bg-${tab.color}-100 text-${tab.color}-700`}>
                      {tab.phase}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'overview' && renderOverviewTab()}
        {activeTab !== 'overview' && (
          <div className="bg-white rounded-lg shadow p-6">
            <div className="text-center py-12">
              <div className="text-6xl mb-4">🚧</div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {tabs.find(t => t.id === activeTab)?.name} Interface
              </h3>
              <p className="text-gray-500 mb-4">
                {tabs.find(t => t.id === activeTab)?.description}
              </p>
              <p className="text-sm text-blue-600">
                Advanced interface implementation in progress...
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ComprehensiveSuperAdmin;
