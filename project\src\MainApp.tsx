import React, { useState, useEffect } from 'react';
import { Shield, Building, Coffee, Loader2 } from 'lucide-react';
import AuthenticatedApp from './components/AuthenticatedApp';

interface AuthData {
  token: string;
  employee: {
    id: string;
    name: string;
    role: string;
    permissions: string[];
  };
  tenant: {
    id: string;
    name: string;
    slug: string;
    business_name: string;
    features: Record<string, boolean>;
  };
  location?: {
    id: string;
    name: string;
  };
}

const MainApp: React.FC = () => {
  const [authData, setAuthData] = useState<AuthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [loginForm, setLoginForm] = useState({
    pin: '',
    tenant_slug: ''
  });
  const [loginLoading, setLoginLoading] = useState(false);
  const [loginError, setLoginError] = useState('');

  useEffect(() => {
    // Check for existing authentication
    const token = localStorage.getItem('authToken');
    const storedAuthData = localStorage.getItem('authData');
    
    if (token && storedAuthData) {
      try {
        const parsedAuthData = JSON.parse(storedAuthData);
        setAuthData(parsedAuthData);
      } catch (error) {
        console.error('Error parsing stored auth data:', error);
        localStorage.removeItem('token');
        localStorage.removeItem('authData');
      }
    }
    setLoading(false);
  }, []);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoginLoading(true);
    setLoginError('');

    try {
      const response = await fetch('http://localhost:4000/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          pin: loginForm.pin,
          tenant_slug: loginForm.tenant_slug || undefined
        }),
      });

      const data = await response.json();

      if (response.ok) {
        const authData: AuthData = {
          token: data.token,
          employee: data.employee,
          tenant: data.tenant,
          location: data.location
        };

        localStorage.setItem('token', data.token);
        localStorage.setItem('authData', JSON.stringify(authData));
        setAuthData(authData);
      } else {
        setLoginError(data.error || 'Login failed');
      }
    } catch (error) {
      console.error('Login error:', error);
      setLoginError('Network error. Please check your connection.');
    } finally {
      setLoginLoading(false);
    }
  };

  const handleLogout = () => {
    setAuthData(null);
    localStorage.removeItem('token');
    localStorage.removeItem('authData');
    setLoginForm({ pin: '', tenant_slug: '' });
    setLoginError('');
  };

  const getRoleInfo = (role: string) => {
    switch (role) {
      case 'super_admin':
        return {
          icon: <Shield className="h-8 w-8" />,
          title: 'Super Admin',
          description: 'Full system access and tenant management',
          color: 'from-red-500 to-red-600'
        };
      case 'tenant_admin':
      case 'manager':
        return {
          icon: <Building className="h-8 w-8" />,
          title: 'Business Manager',
          description: 'Restaurant operations and staff management',
          color: 'from-blue-500 to-blue-600'
        };
      default:
        return {
          icon: <Coffee className="h-8 w-8" />,
          title: 'POS Operator',
          description: 'Point of sale and order management',
          color: 'from-green-500 to-green-600'
        };
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin text-blue-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading application...</p>
        </div>
      </div>
    );
  }

  if (authData) {
    return <AuthenticatedApp authData={authData} onLogout={handleLogout} />;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="mx-auto h-16 w-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full flex items-center justify-center mb-4">
            <Coffee className="h-8 w-8 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Enterprise POS</h1>
          <p className="text-gray-600">Multi-tenant restaurant management system</p>
        </div>

        {/* Login Form */}
        <div className="bg-white rounded-lg shadow-xl p-8">
          <form onSubmit={handleLogin} className="space-y-6">
            <div>
              <label htmlFor="pin" className="block text-sm font-medium text-gray-700 mb-2">
                Employee PIN
              </label>
              <input
                id="pin"
                type="password"
                required
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Enter your PIN"
                value={loginForm.pin}
                onChange={(e) => setLoginForm({ ...loginForm, pin: e.target.value })}
                maxLength={6}
              />
            </div>

            <div>
              <label htmlFor="tenant_slug" className="block text-sm font-medium text-gray-700 mb-2">
                Restaurant Code (Optional)
              </label>
              <input
                id="tenant_slug"
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                placeholder="Leave blank for auto-detection"
                value={loginForm.tenant_slug}
                onChange={(e) => setLoginForm({ ...loginForm, tenant_slug: e.target.value })}
              />
            </div>

            {loginError && (
              <div className="bg-red-50 border border-red-200 rounded-md p-3">
                <p className="text-sm text-red-600">{loginError}</p>
              </div>
            )}

            <button
              type="submit"
              disabled={loginLoading || !loginForm.pin}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loginLoading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                'Sign In'
              )}
            </button>
          </form>
        </div>

        {/* Demo Credentials */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Demo Credentials</h3>
          <div className="space-y-4">
            {[
              { pin: '123456', role: 'super_admin' },
              { pin: '567890', role: 'manager' },
              { pin: '567890', role: 'employee' }
            ].map((demo, index) => {
              const roleInfo = getRoleInfo(demo.role);
              return (
                <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-full bg-gradient-to-r ${roleInfo.color} text-white`}>
                      {roleInfo.icon}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{roleInfo.title}</div>
                      <div className="text-sm text-gray-600">{roleInfo.description}</div>
                    </div>
                  </div>
                  <button
                    type="button"
                    onClick={() => setLoginForm({ ...loginForm, pin: demo.pin })}
                    className="px-3 py-1 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200"
                  >
                    PIN: {demo.pin}
                  </button>
                </div>
              );
            })}
          </div>
        </div>

        {/* Features */}
        <div className="mt-8 text-center">
          <p className="text-sm text-gray-600 mb-4">Features include:</p>
          <div className="flex flex-wrap justify-center gap-2">
            {[
              'Multi-tenant Architecture',
              'Role-based Access',
              'Real-time Analytics',
              'Kitchen Display',
              'Inventory Management',
              'Staff Scheduling'
            ].map((feature, index) => (
              <span
                key={index}
                className="px-3 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {feature}
              </span>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default MainApp;
