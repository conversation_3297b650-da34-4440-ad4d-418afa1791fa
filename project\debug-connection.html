<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Connection Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .success { color: #22c55e; }
        .error { color: #ef4444; }
        .info { color: #3b82f6; }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status.success {
            background: #dcfce7;
            border: 1px solid #22c55e;
        }
        .status.error {
            background: #fef2f2;
            border: 1px solid #ef4444;
        }
        .status.info {
            background: #eff6ff;
            border: 1px solid #3b82f6;
        }
    </style>
</head>
<body>
    <h1>🔍 RestroFlow Backend Connection Debug</h1>
    
    <div class="test-section">
        <h2>🌐 Backend Server Status</h2>
        <button onclick="testHealth()">Test Health Endpoint</button>
        <button onclick="testDatabase()">Test Database Health</button>
        <div id="health-status"></div>
    </div>

    <div class="test-section">
        <h2>🔐 Authentication Test</h2>
        <div>
            <label>PIN: </label>
            <input type="text" id="pin-input" value="888888" placeholder="Enter PIN">
            <button onclick="testLogin()">Test Super Admin Login</button>
        </div>
        <div id="auth-status"></div>
    </div>

    <div class="test-section">
        <h2>📊 API Endpoints Test</h2>
        <button onclick="testTenants()">Test Tenants API</button>
        <button onclick="testUsers()">Test Users API</button>
        <button onclick="testMetrics()">Test Metrics API</button>
        <div id="api-status"></div>
    </div>

    <div class="test-section">
        <h2>🔧 Network Information</h2>
        <div id="network-info">
            <p><strong>Current URL:</strong> <span id="current-url"></span></p>
            <p><strong>Backend URL:</strong> http://localhost:4000</p>
            <p><strong>User Agent:</strong> <span id="user-agent"></span></p>
        </div>
    </div>

    <script>
        // Initialize page
        document.getElementById('current-url').textContent = window.location.href;
        document.getElementById('user-agent').textContent = navigator.userAgent;

        // Utility functions
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function logResponse(response, data) {
            return `
                <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                <strong>Headers:</strong> ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}<br>
                <strong>Data:</strong> <pre>${JSON.stringify(data, null, 2)}</pre>
            `;
        }

        // Test health endpoint
        async function testHealth() {
            showStatus('health-status', '🔄 Testing health endpoint...', 'info');
            
            try {
                const response = await fetch('http://localhost:4000/api/admin/health', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('health-status', `✅ Health check successful!<br>${logResponse(response, data)}`, 'success');
                } else {
                    showStatus('health-status', `❌ Health check failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('health-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Health check error:', error);
            }
        }

        // Test database health
        async function testDatabase() {
            showStatus('health-status', '🔄 Testing database health...', 'info');
            
            try {
                const response = await fetch('http://localhost:4000/api/admin/health/database', {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('health-status', `✅ Database health check successful!<br>${logResponse(response, data)}`, 'success');
                } else {
                    showStatus('health-status', `❌ Database health check failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('health-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Database health check error:', error);
            }
        }

        // Test login
        async function testLogin() {
            const pin = document.getElementById('pin-input').value;
            showStatus('auth-status', `🔄 Testing login with PIN: ${pin}...`, 'info');
            
            try {
                const response = await fetch('http://localhost:4000/api/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ pin: pin })
                });

                const data = await response.json();
                
                if (response.ok) {
                    if (data.employee && data.employee.role === 'super_admin') {
                        showStatus('auth-status', `✅ Super Admin login successful!<br>${logResponse(response, data)}`, 'success');
                        
                        // Store token for further tests
                        localStorage.setItem('authToken', data.token);
                        localStorage.setItem('currentEmployee', JSON.stringify(data.employee));
                    } else {
                        showStatus('auth-status', `⚠️ Login successful but not super admin role!<br>Role: ${data.employee?.role}<br>${logResponse(response, data)}`, 'error');
                    }
                } else {
                    showStatus('auth-status', `❌ Login failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('auth-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Login error:', error);
            }
        }

        // Test tenants API
        async function testTenants() {
            showStatus('api-status', '🔄 Testing tenants API...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('api-status', '❌ No auth token found. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:4000/api/admin/tenants', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('api-status', `✅ Tenants API successful!<br>${logResponse(response, data)}`, 'success');
                } else {
                    showStatus('api-status', `❌ Tenants API failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Tenants API error:', error);
            }
        }

        // Test users API
        async function testUsers() {
            showStatus('api-status', '🔄 Testing users API...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('api-status', '❌ No auth token found. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:4000/api/admin/users', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('api-status', `✅ Users API successful!<br>${logResponse(response, data)}`, 'success');
                } else {
                    showStatus('api-status', `❌ Users API failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Users API error:', error);
            }
        }

        // Test metrics API
        async function testMetrics() {
            showStatus('api-status', '🔄 Testing metrics API...', 'info');
            
            const token = localStorage.getItem('authToken');
            if (!token) {
                showStatus('api-status', '❌ No auth token found. Please login first.', 'error');
                return;
            }

            try {
                const response = await fetch('http://localhost:4000/api/admin/metrics/system', {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                const data = await response.json();
                
                if (response.ok) {
                    showStatus('api-status', `✅ Metrics API successful!<br>${logResponse(response, data)}`, 'success');
                } else {
                    showStatus('api-status', `❌ Metrics API failed!<br>${logResponse(response, data)}`, 'error');
                }
            } catch (error) {
                showStatus('api-status', `❌ Network error: ${error.message}`, 'error');
                console.error('Metrics API error:', error);
            }
        }

        // Auto-run health check on page load
        window.addEventListener('load', () => {
            setTimeout(testHealth, 1000);
        });
    </script>
</body>
</html>
