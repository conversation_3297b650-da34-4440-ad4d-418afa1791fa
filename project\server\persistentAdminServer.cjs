// Persistent Super Admin API Server with Enhanced Error Handling
// Designed to stay running and handle database connections properly

const express = require('express');
const cors = require('cors');
const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

const app = express();
const PORT = 4000;

console.log('🚀 Starting Persistent Super Admin API Server...');

// PostgreSQL connection configuration with enhanced error handling
const pool = new Pool({
  user: 'BARPOS',
  host: 'localhost',
  database: 'BARPOS',
  password: 'Chaand@0319',
  port: 5432,
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 5000,
});

// Enhanced error handling for the pool
pool.on('error', (err, client) => {
  console.error('❌ Unexpected error on idle client', err);
  // Don't exit the process, just log the error
});

pool.on('connect', (client) => {
  console.log('✅ New client connected to PostgreSQL');
});

// Middleware
app.use(cors({
  origin: ['http://localhost:5173', 'http://localhost:5174', 'http://localhost:3000'],
  credentials: true
}));
app.use(express.json());

// Enhanced logging middleware
app.use((req, res, next) => {
  console.log(`📝 ${new Date().toISOString()} - ${req.method} ${req.path}`);
  next();
});

// Database connection test with enhanced error handling
app.get('/api/admin/health/database', async (req, res) => {
  let client;
  try {
    console.log('🔍 Testing database connection...');
    client = await pool.connect();
    const result = await client.query('SELECT NOW(), current_database(), current_user');
    
    console.log('✅ Database connection successful');
    res.json({
      connected: true,
      timestamp: result.rows[0].now,
      database: result.rows[0].current_database,
      user: result.rows[0].current_user,
      host: 'localhost:5432',
      status: 'healthy'
    });
  } catch (error) {
    console.error('❌ Database connection error:', error.message);
    res.status(500).json({
      connected: false,
      error: error.message,
      code: error.code,
      timestamp: new Date().toISOString()
    });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// Authentication endpoints for Super Admin Dashboard
app.post('/api/auth/login', (req, res) => {
  const { pin, tenant_slug } = req.body;

  console.log('🔐 Login attempt with PIN:', pin ? '***' : 'undefined', 'Tenant:', tenant_slug);

  // Valid Super Admin PINs
  const validPins = ['888888', '999999'];

  // Valid Admin PINs for regular admin access
  const adminPins = ['123456', '111111', '000000'];

  if (!pin) {
    return res.status(400).json({
      success: false,
      error: 'PIN is required'
    });
  }

  if (validPins.includes(pin)) {
    console.log('✅ Super Admin login successful');
    const userData = {
      id: 'super-admin',
      name: 'Super Administrator',
      role: 'super_admin',
      permissions: ['all'],
      tenant_id: 1,
      tenant_name: 'BARPOS System',
      tenant_slug: 'barpos-system'
    };

    res.json({
      success: true,
      message: 'Login successful',
      user: userData,
      employee: userData,
      tenant: {
        id: 1,
        name: 'BARPOS System',
        slug: 'barpos-system'
      },
      location: {
        id: 1,
        name: 'Main Location'
      },
      token: jwt.sign(
        {
          employeeId: 'super-admin',
          tenantId: 1,
          locationId: 1,
          role: 'super_admin',
          permissions: ['all']
        },
        process.env.JWT_SECRET || 'barpos-super-secure-jwt-secret-key-2024-production-v2-enhanced',
        { expiresIn: '24h' }
      ),
      // Legacy format support
      ...userData
    });
  } else if (adminPins.includes(pin)) {
    console.log('✅ Restaurant Admin login successful - Full operational privileges granted');
    const userData = {
      id: 'restaurant-admin',
      name: 'Restaurant Administrator',
      role: 'tenant_admin', // Changed from 'admin' to 'tenant_admin' for full privileges
      permissions: [
        'all', // Grant all permissions for restaurant operations
        'tenant_management',
        'advanced_analytics',
        'staff_management',
        'payment_management',
        'inventory_management',
        'staff_scheduling',
        'basic_analytics',
        'table_analytics',
        'payment_processing',
        'pos_access',
        'floor_layout',
        'kitchen_management',
        'order_management',
        'reporting',
        'settings_management',
        'hardware_management',
        'menu_management'
      ],
      tenant_id: 1,
      tenant_name: 'Demo Restaurant',
      tenant_slug: 'demo-restaurant'
    };

    res.json({
      success: true,
      message: 'Login successful',
      user: userData,
      employee: userData,
      tenant: {
        id: 1,
        name: 'Demo Restaurant',
        slug: 'demo-restaurant'
      },
      location: {
        id: 1,
        name: 'Main Location'
      },
      token: jwt.sign(
        {
          employeeId: 'restaurant-admin',
          tenantId: 1,
          locationId: 1,
          role: 'tenant_admin',
          permissions: ['all']
        },
        process.env.JWT_SECRET || 'barpos-super-secure-jwt-secret-key-2024-production-v2-enhanced',
        { expiresIn: '24h' }
      ),
      // Legacy format support
      ...userData
    });
  } else {
    console.log('❌ Login failed - invalid PIN');
    res.status(401).json({
      success: false,
      error: 'Invalid PIN. Try: 888888 (Super Admin), 999999 (Super Admin), 123456 (Admin), 111111 (Admin), or 000000 (Admin)'
    });
  }
});

// Logout endpoint
app.post('/api/auth/logout', (req, res) => {
  console.log('🚪 Logout request');
  res.json({
    success: true,
    message: 'Logged out successfully'
  });
});

// Verify authentication endpoint
app.get('/api/auth/verify', (req, res) => {
  const authHeader = req.headers.authorization;

  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({
      success: false,
      message: 'Invalid or missing token'
    });
  }

  const token = authHeader.split(' ')[1];

  try {
    const decoded = jwt.verify(token, process.env.JWT_SECRET || 'barpos-super-secure-jwt-secret-key-2024-production-v2-enhanced');
    res.json({
      success: true,
      user: {
        id: decoded.employeeId,
        name: decoded.role === 'super_admin' ? 'Super Administrator' : 'Administrator',
        role: decoded.role,
        permissions: decoded.permissions || ['all']
      }
    });
  } catch (error) {
    res.status(401).json({
      success: false,
      message: 'Invalid or expired token'
    });
  }
});

// Simple health check without database
app.get('/api/admin/health', (req, res) => {
  res.json({
    status: 'OK',
    timestamp: new Date().toISOString(),
    server: 'Persistent Admin Server',
    uptime: process.uptime(),
    memory: process.memoryUsage(),
    version: '1.0.0'
  });
});

// System Metrics with enhanced error handling
app.get('/api/admin/metrics/system', async (req, res) => {
  let client;
  try {
    client = await pool.connect();
    
    // Get tenant metrics
    const tenantResult = await client.query('SELECT COUNT(*) as total_tenants FROM tenants');
    const userResult = await client.query('SELECT COUNT(*) as total_users FROM users');
    const revenueResult = await client.query('SELECT COALESCE(SUM(amount), 0) as total_revenue FROM transactions');
    const transactionResult = await client.query('SELECT COUNT(*) as total_transactions FROM transactions');
    
    const metrics = {
      totalTenants: parseInt(tenantResult.rows[0].total_tenants) || 0,
      activeTenants: parseInt(tenantResult.rows[0].total_tenants) || 0,
      totalUsers: parseInt(userResult.rows[0].total_users) || 0,
      activeUsers: parseInt(userResult.rows[0].total_users) || 0,
      monthlyRevenue: parseFloat(revenueResult.rows[0].total_revenue) || 0,
      totalTransactions: parseInt(transactionResult.rows[0].total_transactions) || 0,
      systemUptime: Math.round(process.uptime()),
      databaseConnections: pool.totalCount,
      apiRequests: Math.floor(Math.random() * 5000) + 25000,
      errorRate: Math.random() * 0.3,
      responseTime: Math.floor(Math.random() * 30) + 80,
      memoryUsage: Math.round(process.memoryUsage().heapUsed / 1024 / 1024),
      cpuUsage: Math.floor(Math.random() * 30) + 15,
      diskUsage: Math.floor(Math.random() * 15) + 55,
      lastUpdated: new Date().toISOString()
    };
    
    res.json(metrics);
  } catch (error) {
    console.error('❌ Error fetching system metrics:', error.message);
    res.status(500).json({ 
      error: error.message,
      timestamp: new Date().toISOString()
    });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// Tenant Management
app.get('/api/admin/tenants', async (req, res) => {
  let client;
  try {
    client = await pool.connect();
    
    const query = `
      SELECT 
        t.id,
        t.name,
        t.slug,
        t.status,
        t.email,
        t.phone,
        t.address,
        t.created_at,
        t.updated_at,
        COUNT(u.id) as user_count,
        COALESCE(SUM(tr.amount), 0) as total_revenue
      FROM tenants t
      LEFT JOIN users u ON t.id = u.tenant_id
      LEFT JOIN transactions tr ON t.id = tr.tenant_id
      GROUP BY t.id, t.name, t.slug, t.status, t.email, t.phone, t.address, t.created_at, t.updated_at
      ORDER BY t.created_at DESC
    `;
    
    const result = await client.query(query);
    
    const tenants = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      slug: row.slug,
      status: row.status || 'active',
      plan: 'pro',
      email: row.email,
      phone: row.phone,
      address: row.address,
      createdAt: row.created_at,
      lastLogin: row.updated_at,
      userCount: parseInt(row.user_count) || 0,
      monthlyRevenue: parseFloat(row.total_revenue) || 0,
      locations: 1,
      features: ['basic_pos', 'advanced_analytics', 'api_access']
    }));
    
    res.json(tenants);
  } catch (error) {
    console.error('❌ Error fetching tenants:', error.message);
    res.status(500).json({ error: error.message });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// User Management
app.get('/api/admin/users', async (req, res) => {
  let client;
  try {
    client = await pool.connect();

    const query = `
      SELECT
        u.id,
        u.name,
        u.email,
        u.role,
        u.tenant_id,
        t.name as tenant_name,
        u.status,
        u.last_login,
        u.created_at,
        u.permissions
      FROM users u
      LEFT JOIN tenants t ON u.tenant_id = t.id
      ORDER BY u.created_at DESC
    `;

    const result = await client.query(query);

    const users = result.rows.map(row => ({
      id: row.id,
      name: row.name,
      email: row.email,
      role: row.role,
      tenantId: row.tenant_id,
      tenantName: row.tenant_name || 'System',
      status: row.status,
      lastLogin: row.last_login,
      createdAt: row.created_at,
      permissions: row.permissions || ['pos_access']
    }));

    res.json(users);
  } catch (error) {
    console.error('❌ Error fetching users:', error.message);
    res.status(500).json({ error: error.message });
  } finally {
    if (client) {
      client.release();
    }
  }
});

// Analytics System
app.get('/api/admin/analytics/system', async (req, res) => {
  try {
    const analytics = {
      revenue: {
        total: 288.20,
        growth: 12.5,
        trend: 'up'
      },
      transactions: {
        total: 10,
        growth: 8.3,
        trend: 'up'
      },
      users: {
        total: 6,
        growth: 15.2,
        trend: 'up'
      },
      performance: {
        responseTime: 120,
        uptime: 99.8,
        errorRate: 0.2
      },
      lastUpdated: new Date().toISOString()
    };

    res.json(analytics);
  } catch (error) {
    console.error('❌ Error fetching analytics:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// AI Analytics
app.get('/api/admin/ai/analytics', async (req, res) => {
  try {
    const aiAnalytics = {
      predictions: {
        nextMonthRevenue: 350.75,
        customerGrowth: 18.5,
        popularItems: ['Coffee', 'Sandwich', 'Salad']
      },
      insights: [
        'Peak hours: 12:00-14:00 and 18:00-20:00',
        'Weekend sales 25% higher than weekdays',
        'Mobile orders increasing by 15% monthly'
      ],
      recommendations: [
        'Increase staff during peak hours',
        'Promote weekend specials',
        'Optimize mobile app experience'
      ],
      lastUpdated: new Date().toISOString()
    };

    res.json(aiAnalytics);
  } catch (error) {
    console.error('❌ Error fetching AI analytics:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// Security Audits
app.get('/api/admin/security/audits', async (req, res) => {
  try {
    const audits = [
      {
        id: 1,
        type: 'login_attempt',
        user: 'super-admin',
        action: 'Successful login',
        timestamp: new Date().toISOString(),
        ip: '127.0.0.1',
        status: 'success'
      },
      {
        id: 2,
        type: 'data_access',
        user: 'super-admin',
        action: 'Accessed tenant data',
        timestamp: new Date(Date.now() - 300000).toISOString(),
        ip: '127.0.0.1',
        status: 'success'
      },
      {
        id: 3,
        type: 'login_attempt',
        user: 'unknown',
        action: 'Failed login attempt',
        timestamp: new Date(Date.now() - 600000).toISOString(),
        ip: '*************',
        status: 'failed'
      }
    ];

    res.json(audits);
  } catch (error) {
    console.error('❌ Error fetching security audits:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// System Activity
app.get('/api/admin/activity', async (req, res) => {
  try {
    const activities = [
      {
        id: 1,
        type: 'system',
        message: 'Database backup completed successfully',
        timestamp: new Date().toISOString(),
        severity: 'info'
      },
      {
        id: 2,
        type: 'user',
        message: 'New tenant registered: Restaurant ABC',
        timestamp: new Date(Date.now() - 180000).toISOString(),
        severity: 'info'
      },
      {
        id: 3,
        type: 'system',
        message: 'Server restart completed',
        timestamp: new Date(Date.now() - 360000).toISOString(),
        severity: 'warning'
      },
      {
        id: 4,
        type: 'security',
        message: 'Multiple failed login attempts detected',
        timestamp: new Date(Date.now() - 540000).toISOString(),
        severity: 'error'
      }
    ];

    res.json(activities);
  } catch (error) {
    console.error('❌ Error fetching system activity:', error.message);
    res.status(500).json({ error: error.message });
  }
});

// POS System Endpoints
app.get('/api/products', (req, res) => {
  const products = [
    {
      id: 1,
      name: 'Espresso',
      price: 2.50,
      category: 'Coffee',
      description: 'Rich and bold espresso shot',
      image: '/images/espresso.jpg',
      available: true
    },
    {
      id: 2,
      name: 'Cappuccino',
      price: 3.75,
      category: 'Coffee',
      description: 'Espresso with steamed milk and foam',
      image: '/images/cappuccino.jpg',
      available: true
    },
    {
      id: 3,
      name: 'Club Sandwich',
      price: 8.50,
      category: 'Food',
      description: 'Triple-layer sandwich with turkey, bacon, lettuce, tomato',
      image: '/images/club-sandwich.jpg',
      available: true
    },
    {
      id: 4,
      name: 'Caesar Salad',
      price: 7.25,
      category: 'Food',
      description: 'Fresh romaine lettuce with Caesar dressing and croutons',
      image: '/images/caesar-salad.jpg',
      available: true
    },
    {
      id: 5,
      name: 'Chocolate Cake',
      price: 4.95,
      category: 'Dessert',
      description: 'Rich chocolate cake with chocolate frosting',
      image: '/images/chocolate-cake.jpg',
      available: true
    }
  ];

  res.json(products);
});

app.get('/api/categories', (req, res) => {
  const categories = [
    { id: 1, name: 'Coffee', color: '#8B4513' },
    { id: 2, name: 'Food', color: '#228B22' },
    { id: 3, name: 'Dessert', color: '#FF69B4' },
    { id: 4, name: 'Beverages', color: '#4169E1' }
  ];

  res.json(categories);
});

app.get('/api/floor/tables', (req, res) => {
  const tables = [
    {
      id: 1,
      number: 'T1',
      seats: 4,
      status: 'available',
      x: 100,
      y: 100,
      shape: 'round'
    },
    {
      id: 2,
      number: 'T2',
      seats: 2,
      status: 'occupied',
      x: 250,
      y: 100,
      shape: 'square',
      currentOrder: {
        id: 101,
        total: 25.50,
        items: 3
      }
    },
    {
      id: 3,
      number: 'T3',
      seats: 6,
      status: 'reserved',
      x: 400,
      y: 100,
      shape: 'rectangle',
      reservationTime: '19:30'
    },
    {
      id: 4,
      number: 'T4',
      seats: 4,
      status: 'available',
      x: 100,
      y: 250,
      shape: 'round'
    },
    {
      id: 5,
      number: 'T5',
      seats: 8,
      status: 'occupied',
      x: 250,
      y: 250,
      shape: 'rectangle',
      currentOrder: {
        id: 102,
        total: 45.75,
        items: 6
      }
    }
  ];

  res.json(tables);
});

app.get('/api/floor/layout', (req, res) => {
  const layout = {
    id: 1,
    name: 'Main Dining Area',
    width: 800,
    height: 600,
    tables: [
      { id: 1, number: 'T1', x: 100, y: 100, seats: 4, status: 'available', shape: 'round' },
      { id: 2, number: 'T2', x: 250, y: 100, seats: 2, status: 'occupied', shape: 'square' },
      { id: 3, number: 'T3', x: 400, y: 100, seats: 6, status: 'reserved', shape: 'rectangle' },
      { id: 4, number: 'T4', x: 100, y: 250, seats: 4, status: 'available', shape: 'round' },
      { id: 5, number: 'T5', x: 250, y: 250, seats: 8, status: 'occupied', shape: 'rectangle' }
    ],
    zones: [
      { id: 1, name: 'Main Area', x: 50, y: 50, width: 500, height: 300, color: '#f0f0f0' },
      { id: 2, name: 'VIP Section', x: 600, y: 50, width: 150, height: 200, color: '#ffe6e6' }
    ]
  };

  res.json(layout);
});

app.get('/tenants/current', (req, res) => {
  const tenant = {
    id: 1,
    name: 'Demo Restaurant',
    slug: 'demo-restaurant',
    status: 'active',
    plan: 'pro',
    features: ['basic_pos', 'advanced_analytics', 'floor_management', 'multi_currency', 'multi_language'],
    settings: {
      currency: 'CAD',
      timezone: 'America/Toronto',
      language: 'en-US',
      supportedLanguages: ['en-US', 'es-ES', 'fr-FR', 'de-DE', 'it-IT'],
      culturalSettings: {
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        numberFormat: 'en-US',
        currencyPosition: 'before',
        weekStart: 'sunday',
        layoutDirection: 'ltr'
      }
    }
  };

  res.json(tenant);
});

// Phase 3I: Multi-Language Localization System
// Translation Engine API Endpoints

// Get supported languages
app.get('/api/i18n/languages', (req, res) => {
  const languages = [
    // Tier 1: Primary Markets
    { code: 'en-US', name: 'English (US)', nativeName: 'English', flag: '🇺🇸', tier: 1, rtl: false },
    { code: 'es-ES', name: 'Spanish (Spain)', nativeName: 'Español', flag: '🇪🇸', tier: 1, rtl: false },
    { code: 'es-MX', name: 'Spanish (Mexico)', nativeName: 'Español (México)', flag: '🇲🇽', tier: 1, rtl: false },
    { code: 'fr-FR', name: 'French (France)', nativeName: 'Français', flag: '🇫🇷', tier: 1, rtl: false },
    { code: 'fr-CA', name: 'French (Canada)', nativeName: 'Français (Canada)', flag: '🇨🇦', tier: 1, rtl: false },
    { code: 'de-DE', name: 'German', nativeName: 'Deutsch', flag: '🇩🇪', tier: 1, rtl: false },
    { code: 'it-IT', name: 'Italian', nativeName: 'Italiano', flag: '🇮🇹', tier: 1, rtl: false },

    // Tier 2: Asian Markets
    { code: 'zh-CN', name: 'Chinese (Simplified)', nativeName: '简体中文', flag: '🇨🇳', tier: 2, rtl: false },
    { code: 'zh-TW', name: 'Chinese (Traditional)', nativeName: '繁體中文', flag: '🇹🇼', tier: 2, rtl: false },
    { code: 'ja-JP', name: 'Japanese', nativeName: '日本語', flag: '🇯🇵', tier: 2, rtl: false },
    { code: 'ko-KR', name: 'Korean', nativeName: '한국어', flag: '🇰🇷', tier: 2, rtl: false },
    { code: 'hi-IN', name: 'Hindi', nativeName: 'हिन्दी', flag: '🇮🇳', tier: 2, rtl: false },

    // Tier 3: Emerging Markets
    { code: 'pt-BR', name: 'Portuguese (Brazil)', nativeName: 'Português (Brasil)', flag: '🇧🇷', tier: 3, rtl: false },
    { code: 'pt-PT', name: 'Portuguese (Portugal)', nativeName: 'Português', flag: '🇵🇹', tier: 3, rtl: false },
    { code: 'ar-SA', name: 'Arabic', nativeName: 'العربية', flag: '🇸🇦', tier: 3, rtl: true },
    { code: 'ru-RU', name: 'Russian', nativeName: 'Русский', flag: '🇷🇺', tier: 3, rtl: false },
    { code: 'nl-NL', name: 'Dutch', nativeName: 'Nederlands', flag: '🇳🇱', tier: 3, rtl: false },
    { code: 'sv-SE', name: 'Swedish', nativeName: 'Svenska', flag: '🇸🇪', tier: 3, rtl: false }
  ];

  res.json({
    languages,
    total: languages.length,
    tiers: {
      tier1: languages.filter(l => l.tier === 1).length,
      tier2: languages.filter(l => l.tier === 2).length,
      tier3: languages.filter(l => l.tier === 3).length
    },
    rtlSupported: languages.filter(l => l.rtl).length,
    lastUpdated: new Date().toISOString()
  });
});

// Get translations for a specific language
app.get('/api/i18n/translations/:language', (req, res) => {
  const { language } = req.params;

  // Base translations (English)
  const baseTranslations = {
    // Navigation & Common
    'nav.dashboard': 'Dashboard',
    'nav.pos': 'Point of Sale',
    'nav.orders': 'Orders',
    'nav.menu': 'Menu',
    'nav.inventory': 'Inventory',
    'nav.analytics': 'Analytics',
    'nav.settings': 'Settings',
    'nav.logout': 'Logout',

    // Common Actions
    'action.save': 'Save',
    'action.cancel': 'Cancel',
    'action.delete': 'Delete',
    'action.edit': 'Edit',
    'action.add': 'Add',
    'action.search': 'Search',
    'action.filter': 'Filter',
    'action.export': 'Export',
    'action.print': 'Print',
    'action.refresh': 'Refresh',

    // POS System
    'pos.title': 'Point of Sale',
    'pos.total': 'Total',
    'pos.subtotal': 'Subtotal',
    'pos.tax': 'Tax',
    'pos.discount': 'Discount',
    'pos.payment': 'Payment',
    'pos.cash': 'Cash',
    'pos.card': 'Card',
    'pos.receipt': 'Receipt',
    'pos.order_complete': 'Order Complete',

    // Menu Management
    'menu.categories': 'Categories',
    'menu.products': 'Products',
    'menu.price': 'Price',
    'menu.description': 'Description',
    'menu.available': 'Available',
    'menu.out_of_stock': 'Out of Stock',

    // Kitchen Display
    'kitchen.orders': 'Kitchen Orders',
    'kitchen.preparing': 'Preparing',
    'kitchen.ready': 'Ready',
    'kitchen.served': 'Served',
    'kitchen.prep_time': 'Prep Time',

    // Analytics
    'analytics.revenue': 'Revenue',
    'analytics.orders': 'Orders',
    'analytics.customers': 'Customers',
    'analytics.growth': 'Growth',
    'analytics.today': 'Today',
    'analytics.week': 'This Week',
    'analytics.month': 'This Month',

    // Floor Layout
    'floor.tables': 'Tables',
    'floor.available': 'Available',
    'floor.occupied': 'Occupied',
    'floor.reserved': 'Reserved',
    'floor.cleaning': 'Cleaning',

    // Time & Date
    'time.morning': 'Morning',
    'time.afternoon': 'Afternoon',
    'time.evening': 'Evening',
    'time.night': 'Night',
    'date.today': 'Today',
    'date.yesterday': 'Yesterday',
    'date.tomorrow': 'Tomorrow'
  };

  // Language-specific translations
  const translations = {
    'en-US': baseTranslations,
    'es-ES': {
      ...baseTranslations,
      'nav.dashboard': 'Panel de Control',
      'nav.pos': 'Punto de Venta',
      'nav.orders': 'Pedidos',
      'nav.menu': 'Menú',
      'nav.inventory': 'Inventario',
      'nav.analytics': 'Analíticas',
      'nav.settings': 'Configuración',
      'nav.logout': 'Cerrar Sesión',
      'action.save': 'Guardar',
      'action.cancel': 'Cancelar',
      'action.delete': 'Eliminar',
      'action.edit': 'Editar',
      'action.add': 'Agregar',
      'pos.title': 'Punto de Venta',
      'pos.total': 'Total',
      'pos.payment': 'Pago',
      'pos.receipt': 'Recibo'
    },
    'fr-FR': {
      ...baseTranslations,
      'nav.dashboard': 'Tableau de Bord',
      'nav.pos': 'Point de Vente',
      'nav.orders': 'Commandes',
      'nav.menu': 'Menu',
      'nav.inventory': 'Inventaire',
      'nav.analytics': 'Analytiques',
      'nav.settings': 'Paramètres',
      'nav.logout': 'Déconnexion',
      'action.save': 'Enregistrer',
      'action.cancel': 'Annuler',
      'action.delete': 'Supprimer',
      'pos.title': 'Point de Vente',
      'pos.total': 'Total',
      'pos.payment': 'Paiement'
    },
    'de-DE': {
      ...baseTranslations,
      'nav.dashboard': 'Dashboard',
      'nav.pos': 'Kassensystem',
      'nav.orders': 'Bestellungen',
      'nav.menu': 'Speisekarte',
      'nav.inventory': 'Inventar',
      'nav.analytics': 'Analytik',
      'nav.settings': 'Einstellungen',
      'nav.logout': 'Abmelden',
      'action.save': 'Speichern',
      'action.cancel': 'Abbrechen',
      'pos.title': 'Kassensystem',
      'pos.total': 'Gesamt',
      'pos.payment': 'Zahlung'
    }
  };

  const languageTranslations = translations[language] || translations['en-US'];

  res.json({
    language,
    translations: languageTranslations,
    totalKeys: Object.keys(languageTranslations).length,
    coverage: language === 'en-US' ? 100 : Math.round((Object.keys(languageTranslations).length / Object.keys(baseTranslations).length) * 100),
    lastUpdated: new Date().toISOString(),
    aiGenerated: language !== 'en-US',
    verified: language === 'en-US' || ['es-ES', 'fr-FR', 'de-DE'].includes(language)
  });
});

// Get cultural settings for a specific language/region
app.get('/api/i18n/cultural/:language', (req, res) => {
  const { language } = req.params;

  const culturalSettings = {
    'en-US': {
      dateFormat: 'MM/DD/YYYY',
      timeFormat: '12h',
      numberFormat: 'en-US',
      currencyPosition: 'before',
      weekStart: 'sunday',
      layoutDirection: 'ltr',
      colorScheme: 'blue',
      greetings: ['Good morning', 'Good afternoon', 'Good evening'],
      businessHours: { start: '09:00', end: '22:00' },
      tipping: { enabled: true, defaultPercent: 18, suggestions: [15, 18, 20, 25] }
    },
    'es-ES': {
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      numberFormat: 'es-ES',
      currencyPosition: 'after',
      weekStart: 'monday',
      layoutDirection: 'ltr',
      colorScheme: 'red',
      greetings: ['Buenos días', 'Buenas tardes', 'Buenas noches'],
      businessHours: { start: '10:00', end: '23:00' },
      tipping: { enabled: true, defaultPercent: 10, suggestions: [5, 10, 15] }
    },
    'fr-FR': {
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '24h',
      numberFormat: 'fr-FR',
      currencyPosition: 'after',
      weekStart: 'monday',
      layoutDirection: 'ltr',
      colorScheme: 'blue',
      greetings: ['Bonjour', 'Bon après-midi', 'Bonsoir'],
      businessHours: { start: '11:00', end: '23:00' },
      tipping: { enabled: true, defaultPercent: 15, suggestions: [10, 15, 20] }
    },
    'de-DE': {
      dateFormat: 'DD.MM.YYYY',
      timeFormat: '24h',
      numberFormat: 'de-DE',
      currencyPosition: 'after',
      weekStart: 'monday',
      layoutDirection: 'ltr',
      colorScheme: 'green',
      greetings: ['Guten Morgen', 'Guten Tag', 'Guten Abend'],
      businessHours: { start: '10:00', end: '22:00' },
      tipping: { enabled: true, defaultPercent: 10, suggestions: [5, 10, 15] }
    },
    'ar-SA': {
      dateFormat: 'DD/MM/YYYY',
      timeFormat: '12h',
      numberFormat: 'ar-SA',
      currencyPosition: 'before',
      weekStart: 'saturday',
      layoutDirection: 'rtl',
      colorScheme: 'gold',
      greetings: ['صباح الخير', 'مساء الخير', 'تصبح على خير'],
      businessHours: { start: '12:00', end: '24:00' },
      tipping: { enabled: true, defaultPercent: 10, suggestions: [10, 15, 20] }
    },
    'zh-CN': {
      dateFormat: 'YYYY/MM/DD',
      timeFormat: '24h',
      numberFormat: 'zh-CN',
      currencyPosition: 'before',
      weekStart: 'monday',
      layoutDirection: 'ltr',
      colorScheme: 'red',
      greetings: ['早上好', '下午好', '晚上好'],
      businessHours: { start: '10:00', end: '22:00' },
      tipping: { enabled: false, defaultPercent: 0, suggestions: [] }
    },
    'ja-JP': {
      dateFormat: 'YYYY/MM/DD',
      timeFormat: '24h',
      numberFormat: 'ja-JP',
      currencyPosition: 'before',
      weekStart: 'sunday',
      layoutDirection: 'ltr',
      colorScheme: 'purple',
      greetings: ['おはようございます', 'こんにちは', 'こんばんは'],
      businessHours: { start: '11:00', end: '23:00' },
      tipping: { enabled: false, defaultPercent: 0, suggestions: [] }
    }
  };

  const settings = culturalSettings[language] || culturalSettings['en-US'];

  res.json({
    language,
    cultural: settings,
    isRTL: settings.layoutDirection === 'rtl',
    lastUpdated: new Date().toISOString()
  });
});

// Voice recognition settings and commands
app.get('/api/i18n/voice/:language', (req, res) => {
  const { language } = req.params;

  const voiceCommands = {
    'en-US': {
      commands: [
        { phrase: 'open pos', action: 'navigate_pos', confidence: 0.95 },
        { phrase: 'show menu', action: 'show_menu', confidence: 0.92 },
        { phrase: 'add item', action: 'add_item', confidence: 0.88 },
        { phrase: 'process payment', action: 'process_payment', confidence: 0.94 },
        { phrase: 'print receipt', action: 'print_receipt', confidence: 0.91 },
        { phrase: 'cancel order', action: 'cancel_order', confidence: 0.89 },
        { phrase: 'help', action: 'show_help', confidence: 0.96 }
      ],
      greetings: ['Hello', 'Hi there', 'Good morning', 'Welcome'],
      confirmations: ['Yes', 'Confirm', 'Proceed', 'Continue'],
      cancellations: ['No', 'Cancel', 'Stop', 'Abort']
    },
    'es-ES': {
      commands: [
        { phrase: 'abrir punto de venta', action: 'navigate_pos', confidence: 0.93 },
        { phrase: 'mostrar menú', action: 'show_menu', confidence: 0.90 },
        { phrase: 'agregar artículo', action: 'add_item', confidence: 0.86 },
        { phrase: 'procesar pago', action: 'process_payment', confidence: 0.92 },
        { phrase: 'imprimir recibo', action: 'print_receipt', confidence: 0.89 },
        { phrase: 'cancelar pedido', action: 'cancel_order', confidence: 0.87 },
        { phrase: 'ayuda', action: 'show_help', confidence: 0.94 }
      ],
      greetings: ['Hola', 'Buenos días', 'Bienvenido'],
      confirmations: ['Sí', 'Confirmar', 'Proceder', 'Continuar'],
      cancellations: ['No', 'Cancelar', 'Parar', 'Abortar']
    },
    'fr-FR': {
      commands: [
        { phrase: 'ouvrir point de vente', action: 'navigate_pos', confidence: 0.91 },
        { phrase: 'afficher menu', action: 'show_menu', confidence: 0.88 },
        { phrase: 'ajouter article', action: 'add_item', confidence: 0.84 },
        { phrase: 'traiter paiement', action: 'process_payment', confidence: 0.90 },
        { phrase: 'imprimer reçu', action: 'print_receipt', confidence: 0.87 },
        { phrase: 'annuler commande', action: 'cancel_order', confidence: 0.85 },
        { phrase: 'aide', action: 'show_help', confidence: 0.92 }
      ],
      greetings: ['Bonjour', 'Salut', 'Bienvenue'],
      confirmations: ['Oui', 'Confirmer', 'Procéder', 'Continuer'],
      cancellations: ['Non', 'Annuler', 'Arrêter', 'Abandonner']
    }
  };

  const commands = voiceCommands[language] || voiceCommands['en-US'];

  res.json({
    language,
    voice: {
      ...commands,
      enabled: true,
      autoDetection: true,
      dialectSupport: ['es-ES', 'es-MX', 'fr-FR', 'fr-CA'].includes(language),
      confidenceThreshold: 0.8,
      noiseReduction: true,
      continuousListening: false
    },
    totalCommands: commands.commands.length,
    averageConfidence: commands.commands.reduce((acc, cmd) => acc + cmd.confidence, 0) / commands.commands.length,
    lastUpdated: new Date().toISOString()
  });
});

// AI Translation endpoint (simulated)
app.post('/api/i18n/translate', (req, res) => {
  const { text, from, to, context } = req.body;

  if (!text || !to) {
    return res.status(400).json({ error: 'Text and target language are required' });
  }

  // Simulated AI translation with confidence scoring
  const translations = {
    'es-ES': {
      'Hello': 'Hola',
      'Thank you': 'Gracias',
      'Order': 'Pedido',
      'Payment': 'Pago',
      'Receipt': 'Recibo'
    },
    'fr-FR': {
      'Hello': 'Bonjour',
      'Thank you': 'Merci',
      'Order': 'Commande',
      'Payment': 'Paiement',
      'Receipt': 'Reçu'
    },
    'de-DE': {
      'Hello': 'Hallo',
      'Thank you': 'Danke',
      'Order': 'Bestellung',
      'Payment': 'Zahlung',
      'Receipt': 'Quittung'
    }
  };

  const targetTranslations = translations[to] || {};
  const translatedText = targetTranslations[text] || `[AI: ${text} → ${to}]`;

  res.json({
    originalText: text,
    translatedText,
    sourceLanguage: from || 'auto-detected',
    targetLanguage: to,
    confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
    context: context || 'general',
    aiGenerated: true,
    processingTime: Math.random() * 200 + 50, // 50-250ms
    timestamp: new Date().toISOString()
  });
});

// Phase 3J: Advanced Voice Recognition & Natural Language Processing
// Voice Recognition and NLP API Endpoints

// Voice recognition endpoint
app.post('/api/voice/recognize', (req, res) => {
  const { audioData, language, context } = req.body;

  if (!audioData) {
    return res.status(400).json({ error: 'Audio data is required' });
  }

  // Simulated speech recognition with restaurant-specific vocabulary
  const restaurantPhrases = [
    'I would like to order a large pepperoni pizza',
    'Add extra cheese to that order',
    'Can I get a medium Coke with that',
    'Table five is ready for their order',
    'Process payment for forty-five dollars',
    'Show me today\'s sales report',
    'Send this order to the kitchen',
    'Apply fifteen percent discount',
    'Split the bill three ways',
    'Mark table two as occupied'
  ];

  const recognizedText = restaurantPhrases[Math.floor(Math.random() * restaurantPhrases.length)];
  const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence

  res.json({
    recognizedText,
    confidence,
    language: language || 'en-US',
    context: context || 'restaurant',
    processingTime: Math.random() * 300 + 100, // 100-400ms
    timestamp: new Date().toISOString(),
    audioQuality: Math.random() * 0.2 + 0.8, // 80-100% quality
    noiseLevel: Math.random() * 0.3, // 0-30% noise
    speakerIdentified: Math.random() > 0.3 // 70% chance of speaker identification
  });
});

// Voice authentication endpoint
app.post('/api/voice/authenticate', (req, res) => {
  const { voiceSample, userId, enrollmentMode } = req.body;

  if (!voiceSample) {
    return res.status(400).json({ error: 'Voice sample is required' });
  }

  if (enrollmentMode) {
    // Voice enrollment simulation
    res.json({
      enrolled: true,
      userId: userId || 'user_' + Math.random().toString(36).substr(2, 9),
      voicePrintId: 'vp_' + Math.random().toString(36).substr(2, 12),
      enrollmentQuality: Math.random() * 0.2 + 0.8, // 80-100% quality
      samplesRequired: 3,
      samplesCompleted: 1,
      message: 'Voice enrollment successful. Please provide 2 more samples.',
      timestamp: new Date().toISOString()
    });
  } else {
    // Voice verification simulation
    const isAuthenticated = Math.random() > 0.1; // 90% success rate
    const confidence = isAuthenticated ? Math.random() * 0.2 + 0.8 : Math.random() * 0.4 + 0.1;

    res.json({
      authenticated: isAuthenticated,
      confidence,
      userId: isAuthenticated ? userId || 'user_authenticated' : null,
      securityLevel: isAuthenticated ? 'high' : 'failed',
      antiSpoofingScore: Math.random() * 0.2 + 0.8, // 80-100% anti-spoofing
      processingTime: Math.random() * 200 + 50,
      timestamp: new Date().toISOString(),
      message: isAuthenticated ? 'Voice authentication successful' : 'Voice authentication failed'
    });
  }
});

// Natural Language Processing endpoint
app.post('/api/nlp/process', (req, res) => {
  const { text, context, language } = req.body;

  if (!text) {
    return res.status(400).json({ error: 'Text is required for NLP processing' });
  }

  // Intent recognition for restaurant operations
  const intents = {
    'order': ['order', 'want', 'like', 'get', 'add', 'pizza', 'burger', 'drink'],
    'payment': ['pay', 'bill', 'check', 'card', 'cash', 'total', 'charge'],
    'table': ['table', 'seat', 'booth', 'counter', 'reservation'],
    'kitchen': ['kitchen', 'cook', 'ready', 'prepare', 'chef'],
    'report': ['report', 'sales', 'analytics', 'show', 'display'],
    'help': ['help', 'assist', 'support', 'problem', 'issue']
  };

  let detectedIntent = 'unknown';
  let confidence = 0;

  const lowerText = text.toLowerCase();
  for (const [intent, keywords] of Object.entries(intents)) {
    const matches = keywords.filter(keyword => lowerText.includes(keyword)).length;
    const intentConfidence = matches / keywords.length;
    if (intentConfidence > confidence) {
      confidence = intentConfidence;
      detectedIntent = intent;
    }
  }

  // Entity extraction
  const entities = [];

  // Extract numbers (quantities, prices, table numbers)
  const numbers = text.match(/\b\d+(\.\d+)?\b/g) || [];
  numbers.forEach(num => {
    entities.push({
      type: 'number',
      value: parseFloat(num),
      text: num,
      confidence: 0.95
    });
  });

  // Extract food items
  const foodItems = ['pizza', 'burger', 'salad', 'fries', 'coke', 'water', 'coffee', 'beer'];
  foodItems.forEach(item => {
    if (lowerText.includes(item)) {
      entities.push({
        type: 'food_item',
        value: item,
        text: item,
        confidence: 0.9
      });
    }
  });

  // Extract modifiers
  const modifiers = ['large', 'medium', 'small', 'extra', 'no', 'without', 'with'];
  modifiers.forEach(modifier => {
    if (lowerText.includes(modifier)) {
      entities.push({
        type: 'modifier',
        value: modifier,
        text: modifier,
        confidence: 0.85
      });
    }
  });

  // Generate appropriate response
  const responses = {
    'order': 'I understand you want to place an order. What would you like?',
    'payment': 'I can help you with payment. What\'s the total amount?',
    'table': 'Which table would you like to manage?',
    'kitchen': 'I can send orders to the kitchen or check order status.',
    'report': 'I can show you sales reports and analytics. What would you like to see?',
    'help': 'I\'m here to help! What do you need assistance with?',
    'unknown': 'I\'m not sure I understood. Could you please rephrase that?'
  };

  res.json({
    originalText: text,
    intent: {
      name: detectedIntent,
      confidence: Math.max(confidence, 0.6) // Minimum 60% confidence
    },
    entities,
    language: language || 'en-US',
    context: context || 'restaurant',
    response: responses[detectedIntent],
    processingTime: Math.random() * 150 + 50, // 50-200ms
    timestamp: new Date().toISOString(),
    conversationId: 'conv_' + Math.random().toString(36).substr(2, 10)
  });
});

// Voice analytics endpoint
app.get('/api/voice/analytics', (req, res) => {
  const analytics = {
    overview: {
      totalVoiceInteractions: 1247,
      averageAccuracy: 94.2,
      averageResponseTime: 245, // milliseconds
      successfulAuthentications: 1156,
      failedAuthentications: 91,
      topLanguages: ['en-US', 'es-ES', 'fr-FR']
    },
    performance: {
      speechRecognitionAccuracy: {
        overall: 94.2,
        byLanguage: {
          'en-US': 96.8,
          'es-ES': 92.1,
          'fr-FR': 91.5,
          'de-DE': 89.3,
          'it-IT': 88.7
        },
        byContext: {
          'order_taking': 95.1,
          'payment': 93.8,
          'navigation': 92.4,
          'reporting': 91.2
        }
      },
      intentRecognition: {
        overall: 91.7,
        byIntent: {
          'order': 94.5,
          'payment': 92.1,
          'table': 89.8,
          'kitchen': 88.3,
          'report': 87.9,
          'help': 95.2
        }
      },
      voiceAuthentication: {
        accuracy: 99.1,
        falsePositiveRate: 0.3,
        falseNegativeRate: 0.6,
        averageEnrollmentTime: 45, // seconds
        averageVerificationTime: 1.2 // seconds
      }
    },
    usage: {
      dailyInteractions: [
        { date: '2025-06-04', interactions: 156, accuracy: 93.1 },
        { date: '2025-06-05', interactions: 189, accuracy: 94.8 },
        { date: '2025-06-06', interactions: 203, accuracy: 95.2 },
        { date: '2025-06-07', interactions: 178, accuracy: 93.9 },
        { date: '2025-06-08', interactions: 234, accuracy: 96.1 },
        { date: '2025-06-09', interactions: 198, accuracy: 94.7 },
        { date: '2025-06-10', interactions: 89, accuracy: 95.3 }
      ],
      peakHours: [
        { hour: 11, interactions: 89, accuracy: 94.2 },
        { hour: 12, interactions: 156, accuracy: 95.1 },
        { hour: 13, interactions: 134, accuracy: 93.8 },
        { hour: 18, interactions: 178, accuracy: 96.2 },
        { hour: 19, interactions: 203, accuracy: 95.7 },
        { hour: 20, interactions: 167, accuracy: 94.9 }
      ],
      commandFrequency: [
        { command: 'add item to order', count: 234, accuracy: 96.1 },
        { command: 'process payment', count: 189, accuracy: 94.8 },
        { command: 'show table status', count: 156, accuracy: 92.3 },
        { command: 'send to kitchen', count: 134, accuracy: 95.7 },
        { command: 'apply discount', count: 98, accuracy: 91.2 },
        { command: 'split bill', count: 76, accuracy: 89.8 }
      ]
    },
    improvements: {
      suggestions: [
        {
          area: 'Speech Recognition',
          issue: 'Lower accuracy for German language',
          recommendation: 'Increase German training data and accent variations',
          priority: 'medium',
          estimatedImprovement: '3-5%'
        },
        {
          area: 'Intent Recognition',
          issue: 'Confusion between table and kitchen commands',
          recommendation: 'Enhance context awareness and add disambiguation prompts',
          priority: 'high',
          estimatedImprovement: '7-10%'
        },
        {
          area: 'Voice Authentication',
          issue: 'Slightly higher false negative rate',
          recommendation: 'Adjust sensitivity thresholds and improve noise filtering',
          priority: 'low',
          estimatedImprovement: '1-2%'
        }
      ],
      trends: {
        accuracyTrend: 'improving', // +2.3% over last month
        usageTrend: 'increasing', // +15% over last month
        satisfactionTrend: 'stable' // 4.2/5 rating
      }
    }
  };

  res.json({
    analytics,
    generatedAt: new Date().toISOString(),
    reportPeriod: {
      start: '2025-06-04T00:00:00Z',
      end: '2025-06-10T23:59:59Z'
    },
    nextUpdate: new Date(Date.now() + 30 * 60 * 1000).toISOString() // 30 minutes
  });
});

// Conversation management endpoint
app.post('/api/voice/conversation', (req, res) => {
  const { conversationId, message, context, userId } = req.body;

  if (!message) {
    return res.status(400).json({ error: 'Message is required' });
  }

  // Simulated conversation state management
  const conversationStates = {
    'greeting': {
      responses: [
        'Hello! Welcome to our restaurant. How can I help you today?',
        'Good day! I\'m your AI assistant. What would you like to order?',
        'Hi there! Ready to place an order or need help with something?'
      ],
      nextStates: ['ordering', 'help', 'information']
    },
    'ordering': {
      responses: [
        'Great! What would you like to order?',
        'I can help you with that. What items would you like?',
        'Perfect! Let me take your order. What can I get for you?'
      ],
      nextStates: ['item_selection', 'modification', 'confirmation']
    },
    'item_selection': {
      responses: [
        'Excellent choice! Would you like to add anything else?',
        'Added to your order. Anything else?',
        'Got it! Any modifications or additional items?'
      ],
      nextStates: ['modification', 'confirmation', 'payment']
    },
    'modification': {
      responses: [
        'Sure, I can modify that for you. What changes would you like?',
        'No problem! How would you like to customize that?',
        'Of course! What modifications would you like to make?'
      ],
      nextStates: ['confirmation', 'item_selection']
    },
    'confirmation': {
      responses: [
        'Let me confirm your order: [ORDER_SUMMARY]. Is that correct?',
        'Your order summary: [ORDER_SUMMARY]. Does everything look right?',
        'Here\'s what I have: [ORDER_SUMMARY]. Shall I proceed?'
      ],
      nextStates: ['payment', 'modification', 'completion']
    },
    'payment': {
      responses: [
        'How would you like to pay? Cash, card, or mobile payment?',
        'Great! What payment method would you prefer?',
        'Perfect! How will you be paying today?'
      ],
      nextStates: ['completion', 'payment_processing']
    },
    'completion': {
      responses: [
        'Thank you for your order! It will be ready shortly.',
        'Order complete! We\'ll have that ready for you soon.',
        'Perfect! Your order is being prepared. Thank you!'
      ],
      nextStates: ['greeting']
    }
  };

  // Determine current conversation state based on message content
  const currentState = determineConversationState(message.toLowerCase());
  const stateConfig = conversationStates[currentState] || conversationStates['greeting'];

  const response = stateConfig.responses[Math.floor(Math.random() * stateConfig.responses.length)];
  const nextState = stateConfig.nextStates[Math.floor(Math.random() * stateConfig.nextStates.length)];

  res.json({
    conversationId: conversationId || 'conv_' + Math.random().toString(36).substr(2, 10),
    currentState,
    nextState,
    response,
    context: context || 'restaurant',
    userId: userId || null,
    timestamp: new Date().toISOString(),
    confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
    suggestions: [
      'Add another item',
      'Modify current order',
      'Proceed to payment',
      'Cancel order'
    ]
  });
});

// Helper function to determine conversation state
function determineConversationState(message) {
  if (message.includes('hello') || message.includes('hi') || message.includes('start')) {
    return 'greeting';
  } else if (message.includes('order') || message.includes('want') || message.includes('like')) {
    return 'ordering';
  } else if (message.includes('add') || message.includes('pizza') || message.includes('burger')) {
    return 'item_selection';
  } else if (message.includes('change') || message.includes('modify') || message.includes('extra')) {
    return 'modification';
  } else if (message.includes('confirm') || message.includes('correct') || message.includes('yes')) {
    return 'confirmation';
  } else if (message.includes('pay') || message.includes('bill') || message.includes('card')) {
    return 'payment';
  } else if (message.includes('thank') || message.includes('done') || message.includes('complete')) {
    return 'completion';
  } else {
    return 'greeting';
  }
}

// Phase 3K: AI Cultural Intelligence System
// Cultural Intelligence and Emotional Analysis API Endpoints

// Cultural behavior analysis endpoint
app.post('/api/cultural/analyze', (req, res) => {
  const { customerData, region, context, interactionHistory } = req.body;

  if (!customerData) {
    return res.status(400).json({ error: 'Customer data is required for cultural analysis' });
  }

  // Cultural profiles database
  const culturalProfiles = {
    'north-america': {
      region: 'North America',
      communicationStyle: 'direct',
      diningPreferences: ['individual', 'fast-service', 'customization'],
      timeOrientation: 'punctual',
      socialDynamics: 'individualistic',
      emotionalExpression: 'open',
      serviceExpectations: ['efficiency', 'friendliness', 'personalization'],
      tippingCulture: { expected: true, percentage: 18, range: [15, 25] },
      paymentPreferences: ['card', 'mobile', 'contactless'],
      culturalEvents: ['thanksgiving', 'independence-day', 'christmas']
    },
    'east-asia': {
      region: 'East Asia',
      communicationStyle: 'indirect',
      diningPreferences: ['sharing', 'family-style', 'traditional'],
      timeOrientation: 'flexible',
      socialDynamics: 'collective',
      emotionalExpression: 'reserved',
      serviceExpectations: ['respect', 'attention-to-detail', 'harmony'],
      tippingCulture: { expected: false, percentage: 0, range: [0, 5] },
      paymentPreferences: ['cash', 'mobile', 'digital-wallet'],
      culturalEvents: ['chinese-new-year', 'mid-autumn-festival', 'golden-week']
    },
    'middle-east': {
      region: 'Middle East',
      communicationStyle: 'high-context',
      diningPreferences: ['halal', 'family-style', 'hospitality'],
      timeOrientation: 'relationship-based',
      socialDynamics: 'hierarchical',
      emotionalExpression: 'expressive',
      serviceExpectations: ['hospitality', 'respect', 'accommodation'],
      tippingCulture: { expected: true, percentage: 10, range: [10, 15] },
      paymentPreferences: ['cash', 'card', 'islamic-banking'],
      culturalEvents: ['ramadan', 'eid-al-fitr', 'eid-al-adha']
    },
    'europe': {
      region: 'Europe',
      communicationStyle: 'formal',
      diningPreferences: ['quality', 'traditional', 'leisurely'],
      timeOrientation: 'punctual',
      socialDynamics: 'formal',
      emotionalExpression: 'controlled',
      serviceExpectations: ['professionalism', 'quality', 'tradition'],
      tippingCulture: { expected: true, percentage: 10, range: [5, 15] },
      paymentPreferences: ['card', 'cash', 'contactless'],
      culturalEvents: ['christmas', 'easter', 'national-days']
    },
    'latin-america': {
      region: 'Latin America',
      communicationStyle: 'warm',
      diningPreferences: ['family-style', 'social', 'flavorful'],
      timeOrientation: 'flexible',
      socialDynamics: 'family-oriented',
      emotionalExpression: 'expressive',
      serviceExpectations: ['warmth', 'personal-connection', 'celebration'],
      tippingCulture: { expected: true, percentage: 15, range: [10, 20] },
      paymentPreferences: ['cash', 'card', 'mobile'],
      culturalEvents: ['dia-de-los-muertos', 'carnival', 'independence-days']
    }
  };

  const profile = culturalProfiles[region] || culturalProfiles['north-america'];

  // AI-powered cultural behavior analysis
  const behaviorAnalysis = {
    communicationRecommendations: generateCommunicationRecommendations(profile),
    serviceAdaptations: generateServiceAdaptations(profile),
    menuRecommendations: generateMenuRecommendations(profile),
    timingConsiderations: generateTimingConsiderations(profile),
    culturalSensitivities: generateCulturalSensitivities(profile)
  };

  res.json({
    culturalProfile: profile,
    behaviorAnalysis,
    confidence: Math.random() * 0.2 + 0.8, // 80-100% confidence
    recommendations: generateCulturalRecommendations(profile, context),
    adaptations: generateCulturalAdaptations(profile),
    timestamp: new Date().toISOString(),
    analysisId: 'cultural_' + Math.random().toString(36).substr(2, 10)
  });
});

// Emotion recognition endpoint
app.post('/api/cultural/emotion', (req, res) => {
  const { voiceData, textData, culturalContext, language } = req.body;

  if (!voiceData && !textData) {
    return res.status(400).json({ error: 'Voice or text data is required for emotion analysis' });
  }

  // Simulated emotion recognition with cultural context
  const emotions = ['happy', 'sad', 'angry', 'excited', 'frustrated', 'calm', 'confused', 'satisfied'];
  const detectedEmotion = emotions[Math.floor(Math.random() * emotions.length)];
  const confidence = Math.random() * 0.3 + 0.7; // 70-100% confidence

  // Cultural emotion interpretation
  const culturalEmotionMapping = {
    'east-asia': {
      'angry': { interpretation: 'mild-displeasure', response: 'gentle-acknowledgment' },
      'frustrated': { interpretation: 'concern', response: 'respectful-assistance' },
      'happy': { interpretation: 'satisfaction', response: 'humble-appreciation' }
    },
    'middle-east': {
      'angry': { interpretation: 'strong-displeasure', response: 'respectful-resolution' },
      'frustrated': { interpretation: 'impatience', response: 'immediate-attention' },
      'happy': { interpretation: 'joy', response: 'shared-celebration' }
    },
    'north-america': {
      'angry': { interpretation: 'dissatisfaction', response: 'direct-resolution' },
      'frustrated': { interpretation: 'annoyance', response: 'efficient-solution' },
      'happy': { interpretation: 'satisfaction', response: 'friendly-engagement' }
    }
  };

  const culturalMapping = culturalEmotionMapping[culturalContext] || culturalEmotionMapping['north-america'];
  const emotionInterpretation = culturalMapping[detectedEmotion] || {
    interpretation: detectedEmotion,
    response: 'appropriate-response'
  };

  // Generate culturally appropriate response
  const responses = {
    'gentle-acknowledgment': 'I understand your concern. Please allow me to assist you.',
    'respectful-assistance': 'I apologize for any inconvenience. How may I help you?',
    'humble-appreciation': 'Thank you for your patience. We appreciate your business.',
    'respectful-resolution': 'I sincerely apologize. Let me resolve this immediately.',
    'immediate-attention': 'I understand this is important. Let me help you right away.',
    'shared-celebration': 'Wonderful! We are delighted to serve you.',
    'direct-resolution': 'I understand your frustration. Let me fix this for you.',
    'efficient-solution': 'I can help you with that quickly. What do you need?',
    'friendly-engagement': 'Great! I\'m happy to help you today.'
  };

  res.json({
    detectedEmotion,
    confidence,
    culturalInterpretation: emotionInterpretation.interpretation,
    recommendedResponse: responses[emotionInterpretation.response],
    culturalContext,
    language: language || 'en-US',
    emotionalState: {
      primary: detectedEmotion,
      intensity: Math.random() * 0.5 + 0.5, // 50-100% intensity
      stability: Math.random() * 0.3 + 0.7 // 70-100% stability
    },
    culturalAdaptations: {
      communicationStyle: emotionInterpretation.response,
      serviceApproach: generateServiceApproach(detectedEmotion, culturalContext),
      escalationRisk: calculateEscalationRisk(detectedEmotion, confidence)
    },
    timestamp: new Date().toISOString()
  });
});

// Cultural adaptation endpoint
app.post('/api/cultural/adapt', (req, res) => {
  const { uiElements, content, culturalProfile, adaptationType } = req.body;

  if (!culturalProfile) {
    return res.status(400).json({ error: 'Cultural profile is required for adaptation' });
  }

  // Cultural UI adaptations
  const culturalAdaptations = {
    'east-asia': {
      colors: {
        primary: '#DC143C', // Red for luck
        secondary: '#FFD700', // Gold for prosperity
        accent: '#228B22', // Green for harmony
        background: '#F5F5F5'
      },
      layout: {
        direction: 'ltr',
        emphasis: 'vertical',
        spacing: 'compact',
        hierarchy: 'subtle'
      },
      typography: {
        fontFamily: 'Noto Sans CJK',
        fontSize: 'medium',
        weight: 'normal',
        lineHeight: 1.6
      },
      imagery: {
        style: 'minimalist',
        symbols: ['dragon', 'lotus', 'bamboo'],
        avoid: ['sharp-angles', 'dark-themes']
      }
    },
    'middle-east': {
      colors: {
        primary: '#DAA520', // Gold
        secondary: '#4169E1', // Royal blue
        accent: '#228B22', // Green
        background: '#FFFAF0'
      },
      layout: {
        direction: 'rtl',
        emphasis: 'horizontal',
        spacing: 'generous',
        hierarchy: 'clear'
      },
      typography: {
        fontFamily: 'Noto Sans Arabic',
        fontSize: 'large',
        weight: 'medium',
        lineHeight: 1.8
      },
      imagery: {
        style: 'ornate',
        symbols: ['geometric-patterns', 'calligraphy'],
        avoid: ['human-figures', 'alcohol-imagery']
      }
    },
    'north-america': {
      colors: {
        primary: '#0066CC', // Trust blue
        secondary: '#FF6B35', // Energy orange
        accent: '#28A745', // Success green
        background: '#FFFFFF'
      },
      layout: {
        direction: 'ltr',
        emphasis: 'balanced',
        spacing: 'standard',
        hierarchy: 'clear'
      },
      typography: {
        fontFamily: 'Inter',
        fontSize: 'medium',
        weight: 'medium',
        lineHeight: 1.5
      },
      imagery: {
        style: 'modern',
        symbols: ['stars', 'checkmarks', 'arrows'],
        avoid: ['overly-formal', 'complex-patterns']
      }
    }
  };

  const adaptations = culturalAdaptations[culturalProfile.region?.toLowerCase().replace(' ', '-')] ||
                     culturalAdaptations['north-america'];

  // Content adaptations
  const contentAdaptations = {
    greetings: generateCulturalGreetings(culturalProfile),
    messaging: generateCulturalMessaging(culturalProfile),
    callsToAction: generateCulturalCTAs(culturalProfile),
    errorMessages: generateCulturalErrorMessages(culturalProfile),
    successMessages: generateCulturalSuccessMessages(culturalProfile)
  };

  res.json({
    uiAdaptations: adaptations,
    contentAdaptations,
    culturalProfile: culturalProfile.region,
    adaptationType: adaptationType || 'full',
    confidence: Math.random() * 0.2 + 0.8,
    recommendations: {
      priority: 'high',
      implementation: 'immediate',
      testing: 'cultural-user-testing-recommended'
    },
    timestamp: new Date().toISOString()
  });
});

// Cultural market intelligence endpoint
app.get('/api/cultural/market/:region', (req, res) => {
  const { region } = req.params;

  const marketIntelligence = {
    'north-america': {
      marketSize: '$847B',
      growthRate: '3.2%',
      competitiveIntensity: 'high',
      culturalTrends: [
        'Health-conscious dining',
        'Sustainability focus',
        'Technology integration',
        'Personalization demand',
        'Convenience priority'
      ],
      seasonalPatterns: {
        'Q1': { demand: 'low', events: ['new-year-resolutions'] },
        'Q2': { demand: 'medium', events: ['spring-celebrations'] },
        'Q3': { demand: 'high', events: ['summer-gatherings'] },
        'Q4': { demand: 'peak', events: ['holidays', 'thanksgiving'] }
      },
      customerSegments: [
        { name: 'Health Enthusiasts', size: '28%', preferences: ['organic', 'low-calorie', 'fresh'] },
        { name: 'Convenience Seekers', size: '35%', preferences: ['fast', 'mobile-ordering', 'delivery'] },
        { name: 'Experience Seekers', size: '22%', preferences: ['unique', 'instagram-worthy', 'premium'] },
        { name: 'Value Conscious', size: '15%', preferences: ['affordable', 'portions', 'deals'] }
      ],
      culturalEvents: [
        { name: 'Super Bowl', impact: 'high', date: '2025-02-09', recommendations: ['party-platters', 'group-deals'] },
        { name: 'Thanksgiving', impact: 'peak', date: '2025-11-27', recommendations: ['family-meals', 'catering'] },
        { name: 'Independence Day', impact: 'high', date: '2025-07-04', recommendations: ['bbq-items', 'patriotic-themes'] }
      ]
    },
    'east-asia': {
      marketSize: '$623B',
      growthRate: '5.8%',
      competitiveIntensity: 'very-high',
      culturalTrends: [
        'Traditional authenticity',
        'Family dining experiences',
        'Mobile payment adoption',
        'Social media integration',
        'Quality over quantity'
      ],
      seasonalPatterns: {
        'Q1': { demand: 'peak', events: ['chinese-new-year', 'spring-festival'] },
        'Q2': { demand: 'medium', events: ['cherry-blossom-season'] },
        'Q3': { demand: 'low', events: ['summer-heat'] },
        'Q4': { demand: 'high', events: ['mid-autumn-festival', 'golden-week'] }
      },
      customerSegments: [
        { name: 'Traditional Families', size: '42%', preferences: ['authentic', 'sharing', 'respect'] },
        { name: 'Young Professionals', size: '31%', preferences: ['convenient', 'trendy', 'social'] },
        { name: 'Health Conscious', size: '18%', preferences: ['balanced', 'fresh', 'natural'] },
        { name: 'Luxury Seekers', size: '9%', preferences: ['premium', 'exclusive', 'status'] }
      ],
      culturalEvents: [
        { name: 'Chinese New Year', impact: 'peak', date: '2025-01-29', recommendations: ['family-sets', 'lucky-foods'] },
        { name: 'Mid-Autumn Festival', impact: 'high', date: '2025-09-15', recommendations: ['mooncakes', 'family-gatherings'] },
        { name: 'Golden Week', impact: 'high', date: '2025-10-01', recommendations: ['celebration-menus', 'group-dining'] }
      ]
    }
  };

  const intelligence = marketIntelligence[region] || {
    marketSize: 'Data not available',
    growthRate: 'N/A',
    competitiveIntensity: 'unknown',
    culturalTrends: ['General dining trends'],
    seasonalPatterns: {},
    customerSegments: [],
    culturalEvents: []
  };

  res.json({
    region,
    marketIntelligence: intelligence,
    lastUpdated: new Date().toISOString(),
    dataSource: 'AI Cultural Intelligence Engine',
    confidence: Math.random() * 0.2 + 0.8,
    recommendations: generateMarketRecommendations(intelligence),
    competitiveAnalysis: generateCompetitiveAnalysis(region),
    opportunityScore: Math.random() * 30 + 70 // 70-100 opportunity score
  });
});

// Helper functions for cultural intelligence
function generateCommunicationRecommendations(profile) {
  const recommendations = {
    'direct': ['Be clear and concise', 'Provide specific information', 'Use straightforward language'],
    'indirect': ['Use polite suggestions', 'Allow face-saving opportunities', 'Be patient with responses'],
    'high-context': ['Pay attention to non-verbal cues', 'Understand implied meanings', 'Respect cultural nuances'],
    'formal': ['Use proper titles and greetings', 'Maintain professional distance', 'Follow established protocols'],
    'warm': ['Be friendly and personal', 'Show genuine interest', 'Use expressive communication']
  };

  return recommendations[profile.communicationStyle] || recommendations['direct'];
}

function generateServiceAdaptations(profile) {
  return {
    pace: profile.timeOrientation === 'punctual' ? 'efficient' : 'relaxed',
    interaction: profile.socialDynamics === 'individualistic' ? 'personal' : 'group-focused',
    approach: profile.emotionalExpression === 'reserved' ? 'respectful-distance' : 'warm-engagement',
    customization: profile.diningPreferences.includes('customization') ? 'high' : 'standard'
  };
}

function generateMenuRecommendations(profile) {
  const recommendations = [];

  if (profile.diningPreferences.includes('halal')) {
    recommendations.push('Highlight halal options prominently');
  }
  if (profile.diningPreferences.includes('sharing')) {
    recommendations.push('Promote family-style and sharing platters');
  }
  if (profile.diningPreferences.includes('traditional')) {
    recommendations.push('Feature authentic and traditional dishes');
  }
  if (profile.diningPreferences.includes('healthy')) {
    recommendations.push('Emphasize fresh and healthy options');
  }

  return recommendations;
}

function generateTimingConsiderations(profile) {
  return {
    serviceSpeed: profile.timeOrientation === 'punctual' ? 'fast' : 'relaxed',
    waitingTolerance: profile.timeOrientation === 'flexible' ? 'high' : 'low',
    peakHours: profile.region === 'Europe' ? ['19:00-21:00'] : ['18:00-20:00'],
    culturalMealTimes: getCulturalMealTimes(profile.region)
  };
}

function generateCulturalSensitivities(profile) {
  const sensitivities = {
    'Middle East': ['Respect prayer times', 'Halal requirements', 'Ramadan considerations'],
    'East Asia': ['Face-saving important', 'Hierarchy respect', 'Group harmony'],
    'Europe': ['Privacy important', 'Quality expectations', 'Formal service'],
    'North America': ['Efficiency valued', 'Individual preferences', 'Casual interaction'],
    'Latin America': ['Family focus', 'Warm interaction', 'Celebration culture']
  };

  return sensitivities[profile.region] || ['General cultural respect'];
}

function generateCulturalRecommendations(profile, context) {
  return {
    immediate: [
      `Adapt communication style to ${profile.communicationStyle}`,
      `Consider ${profile.timeOrientation} time orientation`,
      `Respect ${profile.emotionalExpression} emotional expression`
    ],
    strategic: [
      'Implement cultural training for staff',
      'Adapt menu to cultural preferences',
      'Consider cultural calendar for promotions'
    ],
    longTerm: [
      'Develop cultural customer segments',
      'Create culturally-specific marketing',
      'Build cultural community relationships'
    ]
  };
}

function generateCulturalAdaptations(profile) {
  return {
    ui: {
      colorScheme: getCulturalColors(profile.region),
      layout: getCulturalLayout(profile.region),
      imagery: getCulturalImagery(profile.region)
    },
    content: {
      tone: getCulturalTone(profile.communicationStyle),
      messaging: getCulturalMessaging(profile.region),
      callsToAction: getCulturalCTAs(profile.region)
    },
    service: {
      approach: getServiceApproach(profile),
      timing: getServiceTiming(profile),
      interaction: getInteractionStyle(profile)
    }
  };
}

function generateServiceApproach(emotion, culturalContext) {
  const approaches = {
    'happy': 'maintain-positive-energy',
    'frustrated': culturalContext === 'east-asia' ? 'gentle-resolution' : 'direct-solution',
    'angry': culturalContext === 'middle-east' ? 'respectful-mediation' : 'immediate-escalation',
    'confused': 'patient-explanation',
    'excited': 'shared-enthusiasm',
    'calm': 'professional-service'
  };

  return approaches[emotion] || 'standard-service';
}

function calculateEscalationRisk(emotion, confidence) {
  const riskFactors = {
    'angry': 0.8,
    'frustrated': 0.6,
    'confused': 0.3,
    'sad': 0.4,
    'happy': 0.1,
    'excited': 0.2,
    'calm': 0.1
  };

  const baseRisk = riskFactors[emotion] || 0.3;
  const confidenceAdjustment = confidence > 0.8 ? 1.0 : 0.7;

  return Math.min(baseRisk * confidenceAdjustment, 1.0);
}

function generateCulturalGreetings(profile) {
  const greetings = {
    'North America': ['Hello!', 'Hi there!', 'Welcome!', 'Good to see you!'],
    'East Asia': ['Welcome', 'Thank you for coming', 'Please come in', 'We are honored'],
    'Middle East': ['Welcome', 'Peace be upon you', 'We are pleased to serve you', 'Honor to have you'],
    'Europe': ['Good day', 'Welcome', 'How may we serve you', 'Pleased to meet you'],
    'Latin America': ['¡Bienvenidos!', 'Welcome to our family', '¡Hola!', 'We are happy to see you']
  };

  return greetings[profile.region] || greetings['North America'];
}

function generateCulturalMessaging(profile) {
  return {
    orderConfirmation: getCulturalOrderConfirmation(profile.region),
    paymentRequest: getCulturalPaymentRequest(profile.region),
    thankYou: getCulturalThankYou(profile.region),
    apology: getCulturalApology(profile.region)
  };
}

function generateCulturalCTAs(profile) {
  const ctas = {
    'North America': ['Order Now', 'Get Started', 'Try It', 'Add to Cart'],
    'East Asia': ['Please Order', 'Kindly Select', 'Consider This', 'Please Choose'],
    'Middle East': ['Order with Blessing', 'Choose Wisely', 'Select Please', 'Honor Us'],
    'Europe': ['Place Order', 'Select Item', 'Choose Option', 'Proceed'],
    'Latin America': ['¡Ordena Ya!', 'Choose with Joy', '¡Pruébalo!', 'Add with Love']
  };

  return ctas[profile.region] || ctas['North America'];
}

function generateCulturalErrorMessages(profile) {
  const messages = {
    'North America': ['Oops! Something went wrong', 'Error occurred', 'Please try again'],
    'East Asia': ['We apologize for the inconvenience', 'Please forgive the error', 'We will fix this'],
    'Middle East': ['We seek forgiveness for this error', 'Please excuse us', 'We will correct this'],
    'Europe': ['An error has occurred', 'We apologize', 'Please retry'],
    'Latin America': ['¡Perdón! Something happened', 'We are sorry', 'Let us fix this']
  };

  return messages[profile.region] || messages['North America'];
}

function generateCulturalSuccessMessages(profile) {
  const messages = {
    'North America': ['Success!', 'Great job!', 'All set!', 'Perfect!'],
    'East Asia': ['Thank you', 'Completed successfully', 'We appreciate you', 'Honor achieved'],
    'Middle East': ['Blessed success', 'With gratitude', 'Successfully completed', 'Thank you kindly'],
    'Europe': ['Successfully completed', 'Thank you', 'Well done', 'Completed'],
    'Latin America': ['¡Éxito!', '¡Perfecto!', 'With joy completed', '¡Gracias!']
  };

  return messages[profile.region] || messages['North America'];
}

function generateMarketRecommendations(intelligence) {
  return {
    immediate: [
      `Focus on ${intelligence.culturalTrends[0]}`,
      `Target ${intelligence.customerSegments[0]?.name} segment`,
      `Prepare for ${intelligence.culturalEvents[0]?.name}`
    ],
    strategic: [
      'Develop cultural marketing campaigns',
      'Adapt menu to local preferences',
      'Train staff in cultural sensitivity'
    ],
    competitive: [
      'Differentiate through cultural understanding',
      'Build local community relationships',
      'Leverage cultural events for promotion'
    ]
  };
}

function generateCompetitiveAnalysis(region) {
  return {
    marketPosition: 'emerging',
    competitiveAdvantage: 'cultural-intelligence',
    threats: ['established-local-brands', 'cultural-misunderstanding'],
    opportunities: ['underserved-cultural-segments', 'cultural-event-marketing'],
    recommendations: ['cultural-differentiation', 'local-partnership', 'community-engagement']
  };
}

// Additional helper functions for cultural data
function getCulturalMealTimes(region) {
  const mealTimes = {
    'Europe': { breakfast: '07:00-09:00', lunch: '12:00-14:00', dinner: '19:00-21:00' },
    'North America': { breakfast: '06:00-09:00', lunch: '11:00-14:00', dinner: '17:00-20:00' },
    'East Asia': { breakfast: '06:00-08:00', lunch: '11:00-13:00', dinner: '18:00-20:00' },
    'Middle East': { breakfast: '07:00-09:00', lunch: '13:00-15:00', dinner: '20:00-22:00' },
    'Latin America': { breakfast: '07:00-09:00', lunch: '12:00-15:00', dinner: '19:00-22:00' }
  };

  return mealTimes[region] || mealTimes['North America'];
}

function getCulturalColors(region) {
  const colors = {
    'East Asia': { primary: '#DC143C', secondary: '#FFD700', accent: '#228B22' },
    'Middle East': { primary: '#DAA520', secondary: '#4169E1', accent: '#228B22' },
    'North America': { primary: '#0066CC', secondary: '#FF6B35', accent: '#28A745' },
    'Europe': { primary: '#2C3E50', secondary: '#E74C3C', accent: '#27AE60' },
    'Latin America': { primary: '#E67E22', secondary: '#9B59B6', accent: '#F39C12' }
  };

  return colors[region] || colors['North America'];
}

function getCulturalLayout(region) {
  const layouts = {
    'Middle East': { direction: 'rtl', emphasis: 'horizontal' },
    'East Asia': { direction: 'ltr', emphasis: 'vertical' },
    'North America': { direction: 'ltr', emphasis: 'balanced' },
    'Europe': { direction: 'ltr', emphasis: 'structured' },
    'Latin America': { direction: 'ltr', emphasis: 'vibrant' }
  };

  return layouts[region] || layouts['North America'];
}

function getCulturalImagery(region) {
  const imagery = {
    'East Asia': { style: 'minimalist', symbols: ['dragon', 'lotus', 'bamboo'] },
    'Middle East': { style: 'ornate', symbols: ['geometric-patterns', 'calligraphy'] },
    'North America': { style: 'modern', symbols: ['stars', 'checkmarks', 'arrows'] },
    'Europe': { style: 'classic', symbols: ['crowns', 'shields', 'laurels'] },
    'Latin America': { style: 'colorful', symbols: ['sun', 'flowers', 'celebrations'] }
  };

  return imagery[region] || imagery['North America'];
}

function getCulturalTone(communicationStyle) {
  const tones = {
    'direct': 'straightforward',
    'indirect': 'polite',
    'high-context': 'respectful',
    'formal': 'professional',
    'warm': 'friendly'
  };

  return tones[communicationStyle] || 'professional';
}

function getCulturalMessaging(region) {
  const messaging = {
    'North America': { greeting: 'Welcome!', thanks: 'Thank you!', goodbye: 'Have a great day!' },
    'East Asia': { greeting: 'Welcome', thanks: 'Thank you very much', goodbye: 'Please come again' },
    'Middle East': { greeting: 'Welcome, honored guest', thanks: 'We are grateful', goodbye: 'May you be blessed' },
    'Europe': { greeting: 'Good day', thanks: 'Thank you', goodbye: 'Farewell' },
    'Latin America': { greeting: '¡Bienvenidos!', thanks: '¡Gracias!', goodbye: '¡Hasta luego!' }
  };

  return messaging[region] || messaging['North America'];
}

function getCulturalCTAs(region) {
  const ctas = {
    'North America': ['Order Now', 'Get Started', 'Try It'],
    'East Asia': ['Please Order', 'Kindly Select', 'Consider This'],
    'Middle East': ['Order with Blessing', 'Choose Wisely', 'Select Please'],
    'Europe': ['Place Order', 'Select Item', 'Choose Option'],
    'Latin America': ['¡Ordena Ya!', 'Choose with Joy', '¡Pruébalo!']
  };

  return ctas[region] || ctas['North America'];
}

function getServiceApproach(profile) {
  return {
    pace: profile.timeOrientation === 'punctual' ? 'efficient' : 'relaxed',
    interaction: profile.socialDynamics === 'individualistic' ? 'personal' : 'group-focused',
    approach: profile.emotionalExpression === 'reserved' ? 'respectful-distance' : 'warm-engagement'
  };
}

function getServiceTiming(profile) {
  return {
    speed: profile.timeOrientation === 'punctual' ? 'fast' : 'relaxed',
    flexibility: profile.timeOrientation === 'flexible' ? 'high' : 'low'
  };
}

function getInteractionStyle(profile) {
  return {
    communication: profile.communicationStyle,
    emotional: profile.emotionalExpression,
    social: profile.socialDynamics
  };
}

function getCulturalOrderConfirmation(region) {
  const confirmations = {
    'North America': 'Your order has been confirmed!',
    'East Asia': 'Thank you, your order is confirmed',
    'Middle East': 'Your order is blessed and confirmed',
    'Europe': 'Order confirmed, thank you',
    'Latin America': '¡Tu orden está confirmada!'
  };

  return confirmations[region] || confirmations['North America'];
}

function getCulturalPaymentRequest(region) {
  const requests = {
    'North America': 'How would you like to pay?',
    'East Asia': 'Please choose your payment method',
    'Middle East': 'How would you honor us with payment?',
    'Europe': 'Please select payment method',
    'Latin America': '¿Cómo te gustaría pagar?'
  };

  return requests[region] || requests['North America'];
}

function getCulturalThankYou(region) {
  const thanks = {
    'North America': 'Thank you for your business!',
    'East Asia': 'We are deeply grateful for your patronage',
    'Middle East': 'We are honored by your visit',
    'Europe': 'Thank you for choosing us',
    'Latin America': '¡Gracias por elegirnos!'
  };

  return thanks[region] || thanks['North America'];
}

function getCulturalApology(region) {
  const apologies = {
    'North America': 'We apologize for any inconvenience',
    'East Asia': 'We deeply apologize and ask for your forgiveness',
    'Middle East': 'We seek your forgiveness for this error',
    'Europe': 'We sincerely apologize',
    'Latin America': 'Pedimos disculpas sinceramente'
  };

  return apologies[region] || apologies['North America'];
}

// Phase 3L: Global Compliance & Advanced Security System
// Global Compliance and Security API Endpoints

// Global compliance check endpoint
app.post('/api/compliance/check', (req, res) => {
  const { region, dataType, operation, userConsent, dataSubject } = req.body;

  if (!region || !dataType || !operation) {
    return res.status(400).json({ error: 'Region, data type, and operation are required for compliance check' });
  }

  // Global compliance frameworks
  const complianceFrameworks = {
    'eu': {
      framework: 'GDPR',
      region: 'European Union',
      requirements: {
        lawfulBasis: ['consent', 'contract', 'legal-obligation', 'vital-interests', 'public-task', 'legitimate-interests'],
        dataSubjectRights: ['access', 'rectification', 'erasure', 'restriction', 'portability', 'objection'],
        consentRequirements: {
          explicit: true,
          informed: true,
          'freely-given': true,
          specific: true,
          withdrawable: true
        },
        dataRetention: {
          principle: 'storage-limitation',
          maxPeriod: 'necessary-for-purpose',
          automaticDeletion: true
        },
        dataBreachNotification: {
          supervisoryAuthority: 72, // hours
          dataSubject: 'without-undue-delay',
          riskThreshold: 'high-risk'
        }
      },
      penalties: {
        administrative: '20M EUR or 4% global turnover',
        criminal: 'varies by member state'
      }
    },
    'us-ca': {
      framework: 'CCPA/CPRA',
      region: 'California, United States',
      requirements: {
        consumerRights: ['know', 'delete', 'opt-out', 'non-discrimination', 'correct', 'limit'],
        personalInformationCategories: [
          'identifiers', 'personal-records', 'characteristics', 'commercial-information',
          'biometric-information', 'internet-activity', 'geolocation', 'sensory-data',
          'professional-information', 'education-information', 'inferences'
        ],
        disclosureRequirements: {
          privacyPolicy: true,
          collectionNotice: true,
          saleOptOut: true,
          thirdPartyDisclosure: true
        },
        verificationRequirements: {
          identityVerification: true,
          requestAuthentication: true,
          fraudPrevention: true
        }
      },
      penalties: {
        civil: '$2,500 per violation (unintentional), $7,500 per violation (intentional)',
        privateRight: '$100-$750 per consumer per incident'
      }
    },
    'ca': {
      framework: 'PIPEDA',
      region: 'Canada',
      requirements: {
        privacyPrinciples: [
          'accountability', 'identifying-purposes', 'consent', 'limiting-collection',
          'limiting-use-disclosure', 'accuracy', 'safeguards', 'openness',
          'individual-access', 'challenging-compliance'
        ],
        consentRequirements: {
          meaningful: true,
          informed: true,
          ongoing: true,
          withdrawable: true
        },
        dataBreachNotification: {
          privacyCommissioner: 'as-soon-as-feasible',
          affectedIndividuals: 'real-risk-of-significant-harm',
          recordKeeping: true
        },
        crossBorderTransfer: {
          adequateProtection: true,
          contractualSafeguards: true,
          individualConsent: true
        }
      },
      penalties: {
        administrative: 'varies',
        reputational: 'significant'
      }
    }
  };

  const framework = complianceFrameworks[region] || complianceFrameworks['us-ca'];

  // Compliance assessment
  const complianceCheck = {
    compliant: true,
    issues: [],
    recommendations: [],
    riskLevel: 'low'
  };

  // Check consent requirements
  if (framework.requirements.consentRequirements) {
    if (!userConsent || !userConsent.explicit) {
      complianceCheck.compliant = false;
      complianceCheck.issues.push('Explicit consent required but not provided');
      complianceCheck.recommendations.push('Obtain explicit user consent before processing');
      complianceCheck.riskLevel = 'high';
    }

    if (userConsent && !userConsent.informed) {
      complianceCheck.compliant = false;
      complianceCheck.issues.push('Informed consent required');
      complianceCheck.recommendations.push('Provide clear information about data processing purposes');
      complianceCheck.riskLevel = 'medium';
    }
  }

  // Check data subject rights
  if (operation === 'delete' && framework.requirements.dataSubjectRights?.includes('erasure')) {
    complianceCheck.recommendations.push('Ensure complete data deletion across all systems');
  }

  if (operation === 'access' && framework.requirements.dataSubjectRights?.includes('access')) {
    complianceCheck.recommendations.push('Provide comprehensive data access report');
  }

  // Generate compliance score
  const complianceScore = complianceCheck.compliant ?
    (complianceCheck.issues.length === 0 ? 100 : 85) :
    Math.max(50 - (complianceCheck.issues.length * 10), 0);

  res.json({
    framework: framework.framework,
    region: framework.region,
    complianceCheck,
    complianceScore,
    requirements: framework.requirements,
    penalties: framework.penalties,
    timestamp: new Date().toISOString(),
    checkId: 'compliance_' + Math.random().toString(36).substr(2, 10),
    nextReview: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
  });
});

// Advanced security authentication endpoint
app.post('/api/security/authenticate', (req, res) => {
  const { biometricData, authMethod, securityLevel, userContext } = req.body;

  if (!biometricData || !authMethod) {
    return res.status(400).json({ error: 'Biometric data and authentication method are required' });
  }

  // Multi-modal biometric authentication simulation
  const authMethods = {
    'voice': {
      accuracy: 99.2,
      falsePositiveRate: 0.01,
      falseNegativeRate: 0.79,
      processingTime: 1.2,
      securityLevel: 'high',
      quantumResistant: true
    },
    'facial': {
      accuracy: 99.7,
      falsePositiveRate: 0.005,
      falseNegativeRate: 0.295,
      processingTime: 0.8,
      securityLevel: 'very-high',
      quantumResistant: true
    },
    'fingerprint': {
      accuracy: 99.8,
      falsePositiveRate: 0.002,
      falseNegativeRate: 0.198,
      processingTime: 0.5,
      securityLevel: 'very-high',
      quantumResistant: true
    },
    'behavioral': {
      accuracy: 94.5,
      falsePositiveRate: 0.1,
      falseNegativeRate: 5.4,
      processingTime: 2.1,
      securityLevel: 'medium',
      quantumResistant: true
    },
    'iris': {
      accuracy: 99.9,
      falsePositiveRate: 0.001,
      falseNegativeRate: 0.099,
      processingTime: 1.0,
      securityLevel: 'maximum',
      quantumResistant: true
    },
    'multi-modal': {
      accuracy: 99.99,
      falsePositiveRate: 0.0001,
      falseNegativeRate: 0.0099,
      processingTime: 2.5,
      securityLevel: 'maximum',
      quantumResistant: true
    }
  };

  const method = authMethods[authMethod] || authMethods['voice'];
  const isAuthenticated = Math.random() * 100 < method.accuracy;

  // Advanced security assessment
  const securityAssessment = {
    threatLevel: calculateThreatLevel(userContext),
    riskScore: calculateRiskScore(authMethod, securityLevel),
    anomalyDetection: performAnomalyDetection(userContext),
    complianceLevel: assessComplianceLevel(authMethod),
    quantumSecurity: method.quantumResistant
  };

  // Zero-trust verification
  const zeroTrustVerification = {
    deviceTrust: Math.random() > 0.1, // 90% device trust
    networkTrust: Math.random() > 0.05, // 95% network trust
    behaviorTrust: Math.random() > 0.08, // 92% behavior trust
    contextTrust: Math.random() > 0.06, // 94% context trust
    overallTrust: 'verified'
  };

  res.json({
    authenticated: isAuthenticated,
    authMethod,
    securityLevel: method.securityLevel,
    accuracy: method.accuracy,
    processingTime: method.processingTime,
    securityAssessment,
    zeroTrustVerification,
    biometricQuality: Math.random() * 0.2 + 0.8, // 80-100% quality
    livenessDetection: Math.random() > 0.02, // 98% liveness detection
    antiSpoofing: Math.random() > 0.01, // 99% anti-spoofing
    quantumResistant: method.quantumResistant,
    encryptionLevel: 'AES-256-GCM + Post-Quantum',
    sessionToken: 'qt_' + Math.random().toString(36).substr(2, 16),
    expiresIn: 3600, // 1 hour
    timestamp: new Date().toISOString()
  });
});

// Audit trail management endpoint
app.get('/api/audit/trail', (req, res) => {
  const { startDate, endDate, userId, action, resource, limit = 100 } = req.query;

  // Comprehensive audit trail simulation
  const auditEvents = [
    {
      eventId: 'audit_001',
      timestamp: '2025-06-10T14:30:00Z',
      userId: 'user_admin_001',
      userRole: 'super-admin',
      action: 'LOGIN',
      resource: 'admin-dashboard',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      location: 'Toronto, Canada',
      success: true,
      riskLevel: 'low',
      complianceFlags: [],
      dataAccessed: ['user-profile', 'dashboard-metrics'],
      dataModified: [],
      sessionId: 'session_abc123'
    },
    {
      eventId: 'audit_002',
      timestamp: '2025-06-10T14:32:15Z',
      userId: 'user_admin_001',
      userRole: 'super-admin',
      action: 'DATA_ACCESS',
      resource: 'customer-data',
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
      location: 'Toronto, Canada',
      success: true,
      riskLevel: 'medium',
      complianceFlags: ['GDPR-ARTICLE-6', 'CCPA-CONSUMER-DATA'],
      dataAccessed: ['customer-profiles', 'transaction-history'],
      dataModified: [],
      sessionId: 'session_abc123',
      legalBasis: 'legitimate-interest',
      dataSubjectConsent: true
    },
    {
      eventId: 'audit_003',
      timestamp: '2025-06-10T14:35:42Z',
      userId: 'user_staff_005',
      userRole: 'staff',
      action: 'BIOMETRIC_AUTH',
      resource: 'pos-terminal',
      ipAddress: '*************',
      userAgent: 'POS-Terminal/2.1.0',
      location: 'Restaurant Floor',
      success: true,
      riskLevel: 'low',
      complianceFlags: ['BIOMETRIC-DATA-PROCESSING'],
      dataAccessed: ['voice-biometric-template'],
      dataModified: [],
      sessionId: 'session_pos_789',
      biometricMethod: 'voice',
      securityLevel: 'high',
      quantumEncrypted: true
    },
    {
      eventId: 'audit_004',
      timestamp: '2025-06-10T14:38:20Z',
      userId: 'user_customer_123',
      userRole: 'customer',
      action: 'DATA_DELETION_REQUEST',
      resource: 'privacy-portal',
      ipAddress: '************',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0)',
      location: 'External',
      success: true,
      riskLevel: 'low',
      complianceFlags: ['GDPR-RIGHT-TO-ERASURE', 'CCPA-RIGHT-TO-DELETE'],
      dataAccessed: ['customer-profile'],
      dataModified: ['deletion-request-created'],
      sessionId: 'session_customer_456',
      gdprBasis: 'right-to-erasure',
      ccpaCategory: 'personal-information'
    },
    {
      eventId: 'audit_005',
      timestamp: '2025-06-10T14:40:10Z',
      userId: 'system_ai_engine',
      userRole: 'system',
      action: 'AI_CULTURAL_ANALYSIS',
      resource: 'cultural-intelligence',
      ipAddress: '127.0.0.1',
      userAgent: 'AI-Engine/3.0.0',
      location: 'Internal',
      success: true,
      riskLevel: 'low',
      complianceFlags: ['AI-PROCESSING', 'CULTURAL-DATA'],
      dataAccessed: ['customer-cultural-preferences'],
      dataModified: ['cultural-insights-generated'],
      sessionId: 'session_ai_cultural',
      aiModel: 'cultural-intelligence-v3',
      culturalRegion: 'east-asia',
      privacyPreserving: true
    }
  ];

  // Filter audit events based on query parameters
  let filteredEvents = auditEvents;

  if (userId) {
    filteredEvents = filteredEvents.filter(event => event.userId === userId);
  }

  if (action) {
    filteredEvents = filteredEvents.filter(event => event.action === action);
  }

  if (resource) {
    filteredEvents = filteredEvents.filter(event => event.resource === resource);
  }

  // Compliance summary
  const complianceSummary = {
    totalEvents: filteredEvents.length,
    complianceFlags: [...new Set(filteredEvents.flatMap(e => e.complianceFlags))],
    riskDistribution: {
      low: filteredEvents.filter(e => e.riskLevel === 'low').length,
      medium: filteredEvents.filter(e => e.riskLevel === 'medium').length,
      high: filteredEvents.filter(e => e.riskLevel === 'high').length
    },
    successRate: (filteredEvents.filter(e => e.success).length / filteredEvents.length * 100).toFixed(2),
    dataAccessEvents: filteredEvents.filter(e => e.dataAccessed.length > 0).length,
    dataModificationEvents: filteredEvents.filter(e => e.dataModified.length > 0).length
  };

  res.json({
    auditTrail: filteredEvents.slice(0, parseInt(limit)),
    complianceSummary,
    totalEvents: filteredEvents.length,
    queryParameters: { startDate, endDate, userId, action, resource, limit },
    generatedAt: new Date().toISOString(),
    retentionPeriod: '7 years (regulatory requirement)',
    encryptionStatus: 'AES-256-GCM + Post-Quantum',
    integrityVerified: true
  });
});

// Privacy consent management endpoint
app.post('/api/privacy/consent', (req, res) => {
  const { userId, consentType, purpose, dataCategories, action, region } = req.body;

  if (!userId || !consentType || !action) {
    return res.status(400).json({ error: 'User ID, consent type, and action are required' });
  }

  // Consent management based on regional requirements
  const consentFrameworks = {
    'eu': {
      framework: 'GDPR',
      requirements: {
        explicit: true,
        informed: true,
        specific: true,
        freelyGiven: true,
        withdrawable: true,
        granular: true
      },
      legalBases: ['consent', 'contract', 'legal-obligation', 'vital-interests', 'public-task', 'legitimate-interests']
    },
    'us-ca': {
      framework: 'CCPA',
      requirements: {
        optIn: true,
        optOut: true,
        clear: true,
        conspicuous: true,
        verifiable: true
      },
      rights: ['know', 'delete', 'opt-out', 'non-discrimination']
    },
    'ca': {
      framework: 'PIPEDA',
      requirements: {
        meaningful: true,
        informed: true,
        ongoing: true,
        withdrawable: true
      },
      principles: ['accountability', 'consent', 'limiting-collection']
    }
  };

  const framework = consentFrameworks[region] || consentFrameworks['eu'];

  // Process consent action
  let consentRecord = {
    consentId: 'consent_' + Math.random().toString(36).substr(2, 12),
    userId,
    consentType,
    purpose,
    dataCategories: dataCategories || ['personal-data'],
    action, // 'grant', 'withdraw', 'update'
    timestamp: new Date().toISOString(),
    framework: framework.framework,
    region,
    ipAddress: '*************',
    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64)',
    version: '1.0',
    expiresAt: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year
    status: action === 'grant' ? 'active' : action === 'withdraw' ? 'withdrawn' : 'updated'
  };

  // Validate consent against framework requirements
  const validation = {
    valid: true,
    issues: [],
    compliance: framework.framework
  };

  if (framework.requirements.explicit && consentType !== 'explicit') {
    validation.valid = false;
    validation.issues.push('Explicit consent required under ' + framework.framework);
  }

  if (framework.requirements.specific && !purpose) {
    validation.valid = false;
    validation.issues.push('Specific purpose required for consent');
  }

  // Generate consent proof
  const consentProof = {
    digitalSignature: 'sha256_' + Math.random().toString(36).substr(2, 16),
    blockchainHash: 'bc_' + Math.random().toString(36).substr(2, 20),
    timestampProof: new Date().toISOString(),
    immutableRecord: true,
    quantumSecured: true
  };

  res.json({
    consentRecord,
    validation,
    consentProof,
    framework: framework.framework,
    requirements: framework.requirements,
    rights: framework.rights || framework.legalBases,
    nextReview: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days
    withdrawalInstructions: {
      method: 'API or Privacy Portal',
      url: '/privacy/withdraw-consent',
      timeframe: 'immediate'
    }
  });
});

// Helper functions for security and compliance
function calculateThreatLevel(userContext) {
  const factors = {
    location: userContext?.location === 'external' ? 0.3 : 0.1,
    timeOfDay: new Date().getHours() < 6 || new Date().getHours() > 22 ? 0.2 : 0.05,
    deviceTrust: userContext?.deviceTrust === false ? 0.4 : 0.1,
    behaviorAnomaly: userContext?.behaviorAnomaly === true ? 0.5 : 0.05
  };

  const totalThreat = Object.values(factors).reduce((sum, factor) => sum + factor, 0);

  if (totalThreat > 0.7) return 'high';
  if (totalThreat > 0.4) return 'medium';
  return 'low';
}

function calculateRiskScore(authMethod, securityLevel) {
  const methodRisk = {
    'voice': 0.2,
    'facial': 0.15,
    'fingerprint': 0.1,
    'behavioral': 0.4,
    'iris': 0.05,
    'multi-modal': 0.02
  };

  const levelMultiplier = {
    'low': 1.5,
    'medium': 1.0,
    'high': 0.7,
    'very-high': 0.5,
    'maximum': 0.3
  };

  const baseRisk = methodRisk[authMethod] || 0.3;
  const multiplier = levelMultiplier[securityLevel] || 1.0;

  return Math.min(baseRisk * multiplier * 100, 100);
}

function performAnomalyDetection(userContext) {
  return {
    locationAnomaly: Math.random() > 0.95, // 5% chance
    timeAnomaly: Math.random() > 0.98, // 2% chance
    behaviorAnomaly: Math.random() > 0.97, // 3% chance
    deviceAnomaly: Math.random() > 0.99, // 1% chance
    overallAnomalyScore: Math.random() * 10 // 0-10 scale
  };
}

function assessComplianceLevel(authMethod) {
  const complianceLevels = {
    'voice': 'GDPR-Article-9, CCPA-Biometric',
    'facial': 'GDPR-Article-9, CCPA-Biometric, BIPA',
    'fingerprint': 'GDPR-Article-9, CCPA-Biometric, BIPA',
    'behavioral': 'GDPR-Article-6, CCPA-Personal-Info',
    'iris': 'GDPR-Article-9, CCPA-Biometric, BIPA',
    'multi-modal': 'GDPR-Article-9, CCPA-Biometric, BIPA, Enhanced'
  };

  return complianceLevels[authMethod] || 'Standard';
}

// Enhanced error handling middleware
app.use((error, req, res, next) => {
  console.error('🚨 Server error:', error);
  res.status(500).json({
    error: 'Internal server error',
    message: error.message,
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({
    error: 'Not found',
    path: req.originalUrl,
    timestamp: new Date().toISOString()
  });
});

// Start server with enhanced error handling
const server = app.listen(PORT, () => {
  console.log('🎉 ================================');
  console.log('🚀 Persistent Super Admin API Server STARTED');
  console.log('🎉 ================================');
  console.log(`📊 Database: BARPOS @ localhost:5432`);
  console.log(`🌐 Server: http://localhost:${PORT}`);
  console.log(`🔗 Health check: http://localhost:${PORT}/api/admin/health`);
  console.log(`🔗 Database health: http://localhost:${PORT}/api/admin/health/database`);
  console.log(`📈 System metrics: http://localhost:${PORT}/api/admin/metrics/system`);
  console.log(`🏢 Tenants: http://localhost:${PORT}/api/admin/tenants`);
  console.log(`👥 Users: http://localhost:${PORT}/api/admin/users`);
  console.log('🔄 Server will keep running... Press Ctrl+C to stop');
  console.log('✅ Ready to accept connections!');
});

// Enhanced process error handling
process.on('uncaughtException', (err) => {
  console.error('🚨 Uncaught Exception:', err);
  console.error('Stack:', err.stack);
  // Don't exit, just log the error
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('🚨 Unhandled Rejection at:', promise, 'reason:', reason);
  // Don't exit, just log the error
});

// Graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  
  server.close(async () => {
    console.log('🔌 HTTP server closed');
    
    try {
      await pool.end();
      console.log('🗄️ Database pool closed');
    } catch (error) {
      console.error('❌ Error closing database pool:', error);
    }
    
    console.log('✅ Server shutdown complete');
    process.exit(0);
  });
});

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  
  server.close(async () => {
    console.log('🔌 HTTP server closed');
    
    try {
      await pool.end();
      console.log('🗄️ Database pool closed');
    } catch (error) {
      console.error('❌ Error closing database pool:', error);
    }
    
    console.log('✅ Server shutdown complete');
    process.exit(0);
  });
});

module.exports = app;
