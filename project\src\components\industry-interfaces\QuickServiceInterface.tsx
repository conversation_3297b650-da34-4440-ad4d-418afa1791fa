import React, { useState, useEffect } from 'react';
import { 
  Zap, 
  Clock, 
  Car, 
  Smartphone, 
  Star, 
  TrendingUp,
  Users,
  ShoppingBag,
  Timer,
  CheckCircle,
  AlertCircle,
  Play,
  Pause,
  RotateCcw
} from 'lucide-react';

interface OrderItem {
  id: string;
  name: string;
  price: number;
  category: string;
  preparationTime: number;
  calories?: number;
  customizations?: string[];
}

interface QueueOrder {
  id: string;
  orderNumber: number;
  items: OrderItem[];
  total: number;
  orderTime: Date;
  estimatedReady: Date;
  status: 'pending' | 'preparing' | 'ready' | 'completed';
  orderType: 'dine-in' | 'takeout' | 'drive-thru' | 'mobile';
  customerName?: string;
}

interface LoyaltyCustomer {
  id: string;
  name: string;
  phone: string;
  points: number;
  tier: 'Bronze' | 'Silver' | 'Gold' | 'Platinum';
  favoriteItems: string[];
}

const QuickServiceInterface: React.FC = () => {
  const [currentOrder, setCurrentOrder] = useState<OrderItem[]>([]);
  const [orderQueue, setOrderQueue] = useState<QueueOrder[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>('burgers');
  const [loyaltyCustomer, setLoyaltyCustomer] = useState<LoyaltyCustomer | null>(null);
  const [orderType, setOrderType] = useState<'dine-in' | 'takeout' | 'drive-thru' | 'mobile'>('dine-in');
  const [customerName, setCustomerName] = useState<string>('');

  // Mock menu data
  const menuItems: Record<string, OrderItem[]> = {
    burgers: [
      { id: '1', name: 'Classic Burger', price: 8.99, category: 'burgers', preparationTime: 4, calories: 540 },
      { id: '2', name: 'Cheeseburger', price: 9.99, category: 'burgers', preparationTime: 4, calories: 580 },
      { id: '3', name: 'Double Bacon Burger', price: 12.99, category: 'burgers', preparationTime: 6, calories: 720 },
      { id: '4', name: 'Veggie Burger', price: 9.49, category: 'burgers', preparationTime: 5, calories: 420 }
    ],
    sides: [
      { id: '5', name: 'French Fries', price: 3.99, category: 'sides', preparationTime: 3, calories: 320 },
      { id: '6', name: 'Onion Rings', price: 4.49, category: 'sides', preparationTime: 4, calories: 380 },
      { id: '7', name: 'Chicken Nuggets (6pc)', price: 5.99, category: 'sides', preparationTime: 3, calories: 280 },
      { id: '8', name: 'Mozzarella Sticks', price: 4.99, category: 'sides', preparationTime: 4, calories: 350 }
    ],
    drinks: [
      { id: '9', name: 'Soft Drink (Large)', price: 2.49, category: 'drinks', preparationTime: 1, calories: 150 },
      { id: '10', name: 'Milkshake', price: 4.99, category: 'drinks', preparationTime: 2, calories: 420 },
      { id: '11', name: 'Coffee', price: 1.99, category: 'drinks', preparationTime: 2, calories: 5 },
      { id: '12', name: 'Fresh Juice', price: 3.49, category: 'drinks', preparationTime: 1, calories: 110 }
    ],
    desserts: [
      { id: '13', name: 'Apple Pie', price: 3.99, category: 'desserts', preparationTime: 2, calories: 320 },
      { id: '14', name: 'Ice Cream Sundae', price: 4.49, category: 'desserts', preparationTime: 3, calories: 380 },
      { id: '15', name: 'Chocolate Chip Cookie', price: 1.99, category: 'desserts', preparationTime: 1, calories: 160 },
      { id: '16', name: 'Brownie', price: 2.99, category: 'desserts', preparationTime: 2, calories: 280 }
    ]
  };

  // Mock order queue
  const mockQueue: QueueOrder[] = [
    {
      id: '1',
      orderNumber: 101,
      items: [menuItems.burgers[0], menuItems.sides[0]],
      total: 12.98,
      orderTime: new Date(Date.now() - 300000),
      estimatedReady: new Date(Date.now() + 120000),
      status: 'preparing',
      orderType: 'dine-in',
      customerName: 'John D.'
    },
    {
      id: '2',
      orderNumber: 102,
      items: [menuItems.burgers[1], menuItems.drinks[0]],
      total: 12.48,
      orderTime: new Date(Date.now() - 180000),
      estimatedReady: new Date(Date.now() + 60000),
      status: 'ready',
      orderType: 'drive-thru'
    },
    {
      id: '3',
      orderNumber: 103,
      items: [menuItems.burgers[2], menuItems.sides[1], menuItems.drinks[1]],
      total: 21.47,
      orderTime: new Date(Date.now() - 60000),
      estimatedReady: new Date(Date.now() + 240000),
      status: 'pending',
      orderType: 'mobile',
      customerName: 'Sarah M.'
    }
  ];

  useEffect(() => {
    setOrderQueue(mockQueue);
  }, []);

  const addToOrder = (item: OrderItem) => {
    setCurrentOrder([...currentOrder, item]);
  };

  const removeFromOrder = (index: number) => {
    const newOrder = currentOrder.filter((_, i) => i !== index);
    setCurrentOrder(newOrder);
  };

  const getOrderTotal = () => {
    return currentOrder.reduce((sum, item) => sum + item.price, 0);
  };

  const getEstimatedTime = () => {
    const maxTime = Math.max(...currentOrder.map(item => item.preparationTime));
    return maxTime || 0;
  };

  const submitOrder = () => {
    if (currentOrder.length === 0) return;

    const newOrder: QueueOrder = {
      id: Date.now().toString(),
      orderNumber: 104 + orderQueue.length,
      items: [...currentOrder],
      total: getOrderTotal(),
      orderTime: new Date(),
      estimatedReady: new Date(Date.now() + getEstimatedTime() * 60000),
      status: 'pending',
      orderType,
      customerName: customerName || undefined
    };

    setOrderQueue([...orderQueue, newOrder]);
    setCurrentOrder([]);
    setCustomerName('');
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'preparing': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'ready': return 'bg-green-100 text-green-800 border-green-200';
      case 'completed': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getOrderTypeIcon = (type: string) => {
    switch (type) {
      case 'dine-in': return <Users className="w-4 h-4" />;
      case 'takeout': return <ShoppingBag className="w-4 h-4" />;
      case 'drive-thru': return <Car className="w-4 h-4" />;
      case 'mobile': return <Smartphone className="w-4 h-4" />;
      default: return <Users className="w-4 h-4" />;
    }
  };

  const categories = [
    { id: 'burgers', name: 'Burgers', icon: '🍔' },
    { id: 'sides', name: 'Sides', icon: '🍟' },
    { id: 'drinks', name: 'Drinks', icon: '🥤' },
    { id: 'desserts', name: 'Desserts', icon: '🍰' }
  ];

  return (
    <div className="h-full bg-gradient-to-br from-orange-50 via-red-50 to-yellow-50 p-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-orange-600 to-red-500 text-white rounded-2xl shadow-xl p-6 mb-6">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="p-3 bg-white/20 rounded-xl">
              <Zap className="w-8 h-8" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">Quick Service POS</h1>
              <p className="text-orange-100">Fast, efficient order management</p>
            </div>
          </div>
          <div className="flex items-center space-x-6">
            <div className="text-center">
              <div className="text-2xl font-bold">{orderQueue.filter(o => o.status !== 'completed').length}</div>
              <div className="text-orange-200">Active Orders</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{Math.round(orderQueue.reduce((sum, o) => sum + (o.estimatedReady.getTime() - o.orderTime.getTime()), 0) / orderQueue.length / 60000) || 0}m</div>
              <div className="text-orange-200">Avg Time</div>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
        {/* Order Queue */}
        <div className="lg:col-span-1">
          <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-6 h-full">
            <div className="flex items-center space-x-3 mb-4">
              <Timer className="w-6 h-6 text-orange-600" />
              <h2 className="text-xl font-bold text-gray-900">Order Queue</h2>
            </div>
            
            <div className="space-y-3 max-h-96 overflow-y-auto">
              {orderQueue.map((order) => (
                <div key={order.id} className={`p-4 rounded-lg border-2 ${getStatusColor(order.status)}`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getOrderTypeIcon(order.orderType)}
                      <span className="font-bold">#{order.orderNumber}</span>
                    </div>
                    <div className="text-sm font-medium">
                      ${order.total.toFixed(2)}
                    </div>
                  </div>
                  
                  {order.customerName && (
                    <div className="text-sm font-medium mb-1">{order.customerName}</div>
                  )}
                  
                  <div className="text-xs text-gray-600 mb-2">
                    {order.items.map(item => item.name).join(', ')}
                  </div>
                  
                  <div className="flex items-center justify-between text-xs">
                    <span>{order.orderTime.toLocaleTimeString()}</span>
                    <span className="font-medium">
                      Ready: {order.estimatedReady.toLocaleTimeString()}
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Menu Categories & Items */}
        <div className="lg:col-span-2">
          <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-6">
            <div className="flex items-center space-x-3 mb-4">
              <ShoppingBag className="w-6 h-6 text-orange-600" />
              <h2 className="text-xl font-bold text-gray-900">Menu</h2>
            </div>
            
            {/* Category Tabs */}
            <div className="flex space-x-2 mb-6">
              {categories.map((category) => (
                <button
                  key={category.id}
                  onClick={() => setSelectedCategory(category.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    selectedCategory === category.id
                      ? 'bg-gradient-to-r from-orange-600 to-red-500 text-white shadow-lg'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  }`}
                >
                  <span className="text-lg">{category.icon}</span>
                  <span>{category.name}</span>
                </button>
              ))}
            </div>
            
            {/* Menu Items Grid */}
            <div className="grid grid-cols-2 gap-4 max-h-96 overflow-y-auto">
              {menuItems[selectedCategory]?.map((item) => (
                <div
                  key={item.id}
                  onClick={() => addToOrder(item)}
                  className="p-4 border border-gray-200 rounded-lg hover:border-orange-300 hover:shadow-md transition-all duration-200 cursor-pointer"
                >
                  <div className="flex items-start justify-between mb-2">
                    <h3 className="font-semibold text-gray-900">{item.name}</h3>
                    <span className="text-lg font-bold text-orange-600">${item.price}</span>
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>⏱️ {item.preparationTime} min</span>
                    <span>🔥 {item.calories} cal</span>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Current Order & Checkout */}
        <div className="lg:col-span-1">
          <div className="space-y-6">
            {/* Order Type Selection */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Order Type</h3>
              <div className="grid grid-cols-2 gap-2">
                {[
                  { type: 'dine-in', label: 'Dine In', icon: Users },
                  { type: 'takeout', label: 'Takeout', icon: ShoppingBag },
                  { type: 'drive-thru', label: 'Drive-Thru', icon: Car },
                  { type: 'mobile', label: 'Mobile', icon: Smartphone }
                ].map(({ type, label, icon: Icon }) => (
                  <button
                    key={type}
                    onClick={() => setOrderType(type as any)}
                    className={`flex flex-col items-center space-y-1 p-3 rounded-lg transition-all duration-200 ${
                      orderType === type
                        ? 'bg-orange-600 text-white shadow-lg'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    <Icon className="w-5 h-5" />
                    <span className="text-xs font-medium">{label}</span>
                  </button>
                ))}
              </div>
              
              {(orderType === 'mobile' || orderType === 'takeout') && (
                <div className="mt-4">
                  <input
                    type="text"
                    placeholder="Customer name"
                    value={customerName}
                    onChange={(e) => setCustomerName(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  />
                </div>
              )}
            </div>

            {/* Current Order */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-6">
              <div className="flex items-center space-x-3 mb-4">
                <CheckCircle className="w-6 h-6 text-orange-600" />
                <h2 className="text-xl font-bold text-gray-900">Current Order</h2>
              </div>
              
              <div className="space-y-3 max-h-64 overflow-y-auto">
                {currentOrder.length === 0 ? (
                  <div className="text-center text-gray-500 py-8">
                    <ShoppingBag className="w-12 h-12 mx-auto mb-2 text-gray-300" />
                    <p>No items in order</p>
                  </div>
                ) : (
                  currentOrder.map((item, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900">{item.name}</div>
                        <div className="text-sm text-gray-600">${item.price}</div>
                      </div>
                      <button
                        onClick={() => removeFromOrder(index)}
                        className="text-red-500 hover:text-red-700 transition-colors"
                      >
                        ✕
                      </button>
                    </div>
                  ))
                )}
              </div>
              
              {currentOrder.length > 0 && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="flex items-center justify-between mb-2">
                    <span className="font-semibold">Estimated Time:</span>
                    <span className="text-orange-600 font-bold">{getEstimatedTime()} min</span>
                  </div>
                  <div className="flex items-center justify-between mb-4">
                    <span className="text-lg font-bold">Total:</span>
                    <span className="text-xl font-bold text-orange-600">${getOrderTotal().toFixed(2)}</span>
                  </div>
                  
                  <button
                    onClick={submitOrder}
                    className="w-full bg-gradient-to-r from-orange-600 to-red-500 hover:from-orange-700 hover:to-red-600 text-white py-3 px-6 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105"
                  >
                    Submit Order
                  </button>
                </div>
              )}
            </div>

            {/* Quick Stats */}
            <div className="bg-white rounded-2xl shadow-lg border border-orange-200 p-6">
              <h3 className="text-lg font-bold text-gray-900 mb-4">Today's Stats</h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Orders Completed</span>
                  <span className="font-bold text-green-600">127</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Avg Service Time</span>
                  <span className="font-bold text-blue-600">3.2 min</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-gray-600">Revenue</span>
                  <span className="font-bold text-orange-600">$1,847</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickServiceInterface;
