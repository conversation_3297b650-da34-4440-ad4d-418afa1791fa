import React, { useState, useEffect } from 'react';
import {
  Receipt,
  Printer,
  Mail,
  MessageSquare,
  Download,
  Eye,
  Copy,
  QrCode,
  Star,
  MapPin,
  Phone,
  Globe,
  Calendar,
  CreditCard,
  User,
  Hash,
  CheckCircle,
  AlertCircle,
  Clock,
  RefreshCw
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface ReceiptData {
  id: string;
  receipt_number: string;
  order_id: string;
  business_info: {
    name: string;
    address: string;
    city: string;
    phone: string;
    email: string;
  };
  order_info: {
    order_id: string;
    table_number?: number;
    server_name?: string;
    guest_count?: number;
    timestamp: string;
  };
  items: Array<{
    name: string;
    quantity: number;
    price: number;
    total: number;
    modifiers?: string[];
  }>;
  totals: {
    subtotal: number;
    tax: number;
    tip: number;
    processing_fee?: number;
    total: number;
  };
  payment_info: {
    method: string;
    transaction_id: string;
    authorization_code?: string;
  };
  customer_info?: {
    email?: string;
    phone?: string;
    name?: string;
  };
  receipt_options: {
    print?: boolean;
    email?: boolean;
    sms?: boolean;
  };
  generated_at: string;
}

interface BusinessInfo {
  name: string;
  address: string[];
  phone: string;
  email: string;
  website?: string;
  logo?: string;
  taxId?: string;
}

interface EnhancedReceiptSystemProps {
  receiptData: ReceiptData;
  businessInfo: BusinessInfo;
  onPrint?: () => void;
  onEmail?: (email: string) => void;
  onSMS?: (phone: string) => void;
  onDownload?: () => void;
}

const EnhancedReceiptSystem: React.FC<EnhancedReceiptSystemProps> = ({
  receiptData,
  businessInfo,
  onPrint,
  onEmail,
  onSMS,
  onDownload
}) => {
  const [showPreview, setShowPreview] = useState(false);
  const [emailAddress, setEmailAddress] = useState(receiptData.customerInfo?.email || '');
  const [phoneNumber, setPhoneNumber] = useState(receiptData.customerInfo?.phone || '');
  const [feedbackRating, setFeedbackRating] = useState(0);
  const [showFeedback, setShowFeedback] = useState(false);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatPaymentMethod = (method: string) => {
    const methods: { [key: string]: string } = {
      'cash': 'Cash',
      'card': 'Credit/Debit Card',
      'apple_pay': 'Apple Pay',
      'google_pay': 'Google Pay',
      'tap_to_pay': 'Tap to Pay'
    };
    return methods[method] || method;
  };

  const generateReceiptHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <title>Receipt - ${receiptData.orderId}</title>
        <style>
          body { font-family: 'Courier New', monospace; max-width: 300px; margin: 0 auto; padding: 10px; }
          .header { text-align: center; border-bottom: 2px solid #000; padding-bottom: 10px; margin-bottom: 10px; }
          .business-name { font-size: 18px; font-weight: bold; margin-bottom: 5px; }
          .business-info { font-size: 12px; line-height: 1.4; }
          .order-info { margin: 10px 0; padding: 5px 0; border-bottom: 1px dashed #000; }
          .items { margin: 10px 0; }
          .item { display: flex; justify-content: space-between; margin: 2px 0; }
          .item-name { flex: 1; }
          .item-price { text-align: right; }
          .totals { border-top: 1px solid #000; padding-top: 5px; margin-top: 10px; }
          .total-line { display: flex; justify-content: space-between; margin: 2px 0; }
          .final-total { font-weight: bold; font-size: 14px; border-top: 1px solid #000; padding-top: 5px; }
          .payment-info { margin: 10px 0; padding: 5px 0; border-top: 1px dashed #000; }
          .footer { text-align: center; margin-top: 15px; font-size: 12px; }
          .qr-code { text-align: center; margin: 10px 0; }
        </style>
      </head>
      <body>
        <div class="header">
          <div class="business-name">${businessInfo.name}</div>
          <div class="business-info">
            ${businessInfo.address.join('<br>')}
            <br>Tel: ${businessInfo.phone}
            ${businessInfo.email ? `<br>Email: ${businessInfo.email}` : ''}
            ${businessInfo.website ? `<br>Web: ${businessInfo.website}` : ''}
          </div>
        </div>
        
        <div class="order-info">
          <div><strong>Order #:</strong> ${receiptData.orderId}</div>
          <div><strong>Date:</strong> ${formatDate(receiptData.timestamp)}</div>
          ${receiptData.tableNumber ? `<div><strong>Table:</strong> ${receiptData.tableNumber}</div>` : ''}
          ${receiptData.serverName ? `<div><strong>Server:</strong> ${receiptData.serverName}</div>` : ''}
          ${receiptData.guestCount ? `<div><strong>Guests:</strong> ${receiptData.guestCount}</div>` : ''}
        </div>
        
        <div class="items">
          ${receiptData.items.map(item => `
            <div class="item">
              <div class="item-name">
                ${item.quantity}x ${item.name}
                ${item.modifiers ? `<br><small>${item.modifiers.join(', ')}</small>` : ''}
              </div>
              <div class="item-price">$${item.total.toFixed(2)}</div>
            </div>
          `).join('')}
        </div>
        
        <div class="totals">
          <div class="total-line">
            <span>Subtotal:</span>
            <span>$${receiptData.subtotal.toFixed(2)}</span>
          </div>
          <div class="total-line">
            <span>Tax:</span>
            <span>$${receiptData.tax.toFixed(2)}</span>
          </div>
          ${receiptData.tip > 0 ? `
            <div class="total-line">
              <span>Tip:</span>
              <span>$${receiptData.tip.toFixed(2)}</span>
            </div>
          ` : ''}
          ${receiptData.processingFee && receiptData.processingFee > 0 ? `
            <div class="total-line">
              <span>Processing Fee:</span>
              <span>$${receiptData.processingFee.toFixed(2)}</span>
            </div>
          ` : ''}
          <div class="total-line final-total">
            <span>TOTAL:</span>
            <span>$${receiptData.total.toFixed(2)}</span>
          </div>
        </div>
        
        <div class="payment-info">
          <div><strong>Payment Method:</strong> ${formatPaymentMethod(receiptData.paymentMethod)}</div>
          <div><strong>Transaction ID:</strong> ${receiptData.transactionId}</div>
          ${receiptData.authCode ? `<div><strong>Auth Code:</strong> ${receiptData.authCode}</div>` : ''}
        </div>
        
        <div class="footer">
          <div>Thank you for your visit!</div>
          <div>Please come again soon</div>
          ${businessInfo.taxId ? `<div>Tax ID: ${businessInfo.taxId}</div>` : ''}
        </div>
        
        <div class="qr-code">
          <div>Scan for feedback & rewards</div>
          <div>[QR CODE PLACEHOLDER]</div>
        </div>
      </body>
      </html>
    `;
  };

  const handlePrint = () => {
    const printWindow = window.open('', '_blank');
    if (printWindow) {
      printWindow.document.write(generateReceiptHTML());
      printWindow.document.close();
      printWindow.print();
      printWindow.close();
    }
    onPrint?.();
  };

  const handleEmail = () => {
    if (!emailAddress) {
      alert('Please enter an email address');
      return;
    }
    onEmail?.(emailAddress);
  };

  const handleSMS = () => {
    if (!phoneNumber) {
      alert('Please enter a phone number');
      return;
    }
    onSMS?.(phoneNumber);
  };

  const handleDownload = () => {
    const receiptHTML = generateReceiptHTML();
    const blob = new Blob([receiptHTML], { type: 'text/html' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `receipt-${receiptData.orderId}.html`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    onDownload?.();
  };

  const handleCopyToClipboard = () => {
    const receiptText = `
${businessInfo.name}
${businessInfo.address.join('\n')}
Tel: ${businessInfo.phone}

Order #: ${receiptData.orderId}
Date: ${formatDate(receiptData.timestamp)}
${receiptData.tableNumber ? `Table: ${receiptData.tableNumber}` : ''}
${receiptData.serverName ? `Server: ${receiptData.serverName}` : ''}

${receiptData.items.map(item => 
  `${item.quantity}x ${item.name} - $${item.total.toFixed(2)}`
).join('\n')}

Subtotal: $${receiptData.subtotal.toFixed(2)}
Tax: $${receiptData.tax.toFixed(2)}
${receiptData.tip > 0 ? `Tip: $${receiptData.tip.toFixed(2)}` : ''}
TOTAL: $${receiptData.total.toFixed(2)}

Payment: ${formatPaymentMethod(receiptData.paymentMethod)}
Transaction ID: ${receiptData.transactionId}

Thank you for your visit!
    `.trim();

    navigator.clipboard.writeText(receiptText).then(() => {
      alert('Receipt copied to clipboard!');
    });
  };

  const ReceiptPreview: React.FC = () => (
    <div className="bg-white p-6 rounded-lg shadow-lg max-w-sm mx-auto font-mono text-sm">
      {/* Header */}
      <div className="text-center border-b-2 border-black pb-3 mb-3">
        <div className="text-lg font-bold mb-1">{businessInfo.name}</div>
        <div className="text-xs leading-tight">
          {businessInfo.address.map((line, index) => (
            <div key={index}>{line}</div>
          ))}
          <div>Tel: {businessInfo.phone}</div>
          {businessInfo.email && <div>Email: {businessInfo.email}</div>}
        </div>
      </div>

      {/* Order Info */}
      <div className="mb-3 pb-2 border-b border-dashed border-gray-400">
        <div><strong>Order #:</strong> {receiptData.orderId}</div>
        <div><strong>Date:</strong> {formatDate(receiptData.timestamp)}</div>
        {receiptData.tableNumber && <div><strong>Table:</strong> {receiptData.tableNumber}</div>}
        {receiptData.serverName && <div><strong>Server:</strong> {receiptData.serverName}</div>}
        {receiptData.guestCount && <div><strong>Guests:</strong> {receiptData.guestCount}</div>}
      </div>

      {/* Items */}
      <div className="mb-3">
        {receiptData.items.map((item, index) => (
          <div key={index} className="flex justify-between mb-1">
            <div className="flex-1">
              <div>{item.quantity}x {item.name}</div>
              {item.modifiers && item.modifiers.length > 0 && (
                <div className="text-xs text-gray-600 ml-2">
                  {item.modifiers.join(', ')}
                </div>
              )}
            </div>
            <div className="text-right">${item.total.toFixed(2)}</div>
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="border-t border-black pt-2">
        <div className="flex justify-between">
          <span>Subtotal:</span>
          <span>${receiptData.subtotal.toFixed(2)}</span>
        </div>
        <div className="flex justify-between">
          <span>Tax:</span>
          <span>${receiptData.tax.toFixed(2)}</span>
        </div>
        {receiptData.tip > 0 && (
          <div className="flex justify-between">
            <span>Tip:</span>
            <span>${receiptData.tip.toFixed(2)}</span>
          </div>
        )}
        {receiptData.processingFee && receiptData.processingFee > 0 && (
          <div className="flex justify-between text-gray-600">
            <span>Processing Fee:</span>
            <span>${receiptData.processingFee.toFixed(2)}</span>
          </div>
        )}
        <div className="flex justify-between font-bold text-lg border-t border-black pt-2 mt-2">
          <span>TOTAL:</span>
          <span>${receiptData.total.toFixed(2)}</span>
        </div>
      </div>

      {/* Payment Info */}
      <div className="mt-3 pt-2 border-t border-dashed border-gray-400">
        <div><strong>Payment:</strong> {formatPaymentMethod(receiptData.paymentMethod)}</div>
        <div><strong>Transaction ID:</strong> {receiptData.transactionId}</div>
        {receiptData.authCode && <div><strong>Auth Code:</strong> {receiptData.authCode}</div>}
      </div>

      {/* Footer */}
      <div className="text-center mt-4 text-xs">
        <div>Thank you for your visit!</div>
        <div>Please come again soon</div>
        <div className="mt-2">
          <QrCode className="h-8 w-8 mx-auto" />
          <div>Scan for feedback & rewards</div>
        </div>
      </div>
    </div>
  );

  return (
    <div className="space-y-4">
      {/* Action Buttons */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
        <button
          onClick={handlePrint}
          className="flex items-center justify-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
        >
          <Printer className="h-4 w-4" />
          <span>Print</span>
        </button>
        
        <button
          onClick={() => setShowPreview(!showPreview)}
          className="flex items-center justify-center space-x-2 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700"
        >
          <Eye className="h-4 w-4" />
          <span>Preview</span>
        </button>
        
        <button
          onClick={handleDownload}
          className="flex items-center justify-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700"
        >
          <Download className="h-4 w-4" />
          <span>Download</span>
        </button>
        
        <button
          onClick={handleCopyToClipboard}
          className="flex items-center justify-center space-x-2 bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700"
        >
          <Copy className="h-4 w-4" />
          <span>Copy</span>
        </button>
      </div>

      {/* Email & SMS Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">Email Receipt</label>
          <div className="flex space-x-2">
            <input
              type="email"
              value={emailAddress}
              onChange={(e) => setEmailAddress(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="<EMAIL>"
            />
            <button
              onClick={handleEmail}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 flex items-center space-x-1"
            >
              <Mail className="h-4 w-4" />
              <span>Send</span>
            </button>
          </div>
        </div>

        <div className="space-y-2">
          <label className="block text-sm font-medium text-gray-700">SMS Receipt</label>
          <div className="flex space-x-2">
            <input
              type="tel"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder="(*************"
            />
            <button
              onClick={handleSMS}
              className="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center space-x-1"
            >
              <MessageSquare className="h-4 w-4" />
              <span>Send</span>
            </button>
          </div>
        </div>
      </div>

      {/* Receipt Preview */}
      {showPreview && (
        <div className="mt-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4 text-center">Receipt Preview</h3>
          <ReceiptPreview />
        </div>
      )}

      {/* Customer Feedback */}
      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900 mb-3">How was your experience?</h4>
        <div className="flex items-center space-x-2 mb-3">
          {[1, 2, 3, 4, 5].map((rating) => (
            <button
              key={rating}
              onClick={() => setFeedbackRating(rating)}
              className={`${
                rating <= feedbackRating ? 'text-yellow-400' : 'text-gray-300'
              } hover:text-yellow-400 transition-colors`}
            >
              <Star className="h-6 w-6 fill-current" />
            </button>
          ))}
          <span className="text-sm text-gray-600 ml-2">
            {feedbackRating > 0 && `${feedbackRating}/5 stars`}
          </span>
        </div>
        {feedbackRating > 0 && (
          <div className="text-sm text-green-600">
            Thank you for your feedback! 🎉
          </div>
        )}
      </div>
    </div>
  );
};

export default EnhancedReceiptSystem;
