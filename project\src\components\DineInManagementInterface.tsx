import React, { useState, useEffect } from 'react';
import {
  Users,
  MapPin,
  Clock,
  CheckCircle,
  AlertCircle,
  Coffee,
  Utensils,
  CreditCard,
  Search,
  Filter,
  Plus,
  Settings,
  BarChart3,
  RefreshCw,
  Play,
  Pause,
  Square,
  Star,
  TrendingUp,
  Activity,
  Zap
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';
import UnifiedDineInWorkflowManager from './UnifiedDineInWorkflowManager';
import TableSelectionModal from './TableSelectionModal';
import EmployeeVerificationModal from './EmployeeVerificationModal';

interface Table {
  id: string;
  number: number;
  name?: string;
  seats: number;
  status: 'available' | 'occupied' | 'reserved' | 'needs-cleaning' | 'out-of-order';
  substatus?: 'ordering' | 'eating' | 'waiting-for-check' | 'paying';
  section: string;
  guestCount?: number;
  serverName?: string;
  seatedTime?: Date;
  currentOrderId?: string;
  orderTotal?: number;
  orderItems?: number;
}

const DineInManagementInterface: React.FC = () => {
  const { state, dispatch } = useEnhancedAppContext();
  const [activeView, setActiveView] = useState<'dashboard' | 'workflow' | 'tables' | 'analytics'>('dashboard');
  const [showWorkflowManager, setShowWorkflowManager] = useState(false);
  const [showTableSelector, setShowTableSelector] = useState(false);
  const [showEmployeeVerification, setShowEmployeeVerification] = useState(false);
  const [selectedTable, setSelectedTable] = useState<Table | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isLiveMode, setIsLiveMode] = useState(true);

  // Mock table data for demonstration
  const [tables] = useState<Table[]>([
    { id: '1', number: 1, seats: 4, status: 'available', section: 'Main Dining' },
    { id: '2', number: 2, seats: 2, status: 'occupied', substatus: 'eating', section: 'Bar Area', guestCount: 2, serverName: 'Alice', orderTotal: 45.50, orderItems: 3 },
    { id: '3', number: 3, seats: 6, status: 'occupied', substatus: 'ordering', section: 'Patio', guestCount: 4, serverName: 'Bob' },
    { id: '4', number: 4, seats: 4, status: 'reserved', section: 'Main Dining' },
    { id: '5', number: 5, seats: 8, status: 'available', section: 'Private Room' },
    { id: '6', number: 6, seats: 2, status: 'needs-cleaning', section: 'Bar Area' },
    { id: '7', number: 7, seats: 4, status: 'occupied', substatus: 'waiting-for-check', section: 'Main Dining', guestCount: 3, serverName: 'Carol', orderTotal: 78.25, orderItems: 5 },
    { id: '8', number: 8, seats: 6, status: 'available', section: 'Patio' }
  ]);

  const getStatusColor = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 border-green-300 text-green-800';
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return 'bg-yellow-100 border-yellow-300 text-yellow-800';
          case 'eating':
            return 'bg-blue-100 border-blue-300 text-blue-800';
          case 'waiting-for-check':
            return 'bg-purple-100 border-purple-300 text-purple-800';
          case 'paying':
            return 'bg-orange-100 border-orange-300 text-orange-800';
          default:
            return 'bg-red-100 border-red-300 text-red-800';
        }
      case 'reserved':
        return 'bg-indigo-100 border-indigo-300 text-indigo-800';
      case 'needs-cleaning':
        return 'bg-gray-100 border-gray-300 text-gray-800';
      case 'out-of-order':
        return 'bg-red-200 border-red-400 text-red-900';
      default:
        return 'bg-gray-100 border-gray-300 text-gray-800';
    }
  };

  const getStatusIcon = (status: Table['status'], substatus?: Table['substatus']) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4" />;
      case 'occupied':
        switch (substatus) {
          case 'ordering':
            return <Utensils className="h-4 w-4" />;
          case 'eating':
            return <Coffee className="h-4 w-4" />;
          case 'waiting-for-check':
            return <Clock className="h-4 w-4" />;
          case 'paying':
            return <CreditCard className="h-4 w-4" />;
          default:
            return <Users className="h-4 w-4" />;
        }
      case 'reserved':
        return <Clock className="h-4 w-4" />;
      case 'needs-cleaning':
        return <AlertCircle className="h-4 w-4" />;
      case 'out-of-order':
        return <AlertCircle className="h-4 w-4" />;
      default:
        return <Users className="h-4 w-4" />;
    }
  };

  const handleStartWorkflow = () => {
    setShowWorkflowManager(true);
  };

  const handleTableSelect = (table: Table) => {
    setSelectedTable(table);
    if (table.status === 'available') {
      setShowEmployeeVerification(true);
    }
  };

  const handleEmployeeVerified = () => {
    setShowEmployeeVerification(false);
    setShowWorkflowManager(true);
  };

  const filteredTables = tables.filter(table => {
    const matchesSearch = !searchTerm || 
      table.number.toString().includes(searchTerm) ||
      table.section.toLowerCase().includes(searchTerm.toLowerCase()) ||
      table.serverName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'all' || table.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  const tableStats = {
    total: tables.length,
    available: tables.filter(t => t.status === 'available').length,
    occupied: tables.filter(t => t.status === 'occupied').length,
    reserved: tables.filter(t => t.status === 'reserved').length,
    needsCleaning: tables.filter(t => t.status === 'needs-cleaning').length
  };

  const renderDashboard = () => (
    <div className="space-y-8">
      {/* Hero Section */}
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 rounded-2xl shadow-xl p-8 text-white">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-3xl font-bold mb-2">Restaurant Floor Overview</h2>
            <p className="text-blue-100 text-lg">Real-time table management and workflow control</p>
          </div>
          <div className="flex items-center space-x-4">
            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
              <div className="text-2xl font-bold">{tables.length}</div>
              <div className="text-sm text-blue-100">Total Tables</div>
            </div>
            <div className="bg-white/20 backdrop-blur-sm rounded-xl p-4 text-center">
              <div className="text-2xl font-bold">94%</div>
              <div className="text-sm text-blue-100">Efficiency</div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-gradient-to-br from-green-50 to-emerald-50 border border-green-200 rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-gradient-to-r from-green-500 to-emerald-500 rounded-xl shadow-lg">
              <CheckCircle className="h-6 w-6 text-white" />
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-green-600">{tableStats.available}</div>
              <div className="text-sm text-green-500 font-medium">+2 from yesterday</div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-green-800">Available Tables</h3>
            <p className="text-green-600 text-sm">Ready for seating</p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200 rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-xl shadow-lg">
              <Users className="h-6 w-6 text-white" />
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-blue-600">{tableStats.occupied}</div>
              <div className="text-sm text-blue-500 font-medium">Active service</div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-blue-800">Occupied Tables</h3>
            <p className="text-blue-600 text-sm">Currently serving</p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200 rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-gradient-to-r from-purple-500 to-violet-500 rounded-xl shadow-lg">
              <Clock className="h-6 w-6 text-white" />
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-purple-600">{tableStats.reserved}</div>
              <div className="text-sm text-purple-500 font-medium">Upcoming</div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-purple-800">Reserved Tables</h3>
            <p className="text-purple-600 text-sm">Future bookings</p>
          </div>
        </div>

        <div className="bg-gradient-to-br from-orange-50 to-red-50 border border-orange-200 rounded-2xl shadow-lg p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
          <div className="flex items-center justify-between mb-4">
            <div className="p-3 bg-gradient-to-r from-orange-500 to-red-500 rounded-xl shadow-lg">
              <AlertCircle className="h-6 w-6 text-white" />
            </div>
            <div className="text-right">
              <div className="text-3xl font-bold text-orange-600">{tableStats.needsCleaning}</div>
              <div className="text-sm text-orange-500 font-medium">Attention needed</div>
            </div>
          </div>
          <div>
            <h3 className="text-lg font-semibold text-orange-800">Need Cleaning</h3>
            <p className="text-orange-600 text-sm">Maintenance required</p>
          </div>
        </div>
      </div>

      {/* Enhanced Recent Activity */}
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Live Activity Feed</h3>
            <p className="text-gray-600">Real-time updates from your restaurant floor</p>
          </div>
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700">Live</span>
          </div>
        </div>

        <div className="space-y-4">
          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-xl hover:shadow-md transition-all duration-200">
            <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
              <CheckCircle className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <p className="font-semibold text-gray-900">Table 5 became available</p>
              <p className="text-sm text-gray-600">Private Room • Cleaned and ready for next guests</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-green-600">2 min ago</p>
              <p className="text-xs text-gray-500">Just now</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-xl hover:shadow-md transition-all duration-200">
            <div className="p-2 bg-gradient-to-r from-blue-500 to-indigo-500 rounded-lg">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <p className="font-semibold text-gray-900">Table 2 seated 2 guests</p>
              <p className="text-sm text-gray-600">Bar Area • Server: Alice • Order in progress</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-blue-600">5 min ago</p>
              <p className="text-xs text-gray-500">Active</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-purple-50 to-violet-50 border border-purple-200 rounded-xl hover:shadow-md transition-all duration-200">
            <div className="p-2 bg-gradient-to-r from-purple-500 to-violet-500 rounded-lg">
              <Clock className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <p className="font-semibold text-gray-900">Table 7 requested check</p>
              <p className="text-sm text-gray-600">Main Dining • Server: Carol • $78.25 total</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-purple-600">8 min ago</p>
              <p className="text-xs text-gray-500">Pending</p>
            </div>
          </div>

          <div className="flex items-center space-x-4 p-4 bg-gradient-to-r from-orange-50 to-yellow-50 border border-orange-200 rounded-xl hover:shadow-md transition-all duration-200">
            <div className="p-2 bg-gradient-to-r from-orange-500 to-yellow-500 rounded-lg">
              <Utensils className="w-5 h-5 text-white" />
            </div>
            <div className="flex-1">
              <p className="font-semibold text-gray-900">Table 3 placed order</p>
              <p className="text-sm text-gray-600">Patio • Server: Bob • 4 guests ordering</p>
            </div>
            <div className="text-right">
              <p className="text-sm font-medium text-orange-600">12 min ago</p>
              <p className="text-xs text-gray-500">Kitchen</p>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Quick Actions */}
      <div className="bg-white rounded-2xl shadow-xl border border-gray-100 p-8">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="text-2xl font-bold text-gray-900">Quick Actions</h3>
            <p className="text-gray-600">Start workflows and manage tables efficiently</p>
          </div>
          <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl">
            <Zap className="w-6 h-6 text-white" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <button
            onClick={handleStartWorkflow}
            className="group bg-gradient-to-br from-blue-600 via-blue-700 to-purple-700 hover:from-blue-700 hover:via-blue-800 hover:to-purple-800 text-white p-6 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex flex-col items-center space-y-3">
              <div className="p-3 bg-white/20 rounded-xl">
                <Play className="w-6 h-6" />
              </div>
              <span className="text-lg">Start Dine-In Workflow</span>
              <span className="text-sm text-blue-100">Complete table service flow</span>
            </div>
          </button>

          <button
            onClick={() => setShowTableSelector(true)}
            className="group bg-gradient-to-br from-green-600 via-green-700 to-emerald-700 hover:from-green-700 hover:via-green-800 hover:to-emerald-800 text-white p-6 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex flex-col items-center space-y-3">
              <div className="p-3 bg-white/20 rounded-xl">
                <MapPin className="w-6 h-6" />
              </div>
              <span className="text-lg">Select Table</span>
              <span className="text-sm text-green-100">Quick table assignment</span>
            </div>
          </button>

          <button
            onClick={() => setActiveView('tables')}
            className="group bg-gradient-to-br from-orange-600 via-orange-700 to-red-700 hover:from-orange-700 hover:via-orange-800 hover:to-red-800 text-white p-6 rounded-2xl font-semibold transition-all duration-300 transform hover:scale-105 hover:shadow-2xl relative overflow-hidden"
          >
            <div className="absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative flex flex-col items-center space-y-3">
              <div className="p-3 bg-white/20 rounded-xl">
                <Settings className="w-6 h-6" />
              </div>
              <span className="text-lg">Manage Tables</span>
              <span className="text-sm text-orange-100">View and organize tables</span>
            </div>
          </button>
        </div>
      </div>
    </div>
  );

  const renderTablesView = () => (
    <div className="space-y-6">
      {/* Search and Filter */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
          <div className="flex items-center space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search tables..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Status</option>
              <option value="available">Available</option>
              <option value="occupied">Occupied</option>
              <option value="reserved">Reserved</option>
              <option value="needs-cleaning">Needs Cleaning</option>
            </select>
          </div>

          <button className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg flex items-center space-x-2">
            <Plus className="w-4 h-4" />
            <span>Add Table</span>
          </button>
        </div>
      </div>

      {/* Tables Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {filteredTables.map((table) => (
          <div
            key={table.id}
            onClick={() => handleTableSelect(table)}
            className={`bg-white rounded-xl shadow-sm border-2 p-6 cursor-pointer transition-all duration-200 hover:shadow-md hover:scale-105 ${getStatusColor(table.status, table.substatus)}`}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-2">
                {getStatusIcon(table.status, table.substatus)}
                <h3 className="text-lg font-bold">Table {table.number}</h3>
              </div>
              <div className="text-sm font-medium capitalize">
                {table.substatus || table.status}
              </div>
            </div>

            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600">Section:</span>
                <span className="font-medium">{table.section}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">Seats:</span>
                <span className="font-medium">{table.seats}</span>
              </div>
              {table.guestCount && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Guests:</span>
                  <span className="font-medium">{table.guestCount}</span>
                </div>
              )}
              {table.serverName && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Server:</span>
                  <span className="font-medium">{table.serverName}</span>
                </div>
              )}
              {table.orderTotal && (
                <div className="flex justify-between">
                  <span className="text-gray-600">Order Total:</span>
                  <span className="font-medium">${table.orderTotal.toFixed(2)}</span>
                </div>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );

  const renderAnalyticsView = () => (
    <div className="space-y-6">
      {/* Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Table Turnover</h3>
          <div className="text-3xl font-bold text-blue-600 mb-2">2.3</div>
          <p className="text-sm text-gray-600">Average turns per table today</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Average Wait Time</h3>
          <div className="text-3xl font-bold text-green-600 mb-2">12m</div>
          <p className="text-sm text-gray-600">From seating to first order</p>
        </div>

        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Efficiency</h3>
          <div className="text-3xl font-bold text-purple-600 mb-2">94%</div>
          <p className="text-sm text-gray-600">On-time service delivery</p>
        </div>
      </div>

      {/* Section Performance */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Section Performance</h3>
        <div className="space-y-4">
          {['Main Dining', 'Bar Area', 'Patio', 'Private Room'].map((section) => (
            <div key={section} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
              <div className="flex items-center space-x-3">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="font-medium">{section}</span>
              </div>
              <div className="flex items-center space-x-6 text-sm">
                <div className="text-center">
                  <div className="font-bold text-gray-900">85%</div>
                  <div className="text-gray-500">Occupancy</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-gray-900">$45</div>
                  <div className="text-gray-500">Avg Check</div>
                </div>
                <div className="text-center">
                  <div className="font-bold text-gray-900">1.8</div>
                  <div className="text-gray-500">Turnover</div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );

  return (
    <div className="h-full flex flex-col bg-gradient-to-br from-gray-50 via-blue-50/30 to-purple-50/30">
      {/* Enhanced Header */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 px-8 py-6 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 rounded-2xl shadow-lg">
                <Utensils className="w-8 h-8 text-white" />
              </div>
              <div>
                <h1 className="text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent">
                  Dine-In Management
                </h1>
                <p className="text-gray-600 font-medium">Unified workflow interface for seamless table service</p>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-6">
            <div className="flex items-center space-x-3 bg-white rounded-xl px-4 py-2 shadow-sm border border-gray-200">
              <div className={`w-3 h-3 rounded-full ${isLiveMode ? 'bg-green-400 animate-pulse shadow-lg shadow-green-400/50' : 'bg-gray-400'}`}></div>
              <span className="text-sm font-semibold text-gray-700">
                {isLiveMode ? 'Live Mode' : 'Offline'}
              </span>
            </div>

            <button
              onClick={() => setIsLiveMode(!isLiveMode)}
              className="p-3 rounded-xl text-gray-500 hover:text-gray-700 hover:bg-white/80 transition-all duration-200 shadow-sm border border-gray-200 hover:shadow-md"
            >
              <RefreshCw className="w-5 h-5" />
            </button>

            <div className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-4 py-2 rounded-xl shadow-lg">
              <Activity className="w-4 h-4" />
              <span className="text-sm font-semibold">All Systems Active</span>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Navigation Tabs */}
      <div className="bg-white/80 backdrop-blur-sm border-b border-gray-200/50 px-8 shadow-sm">
        <div className="flex space-x-2">
          {[
            { id: 'dashboard', label: 'Dashboard', icon: BarChart3, color: 'blue' },
            { id: 'workflow', label: 'Workflow', icon: Play, color: 'purple' },
            { id: 'tables', label: 'Tables', icon: MapPin, color: 'green' },
            { id: 'analytics', label: 'Analytics', icon: TrendingUp, color: 'orange' }
          ].map((tab) => {
            const Icon = tab.icon;
            const isActive = activeView === tab.id;

            return (
              <button
                key={tab.id}
                onClick={() => setActiveView(tab.id as any)}
                className={`flex items-center space-x-3 py-4 px-6 rounded-t-xl font-semibold text-sm transition-all duration-200 relative ${
                  isActive
                    ? `bg-gradient-to-r ${
                        tab.color === 'blue' ? 'from-blue-500 to-indigo-500' :
                        tab.color === 'purple' ? 'from-purple-500 to-violet-500' :
                        tab.color === 'green' ? 'from-green-500 to-emerald-500' :
                        'from-orange-500 to-red-500'
                      } text-white shadow-lg transform -translate-y-1`
                    : 'text-gray-600 hover:text-gray-800 hover:bg-gray-50 hover:shadow-sm'
                }`}
              >
                <Icon className="w-5 h-5" />
                <span>{tab.label}</span>
                {isActive && (
                  <div className="absolute -bottom-px left-0 right-0 h-1 bg-gradient-to-r from-white/50 to-transparent rounded-full"></div>
                )}
              </button>
            );
          })}
        </div>
      </div>

      {/* Enhanced Main Content */}
      <div className="flex-1 overflow-auto p-8">
        {activeView === 'dashboard' && renderDashboard()}
        {activeView === 'workflow' && (
          <div className="space-y-8">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-gray-100 p-8">
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h3 className="text-2xl font-bold text-gray-900">Workflow Management</h3>
                  <p className="text-gray-600">Manage and monitor dine-in workflows in real-time</p>
                </div>
                <div className="p-3 bg-gradient-to-r from-purple-500 to-violet-500 rounded-xl">
                  <Play className="w-6 h-6 text-white" />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div className="bg-gradient-to-br from-blue-50 via-blue-50 to-purple-50 border border-blue-200 rounded-2xl p-8 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg">
                      <Play className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-blue-900">Complete Workflow</h4>
                  </div>
                  <p className="text-blue-700 mb-6">Full table selection to order completion with employee verification</p>
                  <button
                    onClick={handleStartWorkflow}
                    className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white py-4 px-8 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 w-full shadow-lg hover:shadow-xl"
                  >
                    Start Complete Workflow
                  </button>
                </div>

                <div className="bg-gradient-to-br from-green-50 via-green-50 to-emerald-50 border border-green-200 rounded-2xl p-8 hover:shadow-lg transition-all duration-300">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg">
                      <MapPin className="w-5 h-5 text-white" />
                    </div>
                    <h4 className="text-xl font-bold text-green-900">Quick Table Selection</h4>
                  </div>
                  <p className="text-green-700 mb-6">Select table for existing order without full workflow</p>
                  <button
                    onClick={() => setShowTableSelector(true)}
                    className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white py-4 px-8 rounded-xl font-semibold transition-all duration-300 transform hover:scale-105 w-full shadow-lg hover:shadow-xl"
                  >
                    Select Table Only
                  </button>
                </div>
              </div>

              <div className="bg-gradient-to-r from-gray-50 to-blue-50/50 rounded-2xl p-8 border border-gray-200">
                <h4 className="text-xl font-bold text-gray-900 mb-6">Workflow Status Overview</h4>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div className="text-center bg-white rounded-xl p-6 shadow-sm">
                    <div className="text-3xl font-bold text-blue-600 mb-2">12</div>
                    <div className="text-sm font-medium text-gray-600">Active Workflows</div>
                    <div className="text-xs text-blue-500 mt-1">Currently running</div>
                  </div>
                  <div className="text-center bg-white rounded-xl p-6 shadow-sm">
                    <div className="text-3xl font-bold text-green-600 mb-2">45</div>
                    <div className="text-sm font-medium text-gray-600">Completed Today</div>
                    <div className="text-xs text-green-500 mt-1">+12% from yesterday</div>
                  </div>
                  <div className="text-center bg-white rounded-xl p-6 shadow-sm">
                    <div className="text-3xl font-bold text-purple-600 mb-2">98%</div>
                    <div className="text-sm font-medium text-gray-600">Success Rate</div>
                    <div className="text-xs text-purple-500 mt-1">Excellent performance</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {activeView === 'tables' && renderTablesView()}
        {activeView === 'analytics' && renderAnalyticsView()}
      </div>

      {/* Modals */}
      {showWorkflowManager && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-xl shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-auto">
            <UnifiedDineInWorkflowManager
              onWorkflowComplete={() => setShowWorkflowManager(false)}
              onCancel={() => setShowWorkflowManager(false)}
            />
          </div>
        </div>
      )}

      {showTableSelector && (
        <TableSelectionModal
          isOpen={showTableSelector}
          onClose={() => setShowTableSelector(false)}
          onTableSelect={handleTableSelect}
        />
      )}

      {showEmployeeVerification && selectedTable && (
        <EmployeeVerificationModal
          isOpen={showEmployeeVerification}
          onClose={() => setShowEmployeeVerification(false)}
          onVerified={handleEmployeeVerified}
          tableInfo={{
            tableNumber: selectedTable.number,
            section: selectedTable.section,
            seats: selectedTable.seats
          }}
        />
      )}
    </div>
  );
};

export default DineInManagementInterface;
