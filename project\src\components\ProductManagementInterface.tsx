import React, { useState, useEffect } from 'react';
import {
  Package,
  Plus,
  Search,
  Filter,
  Edit3,
  Trash2,
  Upload,
  Download,
  Image as ImageIcon,
  DollarSign,
  BarChart3,
  AlertTriangle,
  CheckCircle,
  Eye,
  Copy,
  Tag,
  Grid,
  List,
  RefreshCw,
  X
} from 'lucide-react';
import { useEnhancedAppContext } from '../context/EnhancedAppContext';

interface Product {
  id: string;
  name: string;
  description: string;
  price: number;
  cost_price: number;
  category_id: string;
  category_name: string;
  image_url?: string;
  barcode?: string;
  sku: string;
  is_active: boolean;
  is_featured: boolean;
  current_stock: number;
  minimum_stock: number;
  preparation_time: number;
  allergens: string[];
  tags: string[];
  created_at: string;
  updated_at: string;
}

interface Category {
  id: string;
  name: string;
  description: string;
  image_url?: string;
  color: string;
  icon: string;
  is_active: boolean;
  product_count: number;
  sort_order: number;
}

const ProductManagementInterface: React.FC = () => {
  const { apiCall } = useEnhancedAppContext();
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([]);
  const [selectedCategoryFilter, setSelectedCategoryFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showFilters, setShowFilters] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [showProductModal, setShowProductModal] = useState(false);
  const [showCategoryModal, setShowCategoryModal] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [bulkActions, setBulkActions] = useState<string[]>([]);
  const [showImportModal, setShowImportModal] = useState(false);
  const [showExportModal, setShowExportModal] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Debug logging
  console.log('ProductManagementInterface rendered', {
    productsCount: products.length,
    categoriesCount: categories.length,
    isLoading
  });

  const [filters, setFilters] = useState({
    status: 'all', // all, active, inactive
    stock: 'all', // all, in_stock, low_stock, out_of_stock
    featured: 'all', // all, featured, not_featured
    price_min: '',
    price_max: ''
  });

  useEffect(() => {
    fetchProducts();
    fetchCategories();
  }, []);

  useEffect(() => {
    if (error || success) {
      const timer = setTimeout(() => {
        setError(null);
        setSuccess(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error, success]);

  useEffect(() => {
    filterProducts();
  }, [products, selectedCategoryFilter, searchTerm, filters]);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const response = await apiCall('/api/tenant/products');
      if (response.ok) {
        const data = await response.json();
        setProducts(data);
      }
    } catch (error) {
      console.error('Error fetching products:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await apiCall('/api/tenant/categories');
      if (response.ok) {
        const data = await response.json();
        setCategories(data);
      }
    } catch (error) {
      console.error('Error fetching categories:', error);
    }
  };

  const filterProducts = () => {
    let filtered = products;

    // Category filter
    if (selectedCategoryFilter !== 'all') {
      filtered = filtered.filter(product => product.category_id === selectedCategoryFilter);
    }

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.barcode?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Status filter
    if (filters.status !== 'all') {
      filtered = filtered.filter(product => 
        filters.status === 'active' ? product.is_active : !product.is_active
      );
    }

    // Stock filter
    if (filters.stock !== 'all') {
      filtered = filtered.filter(product => {
        switch (filters.stock) {
          case 'in_stock':
            return product.current_stock > product.minimum_stock;
          case 'low_stock':
            return product.current_stock <= product.minimum_stock && product.current_stock > 0;
          case 'out_of_stock':
            return product.current_stock === 0;
          default:
            return true;
        }
      });
    }

    // Featured filter
    if (filters.featured !== 'all') {
      filtered = filtered.filter(product => 
        filters.featured === 'featured' ? product.is_featured : !product.is_featured
      );
    }

    // Price filter
    if (filters.price_min) {
      filtered = filtered.filter(product => product.price >= parseFloat(filters.price_min));
    }
    if (filters.price_max) {
      filtered = filtered.filter(product => product.price <= parseFloat(filters.price_max));
    }

    setFilteredProducts(filtered);
  };

  const createProduct = async (productData: Partial<Product>) => {
    try {
      const response = await apiCall('/api/tenant/products', {
        method: 'POST',
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        const newProduct = await response.json();
        setProducts(prev => [...prev, newProduct]);
        setSuccess('Product created successfully');
        setShowProductModal(false);
        setSelectedProduct(null);
      } else {
        setError('Failed to create product');
      }
    } catch (error) {
      console.error('Error creating product:', error);
      setError('Error creating product');
    }
  };

  const updateProduct = async (productId: string, productData: Partial<Product>) => {
    try {
      const response = await apiCall(`/api/tenant/products/${productId}`, {
        method: 'PUT',
        body: JSON.stringify(productData)
      });

      if (response.ok) {
        const updatedProduct = await response.json();
        setProducts(prev => prev.map(p => p.id === productId ? updatedProduct : p));
        setSuccess('Product updated successfully');
        setShowProductModal(false);
        setSelectedProduct(null);
      } else {
        setError('Failed to update product');
      }
    } catch (error) {
      console.error('Error updating product:', error);
      setError('Error updating product');
    }
  };

  const deleteProduct = async (productId: string) => {
    if (!confirm('Are you sure you want to delete this product?')) return;

    try {
      const response = await apiCall(`/api/tenant/products/${productId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setProducts(prev => prev.filter(p => p.id !== productId));
        setSuccess('Product deleted successfully');
        setShowProductModal(false);
        setSelectedProduct(null);
      } else {
        setError('Failed to delete product');
      }
    } catch (error) {
      console.error('Error deleting product:', error);
      setError('Error deleting product');
    }
  };

  const createCategory = async (categoryData: Partial<Category>) => {
    try {
      console.log('🔧 Creating category with data:', categoryData);

      // Validate required fields
      if (!categoryData.name || categoryData.name.trim() === '') {
        setError('Category name is required');
        return;
      }

      const response = await apiCall('/api/tenant/categories', {
        method: 'POST',
        body: JSON.stringify(categoryData)
      });

      console.log('🔧 Category creation response:', response.status, response.statusText);

      if (response.ok) {
        const newCategory = await response.json();
        console.log('✅ Category created successfully:', newCategory);

        setCategories(prev => [...prev, newCategory]);
        setSuccess('Category created successfully');
        setShowCategoryModal(false);
        setSelectedCategory(null);

        // Refresh categories to ensure consistency
        fetchCategories();
      } else {
        const errorText = await response.text();
        console.error('❌ Category creation failed:', response.status, errorText);
        setError(`Failed to create category: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      console.error('💥 Error creating category:', error);
      setError(`Error creating category: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const updateCategory = async (categoryId: string, categoryData: Partial<Category>) => {
    try {
      const response = await apiCall(`/api/tenant/categories/${categoryId}`, {
        method: 'PUT',
        body: JSON.stringify(categoryData)
      });

      if (response.ok) {
        const updatedCategory = await response.json();
        setCategories(prev => prev.map(c => c.id === categoryId ? updatedCategory : c));
        setSuccess('Category updated successfully');
        setShowCategoryModal(false);
        setSelectedCategory(null);
      } else {
        setError('Failed to update category');
      }
    } catch (error) {
      console.error('Error updating category:', error);
      setError('Error updating category');
    }
  };

  const deleteCategory = async (categoryId: string) => {
    if (!confirm('Are you sure you want to delete this category? All products in this category will be moved to "Uncategorized".')) return;

    try {
      const response = await apiCall(`/api/tenant/categories/${categoryId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setCategories(prev => prev.filter(c => c.id !== categoryId));
        setSuccess('Category deleted successfully');
        setShowCategoryModal(false);
        setSelectedCategory(null);
        // Refresh products to update category assignments
        fetchProducts();
      } else {
        setError('Failed to delete category');
      }
    } catch (error) {
      console.error('Error deleting category:', error);
      setError('Error deleting category');
    }
  };

  const handleBulkAction = async (action: string) => {
    if (bulkActions.length === 0) return;

    try {
      const response = await apiCall('/api/tenant/products/bulk', {
        method: 'POST',
        body: JSON.stringify({
          action,
          product_ids: bulkActions
        })
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Bulk ${action} completed: ${result.processed} successful, ${result.failed} failed`);
        fetchProducts();
        setBulkActions([]);
      } else {
        setError(`Failed to perform bulk ${action}`);
      }
    } catch (error) {
      console.error('Error performing bulk action:', error);
      setError(`Error performing bulk ${action}`);
    }
  };

  const handleImportProducts = async (file: File) => {
    try {
      const formData = new FormData();
      formData.append('file', file);

      const response = await apiCall('/api/tenant/products/import', {
        method: 'POST',
        body: formData
      });

      if (response.ok) {
        const result = await response.json();
        setSuccess(`Import completed: ${result.imported} products imported, ${result.failed} failed`);
        fetchProducts();
        setShowImportModal(false);
      } else {
        setError('Failed to import products');
      }
    } catch (error) {
      console.error('Error importing products:', error);
      setError('Error importing products');
    }
  };

  const handleExportProducts = async (format: 'csv' | 'xlsx') => {
    try {
      const response = await apiCall(`/api/tenant/products/export?format=${format}`);

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `products.${format}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        setSuccess(`Products exported successfully as ${format.toUpperCase()}`);
        setShowExportModal(false);
      } else {
        setError('Failed to export products');
      }
    } catch (error) {
      console.error('Error exporting products:', error);
      setError('Error exporting products');
    }
  };

  const toggleProductSelection = (productId: string) => {
    setBulkActions(prev => 
      prev.includes(productId) 
        ? prev.filter(id => id !== productId)
        : [...prev, productId]
    );
  };

  const getStockStatus = (product: Product) => {
    if (product.current_stock === 0) {
      return { status: 'Out of Stock', color: 'text-red-600 bg-red-100' };
    } else if (product.current_stock <= product.minimum_stock) {
      return { status: 'Low Stock', color: 'text-orange-600 bg-orange-100' };
    } else {
      return { status: 'In Stock', color: 'text-green-600 bg-green-100' };
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-CA', {
      style: 'currency',
      currency: 'CAD'
    }).format(amount);
  };

  const renderProductCard = (product: Product) => {
    const stockStatus = getStockStatus(product);
    const isSelected = bulkActions.includes(product.id);

    return (
      <div key={product.id} className={`bg-white rounded-lg border shadow-sm hover:shadow-md transition-shadow ${isSelected ? 'ring-2 ring-blue-500' : ''}`}>
        <div className="relative">
          {product.image_url ? (
            <img 
              src={product.image_url} 
              alt={product.name}
              className="w-full h-48 object-cover rounded-t-lg"
            />
          ) : (
            <div className="w-full h-48 bg-gray-100 rounded-t-lg flex items-center justify-center">
              <ImageIcon className="h-12 w-12 text-gray-400" />
            </div>
          )}
          
          <div className="absolute top-2 left-2 flex space-x-2">
            {product.is_featured && (
              <span className="px-2 py-1 bg-yellow-500 text-white text-xs rounded-full">
                Featured
              </span>
            )}
            {!product.is_active && (
              <span className="px-2 py-1 bg-gray-500 text-white text-xs rounded-full">
                Inactive
              </span>
            )}
          </div>

          <div className="absolute top-2 right-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={() => toggleProductSelection(product.id)}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
          </div>

          <div className="absolute bottom-2 right-2">
            <span className={`px-2 py-1 text-xs rounded-full ${stockStatus.color}`}>
              {stockStatus.status}
            </span>
          </div>
        </div>

        <div className="p-4">
          <div className="flex justify-between items-start mb-2">
            <h3 className="font-semibold text-gray-900 truncate">{product.name}</h3>
            <span className="text-lg font-bold text-green-600">{formatCurrency(product.price)}</span>
          </div>
          
          <p className="text-sm text-gray-600 mb-2 line-clamp-2">{product.description}</p>
          
          <div className="flex items-center justify-between text-xs text-gray-500 mb-3">
            <span>SKU: {product.sku}</span>
            <span>Stock: {product.current_stock}</span>
          </div>

          <div className="flex items-center space-x-2 mb-3">
            <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
              {product.category_name}
            </span>
            {product.preparation_time > 0 && (
              <span className="px-2 py-1 bg-gray-100 text-gray-800 text-xs rounded">
                {product.preparation_time}min
              </span>
            )}
          </div>

          <div className="flex space-x-2">
            <button
              onClick={() => {
                setSelectedProduct(product);
                setShowProductModal(true);
              }}
              className="flex-1 px-3 py-2 text-sm bg-blue-600 text-white rounded hover:bg-blue-700"
            >
              <Edit3 className="h-4 w-4 inline mr-1" />
              Edit
            </button>
            <button className="px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50">
              <Eye className="h-4 w-4" />
            </button>
            <button className="px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50">
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    );
  };

  const renderProductRow = (product: Product) => {
    const stockStatus = getStockStatus(product);
    const isSelected = bulkActions.includes(product.id);

    return (
      <tr key={product.id} className={`hover:bg-gray-50 ${isSelected ? 'bg-blue-50' : ''}`}>
        <td className="px-6 py-4 whitespace-nowrap">
          <input
            type="checkbox"
            checked={isSelected}
            onChange={() => toggleProductSelection(product.id)}
            className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
          />
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center">
            {product.image_url ? (
              <img className="h-10 w-10 rounded-lg object-cover" src={product.image_url} alt={product.name} />
            ) : (
              <div className="h-10 w-10 bg-gray-100 rounded-lg flex items-center justify-center">
                <ImageIcon className="h-5 w-5 text-gray-400" />
              </div>
            )}
            <div className="ml-4">
              <div className="text-sm font-medium text-gray-900">{product.name}</div>
              <div className="text-sm text-gray-500">{product.sku}</div>
            </div>
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className="px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
            {product.category_name}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
          {formatCurrency(product.price)}
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <span className={`px-2 py-1 text-xs rounded-full ${stockStatus.color}`}>
            {product.current_stock}
          </span>
        </td>
        <td className="px-6 py-4 whitespace-nowrap">
          <div className="flex items-center space-x-2">
            {product.is_active ? (
              <CheckCircle className="h-5 w-5 text-green-500" />
            ) : (
              <AlertTriangle className="h-5 w-5 text-gray-400" />
            )}
            {product.is_featured && (
              <Tag className="h-4 w-4 text-yellow-500" />
            )}
          </div>
        </td>
        <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
          <div className="flex space-x-2">
            <button
              onClick={() => {
                setSelectedProduct(product);
                setShowProductModal(true);
              }}
              className="text-blue-600 hover:text-blue-900"
            >
              <Edit3 className="h-4 w-4" />
            </button>
            <button className="text-gray-600 hover:text-gray-900">
              <Eye className="h-4 w-4" />
            </button>
            <button className="text-gray-600 hover:text-gray-900">
              <Copy className="h-4 w-4" />
            </button>
          </div>
        </td>
      </tr>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading products...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Error and Success Messages */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-md p-4">
          <div className="flex">
            <AlertTriangle className="h-5 w-5 text-red-400" />
            <div className="ml-3">
              <p className="text-sm text-red-800">{error}</p>
            </div>
            <button
              onClick={() => setError(null)}
              className="ml-auto text-red-400 hover:text-red-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {success && (
        <div className="bg-green-50 border border-green-200 rounded-md p-4">
          <div className="flex">
            <CheckCircle className="h-5 w-5 text-green-400" />
            <div className="ml-3">
              <p className="text-sm text-green-800">{success}</p>
            </div>
            <button
              onClick={() => setSuccess(null)}
              className="ml-auto text-green-400 hover:text-green-600"
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Product Management</h2>
          <p className="text-gray-600">Manage your menu items, categories, and inventory</p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => {
              setSelectedCategory(null);
              setShowCategoryModal(true);
            }}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <Tag className="h-4 w-4 mr-2 inline" />
            Categories
          </button>
          <button
            onClick={() => setShowImportModal(true)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <Upload className="h-4 w-4 mr-2 inline" />
            Import
          </button>
          <button
            onClick={() => setShowExportModal(true)}
            className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50"
          >
            <Download className="h-4 w-4 mr-2 inline" />
            Export
          </button>
          <button
            onClick={() => {
              setSelectedProduct(null);
              setShowProductModal(true);
            }}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <Plus className="h-4 w-4 mr-2 inline" />
            Add Product
          </button>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="bg-white p-4 rounded-lg border shadow-sm">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between space-y-4 lg:space-y-0">
          <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search products..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
            
            <select
              value={selectedCategoryFilter}
              onChange={(e) => setSelectedCategoryFilter(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Categories</option>
              {categories.map(category => (
                <option key={category.id} value={category.id}>
                  {category.name} ({category.product_count})
                </option>
              ))}
            </select>

            <button
              onClick={() => setShowFilters(!showFilters)}
              className="px-3 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 flex items-center"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
            </button>
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                onClick={() => setViewMode('grid')}
                className={`p-2 rounded ${viewMode === 'grid' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                <Grid className="h-4 w-4" />
              </button>
              <button
                onClick={() => setViewMode('list')}
                className={`p-2 rounded ${viewMode === 'list' ? 'bg-blue-100 text-blue-600' : 'text-gray-600 hover:bg-gray-100'}`}
              >
                <List className="h-4 w-4" />
              </button>
            </div>
            
            <span className="text-sm text-gray-500">
              {filteredProducts.length} of {products.length} products
            </span>
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Status</label>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters({...filters, status: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Stock Level</label>
                <select
                  value={filters.stock}
                  onChange={(e) => setFilters({...filters, stock: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Stock</option>
                  <option value="in_stock">In Stock</option>
                  <option value="low_stock">Low Stock</option>
                  <option value="out_of_stock">Out of Stock</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Featured</label>
                <select
                  value={filters.featured}
                  onChange={(e) => setFilters({...filters, featured: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Products</option>
                  <option value="featured">Featured</option>
                  <option value="not_featured">Not Featured</option>
                </select>
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Min Price</label>
                <input
                  type="number"
                  value={filters.price_min}
                  onChange={(e) => setFilters({...filters, price_min: e.target.value})}
                  placeholder="0.00"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Max Price</label>
                <input
                  type="number"
                  value={filters.price_max}
                  onChange={(e) => setFilters({...filters, price_max: e.target.value})}
                  placeholder="999.99"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Bulk Actions */}
      {bulkActions.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {bulkActions.length} product{bulkActions.length > 1 ? 's' : ''} selected
            </span>
            <div className="flex space-x-2">
              <button
                onClick={() => handleBulkAction('activate')}
                className="px-3 py-1 text-sm bg-green-600 text-white rounded hover:bg-green-700"
              >
                Activate
              </button>
              <button
                onClick={() => handleBulkAction('deactivate')}
                className="px-3 py-1 text-sm bg-gray-600 text-white rounded hover:bg-gray-700"
              >
                Deactivate
              </button>
              <button
                onClick={() => handleBulkAction('feature')}
                className="px-3 py-1 text-sm bg-yellow-600 text-white rounded hover:bg-yellow-700"
              >
                Feature
              </button>
              <button
                onClick={() => handleBulkAction('delete')}
                className="px-3 py-1 text-sm bg-red-600 text-white rounded hover:bg-red-700"
              >
                Delete
              </button>
              <button
                onClick={() => setBulkActions([])}
                className="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50"
              >
                Clear
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Products Display */}
      {viewMode === 'grid' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredProducts.map(renderProductCard)}
        </div>
      ) : (
        <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    checked={bulkActions.length === filteredProducts.length && filteredProducts.length > 0}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setBulkActions(filteredProducts.map(p => p.id));
                      } else {
                        setBulkActions([]);
                      }
                    }}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Category</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Price</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Stock</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredProducts.map(renderProductRow)}
            </tbody>
          </table>
        </div>
      )}

      {filteredProducts.length === 0 && (
        <div className="text-center py-12">
          <Package className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900">No products found</h3>
          <p className="mt-1 text-sm text-gray-500">
            {searchTerm || selectedCategory !== 'all' || Object.values(filters).some(f => f !== 'all' && f !== '')
              ? 'Try adjusting your search or filters'
              : 'Get started by adding your first product'
            }
          </p>
          {!searchTerm && selectedCategory === 'all' && (
            <div className="mt-6">
              <button
                onClick={() => {
                  setSelectedProduct(null);
                  setShowProductModal(true);
                }}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Product
              </button>
            </div>
          )}
        </div>
      )}

      {/* Product Creation/Edit Modal */}
      {showProductModal && (
        <ProductModal
          product={selectedProduct}
          categories={categories}
          onSave={selectedProduct ?
            (data) => updateProduct(selectedProduct.id, data) :
            (data) => createProduct(data)
          }
          onDelete={selectedProduct ? () => deleteProduct(selectedProduct.id) : undefined}
          onClose={() => {
            setShowProductModal(false);
            setSelectedProduct(null);
          }}
        />
      )}

      {/* Category Management Modal */}
      {showCategoryModal && (
        <CategoryModal
          category={selectedCategory}
          onSave={selectedCategory ?
            (data) => updateCategory(selectedCategory.id, data) :
            (data) => createCategory(data)
          }
          onDelete={selectedCategory ? () => deleteCategory(selectedCategory.id) : undefined}
          onClose={() => {
            setShowCategoryModal(false);
            setSelectedCategory(null);
          }}
        />
      )}

      {/* Import Modal */}
      {showImportModal && (
        <ImportModal
          onImport={handleImportProducts}
          onClose={() => setShowImportModal(false)}
        />
      )}

      {/* Export Modal */}
      {showExportModal && (
        <ExportModal
          onExport={handleExportProducts}
          onClose={() => setShowExportModal(false)}
        />
      )}
    </div>
  );
};

// Product Modal Component
interface ProductModalProps {
  product: Product | null;
  categories: Category[];
  onSave: (data: Partial<Product>) => void;
  onDelete?: () => void;
  onClose: () => void;
}

const ProductModal: React.FC<ProductModalProps> = ({ product, categories, onSave, onDelete, onClose }) => {
  const [formData, setFormData] = useState<Partial<Product>>({
    name: product?.name || '',
    description: product?.description || '',
    price: product?.price || 0,
    cost_price: product?.cost_price || 0,
    category_id: product?.category_id || '',
    sku: product?.sku || '',
    current_stock: product?.current_stock || 0,
    minimum_stock: product?.minimum_stock || 5,
    preparation_time: product?.preparation_time || 0,
    is_active: product?.is_active ?? true,
    is_featured: product?.is_featured || false,
    allergens: product?.allergens || [],
    tags: product?.tags || []
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {product ? 'Edit Product' : 'Create New Product'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Product Name *
              </label>
              <input
                type="text"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                SKU *
              </label>
              <input
                type="text"
                value={formData.sku}
                onChange={(e) => setFormData({...formData, sku: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Price *
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.price}
                onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Cost Price
              </label>
              <input
                type="number"
                step="0.01"
                value={formData.cost_price}
                onChange={(e) => setFormData({...formData, cost_price: parseFloat(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Category *
              </label>
              <select
                value={formData.category_id}
                onChange={(e) => setFormData({...formData, category_id: e.target.value})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                required
              >
                <option value="">Select Category</option>
                {categories.map(category => (
                  <option key={category.id} value={category.id}>{category.name}</option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Preparation Time (minutes)
              </label>
              <input
                type="number"
                value={formData.preparation_time}
                onChange={(e) => setFormData({...formData, preparation_time: parseInt(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Current Stock
              </label>
              <input
                type="number"
                value={formData.current_stock}
                onChange={(e) => setFormData({...formData, current_stock: parseInt(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Minimum Stock
              </label>
              <input
                type="number"
                value={formData.minimum_stock}
                onChange={(e) => setFormData({...formData, minimum_stock: parseInt(e.target.value) || 0})}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div className="flex items-center space-x-6">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Active</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_featured}
                onChange={(e) => setFormData({...formData, is_featured: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Featured</span>
            </label>
          </div>

          <div className="flex justify-between items-center pt-6 border-t border-gray-200">
            {onDelete && (
              <button
                type="button"
                onClick={onDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete Product
              </button>
            )}

            <div className="flex space-x-3 ml-auto">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
              >
                {product ? 'Update Product' : 'Create Product'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

// Category Modal Component
interface CategoryModalProps {
  category: Category | null;
  onSave: (data: Partial<Category>) => void;
  onDelete?: () => void;
  onClose: () => void;
}

const CategoryModal: React.FC<CategoryModalProps> = ({ category, onSave, onDelete, onClose }) => {
  const [formData, setFormData] = useState<Partial<Category>>({
    name: category?.name || '',
    description: category?.description || '',
    color: category?.color || '#3B82F6',
    icon: category?.icon || 'package',
    is_active: category?.is_active ?? true
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [validationError, setValidationError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setValidationError(null);

    // Validate form data
    if (!formData.name || formData.name.trim() === '') {
      setValidationError('Category name is required');
      return;
    }

    if (formData.name.trim().length < 2) {
      setValidationError('Category name must be at least 2 characters long');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('🔧 CategoryModal submitting data:', formData);
      await onSave(formData);
    } catch (error) {
      console.error('💥 CategoryModal submission error:', error);
      setValidationError('Failed to save category. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">
            {category ? 'Edit Category' : 'Create New Category'}
          </h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* Validation Error Display */}
          {validationError && (
            <div className="bg-red-50 border border-red-200 rounded-md p-3">
              <div className="flex">
                <AlertTriangle className="h-5 w-5 text-red-400" />
                <div className="ml-3">
                  <p className="text-sm text-red-800">{validationError}</p>
                </div>
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Category Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => {
                setFormData({...formData, name: e.target.value});
                setValidationError(null); // Clear validation error on change
              }}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter category name"
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({...formData, description: e.target.value})}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Color
            </label>
            <input
              type="color"
              value={formData.color}
              onChange={(e) => setFormData({...formData, color: e.target.value})}
              className="w-full h-10 border border-gray-300 rounded-md"
            />
          </div>

          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_active}
                onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span className="ml-2 text-sm text-gray-700">Active</span>
            </label>
          </div>

          <div className="flex justify-between items-center pt-6 border-t border-gray-200">
            {onDelete && (
              <button
                type="button"
                onClick={onDelete}
                className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700"
              >
                Delete Category
              </button>
            )}

            <div className="flex space-x-3 ml-auto">
              <button
                type="button"
                onClick={onClose}
                disabled={isSubmitting}
                className="px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 disabled:opacity-50"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 flex items-center"
              >
                {isSubmitting && (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                )}
                {category ? 'Update Category' : 'Create Category'}
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
};

// Import Modal Component
interface ImportModalProps {
  onImport: (file: File) => void;
  onClose: () => void;
}

const ImportModal: React.FC<ImportModalProps> = ({ onImport, onClose }) => {
  const [file, setFile] = useState<File | null>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (file) {
      onImport(file);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Import Products</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select CSV or Excel File
            </label>
            <input
              type="file"
              accept=".csv,.xlsx,.xls"
              onChange={(e) => setFile(e.target.files?.[0] || null)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              required
            />
          </div>

          <div className="text-sm text-gray-600">
            <p>Supported formats: CSV, Excel (.xlsx, .xls)</p>
            <p>Required columns: name, sku, price, category_id</p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={!file}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
            >
              Import
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

// Export Modal Component
interface ExportModalProps {
  onExport: (format: 'csv' | 'xlsx') => void;
  onClose: () => void;
}

const ExportModal: React.FC<ExportModalProps> = ({ onExport, onClose }) => {
  const [format, setFormat] = useState<'csv' | 'xlsx'>('csv');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onExport(format);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg w-full max-w-md">
        <div className="flex justify-between items-center p-6 border-b border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900">Export Products</h3>
          <button onClick={onClose} className="text-gray-400 hover:text-gray-600">
            <X className="h-6 w-6" />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Export Format
            </label>
            <select
              value={format}
              onChange={(e) => setFormat(e.target.value as 'csv' | 'xlsx')}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="csv">CSV</option>
              <option value="xlsx">Excel (.xlsx)</option>
            </select>
          </div>

          <div className="text-sm text-gray-600">
            <p>Export includes all product data including inventory levels and pricing.</p>
          </div>

          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              type="submit"
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
            >
              Export
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ProductManagementInterface;
