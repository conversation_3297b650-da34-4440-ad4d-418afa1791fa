/* Enhanced POS System Styles - Simplified for Tailwind CSS v4 Compatibility */

/* Enhanced <PERSON><PERSON> Styles */
.btn-primary {
  background: linear-gradient(to right, #2563eb, #9333ea);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.btn-primary:hover {
  background: linear-gradient(to right, #1d4ed8, #7c3aed);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-primary:active {
  transform: scale(0.95);
}

.btn-secondary {
  background: white;
  color: #374151;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: 1px solid #d1d5db;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  transform: scale(1);
  transition: all 0.2s;
  cursor: pointer;
}

.btn-secondary:hover {
  background: #f9fafb;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-secondary:active {
  transform: scale(0.95);
}

.btn-success {
  background: linear-gradient(to right, #059669, #047857);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.btn-success:hover {
  background: linear-gradient(to right, #047857, #065f46);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-success:active {
  transform: scale(0.95);
}

.btn-danger {
  background: linear-gradient(to right, #dc2626, #ec4899);
  color: white;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  transform: scale(1);
  transition: all 0.2s;
  border: none;
  cursor: pointer;
}

.btn-danger:hover {
  background: linear-gradient(to right, #b91c1c, #db2777);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  transform: scale(1.05);
}

.btn-danger:active {
  transform: scale(0.95);
}

/* Enhanced Card Styles */
.card {
  background: white;
  border-radius: 0.75rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.card-header {
  padding: 1rem;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(to right, #f9fafb, #f3f4f6);
}

.card-body {
  padding: 1rem;
}

.card-footer {
  padding: 1rem;
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
}

/* Enhanced Form Styles */
.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #d1d5db;
  border-radius: 0.5rem;
  background: white;
  transition: all 0.2s;
}

.form-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-error {
  font-size: 0.875rem;
  color: #dc2626;
  margin-top: 0.25rem;
}

/* Enhanced Status Indicators */
.status-online {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #047857;
}

.status-offline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #b91c1c;
}

.status-loading {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #d97706;
}

.status-dot {
  width: 0.5rem;
  height: 0.5rem;
  border-radius: 50%;
}

.status-dot-online {
  background: #10b981;
  animation: pulse 2s infinite;
}

.status-dot-offline {
  background: #ef4444;
}

.status-dot-loading {
  background: #f59e0b;
  animation: pulse 2s infinite;
}

/* Enhanced Loading States */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid #d1d5db;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Enhanced Badges */
.badge {
  display: inline-flex;
  align-items: center;
  padding: 0.125rem 0.625rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
}

.badge-primary {
  background: #dbeafe;
  color: #1e40af;
}

.badge-success {
  background: #d1fae5;
  color: #065f46;
}

.badge-warning {
  background: #fef3c7;
  color: #92400e;
}

.badge-danger {
  background: #fee2e2;
  color: #991b1b;
}

.badge-info {
  background: #e0e7ff;
  color: #3730a3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .card {
    margin: 0 0.5rem;
  }
}

/* Print Styles */
@media print {
  .btn-primary,
  .btn-secondary,
  .btn-success,
  .btn-danger {
    background: white !important;
    color: black !important;
    border: 1px solid black !important;
  }
}
