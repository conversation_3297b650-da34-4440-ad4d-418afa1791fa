import React, { useState, useEffect } from 'react';
import {
  Lock, Mail, Eye, EyeOff, Shield, Smartphone, Key,
  AlertTriangle, CheckCircle, Loader, ArrowRight, Building
} from 'lucide-react';

interface LoginFormData {
  email: string;
  password: string;
  tenantSlug: string;
  rememberMe: boolean;
}

interface TwoFactorData {
  code: string;
  method: 'sms' | 'authenticator';
}

const TenantLogin: React.FC = () => {
  const [step, setStep] = useState<'login' | 'tenant' | '2fa' | 'success'>('tenant');
  const [formData, setFormData] = useState<LoginFormData>({
    email: '',
    password: '',
    tenantSlug: '',
    rememberMe: false
  });
  const [twoFactorData, setTwoFactorData] = useState<TwoFactorData>({
    code: '',
    method: 'authenticator'
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [tenantInfo, setTenantInfo] = useState<any>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [requires2FA, setRequires2FA] = useState(false);

  // Check if user is already logged in
  useEffect(() => {
    const token = localStorage.getItem('tenantAuthToken');
    if (token) {
      // Verify token and redirect if valid
      verifyExistingToken(token);
    }
  }, []);

  // Password strength checker
  useEffect(() => {
    const calculateStrength = (password: string) => {
      let strength = 0;
      if (password.length >= 8) strength += 1;
      if (/[A-Z]/.test(password)) strength += 1;
      if (/[a-z]/.test(password)) strength += 1;
      if (/[0-9]/.test(password)) strength += 1;
      if (/[^A-Za-z0-9]/.test(password)) strength += 1;
      return strength;
    };
    
    setPasswordStrength(calculateStrength(formData.password));
  }, [formData.password]);

  const verifyExistingToken = async (token: string) => {
    try {
      const response = await fetch('/api/tenant/auth/verify', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        // Token is valid, redirect to dashboard
        window.location.href = '/tenant/dashboard';
      } else {
        // Token is invalid, remove it
        localStorage.removeItem('tenantAuthToken');
      }
    } catch (error) {
      console.error('Token verification error:', error);
      localStorage.removeItem('tenantAuthToken');
    }
  };

  const handleTenantSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.tenantSlug.trim()) {
      setError('Please enter your restaurant identifier');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      // Verify tenant exists and get tenant info
      const response = await fetch(`/api/tenant/verify/${formData.tenantSlug}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      if (response.ok) {
        const tenant = await response.json();
        setTenantInfo(tenant);
        setStep('login');
        setSuccess(`Welcome to ${tenant.name}! Please enter your credentials.`);
      } else {
        const errorData = await response.json();
        setError(errorData.message || 'Restaurant not found. Please check your identifier.');
      }
    } catch (error) {
      console.error('Tenant verification error:', error);
      setError('Unable to verify restaurant. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleLoginSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email || !formData.password) {
      setError('Please fill in all required fields');
      return;
    }

    if (passwordStrength < 3) {
      setError('Password does not meet security requirements');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tenant/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          tenantSlug: formData.tenantSlug,
          rememberMe: formData.rememberMe
        })
      });

      const data = await response.json();

      if (response.ok) {
        if (data.requires2FA) {
          setRequires2FA(true);
          setStep('2fa');
          setSuccess('Please enter your 2FA code to complete login');
        } else {
          // Login successful without 2FA
          localStorage.setItem('tenantAuthToken', data.token);
          if (formData.rememberMe) {
            localStorage.setItem('tenantRememberMe', 'true');
          }
          setStep('success');
          setTimeout(() => {
            window.location.href = '/tenant/dashboard';
          }, 2000);
        }
      } else {
        setError(data.message || 'Login failed. Please check your credentials.');
      }
    } catch (error) {
      console.error('Login error:', error);
      setError('Login failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handle2FASubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!twoFactorData.code || twoFactorData.code.length !== 6) {
      setError('Please enter a valid 6-digit code');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/tenant/auth/verify-2fa', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: formData.email,
          tenantSlug: formData.tenantSlug,
          code: twoFactorData.code,
          method: twoFactorData.method
        })
      });

      const data = await response.json();

      if (response.ok) {
        localStorage.setItem('tenantAuthToken', data.token);
        if (formData.rememberMe) {
          localStorage.setItem('tenantRememberMe', 'true');
        }
        setStep('success');
        setTimeout(() => {
          window.location.href = '/tenant/dashboard';
        }, 2000);
      } else {
        setError(data.message || 'Invalid 2FA code. Please try again.');
      }
    } catch (error) {
      console.error('2FA verification error:', error);
      setError('2FA verification failed. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const getPasswordStrengthColor = () => {
    if (passwordStrength <= 1) return 'bg-red-500';
    if (passwordStrength <= 2) return 'bg-yellow-500';
    if (passwordStrength <= 3) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getPasswordStrengthText = () => {
    if (passwordStrength <= 1) return 'Weak';
    if (passwordStrength <= 2) return 'Fair';
    if (passwordStrength <= 3) return 'Good';
    return 'Strong';
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Header */}
        <div className="text-center">
          <div className="mx-auto h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center">
            <Building className="h-6 w-6 text-white" />
          </div>
          <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
            RestroFlow Tenant Access
          </h2>
          <p className="mt-2 text-sm text-gray-600">
            {step === 'tenant' && 'Enter your restaurant identifier to continue'}
            {step === 'login' && `Sign in to ${tenantInfo?.name || 'your restaurant'}`}
            {step === '2fa' && 'Enter your two-factor authentication code'}
            {step === 'success' && 'Login successful! Redirecting...'}
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="flex items-center justify-center space-x-2">
          <div className={`w-3 h-3 rounded-full ${
            ['tenant', 'login', '2fa', 'success'].includes(step) ? 'bg-blue-600' : 'bg-gray-300'
          }`}></div>
          <div className={`w-3 h-3 rounded-full ${
            ['login', '2fa', 'success'].includes(step) ? 'bg-blue-600' : 'bg-gray-300'
          }`}></div>
          {requires2FA && (
            <div className={`w-3 h-3 rounded-full ${
              ['2fa', 'success'].includes(step) ? 'bg-blue-600' : 'bg-gray-300'
            }`}></div>
          )}
          <div className={`w-3 h-3 rounded-full ${
            step === 'success' ? 'bg-green-600' : 'bg-gray-300'
          }`}></div>
        </div>

        {/* Error/Success Messages */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="flex">
              <AlertTriangle className="h-5 w-5 text-red-400" />
              <div className="ml-3">
                <p className="text-sm text-red-800">{error}</p>
              </div>
            </div>
          </div>
        )}

        {success && (
          <div className="bg-green-50 border border-green-200 rounded-md p-4">
            <div className="flex">
              <CheckCircle className="h-5 w-5 text-green-400" />
              <div className="ml-3">
                <p className="text-sm text-green-800">{success}</p>
              </div>
            </div>
          </div>
        )}

        {/* Login Form */}
        {step === 'login' && (
          <form className="mt-8 space-y-6" onSubmit={handleLoginSubmit}>
            <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
              {/* Tenant Info Display */}
              {tenantInfo && (
                <div className="bg-blue-50 border border-blue-200 rounded-md p-3">
                  <div className="flex items-center">
                    {tenantInfo.logo ? (
                      <img src={tenantInfo.logo} alt={tenantInfo.name} className="h-8 w-8 rounded" />
                    ) : (
                      <Building className="h-8 w-8 text-blue-600" />
                    )}
                    <div className="ml-3">
                      <p className="text-sm font-medium text-blue-900">{tenantInfo.name}</p>
                      <p className="text-xs text-blue-700">{tenantInfo.slug}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Email Field */}
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                  Email Address
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Mail className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="email"
                    name="email"
                    type="email"
                    autoComplete="email"
                    required
                    className="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="Enter your email address"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  />
                </div>
              </div>

              {/* Password Field */}
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700">
                  Password
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Lock className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    autoComplete="current-password"
                    required
                    className="appearance-none relative block w-full pl-10 pr-10 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  />
                  <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                    <button
                      type="button"
                      className="text-gray-400 hover:text-gray-600"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                    </button>
                  </div>
                </div>

                {/* Password Strength Indicator */}
                {formData.password && (
                  <div className="mt-2">
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-gray-500">Password Strength:</span>
                      <span className={`font-medium ${
                        passwordStrength <= 1 ? 'text-red-600' :
                        passwordStrength <= 2 ? 'text-yellow-600' :
                        passwordStrength <= 3 ? 'text-blue-600' :
                        'text-green-600'
                      }`}>
                        {getPasswordStrengthText()}
                      </span>
                    </div>
                    <div className="mt-1 w-full bg-gray-200 rounded-full h-1">
                      <div
                        className={`h-1 rounded-full transition-all duration-300 ${getPasswordStrengthColor()}`}
                        style={{ width: `${(passwordStrength / 5) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                )}
              </div>

              {/* Remember Me */}
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <input
                    id="rememberMe"
                    name="rememberMe"
                    type="checkbox"
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                    checked={formData.rememberMe}
                    onChange={(e) => setFormData({ ...formData, rememberMe: e.target.checked })}
                  />
                  <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900">
                    Remember me for 30 days
                  </label>
                </div>
                <div className="text-sm">
                  <a href="/tenant/forgot-password" className="font-medium text-blue-600 hover:text-blue-500">
                    Forgot password?
                  </a>
                </div>
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  disabled={loading || passwordStrength < 3}
                  className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      <Shield className="mr-2 h-4 w-4" />
                      Sign In Securely
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        )}

        {/* 2FA Form */}
        {step === '2fa' && (
          <form className="mt-8 space-y-6" onSubmit={handle2FASubmit}>
            <div className="bg-white rounded-lg shadow-md p-6 space-y-4">
              <div className="text-center">
                <div className="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Smartphone className="h-6 w-6 text-green-600" />
                </div>
                <h3 className="mt-2 text-lg font-medium text-gray-900">Two-Factor Authentication</h3>
                <p className="mt-1 text-sm text-gray-600">
                  Enter the 6-digit code from your authenticator app or SMS
                </p>
              </div>

              {/* 2FA Method Selection */}
              <div className="flex space-x-4">
                <button
                  type="button"
                  onClick={() => setTwoFactorData({ ...twoFactorData, method: 'authenticator' })}
                  className={`flex-1 p-3 border rounded-md text-sm font-medium ${
                    twoFactorData.method === 'authenticator'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Key className="h-4 w-4 mx-auto mb-1" />
                  Authenticator App
                </button>
                <button
                  type="button"
                  onClick={() => setTwoFactorData({ ...twoFactorData, method: 'sms' })}
                  className={`flex-1 p-3 border rounded-md text-sm font-medium ${
                    twoFactorData.method === 'sms'
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-300 text-gray-700 hover:bg-gray-50'
                  }`}
                >
                  <Smartphone className="h-4 w-4 mx-auto mb-1" />
                  SMS Code
                </button>
              </div>

              {/* 2FA Code Input */}
              <div>
                <label htmlFor="twoFactorCode" className="block text-sm font-medium text-gray-700">
                  Verification Code
                </label>
                <div className="mt-1">
                  <input
                    id="twoFactorCode"
                    name="twoFactorCode"
                    type="text"
                    maxLength={6}
                    pattern="[0-9]{6}"
                    required
                    className="appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm text-center text-lg tracking-widest"
                    placeholder="000000"
                    value={twoFactorData.code}
                    onChange={(e) => setTwoFactorData({ ...twoFactorData, code: e.target.value.replace(/\D/g, '') })}
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div>
                <button
                  type="submit"
                  disabled={loading || twoFactorData.code.length !== 6}
                  className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      Verify & Continue
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </button>
              </div>

              {/* Back Button */}
              <div>
                <button
                  type="button"
                  onClick={() => setStep('login')}
                  className="w-full text-center text-sm text-gray-600 hover:text-gray-900"
                >
                  ← Back to login
                </button>
              </div>
            </div>
          </form>
        )}

        {/* Success State */}
        {step === 'success' && (
          <div className="bg-white rounded-lg shadow-md p-6 text-center">
            <div className="mx-auto h-12 w-12 bg-green-100 rounded-full flex items-center justify-center">
              <CheckCircle className="h-6 w-6 text-green-600" />
            </div>
            <h3 className="mt-2 text-lg font-medium text-gray-900">Login Successful!</h3>
            <p className="mt-1 text-sm text-gray-600">
              Redirecting to your dashboard...
            </p>
            <div className="mt-4">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600 mx-auto"></div>
            </div>
          </div>
        )}

        {/* Footer */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            Protected by enterprise-grade security • RestroFlow © 2025
          </p>
        </div>
      </div>
    </div>
  );
};

export default TenantLogin;

        {/* Tenant Identification Form */}
        {step === 'tenant' && (
          <form className="mt-8 space-y-6" onSubmit={handleTenantSubmit}>
            <div className="bg-white rounded-lg shadow-md p-6">
              <div>
                <label htmlFor="tenantSlug" className="block text-sm font-medium text-gray-700">
                  Restaurant Identifier
                </label>
                <div className="mt-1 relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Building className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    id="tenantSlug"
                    name="tenantSlug"
                    type="text"
                    required
                    className="appearance-none relative block w-full pl-10 pr-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-blue-500 focus:border-blue-500 focus:z-10 sm:text-sm"
                    placeholder="e.g., my-restaurant"
                    value={formData.tenantSlug}
                    onChange={(e) => setFormData({ ...formData, tenantSlug: e.target.value.toLowerCase().replace(/[^a-z0-9-]/g, '') })}
                  />
                </div>
                <p className="mt-2 text-xs text-gray-500">
                  This is the unique identifier provided by your system administrator
                </p>
              </div>

              <div className="mt-6">
                <button
                  type="submit"
                  disabled={loading || !formData.tenantSlug.trim()}
                  className="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loading ? (
                    <Loader className="h-4 w-4 animate-spin" />
                  ) : (
                    <>
                      Continue
                      <ArrowRight className="ml-2 h-4 w-4" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </form>
        )}
