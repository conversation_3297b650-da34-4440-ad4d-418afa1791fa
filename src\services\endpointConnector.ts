/**
 * Comprehensive Endpoint Connector for RESTROFLOW
 * Scans, maps, and connects all available API endpoints
 */

export interface EndpointInfo {
  method: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  path: string;
  category: string;
  description: string;
  requiresAuth: boolean;
  requiredRole?: string;
  parameters?: string[];
  responseType: string;
  status: 'active' | 'deprecated' | 'testing';
}

export class EndpointConnector {
  private baseURL: string;
  private authToken: string | null = null;
  private endpoints: EndpointInfo[] = [];

  constructor(baseURL: string = 'http://localhost:4000') {
    this.baseURL = baseURL;
    this.authToken = localStorage.getItem('authToken');
    this.initializeEndpoints();
  }

  private initializeEndpoints() {
    this.endpoints = [
      // Health & System
      { method: 'GET', path: '/api/health', category: 'system', description: 'System health check', requiresAuth: false, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/health/database', category: 'system', description: 'Database health check', requiresAuth: false, responseType: 'json', status: 'active' },
      
      // Authentication
      { method: 'POST', path: '/api/auth/login', category: 'auth', description: 'User authentication', requiresAuth: false, parameters: ['pin', 'tenant_slug'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/auth/verify', category: 'auth', description: 'Token verification', requiresAuth: true, responseType: 'json', status: 'active' },
      
      // Products
      { method: 'GET', path: '/api/products', category: 'products', description: 'Get all products', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/products', category: 'products', description: 'Create new product', requiresAuth: true, parameters: ['name', 'price', 'category_id'], responseType: 'json', status: 'active' },
      
      // Categories
      { method: 'GET', path: '/api/categories', category: 'categories', description: 'Get all categories', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/categories', category: 'categories', description: 'Create new category', requiresAuth: true, parameters: ['name'], responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/categories/:id', category: 'categories', description: 'Update category', requiresAuth: true, parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'DELETE', path: '/api/categories/:id', category: 'categories', description: 'Delete category', requiresAuth: true, parameters: ['id'], responseType: 'json', status: 'active' },
      
      // Orders
      { method: 'GET', path: '/api/orders', category: 'orders', description: 'Get all orders', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/orders', category: 'orders', description: 'Create new order', requiresAuth: true, parameters: ['items', 'total'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/orders/duplicate', category: 'orders', description: 'Duplicate existing order', requiresAuth: true, parameters: ['order_id'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/orders/:id/kitchen', category: 'orders', description: 'Send order to kitchen', requiresAuth: true, parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/orders/:id/cancel', category: 'orders', description: 'Cancel order', requiresAuth: true, parameters: ['id', 'reason'], responseType: 'json', status: 'active' },
      
      // Payments
      { method: 'POST', path: '/api/payments/process', category: 'payments', description: 'Process payment', requiresAuth: true, parameters: ['order_id', 'amount', 'payment_method'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/payments/methods', category: 'payments', description: 'Get payment methods', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/payments/history', category: 'payments', description: 'Get payment history', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/payments/refund', category: 'payments', description: 'Process refund', requiresAuth: true, requiredRole: 'manager', parameters: ['payment_id', 'amount'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/payments/analytics', category: 'payments', description: 'Payment analytics', requiresAuth: true, responseType: 'json', status: 'active' },
      
      // Tenants
      { method: 'GET', path: '/api/tenants', category: 'tenants', description: 'Get all tenants', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/tenants/public', category: 'tenants', description: 'Get public tenant list', requiresAuth: false, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/tenants/public/:slug', category: 'tenants', description: 'Get public tenant info', requiresAuth: false, parameters: ['slug'], responseType: 'json', status: 'active' },
      
      // Admin - Dashboard
      { method: 'GET', path: '/api/admin/dashboard/stats', category: 'admin', description: 'Admin dashboard statistics', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/admin/dashboard/critical', category: 'admin', description: 'Critical dashboard data', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/admin/metrics', category: 'admin', description: 'System metrics', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      
      // Admin - Users
      { method: 'GET', path: '/api/admin/users', category: 'admin', description: 'Get all users', requiresAuth: true, requiredRole: 'admin', responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/admin/users', category: 'admin', description: 'Create new user', requiresAuth: true, requiredRole: 'admin', parameters: ['name', 'email', 'role'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/admin/users/:id', category: 'admin', description: 'Get user details', requiresAuth: true, requiredRole: 'admin', parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/admin/users/:id', category: 'admin', description: 'Update user', requiresAuth: true, requiredRole: 'admin', parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'DELETE', path: '/api/admin/users/:id', category: 'admin', description: 'Delete user', requiresAuth: true, requiredRole: 'admin', parameters: ['id'], responseType: 'json', status: 'active' },
      
      // Admin - Tenants
      { method: 'GET', path: '/api/admin/tenants', category: 'admin', description: 'Get all tenants (admin)', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/admin/tenants', category: 'admin', description: 'Create new tenant', requiresAuth: true, requiredRole: 'super_admin', parameters: ['name', 'business_name'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/admin/tenants/:id', category: 'admin', description: 'Get tenant details', requiresAuth: true, requiredRole: 'super_admin', parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/admin/tenants/:id', category: 'admin', description: 'Update tenant', requiresAuth: true, requiredRole: 'super_admin', parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'DELETE', path: '/api/admin/tenants/:id', category: 'admin', description: 'Delete tenant', requiresAuth: true, requiredRole: 'super_admin', parameters: ['id'], responseType: 'json', status: 'active' },
      
      // Analytics
      { method: 'GET', path: '/api/admin/analytics', category: 'analytics', description: 'Admin analytics', requiresAuth: true, requiredRole: 'super_admin', responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/analytics/sales', category: 'analytics', description: 'Sales analytics', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/analytics/customers', category: 'analytics', description: 'Customer analytics', requiresAuth: true, responseType: 'json', status: 'active' },
      
      // Inventory
      { method: 'GET', path: '/api/inventory', category: 'inventory', description: 'Get inventory items', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/inventory/:id', category: 'inventory', description: 'Update inventory item', requiresAuth: true, parameters: ['id'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/inventory/:id/adjust', category: 'inventory', description: 'Adjust inventory stock', requiresAuth: true, parameters: ['id', 'adjustment'], responseType: 'json', status: 'active' },
      
      // Kitchen
      { method: 'GET', path: '/api/kitchen/orders', category: 'kitchen', description: 'Get kitchen orders', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/kitchen/orders/:id/status', category: 'kitchen', description: 'Update kitchen order status', requiresAuth: true, parameters: ['id', 'status'], responseType: 'json', status: 'active' },
      
      // Floor Management
      { method: 'GET', path: '/api/floor/layout', category: 'floor', description: 'Get floor layout', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/floor/tables', category: 'floor', description: 'Get all tables', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/floor/tables', category: 'floor', description: 'Create new table', requiresAuth: true, parameters: ['number', 'seats'], responseType: 'json', status: 'active' },
      { method: 'PUT', path: '/api/floor/tables/:tableId', category: 'floor', description: 'Update table', requiresAuth: true, parameters: ['tableId'], responseType: 'json', status: 'active' },
      { method: 'DELETE', path: '/api/floor/tables/:tableId', category: 'floor', description: 'Delete table', requiresAuth: true, parameters: ['tableId'], responseType: 'json', status: 'active' },
      
      // Hardware
      { method: 'GET', path: '/api/hardware/devices', category: 'hardware', description: 'Get hardware devices', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/hardware/printers', category: 'hardware', description: 'Get receipt printers', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/hardware/printers/:printerId/test', category: 'hardware', description: 'Test printer', requiresAuth: true, parameters: ['printerId'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/hardware/printers/:printerId/print', category: 'hardware', description: 'Print receipt', requiresAuth: true, parameters: ['printerId'], responseType: 'json', status: 'active' },
      
      // AI Features
      { method: 'POST', path: '/api/ai/fraud/analyze-transaction', category: 'ai', description: 'AI fraud detection', requiresAuth: true, parameters: ['transaction_data'], responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/ai/predictions/sales-forecast', category: 'ai', description: 'AI sales forecast', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/ai/predictions/demand-forecast', category: 'ai', description: 'AI demand forecast', requiresAuth: true, responseType: 'json', status: 'active' },
      
      // Global Features
      { method: 'GET', path: '/api/global/currencies/supported', category: 'global', description: 'Supported currencies', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'GET', path: '/api/global/currencies/exchange-rates', category: 'global', description: 'Exchange rates', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/global/currencies/convert', category: 'global', description: 'Currency conversion', requiresAuth: true, parameters: ['amount', 'from_currency', 'to_currency'], responseType: 'json', status: 'active' },
      
      // Settings
      { method: 'GET', path: '/api/settings/all', category: 'settings', description: 'Get all settings', requiresAuth: true, responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/settings/update', category: 'settings', description: 'Update settings', requiresAuth: true, parameters: ['settings'], responseType: 'json', status: 'active' },
      { method: 'POST', path: '/api/settings/reset', category: 'settings', description: 'Reset settings', requiresAuth: true, parameters: ['category'], responseType: 'json', status: 'active' }
    ];
  }

  // Get all endpoints
  getAllEndpoints(): EndpointInfo[] {
    return this.endpoints;
  }

  // Get endpoints by category
  getEndpointsByCategory(category: string): EndpointInfo[] {
    return this.endpoints.filter(endpoint => endpoint.category === category);
  }

  // Get available categories
  getCategories(): string[] {
    return [...new Set(this.endpoints.map(endpoint => endpoint.category))];
  }

  // Test endpoint connectivity
  async testEndpoint(endpoint: EndpointInfo): Promise<{ success: boolean; status?: number; error?: string; responseTime?: number }> {
    const startTime = Date.now();
    
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      };

      if (endpoint.requiresAuth && this.authToken) {
        headers['Authorization'] = `Bearer ${this.authToken}`;
      }

      const response = await fetch(`${this.baseURL}${endpoint.path}`, {
        method: endpoint.method,
        headers,
        ...(endpoint.method === 'POST' && { body: JSON.stringify({}) })
      });

      const responseTime = Date.now() - startTime;

      return {
        success: response.ok,
        status: response.status,
        responseTime
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        responseTime: Date.now() - startTime
      };
    }
  }

  // Test all endpoints
  async testAllEndpoints(): Promise<Record<string, any>> {
    const results: Record<string, any> = {};
    
    for (const endpoint of this.endpoints) {
      const key = `${endpoint.method} ${endpoint.path}`;
      results[key] = await this.testEndpoint(endpoint);
    }

    return results;
  }

  // Get endpoint statistics
  getEndpointStats(): Record<string, number> {
    const stats: Record<string, number> = {
      total: this.endpoints.length,
      requiresAuth: this.endpoints.filter(e => e.requiresAuth).length,
      public: this.endpoints.filter(e => !e.requiresAuth).length,
      adminOnly: this.endpoints.filter(e => e.requiredRole === 'super_admin' || e.requiredRole === 'admin').length
    };

    // Count by category
    this.getCategories().forEach(category => {
      stats[category] = this.getEndpointsByCategory(category).length;
    });

    // Count by method
    ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'].forEach(method => {
      stats[method] = this.endpoints.filter(e => e.method === method).length;
    });

    return stats;
  }

  // Set authentication token
  setAuthToken(token: string) {
    this.authToken = token;
    localStorage.setItem('authToken', token);
  }

  // Clear authentication token
  clearAuthToken() {
    this.authToken = null;
    localStorage.removeItem('authToken');
  }

  // Make authenticated request
  async makeRequest(endpoint: EndpointInfo, data?: any): Promise<any> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };

    if (endpoint.requiresAuth && this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    }

    const config: RequestInit = {
      method: endpoint.method,
      headers
    };

    if (data && ['POST', 'PUT', 'PATCH'].includes(endpoint.method)) {
      config.body = JSON.stringify(data);
    }

    const response = await fetch(`${this.baseURL}${endpoint.path}`, config);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return response.json();
  }
}

// Export singleton instance
export const endpointConnector = new EndpointConnector();
export default endpointConnector;
