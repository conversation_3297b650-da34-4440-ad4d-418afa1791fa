const jwt = require('jsonwebtoken');
const { Pool } = require('pg');

const pool = new Pool({
  user: 'BARP<PERSON>',
  host: 'localhost',
  database: 'RESTROFLOW',
  password: 'Chaand@0319',
  port: 5432,
});

const JWT_SECRET = process.env.JWT_SECRET || 'barpos-super-secure-jwt-secret-key-2024-production-v2-enhanced';

// Middleware to verify JWT token and extract tenant info
const authenticateToken = async (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];

  if (!token) {
    return res.status(401).json({ error: 'Access token required' });
  }

  try {
    const decoded = jwt.verify(token, JWT_SECRET);
    
    // Get employee and tenant info
    const employeeResult = await pool.query(`
      SELECT e.*, t.slug as tenant_slug, t.name as tenant_name, t.status as tenant_status,
             l.name as location_name, l.id as location_id
      FROM employees e
      JOIN tenants t ON e.tenant_id = t.id
      LEFT JOIN locations l ON e.location_id = l.id
      WHERE e.id = $1 AND e.is_active = true AND t.status = 'active'
    `, [decoded.employeeId]);

    if (employeeResult.rows.length === 0) {
      return res.status(403).json({ error: 'Invalid or inactive user' });
    }

    const employee = employeeResult.rows[0];
    
    req.user = {
      id: employee.id,
      employeeId: employee.id, // For compatibility
      name: employee.name,
      role: employee.role,
      permissions: employee.permissions || [],
      tenantId: employee.tenant_id, // For compatibility
      tenant_id: employee.tenant_id,
      tenant_slug: employee.tenant_slug,
      tenant_name: employee.tenant_name,
      locationId: employee.location_id, // For compatibility
      location_id: employee.location_id,
      location_name: employee.location_name
    };

    next();
  } catch (error) {
    console.error('Token verification error:', error);
    return res.status(403).json({ error: 'Invalid token' });
  }
};

// Middleware to check specific permissions
const requirePermission = (permissionOrRole, action = null) => {
  return (req, res, next) => {
    const { role, permissions } = req.user;

    // If checking for a specific role (like 'super_admin')
    if (!action && typeof permissionOrRole === 'string') {
      // Grant access if user has the required role, or if they're super_admin, tenant_admin, or admin
      if (role === permissionOrRole || role === 'super_admin' || role === 'tenant_admin' || role === 'admin') {
        return next();
      }
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    // Super admin, tenant admin, and admin have all permissions for comprehensive restaurant management
    if (role === 'super_admin' || role === 'tenant_admin' || role === 'admin' || role === 'manager') {
      return next();
    }

    // Ensure permissions is an array
    const userPermissions = Array.isArray(permissions) ? permissions : [];

    // Check if user has specific permission
    const hasPermission = userPermissions.some(p =>
      (typeof p === 'string' && p === 'all') ||
      (typeof p === 'object' && p.resource === permissionOrRole && p.action === action)
    );

    if (!hasPermission) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }

    next();
  };
};

// Middleware to ensure tenant isolation
const ensureTenantIsolation = (req, res, next) => {
  // Add tenant_id to query parameters for database queries
  req.tenantId = req.user.tenant_id;
  req.locationId = req.user.location_id;
  next();
};

// Generate JWT token
const generateToken = (employee, tenant) => {
  return jwt.sign(
    {
      employeeId: employee.id,
      tenantId: tenant.id,
      role: employee.role
    },
    JWT_SECRET,
    { expiresIn: '24h' }
  );
};

// Middleware for super admin access
const requireSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'super_admin') {
    return res.status(403).json({
      error: 'Super admin access required',
      message: 'This endpoint requires super administrator privileges'
    });
  }
  next();
};

// Middleware for admin access (includes super_admin, tenant_admin, admin)
const requireAdmin = (req, res, next) => {
  if (!['super_admin', 'tenant_admin', 'admin'].includes(req.user.role)) {
    return res.status(403).json({
      error: 'Admin access required',
      message: 'This endpoint requires administrator privileges'
    });
  }
  next();
};

// Middleware for tenant admin access
const requireTenantAdmin = (req, res, next) => {
  if (!['super_admin', 'tenant_admin', 'admin'].includes(req.user.role)) {
    return res.status(403).json({
      error: 'Admin access required',
      message: 'This endpoint requires administrator privileges'
    });
  }
  next();
};

// Middleware for manager access (includes admin roles + manager)
const requireManager = (req, res, next) => {
  if (!['super_admin', 'tenant_admin', 'admin', 'manager'].includes(req.user.role)) {
    return res.status(403).json({
      error: 'Manager access required',
      message: 'This endpoint requires manager privileges or higher'
    });
  }
  next();
};

// Middleware for tenant access (any authenticated user of the tenant)
const requireTenantAccess = (req, res, next) => {
  if (!req.user.tenant_id) {
    return res.status(403).json({
      error: 'Tenant access required',
      message: 'This endpoint requires valid tenant access'
    });
  }
  next();
};

// Enhanced authentication with session management
const authenticateTokenWithSession = async (req, res, next) => {
  try {
    // First run standard authentication
    await new Promise((resolve, reject) => {
      authenticateToken(req, res, (err) => {
        if (err) reject(err);
        else resolve();
      });
    });

    // Check for active session
    const sessionResult = await pool.query(`
      SELECT * FROM user_sessions
      WHERE user_id = $1 AND is_active = true AND expires_at > NOW()
    `, [req.user.id]);

    if (sessionResult.rows.length === 0) {
      return res.status(401).json({
        error: 'Session expired',
        message: 'Please log in again'
      });
    }

    // Update session last activity
    await pool.query(`
      UPDATE user_sessions
      SET last_activity = NOW()
      WHERE user_id = $1 AND is_active = true
    `, [req.user.id]);

    next();
  } catch (error) {
    console.error('Session authentication error:', error);
    return res.status(401).json({
      error: 'Authentication failed',
      message: 'Invalid or expired session'
    });
  }
};

module.exports = {
  authenticateToken,
  authenticateTokenWithSession,
  requirePermission,
  requireSuperAdmin,
  requireAdmin,
  requireTenantAdmin,
  requireManager,
  requireTenantAccess,
  ensureTenantIsolation,
  generateToken,
  JWT_SECRET
};
