#!/usr/bin/env node

/**
 * Frontend Integration Testing Suite
 * Tests frontend-backend integration and UI functionality
 */

import puppeteer from 'puppeteer';
import colors from 'colors';

const FRONTEND_URL = 'http://localhost:5173';
const TEST_RESULTS = {
  passed: 0,
  failed: 0,
  total: 0,
  details: []
};

// Utility functions
const log = (message, type = 'info') => {
  const timestamp = new Date().toISOString();
  switch(type) {
    case 'success': console.log(`[${timestamp}] ✅ ${message}`.green); break;
    case 'error': console.log(`[${timestamp}] ❌ ${message}`.red); break;
    case 'warning': console.log(`[${timestamp}] ⚠️  ${message}`.yellow); break;
    case 'info': console.log(`[${timestamp}] ℹ️  ${message}`.blue); break;
    default: console.log(`[${timestamp}] ${message}`);
  }
};

const recordTest = (testName, passed, details = '') => {
  TEST_RESULTS.total++;
  if (passed) {
    TEST_RESULTS.passed++;
    log(`${testName} - PASSED ${details}`, 'success');
  } else {
    TEST_RESULTS.failed++;
    log(`${testName} - FAILED ${details}`, 'error');
  }
  TEST_RESULTS.details.push({ testName, passed, details, timestamp: new Date().toISOString() });
};

const testPageLoad = async (page, url, pageName) => {
  try {
    const startTime = Date.now();
    await page.goto(url, { waitUntil: 'networkidle0', timeout: 30000 });
    const loadTime = Date.now() - startTime;
    
    const title = await page.title();
    recordTest(`${pageName} Page Load`, true, `Load time: ${loadTime}ms, Title: ${title}`);
    return true;
  } catch (error) {
    recordTest(`${pageName} Page Load`, false, `Error: ${error.message}`);
    return false;
  }
};

const testNavigation = async (page) => {
  try {
    // Test Super Admin navigation
    await page.goto(`${FRONTEND_URL}/super-admin.html`, { waitUntil: 'networkidle0' });
    
    // Test navigation to different sections
    const navigationTests = [
      { selector: 'a[href="#tenant-management"]', name: 'Tenant Management' },
      { selector: 'a[href="#analytics"]', name: 'Analytics' },
      { selector: 'a[href="#system-health"]', name: 'System Health' }
    ];
    
    for (const test of navigationTests) {
      try {
        await page.click(test.selector);
        await page.waitForTimeout(1000); // Wait for navigation
        const currentUrl = page.url();
        recordTest(`Navigation to ${test.name}`, currentUrl.includes(test.selector.match(/#(.+)"/)[1]), 
          `URL: ${currentUrl}`);
      } catch (error) {
        recordTest(`Navigation to ${test.name}`, false, `Error: ${error.message}`);
      }
    }
  } catch (error) {
    recordTest('Navigation Testing', false, `Error: ${error.message}`);
  }
};

const testDataFlow = async (page) => {
  try {
    await page.goto(`${FRONTEND_URL}/super-admin.html`, { waitUntil: 'networkidle0' });
    
    // Wait for data to load
    await page.waitForTimeout(3000);
    
    // Check if metrics are displayed
    const metricsElements = await page.$$('[data-testid="metric-card"], .metric-card, .bg-white');
    recordTest('Metrics Data Display', metricsElements.length > 0, 
      `Found ${metricsElements.length} metric elements`);
    
    // Check for tenant data
    const tenantElements = await page.$$('[data-testid="tenant-item"], .tenant-card');
    recordTest('Tenant Data Display', tenantElements.length >= 0, 
      `Found ${tenantElements.length} tenant elements`);
    
    // Check for charts/analytics
    const chartElements = await page.$$('svg, canvas, .recharts-wrapper');
    recordTest('Analytics Charts Display', chartElements.length >= 0, 
      `Found ${chartElements.length} chart elements`);
    
  } catch (error) {
    recordTest('Data Flow Testing', false, `Error: ${error.message}`);
  }
};

const testResponsiveness = async (page) => {
  const viewports = [
    { width: 1920, height: 1080, name: 'Desktop' },
    { width: 1024, height: 768, name: 'Tablet' },
    { width: 375, height: 667, name: 'Mobile' }
  ];
  
  for (const viewport of viewports) {
    try {
      await page.setViewport(viewport);
      await page.goto(`${FRONTEND_URL}/super-admin.html`, { waitUntil: 'networkidle0' });
      
      // Check if page renders properly
      const bodyElement = await page.$('body');
      const isVisible = await bodyElement.isIntersectingViewport();
      
      recordTest(`${viewport.name} Responsiveness`, isVisible, 
        `Viewport: ${viewport.width}x${viewport.height}`);
    } catch (error) {
      recordTest(`${viewport.name} Responsiveness`, false, `Error: ${error.message}`);
    }
  }
};

const testErrorHandling = async (page) => {
  try {
    // Test invalid URL
    await page.goto(`${FRONTEND_URL}/nonexistent.html`, { waitUntil: 'networkidle0' });
    const pageContent = await page.content();
    recordTest('Invalid URL Handling', pageContent.includes('404') || pageContent.includes('Not Found'), 
      'Properly handles invalid URLs');
  } catch (error) {
    recordTest('Invalid URL Handling', true, 'Correctly throws error for invalid URL');
  }
};

const testPerformance = async (page) => {
  try {
    // Enable performance monitoring
    await page.tracing.start({ path: 'trace.json' });
    
    const startTime = Date.now();
    await page.goto(`${FRONTEND_URL}/super-admin.html`, { waitUntil: 'networkidle0' });
    const loadTime = Date.now() - startTime;
    
    await page.tracing.stop();
    
    // Check performance metrics
    const performanceMetrics = await page.metrics();
    
    recordTest('Page Load Performance', loadTime < 5000, 
      `Load time: ${loadTime}ms, JS Heap: ${Math.round(performanceMetrics.JSHeapUsedSize / 1024 / 1024)}MB`);
    
    recordTest('Memory Usage', performanceMetrics.JSHeapUsedSize < 50 * 1024 * 1024, 
      `JS Heap: ${Math.round(performanceMetrics.JSHeapUsedSize / 1024 / 1024)}MB`);
    
  } catch (error) {
    recordTest('Performance Testing', false, `Error: ${error.message}`);
  }
};

const runFrontendTests = async () => {
  console.log('🎨 Starting Frontend Integration Testing Suite...'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  
  let browser;
  let page;
  
  try {
    // Launch browser
    browser = await puppeteer.launch({ 
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    page = await browser.newPage();
    
    // Set user agent
    await page.setUserAgent('Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    
    // Test different pages
    await testPageLoad(page, `${FRONTEND_URL}/super-admin.html`, 'Super Admin Dashboard');
    await testPageLoad(page, `${FRONTEND_URL}/unified-pos.html`, 'Unified POS');
    
    // Test navigation
    await testNavigation(page);
    
    // Test data flow
    await testDataFlow(page);
    
    // Test responsiveness
    await testResponsiveness(page);
    
    // Test error handling
    await testErrorHandling(page);
    
    // Test performance
    await testPerformance(page);
    
  } catch (error) {
    console.error('💥 Frontend test suite failed:', error.message);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  // Print results
  console.log('\n' + '=' .repeat(60).cyan);
  console.log('📊 FRONTEND TEST RESULTS'.cyan.bold);
  console.log('=' .repeat(60).cyan);
  console.log(`Total Tests: ${TEST_RESULTS.total}`.white);
  console.log(`Passed: ${TEST_RESULTS.passed}`.green);
  console.log(`Failed: ${TEST_RESULTS.failed}`.red);
  console.log(`Success Rate: ${((TEST_RESULTS.passed / TEST_RESULTS.total) * 100).toFixed(1)}%`.yellow);
  
  if (TEST_RESULTS.failed > 0) {
    console.log('\n❌ FAILED TESTS:'.red.bold);
    TEST_RESULTS.details.filter(t => !t.passed).forEach(test => {
      console.log(`  - ${test.testName}: ${test.details}`.red);
    });
  }
  
  console.log('\n' + (TEST_RESULTS.failed === 0 ? '✅ ALL FRONTEND TESTS PASSED!'.green.bold : '⚠️ SOME FRONTEND TESTS FAILED'.red.bold));
};

// Run tests
runFrontendTests();
